package com.gzhuxn.personals.controller.app.recommend.vo.user;

import com.gzhuxn.personals.domain.user.UserDetail;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppRecommendUserPageVoToUserDetailMapperImpl implements AppRecommendUserPageVoToUserDetailMapper {

    @Override
    public UserDetail convert(AppRecommendUserPageVo source) {
        if ( source == null ) {
            return null;
        }

        UserDetail userDetail = new UserDetail();

        userDetail.setAddrProvinceCode( source.getAddrProvince() );
        userDetail.setAddrNewProvinceCode( source.getAddrNewProvince() );
        userDetail.setEdu( source.getEdu() );
        userDetail.setJob( source.getJob() );

        return userDetail;
    }

    @Override
    public UserDetail convert(AppRecommendUserPageVo source, UserDetail target) {
        if ( source == null ) {
            return target;
        }

        target.setAddrProvinceCode( source.getAddrProvince() );
        target.setAddrNewProvinceCode( source.getAddrNewProvince() );
        target.setEdu( source.getEdu() );
        target.setJob( source.getJob() );

        return target;
    }
}
