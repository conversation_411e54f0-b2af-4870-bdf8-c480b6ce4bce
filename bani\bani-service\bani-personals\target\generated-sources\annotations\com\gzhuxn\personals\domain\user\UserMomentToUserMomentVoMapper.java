package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentVoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentCreateBoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentUpdateBoToUserMomentMapper;
import com.gzhuxn.personals.domain.user.bo.UserMomentBoToUserMomentMapper;
import com.gzhuxn.personals.domain.user.vo.UserMomentVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserMomentCreateBoToUserMomentMapper.class,AppUserMomentUpdateBoToUserMomentMapper.class,AppRecommendMomentVoToUserMomentMapper.class,UserMomentBoToUserMomentMapper.class,UserMomentToAppUserMomentPageVoMapper.class,UserMomentToAppRecommendMomentPageVoMapper.class,UserMomentToAppRecommendMomentDetailVoMapper.class,UserMomentToAppUserMomentDetailVoMapper.class,UserMomentToAppRecommendMomentVoMapper.class},
    imports = {}
)
public interface UserMomentToUserMomentVoMapper extends BaseMapper<UserMoment, UserMomentVo> {
}
