package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserAlbumBoToUserAlbumMapper;
import com.gzhuxn.personals.domain.user.vo.UserAlbumVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAlbumBoToUserAlbumMapper.class,UserAlbumToAppUserAlbumListVoMapper.class},
    imports = {}
)
public interface UserAlbumToUserAlbumVoMapper extends BaseMapper<UserAlbum, UserAlbumVo> {
}
