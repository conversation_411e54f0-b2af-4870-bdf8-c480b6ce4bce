package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.vo.MsgContentVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentToMsgContentVoMapperImpl implements MsgContentToMsgContentVoMapper {

    @Override
    public MsgContentVo convert(MsgContent arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContentVo msgContentVo = new MsgContentVo();

        msgContentVo.setId( arg0.getId() );
        msgContentVo.setType( arg0.getType() );
        msgContentVo.setSubType( arg0.getSubType() );
        msgContentVo.setContent( arg0.getContent() );

        return msgContentVo;
    }

    @Override
    public MsgContentVo convert(MsgContent arg0, MsgContentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}
