package com.gzhuxn.personals.controller.app.recommend.bo.user;

import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppNearbyUserQueryBoToAppRecommendUserPageBoMapperImpl implements AppNearbyUserQueryBoToAppRecommendUserPageBoMapper {

    @Override
    public AppRecommendUserPageBo convert(AppNearbyUserQueryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppRecommendUserPageBo appRecommendUserPageBo = new AppRecommendUserPageBo();

        appRecommendUserPageBo.setAgeMin( arg0.getAgeMin() );
        appRecommendUserPageBo.setAgeMax( arg0.getAgeMax() );
        appRecommendUserPageBo.setHeightMin( arg0.getHeightMin() );
        appRecommendUserPageBo.setHeightMax( arg0.getHeightMax() );
        appRecommendUserPageBo.setEducation( arg0.getEducation() );
        appRecommendUserPageBo.setLocation( arg0.getLocation() );
        appRecommendUserPageBo.setUserId( arg0.getUserId() );
        appRecommendUserPageBo.setPid( arg0.getPid() );
        appRecommendUserPageBo.setIsMatched( arg0.getIsMatched() );
        appRecommendUserPageBo.setLon( arg0.getLon() );
        appRecommendUserPageBo.setLat( arg0.getLat() );
        appRecommendUserPageBo.setDistance( arg0.getDistance() );

        return appRecommendUserPageBo;
    }

    @Override
    public AppRecommendUserPageBo convert(AppNearbyUserQueryBo arg0, AppRecommendUserPageBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAgeMin( arg0.getAgeMin() );
        arg1.setAgeMax( arg0.getAgeMax() );
        arg1.setHeightMin( arg0.getHeightMin() );
        arg1.setHeightMax( arg0.getHeightMax() );
        arg1.setEducation( arg0.getEducation() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPid( arg0.getPid() );
        arg1.setIsMatched( arg0.getIsMatched() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setDistance( arg0.getDistance() );

        return arg1;
    }
}
