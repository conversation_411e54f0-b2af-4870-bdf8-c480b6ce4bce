{"version": 3, "file": "recommend.js", "sources": ["pagesubs/my/setting/recommend.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcc2V0dGluZ1xyZWNvbW1lbmQudnVl"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"推荐设置\" :show-back=\"true\" @heightChange=\"handleNavHeightChange\">\n\t\t<template #content>\n\t\t\t<!-- 推荐设置内容 -->\n\t\t\t<view class=\"main-container\">\n\t\t\t<!-- 提示卡片 -->\n\t\t\t<view class=\"tips-card\">\n\t\t\t\t<view class=\"tips-header\">\n\t\t\t\t\t<uni-icons type=\"info-filled\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t<text class=\"tips-title\">智能推荐说明</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"tips-text\">根据您的择偶条件，为您推荐匹配的用户。条件不足时会适当放宽匹配范围，设置次日生效。</text>\n\t\t\t</view>\n\n\t\t\t<!-- 设置卡片 -->\n\t\t\t<view class=\"settings-card\">\n\t\t\t\t<!-- 年龄设置 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-title\">年龄范围</text>\n\t\t\t\t\t\t<text class=\"setting-value\">{{ recommendConfig.ageMin }}岁 - {{ recommendConfig.ageMax }}岁</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container\">\n\t\t\t\t\t\t<slider-range :min=\"18\" :max=\"70\" :step=\"1\"\n\t\t\t\t\t\t\t:value=\"[recommendConfig.ageMin, recommendConfig.ageMax]\" @change=\"onAgeChange\"\n\t\t\t\t\t\t\tactiveColor=\"#696CF3\" backgroundColor=\"#E5E6F3\" block-size=\"24\" :format=\"formatAge\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 身高设置 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-title\">身高范围</text>\n\t\t\t\t\t\t<text class=\"setting-value\">{{ recommendConfig.heightMin }}cm - {{ recommendConfig.heightMax\n\t\t\t\t\t\t}}cm</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"slider-container\">\n\t\t\t\t\t\t<slider-range :min=\"140\" :max=\"220\" :step=\"5\"\n\t\t\t\t\t\t\t:value=\"[recommendConfig.heightMin, recommendConfig.heightMax]\" @change=\"onHeightChange\"\n\t\t\t\t\t\t\tactiveColor=\"#696CF3\" backgroundColor=\"#E5E6F3\" block-size=\"24\" :format=\"formatHeight\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 学历设置 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-title\">学历要求</text>\n\t\t\t\t\t\t<text class=\"setting-subtitle\">最低学历要求</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<picker @change=\"onEducationChange\" :value=\"educationIndex\" :range=\"educationOptions\">\n\t\t\t\t\t\t<view class=\"picker-container\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">{{ educationOptions[educationIndex] }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 地区设置 -->\n\t\t\t\t<view class=\"setting-item\">\n\t\t\t\t\t<view class=\"setting-header\">\n\t\t\t\t\t\t<text class=\"setting-title\">地区要求</text>\n\t\t\t\t\t\t<text class=\"setting-subtitle\">期望交友范围</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<picker @change=\"onLocationChange\" :value=\"locationIndex\" :range=\"locationOptions\">\n\t\t\t\t\t\t<view class=\"picker-container\">\n\t\t\t\t\t\t\t<text class=\"picker-text\">{{ locationOptions[locationIndex] }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</picker>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 保存按钮 -->\n\t\t\t<view class=\"action-container\">\n\t\t\t\t<button class=\"save-button\" @click=\"saveSettings\" :disabled=\"isSaving\">\n\t\t\t\t\t<text v-if=\"!isSaving\">保存设置</text>\n\t\t\t\t\t<text v-else>保存中...</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { getUserConfigMap, updateUserConfig } from '@/api/my/config'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport $store from '@/store'\n\n// 导航栏高度\nconst navBarHeight = ref(0)\n\n// 推荐设置相关数据\nconst recommendConfig = ref({\n\tageMin: 18,\n\tageMax: 70,\n\theightMin: 140,\n\theightMax: 220,\n\teducation: 0,\n\tlocation: 0\n})\nconst educationIndex = ref(0)\nconst locationIndex = ref(0)\nconst isSaving = ref(false)\n\nconst educationOptions = $store.dict.getNames('recommend_set_education')\nconst locationOptions = $store.dict.getNames('recommend_set_location')\n\n// 导航栏高度变化\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 格式化年龄显示\nconst formatAge = (val) => {\n\treturn val + '岁'\n}\n\n// 格式化身高显示\nconst formatHeight = (val) => {\n\treturn val + 'cm'\n}\n\n// 年龄范围变化\nconst onAgeChange = (e) => {\n\trecommendConfig.value.ageMin = e[0]\n\trecommendConfig.value.ageMax = e[1]\n}\n\n// 身高范围变化\nconst onHeightChange = (e) => {\n\trecommendConfig.value.heightMin = e[0]\n\trecommendConfig.value.heightMax = e[1]\n}\n\n// 学历选择变化\nconst onEducationChange = (e) => {\n\teducationIndex.value = e.detail.value\n}\n\n// 地区选择变化\nconst onLocationChange = (e) => {\n\tlocationIndex.value = e.detail.value\n}\n\n// 保存设置\nconst saveSettings = async () => {\n\tif (isSaving.value) return\n\n\t// 验证数据\n\tif (recommendConfig.value.ageMin >= recommendConfig.value.ageMax) {\n\t\tuni.showToast({\n\t\t\ttitle: '年龄范围设置错误',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\tif (recommendConfig.value.heightMin >= recommendConfig.value.heightMax) {\n\t\tuni.showToast({\n\t\t\ttitle: '身高范围设置错误',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\tisSaving.value = true\n\trecommendConfig.value.education = $store.dict.getIdByIndex('recommend_set_education', educationIndex.value)\n\trecommendConfig.value.location = $store.dict.getIdByIndex('recommend_set_location', locationIndex.value)\n\tupdateUserConfig({\n\t\tconfigKey: 'user_recommend_settings',\n\t\tval: JSON.stringify(recommendConfig.value)\n\t}).then(res => {\n\t\tuni.showToast({\n\t\t\ttitle: '设置保存成功',\n\t\t\ticon: 'success'\n\t\t})\n\t\t// 延迟返回上一页\n\t\tsetTimeout(() => {\n\t\t\tuni.navigateBack()\n\t\t\tisSaving.value = false\n\t\t}, 1500)\n\t})\n}\n// 页面加载时获取当前设置\nonLoad(() => {\n\tgetUserConfigMap(2).then(res => {\n\t\tif (res.data) {\n\t\t\tconst config = JSON.parse(res.data['user_recommend_settings'])\n\t\t\tObject.assign(recommendConfig.value,config)\n\t\t\teducationIndex.value = $store.dict.getIndexById('recommend_set_education', config.education)\n\t\t\tlocationIndex.value = $store.dict.getIndexById('recommend_set_location', config.location)\n\t\t}\n\t})\n})\n\n</script>\n\n<style lang=\"scss\" scoped>\n// 引入uni.scss变量\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg,\n\t\t\trgba(105, 108, 243, 0.08) 0%,\n\t\t\trgba(105, 108, 243, 0.05) 30%,\n\t\t\trgba(105, 108, 243, 0.02) 60%,\n\t\t\trgba(255, 255, 255, 1) 100%);\n}\n\n.main-container {\n\tmargin-top: 20rpx;\n\tpadding: 0 24rpx 24rpx;\n\n\t.tips-card {\n\t\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.08), rgba(155, 157, 245, 0.12));\n\t\tborder-radius: 20rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tborder: 1rpx solid rgba(105, 108, 243, 0.15);\n\t\tbackdrop-filter: blur(10rpx);\n\n\t\t.tips-header {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t.tips-title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tmargin-left: 12rpx;\n\t\t\t}\n\t\t}\n\n\t\t.tips-text {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #666;\n\t\t\tline-height: 1.6;\n\t\t}\n\t}\n\n\t.settings-card {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 32rpx 24rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(105, 108, 243, 0.08);\n\t\tbackdrop-filter: blur(10rpx);\n\n\t\t.setting-item {\n\t\t\tmargin-bottom: 48rpx;\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t.setting-header {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmargin-bottom: 24rpx;\n\n\t\t\t\t.setting-title {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t}\n\n\t\t\t\t.setting-subtitle {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t}\n\n\t\t\t\t.setting-value {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #696CF3;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.slider-container {\n\t\t\t\tpadding: 0 8rpx;\n\t\t\t}\n\n\t\t\t.picker-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tpadding: 24rpx 20rpx;\n\t\t\t\tbackground: rgba(105, 108, 243, 0.05);\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tborder: 1rpx solid rgba(105, 108, 243, 0.1);\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&:active {\n\t\t\t\t\tbackground: rgba(105, 108, 243, 0.1);\n\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t}\n\n\t\t\t\t.picker-text {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.action-container {\n\t\tpadding: 24rpx 0 40rpx;\n\n\t\t.save-button {\n\t\t\twidth: 100%;\n\t\t\theight: 88rpx;\n\t\t\tline-height: 88rpx;\n\t\t\tborder-radius: 44rpx;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\t\tcolor: #fff;\n\t\t\tborder: none;\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba($primary-color, 0.2);\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.98);\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba($primary-color, 0.15);\n\t\t\t\tbackground: linear-gradient(135deg, darken($primary-color, 5%), darken($primary-color, 2%));\n\t\t\t}\n\n\t\t\t&[disabled] {\n\t\t\t\tbackground: #ccc;\n\t\t\t\tbox-shadow: none;\n\t\t\t\ttransform: none;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/setting/recommend.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "navBarHeight", "ref", "recommendConfig", "educationIndex", "locationIndex", "isSaving", "educationOptions", "$store", "locationOptions", "handleNavHeightChange", "height", "formatAge", "val", "formatHeight", "onAgeChange", "e", "onHeightChange", "onEducationChange", "onLocationChange", "saveSettings", "uni", "updateUserConfig", "res", "onLoad", "getUserConfigMap", "config", "MiniProgramPage"], "mappings": "8XAwFA,MAAMA,EAAgB,IAAW,wFAIjC,MAAMC,EAAeC,EAAG,IAAC,CAAC,EAGpBC,EAAkBD,EAAAA,IAAI,CAC3B,OAAQ,GACR,OAAQ,GACR,UAAW,IACX,UAAW,IACX,UAAW,EACX,SAAU,CACX,CAAC,EACKE,EAAiBF,EAAG,IAAC,CAAC,EACtBG,EAAgBH,EAAG,IAAC,CAAC,EACrBI,EAAWJ,EAAG,IAAC,EAAK,EAEpBK,EAAmBC,EAAM,OAAC,KAAK,SAAS,yBAAyB,EACjEC,EAAkBD,EAAM,OAAC,KAAK,SAAS,wBAAwB,EAG/DE,EAAyBC,GAAW,CACzCV,EAAa,MAAQU,CACtB,EAGMC,EAAaC,GACXA,EAAM,IAIRC,EAAgBD,GACdA,EAAM,KAIRE,EAAeC,GAAM,CAC1Bb,EAAgB,MAAM,OAASa,EAAE,CAAC,EAClCb,EAAgB,MAAM,OAASa,EAAE,CAAC,CACnC,EAGMC,EAAkBD,GAAM,CAC7Bb,EAAgB,MAAM,UAAYa,EAAE,CAAC,EACrCb,EAAgB,MAAM,UAAYa,EAAE,CAAC,CACtC,EAGME,EAAqBF,GAAM,CAChCZ,EAAe,MAAQY,EAAE,OAAO,KACjC,EAGMG,EAAoBH,GAAM,CAC/BX,EAAc,MAAQW,EAAE,OAAO,KAChC,EAGMI,EAAe,SAAY,CAChC,GAAI,CAAAd,EAAS,MAGb,IAAIH,EAAgB,MAAM,QAAUA,EAAgB,MAAM,OAAQ,CACjEkB,EAAAA,MAAI,UAAU,CACb,MAAO,WACP,KAAM,MACT,CAAG,EACD,MACA,CAED,GAAIlB,EAAgB,MAAM,WAAaA,EAAgB,MAAM,UAAW,CACvEkB,EAAAA,MAAI,UAAU,CACb,MAAO,WACP,KAAM,MACT,CAAG,EACD,MACA,CACDf,EAAS,MAAQ,GACjBH,EAAgB,MAAM,UAAYK,EAAM,OAAC,KAAK,aAAa,0BAA2BJ,EAAe,KAAK,EAC1GD,EAAgB,MAAM,SAAWK,EAAM,OAAC,KAAK,aAAa,yBAA0BH,EAAc,KAAK,EACvGiB,mBAAiB,CAChB,UAAW,0BACX,IAAK,KAAK,UAAUnB,EAAgB,KAAK,CAC3C,CAAE,EAAE,KAAKoB,GAAO,CACdF,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,SACT,CAAG,EAED,WAAW,IAAM,CAChBA,EAAAA,MAAI,aAAc,EAClBf,EAAS,MAAQ,EACjB,EAAE,IAAI,CACT,CAAE,EACF,EAEAkB,OAAAA,EAAAA,OAAO,IAAM,CACZC,EAAAA,iBAAiB,CAAC,EAAE,KAAKF,GAAO,CAC/B,GAAIA,EAAI,KAAM,CACb,MAAMG,EAAS,KAAK,MAAMH,EAAI,KAAK,uBAA0B,EAC7D,OAAO,OAAOpB,EAAgB,MAAMuB,CAAM,EAC1CtB,EAAe,MAAQI,SAAO,KAAK,aAAa,0BAA2BkB,EAAO,SAAS,EAC3FrB,EAAc,MAAQG,SAAO,KAAK,aAAa,yBAA0BkB,EAAO,QAAQ,CACxF,CACH,CAAE,CACF,CAAC,+zBClMD,GAAG,WAAWC,CAAe"}