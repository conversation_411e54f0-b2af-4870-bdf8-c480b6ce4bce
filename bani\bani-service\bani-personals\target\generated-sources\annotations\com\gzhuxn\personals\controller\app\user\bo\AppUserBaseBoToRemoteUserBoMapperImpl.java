package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserBaseBoToRemoteUserBoMapperImpl implements AppUserBaseBoToRemoteUserBoMapper {

    @Override
    public RemoteUserBo convert(AppUserBaseBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteUserBo remoteUserBo = new RemoteUserBo();

        remoteUserBo.setUserId( arg0.getUserId() );
        remoteUserBo.setNickName( arg0.getNickName() );
        remoteUserBo.setSex( arg0.getSex() );

        return remoteUserBo;
    }

    @Override
    public RemoteUserBo convert(AppUserBaseBo arg0, RemoteUserBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );

        return arg1;
    }
}
