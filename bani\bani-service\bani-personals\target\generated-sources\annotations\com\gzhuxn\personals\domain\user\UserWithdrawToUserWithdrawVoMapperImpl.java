package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserWithdrawToUserWithdrawVoMapperImpl implements UserWithdrawToUserWithdrawVoMapper {

    @Override
    public UserWithdrawVo convert(UserWithdraw arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWithdrawVo userWithdrawVo = new UserWithdrawVo();

        userWithdrawVo.setId( arg0.getId() );
        userWithdrawVo.setUserId( arg0.getUserId() );
        userWithdrawVo.setAmount( arg0.getAmount() );
        userWithdrawVo.setWithdrawQrCodeImage( arg0.getWithdrawQrCodeImage() );
        userWithdrawVo.setAuditStatus( arg0.getAuditStatus() );
        userWithdrawVo.setAuditTime( arg0.getAuditTime() );
        userWithdrawVo.setAuditUserId( arg0.getAuditUserId() );
        userWithdrawVo.setRemitStatus( arg0.getRemitStatus() );
        userWithdrawVo.setRemitTime( arg0.getRemitTime() );

        return userWithdrawVo;
    }

    @Override
    public UserWithdrawVo convert(UserWithdraw arg0, UserWithdrawVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setWithdrawQrCodeImage( arg0.getWithdrawQrCodeImage() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setAuditTime( arg0.getAuditTime() );
        arg1.setAuditUserId( arg0.getAuditUserId() );
        arg1.setRemitStatus( arg0.getRemitStatus() );
        arg1.setRemitTime( arg0.getRemitTime() );

        return arg1;
    }
}
