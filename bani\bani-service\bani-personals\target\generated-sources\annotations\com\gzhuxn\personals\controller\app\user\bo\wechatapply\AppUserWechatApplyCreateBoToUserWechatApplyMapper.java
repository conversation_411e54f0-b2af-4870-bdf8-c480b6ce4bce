package com.gzhuxn.personals.controller.app.user.bo.wechatapply;

import com.gzhuxn.personals.domain.user.UserWechatApply;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppUserWechatApplyCreateBoToUserWechatApplyMapper extends BaseMapper<AppUserWechatApplyCreateBo, UserWechatApply> {
}
