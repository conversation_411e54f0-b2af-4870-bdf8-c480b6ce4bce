package com.gzhuxn.personals.controller.app.message.vo;

import com.gzhuxn.personals.domain.message.MsgGroup;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppMsgGroupDetailVoToMsgGroupMapperImpl implements AppMsgGroupDetailVoToMsgGroupMapper {

    @Override
    public MsgGroup convert(AppMsgGroupDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroup msgGroup = new MsgGroup();

        msgGroup.setId( arg0.getId() );
        msgGroup.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            msgGroup.setNum( arg0.getNum().intValue() );
        }
        msgGroup.setType( arg0.getType() );
        msgGroup.setBusinessId( arg0.getBusinessId() );
        msgGroup.setStatus( arg0.getStatus() );
        msgGroup.setRemark( arg0.getRemark() );

        return msgGroup;
    }

    @Override
    public MsgGroup convert(AppMsgGroupDetailVo arg0, MsgGroup arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            arg1.setNum( arg0.getNum().intValue() );
        }
        else {
            arg1.setNum( null );
        }
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
