{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup>\r\nimport {\r\n\tonLaunch,\r\n\tonShow,\r\n\tonHide\r\n} from \"@dcloudio/uni-app\";\r\n//注意：一定要下载text-encoding-shim包，后台地址和token和后台约束好，复制代码后替换url和地址，即可使用。\r\nimport * as TextEncoding from \"text-encoding-shim\";\r\n\r\nlet buffer = ''; //定义在页面的最外面。\r\nlet encoder = new TextEncoding.TextDecoder(\"utf-8\");//定义在页面的最外面。\r\nconst requestTask = null; //定义在页面的最外面。\r\n\r\nonLaunch(() => {\r\n\tconsole.log('App Launch')\r\n\t//startSSE()\r\n}),\r\n\tonShow(() => {\r\n\t\tconsole.log('App Show')\r\n\t}),\r\n\tonHide(() => {\r\n\t\tconsole.log('App Hide')\r\n\t})\r\n\r\nconst startSSE = () => {\r\n\trequestTask = uni.request({\r\n\t\turl: \"/resource/sse\",\r\n\t\tmethod: 'get',\r\n\t\theader: {\r\n\t\t\t'Accept': 'text/event-stream',//必填返回的是文本\r\n\t\t\t'Cache-Control': 'no-cache',\r\n\t\t\t'Connection': 'keep-alive',\r\n\t\t\t'Authorization': 'Bearer '\r\n\t\t},\r\n\t\tresponseType: 'arraybuffer',//接受的是流\r\n\t\tenableChunked: true,//开启分包\r\n\t\tsuccess: (res) => { }\r\n\t});\r\n\r\n\trequestTask.onChunkReceived((res) => {\r\n\t\ttry {\r\n\t\t\t// 将ArrayBuffer转为字符串并追加到缓冲区\r\n\r\n\t\t\tlet arrayBuffer = new Uint8Array(res.data)\r\n\t\t\tlet chunkStr = encoder.decode(arrayBuffer);\r\n\t\t\tbuffer += chunkStr;\r\n\r\n\t\t\t// 分割完整事件（以\\n\\n分隔）\r\n\t\t\tlet eventEndIndex;\r\n\t\t\twhile ((eventEndIndex = buffer.indexOf('\\n\\n')) >= 0) {\r\n\t\t\t\tconst eventData = buffer.slice(0, eventEndIndex);\r\n\t\t\t\tbuffer = buffer.slice(eventEndIndex + 2);\r\n\r\n\t\t\t\t// 解析SSE事件内容\r\n\t\t\t\tconst message = this.parseSSEEvent(eventData);\r\n\t\t\t\tif (message) {\r\n\t\t\t\t\tconsole.log('收到事件:', message);\r\n\t\t\t\t\t// 触发自定义事件或更新数据 \r\n\t\t\t\t\t//数据拿到后，做自己的业务处理\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} catch (e) {\r\n\t\t\tconsole.error('数据处理异常:', e);\r\n\t\t}\r\n\t});\r\n}\r\n// 解析SSE事件格式\r\nconst parseSSEEvent = (rawData) => {\r\n\tconst lines = rawData.split('\\n');\r\n\tlet event = { data: '' };\r\n\r\n\tlines.forEach(line => {\r\n\t\tconst colonIndex = line.indexOf(':');\r\n\t\tif (colonIndex > 0) {\r\n\t\t\tconst field = line.slice(0, colonIndex).trim();\r\n\t\t\tconst value = line.slice(colonIndex + 1).trim();\r\n\t\t\tif (field === 'data') {\r\n\t\t\t\tevent.data += value + '\\n';\r\n\t\t\t} else if (field === 'event') {\r\n\t\t\t\tevent.type = value;\r\n\t\t\t} else if (field === 'id') {\r\n\t\t\t\tevent.id = value;\r\n\t\t\t} else if (field === 'retry') {\r\n\t\t\t\tevent.retry = parseInt(value, 10);\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tevent.data = event.data.trimEnd(); // 移除末尾换行\r\n\treturn event.data ? event : null;\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/static/fonts/iconfont.css';\r\n\r\n#app {\r\n\t//background-image: linear-gradient(to bottom, #A5A0DD, #F5F7FA);\r\n\tbackground-image: linear-gradient(110deg, #FFEEF1 10%, #FAFAFA 60%, #F5F4FB);\r\n}\r\n\r\n.margin-split {\r\n\tmargin: 0 20rpx;\r\n}\r\n\r\n.page-content {\r\n\t@extend .margin-split;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n\r\n.file-picker__box {\r\n\tborder: 0rpx dashed #e5e5e5 !important;\r\n\tbackground: #fff !important;\r\n}\r\n\r\n.top-menu {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tpadding: 32rpx;\r\n\tmargin-top: 26rpx;\r\n\tmargin-bottom: 16rpx;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\r\n\t:deep(.uni-segmented-control) {\r\n\t\tbackground-color: transparent;\r\n\r\n\t\t.segmented-control__item {\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-weight: 500;\r\n\r\n\t\t\t&.segmented-control__item--button--active {\r\n\t\t\t\tcolor: $primary-color;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import App from './App'\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport '@/utils/uni.promisify.adaptor'\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\t...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport {\r\n\tcreateSSRApp\r\n} from 'vue'\r\nimport * as Pinia from 'pinia'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\tapp.use(Pinia.createPinia())\r\n\treturn {\r\n\t\tapp,\r\n\t\tPinia\r\n\t}\r\n}\r\n// #endif"], "names": ["TextEncoding.TextDecoder", "onLaunch", "uni", "onShow", "onHide", "createApp", "app", "createSSRApp", "App", "Pinia.createPinia", "Pinia"], "mappings": "+IAUc,WAAIA,EAAAA,wBAAAA,YAAyB,OAAO,EAGlDC,EAAAA,SAAS,IAAM,CACdC,EAAAA,MAAY,MAAA,MAAA,gBAAA,YAAY,CAEzB,CAAC,EACAC,EAAAA,OAAO,IAAM,CACZD,EAAAA,MAAA,MAAA,MAAA,gBAAY,UAAU,CACxB,CAAE,EACDE,EAAAA,OAAO,IAAM,CACZF,EAAAA,MAAA,MAAA,MAAA,gBAAY,UAAU,CACxB,CAAE,WCNK,SAASG,GAAY,CAC3B,MAAMC,EAAMC,EAAY,aAACC,CAAG,EAC5B,OAAAF,EAAI,IAAIG,EAAAA,aAAmB,EACpB,CACN,IAAAH,EACF,MAAEI,EAAK,KACL,CACF"}