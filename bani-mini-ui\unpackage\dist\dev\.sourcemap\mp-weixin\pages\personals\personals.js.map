{"version": 3, "file": "personals.js", "sources": ["pages/personals/personals.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGVyc29uYWxzL3BlcnNvbmFscy52dWU"], "sourcesContent": ["<template>\r\n\r\n\t<!-- 自定义导航栏 -->\r\n\t<scroll-nav-page :enableScrollGradient=\"false\" @scroll=\"handlePageScroll\" @heightChange=\"handleNavHeightChange\">\r\n\t\t<template #nav-left>\r\n\t\t\t<text class=\"app-name\">伴你有约</text>\r\n\t\t</template>\r\n\t\t<template #content>\r\n\t\t\t<z-paging ref=\"pagePaging\" v-model=\"recommendList\" :auto-clean-list-when-reload=\"false\"\r\n\t\t\t\t:auto-scroll-to-top-when-reload=\"false\"\r\n\t\t\t\t@query=\"(pageNo, pageSize) => queryRecommendList(pageNo, pageSize)\" :auto=\"true\"\r\n\t\t\t\t:default-page-size=\"10\">\r\n\t\t\t\t<template #top>\r\n\t\t\t\t\t<view :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t<!-- 自定义刷新组件 -->\r\n\t\t\t\t<template #refresher=\"{ refresherStatus }\">\r\n\t\t\t\t\t<custom-refresher :refresher-status=\"refresherStatus\" />\r\n\t\t\t\t</template>\r\n\t\t\t\t<!-- 功能卡片区域 -->\r\n\t\t\t\t<view class=\"function-cards\">\r\n\t\t\t\t\t<view class=\"card-row\">\r\n\t\t\t\t\t\t<view class=\"function-card club-card\" @click=\"goToClub\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"card-title\">俱乐部</text>\r\n\t\t\t\t\t\t\t\t<text class=\"card-desc\">兴趣社群</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"home\" size=\"32\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"function-card partner-card\" @click=\"goToPartner\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"card-title\">找搭子</text>\r\n\t\t\t\t\t\t\t\t<text class=\"card-desc\">寻找伙伴</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"search\" size=\"32\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card-row\">\r\n\t\t\t\t\t\t<view class=\"function-card dating-card\" @click=\"goToDating\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"card-title\">相亲广场</text>\r\n\t\t\t\t\t\t\t\t<text class=\"card-desc\">缘分相遇</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"heart\" size=\"32\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"function-card chat-card\" @click=\"goToChat\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"card-title\">畅聊圈</text>\r\n\t\t\t\t\t\t\t\t<text class=\"card-desc\">自由交流</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"card-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"32\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 小程序中直接修改组件style为position: sticky;无效，需要在组件外层套一层view -->\r\n\t\t\t\t<view style=\"z-index: 100;position: sticky;top: 0;\">\r\n\t\t\t\t\t<view class=\"nav-tabs\">\r\n\t\t\t\t\t\t<view class=\"tabs-left\">\r\n\t\t\t\t\t\t\t<view v-for=\"tab in tabList\" :key=\"tab.value\" class=\"tab-item\"\r\n\t\t\t\t\t\t\t\t:class=\"{ active: currentTab === tab.value }\" @click=\"handleTabClick(tab.value)\">\r\n\t\t\t\t\t\t\t\t<text :style=\"{ color: currentTab === tab.value ? primaryColor : '#666' }\">{{\r\n\t\t\t\t\t\t\t\t\ttab.label }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"tabs-right\">\r\n\t\t\t\t\t\t\t<view class=\"filter-button\" @click=\"goToFilter\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"tune\" size=\"20\" :color=\"primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 根据当前选中的tab显示对应的列表 -->\r\n\t\t\t\t<!-- 推荐列表 -->\r\n\t\t\t\t<view class=\"recommend-list\">\r\n\t\t\t\t\t<view class=\"waterfall\">\r\n\t\t\t\t\t\t<view class=\"waterfall-column\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in localRecommendListLeft\" :key=\"item.uid || index\"\r\n\t\t\t\t\t\t\t\tclass=\"recommend-item\" @click=\"goToProfile(item)\">\r\n\t\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.avatar\" mode=\"aspectFill\" class=\"user-avatar\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ height: item.imageHeight + 'rpx' }\"></image>\r\n\t\t\t\t\t\t\t\t\t<text v-show=\"item.distance && currentTab === 'nearby'\" class=\"distance-tag\">{{\r\n\t\t\t\t\t\t\t\t\t\titem.distance\r\n\t\t\t\t\t\t\t\t\t\t}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name-row\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{ item.nickname }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"item.gender === 'male' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{ color: item.gender === 'male' ? '#4A90E2' : '#E91E63' }\"></text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.isVerified\" class=\"verified-tag\">已实名</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"location\">{{ item.currentCity }} · {{ item.hometown }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"basic-info\">\r\n\t\t\t\t\t\t\t\t\t\t{{ item.age }}岁 | {{ item.height }}cm | {{ item.occupation }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"waterfall-column\">\r\n\t\t\t\t\t\t\t<view v-for=\"(item, index) in localRecommendListRight\" :key=\"item.id || index\"\r\n\t\t\t\t\t\t\t\tclass=\"recommend-item\" @click=\"goToProfile(item)\">\r\n\t\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.avatar\" mode=\"aspectFill\" class=\"user-avatar\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ height: item.imageHeight + 'rpx' }\"></image>\r\n\t\t\t\t\t\t\t\t\t<text v-show=\"item.distance && currentTab === 'nearby'\" class=\"distance-tag\">{{\r\n\t\t\t\t\t\t\t\t\t\titem.distance\r\n\t\t\t\t\t\t\t\t\t\t}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"name-row\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{ item.nickname }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t:class=\"item.gender === 'male' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\r\n\t\t\t\t\t\t\t\t\t\t\t:style=\"{ color: item.gender === 'male' ? '#4A90E2' : '#E91E63' }\"></text>\r\n\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.isVerified\" class=\"verified-tag\">已实名</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"location\">{{ item.currentCity }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"basic-info\">\r\n\t\t\t\t\t\t\t\t\t\t{{ item.age }}岁 | {{ item.height }}cm |{{ item.occupation }}\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t<template #empty>\r\n\t\t\t\t\t<empty-state :current-tab=\"currentTab\" />\r\n\t\t\t\t</template>\r\n\t\t\t</z-paging>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n\r\n\t<!-- 关注订阅号组件 -->\r\n\t<mp-subscribe :navBarHeight=\"navBarHeight\" />\r\n</template>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { onShow } from '@dcloudio/uni-app'\r\nimport globalConfig from '@/config'\r\nimport $store from '@/store'\r\nimport {\r\n\tgetSameCityUsers,\r\n\tgetNearbyUsers,\r\n\ttransformUserData,\r\n\tbuildSameCityParams,\r\n\tbuildNearbyParams\r\n} from '@/api/recommend/recommend'\r\nimport { getLocation } from '@/utils/common.js'\r\nimport EmptyState from './components/empty-state.vue'\r\n\r\n// 响应式数据\r\nconst tabList = ref(\r\n\t$store.isUserShort()\r\n\t\t? [{ label: '附近', value: 'nearby' }]\r\n\t\t: [{ label: '同城', value: 'local' }, { label: '附近', value: 'nearby' }]\r\n)\r\nconst currentTab = ref($store.isUserShort() ? 'nearby' : 'local') // 当前选中的tab值\r\nconst pageScrollTop = ref(0)\r\nconst navBarHeight = ref(0)\r\n\r\n// 推荐列表数据\r\nconst recommendList = ref([])\r\n\r\n// 位置信息\r\nconst userLocation = ref({})\r\n\r\n// 筛选条件\r\nconst filterConditions = ref({\r\n\tageMin: null,\r\n\tageMax: null,\r\n\theightMin: null,\r\n\theightMax: null,\r\n\teducation: null,\r\n\tlocation: null\r\n})\r\n\r\n// 主题色\r\nconst primaryColor = globalConfig?.theme?.primaryColor || '#696CF3'\r\n\r\n// 将同城推荐数据分成两列\r\nconst localRecommendListLeft = computed(() => {\r\n\treturn recommendList.value.filter((_, index) => index % 2 === 0)\r\n})\r\n\r\nconst localRecommendListRight = computed(() => {\r\n\treturn recommendList.value.filter((_, index) => index % 2 === 1)\r\n})\r\n\r\n\r\n\r\n// 模板引用\r\nconst pagePaging = ref(null)\r\n\r\n// 处理标签点击\r\nconst handleTabClick = (value) => {\r\n\tif (value !== currentTab.value) {\r\n\t\tcurrentTab.value = value\r\n\t\t// 切换tab时重新加载数据\r\n\t\tif (pagePaging.value) {\r\n\t\t\tpagePaging.value.reload()\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 页面滚动处理\r\nconst handlePageScroll = (e) => {\r\n\tpageScrollTop.value = e.scrollTop || 0\r\n}\r\n\r\n// 导航栏高度变化处理\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 功能卡片点击事件\r\nconst goToClub = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/club/club'\r\n\t})\r\n}\r\n\r\nconst goToPartner = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/activity/activity'\r\n\t})\r\n}\r\n\r\nconst goToDating = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/dating/dating'\r\n\t})\r\n}\r\n\r\nconst goToChat = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pages/moment/moment'\r\n\t})\r\n}\r\n\r\n// 跳转到筛选页面\r\nconst goToFilter = () => {\r\n\t// 添加全局事件监听作为备选方案\r\n\tconst handleFilterApplied = (filterData) => {\r\n\t\t// 应用筛选条件，重新加载数据\r\n\t\tapplyFilterData(filterData)\r\n\t\t// 移除事件监听\r\n\t\tuni.$off('filterApplied', handleFilterApplied)\r\n\t}\r\n\r\n\t// 监听全局事件\r\n\tuni.$on('filterApplied', handleFilterApplied)\r\n\r\n\tuni.navigateTo({\r\n\t\turl: '/pagesubs/personals/filter',\r\n\t\tanimationType: 'slide-in-right',\r\n\t\tevents: {\r\n\t\t\t// 监听筛选结果\r\n\t\t\tfilterApplied: (filterData) => {\r\n\t\t\t\t// 应用筛选条件，重新加载数据\r\n\t\t\t\tapplyFilterData(filterData)\r\n\t\t\t\t// 移除全局事件监听\r\n\t\t\t\tuni.$off('filterApplied', handleFilterApplied)\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 应用筛选数据\r\nconst applyFilterData = (filterData) => {\r\n\t// 合并筛选条件\r\n\tfilterConditions.value = { ...filterConditions.value, ...filterData }\r\n\r\n\t// 重新加载数据\r\n\tif (pagePaging.value) {\r\n\t\tpagePaging.value.reload()\r\n\t}\r\n\r\n\tuni.showToast({\r\n\t\ttitle: '筛选条件已应用',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\n// 查询推荐列表（z-paging 回调）- 根据当前tab调用不同接口\r\nconst queryRecommendList = (pageNo, pageSize) => {\r\n\tif (currentTab.value === 'local') {\r\n\t\t// 同城推荐\r\n\t\tconst params = buildSameCityParams(pageNo, pageSize, filterConditions.value)\r\n\t\tgetSameCityUsers(params).then(response => {\r\n\t\t\tconst transformedData = response.rows.map(item => transformUserData(item))\r\n\t\t\tpagePaging.value.complete(transformedData)\r\n\t\t})\r\n\t} else if (currentTab.value === 'nearby') {\r\n\t\tgetLocation().then((location) => {\r\n\t\t\tuserLocation.value = location\r\n\t\t\t// 附近推荐\r\n\t\t\tconst params = buildNearbyParams(\r\n\t\t\t\tpageNo,\r\n\t\t\t\tpageSize,\r\n\t\t\t\tuserLocation.value.longitude,\r\n\t\t\t\tuserLocation.value.latitude,\r\n\t\t\t\tfilterConditions.value\r\n\t\t\t)\r\n\t\t\tgetNearbyUsers(params).then(response => {\r\n\t\t\t\tconst transformedData = response.rows.map(item => transformUserData(item))\r\n\t\t\t\tpagePaging.value.complete(transformedData)\r\n\t\t\t})\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 跳转到用户详情页\r\nconst goToProfile = (user) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/personals/profile?userId=${user.id}`\r\n\t})\r\n}\r\n\r\n// 手动触发数据加载\r\nconst loadDefaultData = () => {\r\n\tif (pagePaging.value) {\r\n\t\tpagePaging.value.reload()\r\n\t}\r\n}\r\n\r\n// 页面加载时初始化数据\r\n// onMounted(() => {\r\n// \tloadDefaultData() // 注释掉，因为z-paging的auto=true会自动触发查询\r\n// })\r\n\r\n// 页面显示时重新加载数据（tabBar重复点击时会触发）\r\nonShow(() => {\r\n\tconsole.log('推荐页面显示，重新加载数据')\r\n\tloadDefaultData()\r\n})\r\n\r\n// 页面卸载时清理事件监听\r\nonUnmounted(() => {\r\n\t// 清理可能残留的全局事件监听\r\n\tuni.$off('filterApplied')\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/uni.scss';\r\n\r\n.app-name {\r\n\tfont-family: 'SimSun', '宋体', serif;\r\n\tfont-weight: bold;\r\n\tfont-size: $title-size-md;\r\n}\r\n\r\n.content {\r\n\tmin-height: 100vh;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.function-cards {\r\n\tmargin-top: 20rpx;\r\n\tpadding: 0 24rpx;\r\n\tmargin-bottom: 32rpx;\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\tbackground: #f8f9fa;\r\n}\r\n\r\n.card-row {\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.function-card {\r\n\tflex: 1;\r\n\theight: 120rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\tbox-shadow: 0 4rpx 20rpx rgba(105, 108, 243, 0.15);\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n\r\n\t&::before {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));\r\n\t\tborder-radius: 20rpx;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.card-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\t\tz-index: 2;\r\n\r\n\t\t.card-title {\r\n\t\t\tfont-size: $font-size-lg;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: white;\r\n\t\t\tmargin-bottom: 8rpx;\r\n\t\t}\r\n\r\n\t\t.card-desc {\r\n\t\t\tfont-size: $font-size-xs;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t}\r\n\t}\r\n\r\n\t.card-icon {\r\n\t\twidth: 65rpx;\r\n\t\theight: 65rpx;\r\n\t\tpadding: 10rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 2;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tflex-shrink: 0;\r\n\t}\r\n}\r\n\r\n.club-card {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.partner-card {\r\n\tbackground: linear-gradient(135deg, #f093fb, #f5576c);\r\n}\r\n\r\n.dating-card {\r\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\r\n}\r\n\r\n.chat-card {\r\n\tbackground: linear-gradient(135deg, #43e97b, #38f9d7);\r\n}\r\n\r\n.nav-tabs {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\theight: 44px;\r\n\tposition: relative;\r\n\tpadding: 0 12rpx;\r\n\tbackground: white;\r\n\r\n\t.tabs-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.tabs-right {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-right: 14rpx;\r\n\r\n\t\t.filter-button {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: rgba(105, 108, 243, 0.1);\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\tbackground: rgba(105, 108, 243, 0.2);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tab-item {\r\n\t\tposition: relative;\r\n\t\tpadding: 0 20rpx;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcursor: pointer;\r\n\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n\t\ttext {\r\n\t\t\tfont-size: $font-size-md;\r\n\t\t\tfont-weight: bold;\r\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\t\ttransform-origin: center;\r\n\t\t}\r\n\r\n\t\t&.active {\r\n\t\t\ttransform: scale(1.25);\r\n\t\t\tz-index: 10;\r\n\r\n\t\t\ttext {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttransform: scale(1.15);\r\n\t\t\t}\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: -10rpx;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\twidth: 36rpx;\r\n\t\t\t\theight: 6rpx;\r\n\t\t\t\tbackground: $primary-color;\r\n\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\tanimation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover:not(.active) {\r\n\t\t\ttransform: scale(1.08);\r\n\r\n\t\t\ttext {\r\n\t\t\t\ttransform: scale(1.05);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes scaleIn {\r\n\t0% {\r\n\t\twidth: 0;\r\n\t\topacity: 0;\r\n\t\ttransform: translateX(-50%) scaleX(0);\r\n\t}\r\n\r\n\t50% {\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t100% {\r\n\t\twidth: 36rpx;\r\n\t\topacity: 1;\r\n\t\ttransform: translateX(-50%) scaleX(1);\r\n\t}\r\n}\r\n\r\n.paging-content {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.tab-content {\r\n\tflex: 1;\r\n\toverflow: hidden;\r\n}\r\n\r\n/* 自定义下拉刷新样式 */\r\n.custom-refresher {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmin-height: 120rpx;\r\n\tbackground: transparent;\r\n}\r\n\r\n.refresher-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.refresher-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.refresher-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 50%;\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tbox-shadow: 0 4rpx 20rpx rgba(105, 108, 243, 0.1);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.refresher-icon.release {\r\n\tbackground: $primary-color;\r\n\ttransform: scale(1.1);\r\n}\r\n\r\n.refresher-icon.release .iconfont {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.refresher-icon.complete {\r\n\tbackground: $success-color;\r\n}\r\n\r\n.refresher-icon.complete .iconfont {\r\n\tcolor: #ffffff;\r\n}\r\n\r\n.refresher-icon .iconfont {\r\n\tfont-size: 32rpx;\r\n\tcolor: $primary-color;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.refresher-text {\r\n\tfont-size: $font-size-sm;\r\n\tcolor: $text-secondary;\r\n\tfont-weight: $font-weight-medium;\r\n}\r\n\r\n/* 加载动画 */\r\n.loading-spinner {\r\n\twidth: 32rpx;\r\n\theight: 32rpx;\r\n\tborder: 4rpx solid rgba(105, 108, 243, 0.2);\r\n\tborder-top: 4rpx solid $primary-color;\r\n\tborder-radius: 50%;\r\n\tanimation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n\t0% {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n.recommend-list {\r\n\tpadding: 24rpx;\r\n\r\n\t.waterfall {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.waterfall-column {\r\n\t\twidth: 48.5%;\r\n\t}\r\n\r\n\t.recommend-item {\r\n\t\tbackground: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcursor: pointer;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.98);\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(105, 108, 243, 0.15);\r\n\t\t}\r\n\r\n\t\t.avatar-container {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.user-avatar {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t\t}\r\n\r\n\t\t.distance-tag {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 8rpx;\r\n\t\t\tleft: 8rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tcolor: white;\r\n\t\t\tbackground: rgba(105, 108, 243, 0.9);\r\n\t\t\tpadding: 4rpx 8rpx;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\r\n\t\t\tz-index: 10;\r\n\t\t\ttransition: opacity 0.3s ease;\r\n\t\t}\r\n\r\n\t\t.user-info {\r\n\t\t\tpadding: 16rpx;\r\n\r\n\t\t\t.name-row {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\r\n\t\t\t\t.nickname {\r\n\t\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.gender-icon {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.verified-tag {\r\n\t\t\t\t\tbackground: $primary-color;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.location {\r\n\t\t\t\tfont-size: $font-size-sm;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.basic-info {\r\n\t\t\t\tfont-size: $font-size-sm;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 空状态样式已移至独立组件 empty-state.vue</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pages/personals/personals.vue'\nwx.createPage(MiniProgramPage)"], "names": ["EmptyState", "tabList", "ref", "$store", "currentTab", "pageScrollTop", "navBarHeight", "recommendList", "userLocation", "filterConditions", "primaryColor", "globalConfig", "localRecommendListLeft", "computed", "_", "index", "localRecommendListRight", "pagePaging", "handleTabClick", "value", "handlePageScroll", "e", "handleNavHeightChange", "height", "goToClub", "uni", "goTo<PERSON><PERSON>ner", "goToDating", "goToChat", "goToFilter", "handleFilterApplied", "filterData", "applyFilterData", "queryRecommendList", "pageNo", "pageSize", "params", "buildSameCityParams", "getSameCityUsers", "response", "transformedData", "item", "transformUserData", "getLocation", "location", "buildNearbyParams", "getNearbyUsers", "goToProfile", "user", "loadDefaultData", "onShow", "onUnmounted", "MiniProgramPage"], "mappings": "ovBAkKA,MAAMA,EAAa,IAAW,qEAG9B,MAAMC,EAAUC,EAAG,IAClBC,EAAAA,OAAO,YAAa,EACjB,CAAC,CAAE,MAAO,KAAM,MAAO,QAAQ,CAAE,EACjC,CAAC,CAAE,MAAO,KAAM,MAAO,OAAO,EAAI,CAAE,MAAO,KAAM,MAAO,SAAU,CACtE,EACMC,EAAaF,EAAG,IAACC,EAAM,OAAC,YAAW,EAAK,SAAW,OAAO,EAC1DE,EAAgBH,EAAG,IAAC,CAAC,EACrBI,EAAeJ,EAAG,IAAC,CAAC,EAGpBK,EAAgBL,EAAG,IAAC,EAAE,EAGtBM,EAAeN,EAAG,IAAC,EAAE,EAGrBO,EAAmBP,EAAAA,IAAI,CAC5B,OAAQ,KACR,OAAQ,KACR,UAAW,KACX,UAAW,KACX,UAAW,KACX,SAAU,IACX,CAAC,EAGKQ,IAAeC,GAAAA,EAAAA,EAAY,eAAZA,YAAAA,EAAc,QAAdA,YAAAA,EAAqB,eAAgB,UAGpDC,EAAyBC,EAAQ,SAAC,IAChCN,EAAc,MAAM,OAAO,CAACO,EAAGC,IAAUA,EAAQ,IAAM,CAAC,CAC/D,EAEKC,EAA0BH,EAAQ,SAAC,IACjCN,EAAc,MAAM,OAAO,CAACO,EAAGC,IAAUA,EAAQ,IAAM,CAAC,CAC/D,EAKKE,EAAaf,EAAG,IAAC,IAAI,EAGrBgB,EAAkBC,GAAU,CAC7BA,IAAUf,EAAW,QACxBA,EAAW,MAAQe,EAEfF,EAAW,OACdA,EAAW,MAAM,OAAQ,EAG5B,EAGMG,EAAoBC,GAAM,CAC/BhB,EAAc,MAAQgB,EAAE,WAAa,CACtC,EAGMC,EAAyBC,GAAW,CACzCjB,EAAa,MAAQiB,CACtB,EAGMC,EAAW,IAAM,CACtBC,EAAAA,MAAI,WAAW,CACd,IAAK,kBACP,CAAE,CACF,EAEMC,EAAc,IAAM,CACzBD,EAAAA,MAAI,WAAW,CACd,IAAK,0BACP,CAAE,CACF,EAEME,EAAa,IAAM,CACxBF,EAAAA,MAAI,WAAW,CACd,IAAK,sBACP,CAAE,CACF,EAEMG,EAAW,IAAM,CACtBH,EAAAA,MAAI,WAAW,CACd,IAAK,sBACP,CAAE,CACF,EAGMI,EAAa,IAAM,CAExB,MAAMC,EAAuBC,GAAe,CAE3CC,EAAgBD,CAAU,EAE1BN,QAAI,KAAK,gBAAiBK,CAAmB,CAC7C,EAGDL,QAAI,IAAI,gBAAiBK,CAAmB,EAE5CL,EAAAA,MAAI,WAAW,CACd,IAAK,6BACL,cAAe,iBACf,OAAQ,CAEP,cAAgBM,GAAe,CAE9BC,EAAgBD,CAAU,EAE1BN,QAAI,KAAK,gBAAiBK,CAAmB,CAC7C,CACD,CACH,CAAE,CACF,EAGME,EAAmBD,GAAe,CAEvCtB,EAAiB,MAAQ,CAAE,GAAGA,EAAiB,MAAO,GAAGsB,CAAY,EAGjEd,EAAW,OACdA,EAAW,MAAM,OAAQ,EAG1BQ,EAAAA,MAAI,UAAU,CACb,MAAO,UACP,KAAM,SACR,CAAE,CACF,EAGMQ,EAAqB,CAACC,EAAQC,IAAa,CAChD,GAAI/B,EAAW,QAAU,QAAS,CAEjC,MAAMgC,EAASC,EAAAA,oBAAoBH,EAAQC,EAAU1B,EAAiB,KAAK,EAC3E6B,EAAAA,iBAAiBF,CAAM,EAAE,KAAKG,GAAY,CACzC,MAAMC,EAAkBD,EAAS,KAAK,IAAIE,GAAQC,EAAAA,kBAAkBD,CAAI,CAAC,EACzExB,EAAW,MAAM,SAASuB,CAAe,CAC5C,CAAG,CACH,MAAYpC,EAAW,QAAU,UAC/BuC,cAAa,EAAC,KAAMC,GAAa,CAChCpC,EAAa,MAAQoC,EAErB,MAAMR,EAASS,EAAiB,kBAC/BX,EACAC,EACA3B,EAAa,MAAM,UACnBA,EAAa,MAAM,SACnBC,EAAiB,KACjB,EACDqC,EAAAA,eAAeV,CAAM,EAAE,KAAKG,GAAY,CACvC,MAAMC,EAAkBD,EAAS,KAAK,IAAIE,GAAQC,EAAAA,kBAAkBD,CAAI,CAAC,EACzExB,EAAW,MAAM,SAASuB,CAAe,CAC7C,CAAI,CACJ,CAAG,CAEH,EAGMO,EAAeC,GAAS,CAC7BvB,EAAAA,MAAI,WAAW,CACd,IAAK,sCAAsCuB,EAAK,EAAE,EACpD,CAAE,CACF,EAGMC,EAAkB,IAAM,CACzBhC,EAAW,OACdA,EAAW,MAAM,OAAQ,CAE3B,EAQAiC,OAAAA,EAAAA,OAAO,IAAM,CACZzB,EAAAA,yDAAY,eAAe,EAC3BwB,EAAiB,CAClB,CAAC,EAGDE,EAAAA,YAAY,IAAM,CAEjB1B,EAAG,MAAC,KAAK,eAAe,CACzB,CAAC,suDCjWD,GAAG,WAAW2B,CAAe"}