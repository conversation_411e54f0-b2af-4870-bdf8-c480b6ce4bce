package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.user.AppRecommendUserPageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAppRecommendUserPageVoMapperImpl implements UserDetailToAppRecommendUserPageVoMapper {

    @Override
    public AppRecommendUserPageVo convert(UserDetail source) {
        if ( source == null ) {
            return null;
        }

        AppRecommendUserPageVo appRecommendUserPageVo = new AppRecommendUserPageVo();

        appRecommendUserPageVo.setAddrNewProvince( source.getAddrNewProvinceCode() );
        appRecommendUserPageVo.setAddrProvince( source.getAddrProvinceCode() );
        appRecommendUserPageVo.setEdu( source.getEdu() );
        appRecommendUserPageVo.setJob( source.getJob() );

        return appRecommendUserPageVo;
    }

    @Override
    public AppRecommendUserPageVo convert(UserDetail source, AppRecommendUserPageVo target) {
        if ( source == null ) {
            return target;
        }

        target.setAddrNewProvince( source.getAddrNewProvinceCode() );
        target.setAddrProvince( source.getAddrProvinceCode() );
        target.setEdu( source.getEdu() );
        target.setJob( source.getJob() );

        return target;
    }
}
