package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActivityFeeVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityFeeToActivityFeeVoMapperImpl implements ActivityFeeToActivityFeeVoMapper {

    @Override
    public ActivityFeeVo convert(ActivityFee arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActivityFeeVo activityFeeVo = new ActivityFeeVo();

        activityFeeVo.setId( arg0.getId() );
        activityFeeVo.setActivityId( arg0.getActivityId() );
        activityFeeVo.setType( arg0.getType() );
        activityFeeVo.setBusinessId( arg0.getBusinessId() );
        activityFeeVo.setAmount( arg0.getAmount() );
        activityFeeVo.setContentJs( arg0.getContentJs() );

        return activityFeeVo;
    }

    @Override
    public ActivityFeeVo convert(ActivityFee arg0, ActivityFeeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setContentJs( arg0.getContentJs() );

        return arg1;
    }
}
