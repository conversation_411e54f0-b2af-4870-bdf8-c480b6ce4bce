package com.gzhuxn.personals.controller.app.order.bo;

import com.gzhuxn.personals.domain.order.UserOrder;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppCreateOrderBoToUserOrderMapperImpl implements AppCreateOrderBoToUserOrderMapper {

    @Override
    public UserOrder convert(AppCreateOrderBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserOrder userOrder = new UserOrder();

        userOrder.setUserId( arg0.getUserId() );
        userOrder.setOriginalAmount( arg0.getOriginalAmount() );
        userOrder.setAmount( arg0.getAmount() );
        userOrder.setBusinessId( arg0.getBusinessId() );
        userOrder.setWithdrawCoin( arg0.getWithdrawCoin() );
        userOrder.setCoin( arg0.getCoin() );

        return userOrder;
    }

    @Override
    public UserOrder convert(AppCreateOrderBo arg0, UserOrder arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
