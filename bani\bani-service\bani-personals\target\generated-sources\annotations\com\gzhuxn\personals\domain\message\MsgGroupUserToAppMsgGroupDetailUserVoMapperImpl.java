package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupUserToAppMsgGroupDetailUserVoMapperImpl implements MsgGroupUserToAppMsgGroupDetailUserVoMapper {

    @Override
    public AppMsgGroupDetailUserVo convert(MsgGroupUser source) {
        if ( source == null ) {
            return null;
        }

        AppMsgGroupDetailUserVo appMsgGroupDetailUserVo = new AppMsgGroupDetailUserVo();

        appMsgGroupDetailUserVo.setUid( source.getUserId() );
        appMsgGroupDetailUserVo.setId( source.getId() );
        appMsgGroupDetailUserVo.setName( source.getName() );
        appMsgGroupDetailUserVo.setType( source.getType() );

        return appMsgGroupDetailUserVo;
    }

    @Override
    public AppMsgGroupDetailUserVo convert(MsgGroupUser source, AppMsgGroupDetailUserVo target) {
        if ( source == null ) {
            return target;
        }

        target.setUid( source.getUserId() );
        target.setId( source.getId() );
        target.setName( source.getName() );
        target.setType( source.getType() );

        return target;
    }
}
