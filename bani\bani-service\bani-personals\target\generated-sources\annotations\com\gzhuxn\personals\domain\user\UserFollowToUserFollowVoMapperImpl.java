package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserFollowVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserFollowToUserFollowVoMapperImpl implements UserFollowToUserFollowVoMapper {

    @Override
    public UserFollowVo convert(UserFollow arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserFollowVo userFollowVo = new UserFollowVo();

        userFollowVo.setId( arg0.getId() );
        userFollowVo.setUserId( arg0.getUserId() );
        userFollowVo.setType( arg0.getType() );
        userFollowVo.setBusinessId( arg0.getBusinessId() );

        return userFollowVo;
    }

    @Override
    public UserFollowVo convert(UserFollow arg0, UserFollowVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
