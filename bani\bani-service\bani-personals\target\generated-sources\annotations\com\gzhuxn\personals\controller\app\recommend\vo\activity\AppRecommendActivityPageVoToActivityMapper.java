package com.gzhuxn.personals.controller.app.recommend.vo.activity;

import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityToAppRecommendActivityPageVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActivityToAppRecommendActivityPageVoMapper.class},
    imports = {}
)
public interface AppRecommendActivityPageVoToActivityMapper extends BaseMapper<AppRecommendActivityPageVo, Activity> {
}
