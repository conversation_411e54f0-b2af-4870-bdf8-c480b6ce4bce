package com.gzhuxn.personals.controller.app.user.vo.blacklist;

import com.gzhuxn.personals.domain.user.UserBlacklist;
import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserBlacklistVoToUserBlacklistMapperImpl implements AppUserBlacklistVoToUserBlacklistMapper {

    @Override
    public UserBlacklist convert(AppUserBlacklistVo source) {
        if ( source == null ) {
            return null;
        }

        UserBlacklist userBlacklist = new UserBlacklist();

        userBlacklist.setOppositeUserId( source.getUid() );
        if ( source.getCreateTime() != null ) {
            userBlacklist.setCreateTime( LocalDateTime.ofInstant( source.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        userBlacklist.setId( source.getId() );
        userBlacklist.setUserId( source.getUserId() );

        return userBlacklist;
    }

    @Override
    public UserBlacklist convert(AppUserBlacklistVo source, UserBlacklist target) {
        if ( source == null ) {
            return target;
        }

        target.setOppositeUserId( source.getUid() );
        if ( source.getCreateTime() != null ) {
            target.setCreateTime( LocalDateTime.ofInstant( source.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            target.setCreateTime( null );
        }
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );

        return target;
    }
}
