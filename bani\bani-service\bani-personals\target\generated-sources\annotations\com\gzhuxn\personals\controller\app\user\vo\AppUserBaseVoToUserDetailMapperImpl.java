package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserDetail;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserBaseVoToUserDetailMapperImpl implements AppUserBaseVoToUserDetailMapper {

    @Override
    public UserDetail convert(AppUserBaseVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetail userDetail = new UserDetail();

        userDetail.setUserId( arg0.getUserId() );
        userDetail.setNickName( arg0.getNickName() );
        userDetail.setSex( arg0.getSex() );
        userDetail.setPhoneNumber( arg0.getPhoneNumber() );
        userDetail.setBirthday( arg0.getBirthday() );
        userDetail.setHeight( arg0.getHeight() );
        userDetail.setWeight( arg0.getWeight() );
        userDetail.setEdu( arg0.getEdu() );
        userDetail.setJob( arg0.getJob() );
        userDetail.setAffectiveStatus( arg0.getAffectiveStatus() );
        userDetail.setRevenue( arg0.getRevenue() );
        userDetail.setWechat( arg0.getWechat() );
        userDetail.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        userDetail.setAddrCityCode( arg0.getAddrCityCode() );
        userDetail.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        userDetail.setAddrStreetCode( arg0.getAddrStreetCode() );
        userDetail.setAddr( arg0.getAddr() );
        userDetail.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        userDetail.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        userDetail.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        userDetail.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        userDetail.setAddrNew( arg0.getAddrNew() );

        return userDetail;
    }

    @Override
    public UserDetail convert(AppUserBaseVo arg0, UserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setAffectiveStatus( arg0.getAffectiveStatus() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        arg1.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        arg1.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        arg1.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        arg1.setAddrNew( arg0.getAddrNew() );

        return arg1;
    }
}
