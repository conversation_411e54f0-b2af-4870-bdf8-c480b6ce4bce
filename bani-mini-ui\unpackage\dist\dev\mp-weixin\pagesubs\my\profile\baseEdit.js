"use strict";const t=require("../../../common/vendor.js"),d=require("../../../store/index.js"),T=require("../../../api/my/my.js"),D=require("../../../utils/common.js");if(!Array){const b=t.resolveComponent("uni-icons"),u=t.resolveComponent("scroll-nav-page"),f=t.resolveComponent("uni-popup-dialog"),_=t.resolveComponent("uni-popup");(b+u+f+_)()}const V=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",J=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",K=()=>"../../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js",Q=()=>"../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";Math||(V+J+K+Q+X+Z)();const X=()=>"../../../components/district-select/district-select.js",Z=()=>"../../../components/tag-select/tag-select.js",k={__name:"baseEdit",setup(b){const u=t.ref(0),f=t.ref(0),_=t.ref(0),h=t.ref(!1),a=t.ref({nickName:"",gender:"",birthday:"",height:"",weight:"",edu:"",job:"",affectiveStatus:"",revenue:"",addrProvinceCode:"",addrCityCode:"",addrDistrictCode:"",addrStreetCode:"",addr:"",addrNewProvinceCode:"",addrNewCityCode:"",addrNewDistrictCode:"",addrNewStreetCode:"",addrNew:""}),p=t.ref(null),s=t.ref(""),w=t.ref(null),m=t.ref(""),N=t.ref(null),c=t.ref([]),l=t.ref([]),y=t.ref([{value:"0",name:"男"},{value:"1",name:"女"}]),g=(e,o)=>{if(!o||!e)return"";try{return!d.$store||!d.$store.dict||typeof d.$store.dict.getNameById!="function"?(t.index.__f__("warn","at pagesubs/my/profile/baseEdit.vue:236","Store 或字典方法不可用"),""):d.$store.dict.getNameById(e,o)||""}catch(r){return t.index.__f__("error","at pagesubs/my/profile/baseEdit.vue:242","获取字典文本失败:",r),""}},i=t.computed(()=>a.value?{affectiveStatusText:g("user_affective_status",a.value.affectiveStatus),eduText:g("user_edu",a.value.edu),jobText:g("user_job",a.value.job),revenueText:g("user_revenue",a.value.revenue)}:{affectiveStatusText:"",eduText:"",jobText:"",revenueText:""}),j=t.computed(()=>{try{const e=d.$store.dict.get("user_job");return!e||!Array.isArray(e)?[]:e.map(o=>({id:o.id,name:o.name}))}catch(e){return t.index.__f__("error","at pagesubs/my/profile/baseEdit.vue:282","获取职业数据失败:",e),[]}}),v=e=>{try{const o=d.$store.dict.get(e);return!o||!Array.isArray(o)?(t.index.__f__("warn","at pagesubs/my/profile/baseEdit.vue:292","字典数据不存在或格式错误:",e),[]):o}catch(o){return t.index.__f__("error","at pagesubs/my/profile/baseEdit.vue:297","获取字典选项失败:",o),[]}};t.onPageScroll(e=>{f.value=e.scrollTop}),t.onLoad(e=>{u.value=parseInt(e.baseType||0),E()});const E=async()=>{try{c.value=D.heightDicts(),l.value=D.weightDicts();const e=await T.getUserBase();e.code===200&&e.data&&(a.value={nickName:e.data.nickName||"",gender:e.data.sex||"",birthday:e.data.birthday||"",height:e.data.height||"",weight:e.data.weight||"",edu:e.data.edu||"",job:e.data.job||"",affectiveStatus:e.data.affectiveStatus||"",revenue:e.data.revenue||"",addrProvinceCode:e.data.addrProvinceCode||"",addrCityCode:e.data.addrCityCode||"",addrDistrictCode:e.data.addrDistrictCode||"",addrStreetCode:e.data.addrStreetCode||"",addr:e.data.addr||"",addrNewProvinceCode:e.data.addrNewProvinceCode||"",addrNewCityCode:e.data.addrNewCityCode||"",addrNewDistrictCode:e.data.addrNewDistrictCode||"",addrNewStreetCode:e.data.addrNewStreetCode||"",addrNew:e.data.addrNew||""}),t.index.__f__("log","at pagesubs/my/profile/baseEdit.vue:345","基础信息:",e)}catch(e){t.index.__f__("error","at pagesubs/my/profile/baseEdit.vue:347","加载基础信息失败:",e)}},I=()=>{if(!c.value.length)return 0;const e=a.value.height||168,o=c.value.findIndex(r=>r.id===e);return o>=0?o:0},A=()=>{if(!l.value.length)return 0;const e=a.value.weight||50,o=l.value.findIndex(r=>r.id===e);return o>=0?o:0},F=()=>{const e=new Date;return new Date(e.getFullYear()-18,e.getMonth(),e.getDate()).toISOString().split("T")[0]},P=()=>a.value.birthday||F(),z=e=>e==="0"?"男":e==="1"?"女":"",B=()=>{if(!a.value.gender)return 0;const e=y.value.findIndex(o=>o.value===a.value.gender);return e>=0?e:0},$=e=>{const o=y.value[e.detail.value];a.value.gender=o.value},H=()=>{s.value=a.value.nickName,p.value.open()},q=e=>{const o=s.value||e;o&&o.trim()&&(a.value.nickName=o.trim()),p.value.close()},O=()=>{p.value.close()},G=e=>{a.value.birthday=e.detail.value},R=e=>{const o=c.value[e.detail.value];a.value.height=o.id},W=e=>{const o=l.value[e.detail.value];a.value.weight=o.id},S=e=>{m.value=e,w.value.open()},Y=e=>{m.value==="registered"?(a.value.addrProvinceCode=e.codes.provinceCode,a.value.addrCityCode=e.codes.cityCode,a.value.addrDistrictCode=e.codes.districtCode,a.value.addrStreetCode=e.codes.streetCode,a.value.addr=e.fullName):m.value==="current"&&(a.value.addrNewProvinceCode=e.codes.provinceCode,a.value.addrNewCityCode=e.codes.cityCode,a.value.addrNewDistrictCode=e.codes.districtCode,a.value.addrNewStreetCode=e.codes.streetCode,a.value.addrNew=e.fullName)},C=(e,o,r)=>{const n=v(e);n&&n[r.detail.value]&&(a.value[o]=n[r.detail.value].id)},x=(e,o)=>{if(!o)return 0;const n=v(e).findIndex(U=>U.id===o);return n>=0?n:0},L=e=>{t.index.__f__("log","at pagesubs/my/profile/baseEdit.vue:511","职业选择:",e),e&&e.value&&(a.value.job=e.value)},M=async()=>{if(!a.value.nickName){t.index.showToast({title:"请输入昵称",icon:"none"});return}if(!a.value.gender){t.index.showToast({title:"请选择性别",icon:"none"});return}h.value=!0;try{const e={...a.value,sex:a.value.gender};delete e.gender;const o=await T.updateUserBase(e);t.index.__f__("log","at pagesubs/my/profile/baseEdit.vue:547","提交成功:",o),t.index.showToast({title:"保存成功",icon:"success"}),u.value===0?t.index.navigateTo({url:"/pagesubs/my/profile/avatar/avatarEdit"}):t.index.navigateBack()}catch(e){t.index.__f__("error","at pagesubs/my/profile/baseEdit.vue:563","提交失败:",e),t.index.showToast({title:"保存失败",icon:"error"})}finally{h.value=!1}};return(e,o)=>({a:t.t(a.value.nickName||"请输入昵称"),b:a.value.nickName?"":1,c:t.p({type:"right",size:"16",color:"#ccc"}),d:t.o(r=>H()),e:t.t(z(a.value.gender)||"请选择性别"),f:a.value.gender?"":1,g:t.p({type:"right",size:"16",color:"#ccc"}),h:y.value,i:B(),j:t.o($),k:t.t(a.value.birthday||"请选择出生日期"),l:a.value.birthday?"":1,m:t.p({type:"right",size:"16",color:"#ccc"}),n:P(),o:t.o(G),p:t.t(a.value.height?a.value.height+"cm":"请选择身高"),q:a.value.height?"":1,r:t.p({type:"right",size:"16",color:"#ccc"}),s:c.value,t:I(),v:t.o(R),w:t.t(a.value.weight?a.value.weight+"kg":"请选择体重"),x:a.value.weight?"":1,y:t.p({type:"right",size:"16",color:"#ccc"}),z:l.value,A:A(),B:t.o(W),C:t.t(i.value.eduText||"请选择学历"),D:a.value.edu?"":1,E:t.p({type:"right",size:"16",color:"#ccc"}),F:v("user_edu"),G:x("user_edu",a.value.edu),H:t.o(r=>C("user_edu","edu",r)),I:t.t(i.value.jobText||"请选择职业"),J:a.value.job?"":1,K:t.p({type:"right",size:"16",color:"#ccc"}),L:t.o(r=>N.value.open()),M:t.t(i.value.affectiveStatusText||"请选择情感状况"),N:i.value.affectiveStatusText?"":1,O:t.p({type:"right",size:"16",color:"#ccc"}),P:v("user_affective_status"),Q:x("user_affective_status",a.value.affectiveStatus),R:t.o(r=>C("user_affective_status","affectiveStatus",r)),S:t.t(i.value.revenueText||"请选择收入"),T:i.value.revenueText?"":1,U:t.p({type:"right",size:"16",color:"#ccc"}),V:v("user_revenue"),W:x("user_revenue",a.value.revenue),X:t.o(r=>C("user_revenue","revenue",r)),Y:t.t(a.value.addr||"请选择户籍地址"),Z:a.value.addr?"":1,aa:t.p({type:"right",size:"16",color:"#ccc"}),ab:t.o(r=>S("registered")),ac:t.t(a.value.addrNew||"请选择现居地址"),ad:a.value.addrNew?"":1,ae:t.p({type:"right",size:"16",color:"#ccc"}),af:t.o(r=>S("current")),ag:t.t(u.value===0?"下一步":"保存"),ah:t.o(M),ai:h.value,aj:_.value+"px",ak:t.p({"show-back":!0,title:"基本信息"}),al:s.value,am:t.o(r=>s.value=r.detail.value),an:t.o(q),ao:t.o(O),ap:t.p({mode:"input",title:"编辑昵称",placeholder:"请输入昵称",value:s.value}),aq:t.sr(p,"4fd28516-12",{k:"nicknamePopup"}),ar:t.p({type:"dialog"}),as:t.sr(w,"4fd28516-14",{k:"districtSelectRef"}),at:t.o(Y),av:t.sr(N,"4fd28516-15",{k:"jobSelectRef"}),aw:t.o(L),ax:t.p({title:"请选择您的职业",localdata:j.value})})}};k.__runtimeHooks=1;wx.createPage(k);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/profile/baseEdit.js.map
