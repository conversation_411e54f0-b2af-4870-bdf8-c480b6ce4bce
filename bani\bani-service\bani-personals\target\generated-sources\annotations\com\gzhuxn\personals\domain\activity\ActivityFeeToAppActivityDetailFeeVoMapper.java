package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.bo.AppActivityFeeBoToActivityFeeMapper;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailFeeVo;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailFeeVoToActivityFeeMapper;
import com.gzhuxn.personals.domain.activity.bo.ActivityFeeBoToActivityFeeMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppActivityDetailFeeVoToActivityFeeMapper.class,AppActivityFeeBoToActivityFeeMapper.class,ActivityFeeBoToActivityFeeMapper.class,ActivityFeeToActivityFeeVoMapper.class},
    imports = {}
)
public interface ActivityFeeToAppActivityDetailFeeVoMapper extends BaseMapper<ActivityFee, AppActivityDetailFeeVo> {
}
