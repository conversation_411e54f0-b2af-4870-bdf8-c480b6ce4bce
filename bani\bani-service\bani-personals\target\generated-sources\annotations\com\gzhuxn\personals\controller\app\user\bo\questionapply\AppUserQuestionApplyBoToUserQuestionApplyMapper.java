package com.gzhuxn.personals.controller.app.user.bo.questionapply;

import com.gzhuxn.personals.domain.user.UserQuestionApply;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppUserQuestionApplyBoToUserQuestionApplyMapper extends BaseMapper<AppUserQuestionApplyBo, UserQuestionApply> {
}
