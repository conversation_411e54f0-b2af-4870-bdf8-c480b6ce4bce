{"version": 3, "file": "detail.js", "sources": ["pagesubs/moment/detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbW9tZW50XGRldGFpbC52dWU"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"动态详情\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"moment-detail-page\">\n\t\t\t\t<!-- 主要内容区域 -->\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<!-- 动态内容 -->\n\t\t\t\t\t<view class=\"moment-content\">\n\t\t\t\t\t\t<!-- 用户信息 -->\n\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t<view class=\"user-avatar\">\n\t\t\t\t\t\t\t\t<image :src=\"momentDetail.user.avatar\" mode=\"aspectFill\" class=\"avatar-img\"></image>\n\t\t\t\t\t\t\t\t<view class=\"verified-badge\" v-if=\"momentDetail.user.isIdentity\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"12\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t\t\t<view class=\"user-name-row\">\n\t\t\t\t\t\t\t\t\t<text class=\"username\">{{ momentDetail.user.nickname }}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"user-tags\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"age-tag\">{{ momentDetail.user.age }}年</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"location-tag\">{{ momentDetail.user.height }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"occupation-tag\">{{ momentDetail.user.city }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"follow-btn\" @click.stop=\"handleFollow\" v-if=\"!momentDetail.user.isMe\">\n\t\t\t\t\t\t\t\t<text class=\"follow-text\">{{ momentDetail.isFollowed ? '已关注' : '关注' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 动态文本内容 -->\n\t\t\t\t\t\t<view class=\"moment-text\" v-if=\"momentDetail.content\">\n\t\t\t\t\t\t\t<text class=\"content-text\">{{ momentDetail.content }}</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 动态图片 -->\n\t\t\t\t\t\t<view class=\"moment-images\" v-if=\"momentDetail.images && momentDetail.images.length > 0\">\n\t\t\t\t\t\t\t<view class=\"image-grid\" :class=\"getImageGridClass(momentDetail.images.length)\">\n\t\t\t\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in momentDetail.images\" :key=\"index\"\n\t\t\t\t\t\t\t\t\t@click=\"previewImage(momentDetail.images, image)\">\n\t\t\t\t\t\t\t\t\t<image :src=\"image\" mode=\"aspectFill\" class=\"moment-image\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 话题标签 -->\n\t\t\t\t\t\t<view class=\"topic-tags\" v-if=\"momentDetail.tags && momentDetail.tags.length > 0\">\n\t\t\t\t\t\t\t<view class=\"topic-tag\" v-for=\"(tag, index) in momentDetail.tags\" :key=\"index\"\n\t\t\t\t\t\t\t\t@click=\"handleTopicTagClick(tag)\">\n\t\t\t\t\t\t\t\t<text class=\"topic-tag-text\">#{{ tag.tagValName }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 发布时间和位置 -->\n\t\t\t\t\t\t<view class=\"moment-meta\">\n\t\t\t\t\t\t\t<text class=\"publish-time\">{{ momentDetail.time }}</text>\n\t\t\t\t\t\t\t<text class=\"location\" v-if=\"momentDetail.location\">{{ momentDetail.location }}</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 互动统计 -->\n\t\t\t\t\t\t<view class=\"interaction-stats\">\n\t\t\t\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont bani-dianzan\" :style=\"{\n\t\t\t\t\t\t\t\t\tcolor: '#999',\n\t\t\t\t\t\t\t\t\tfontSize: '16px'\n\t\t\t\t\t\t\t\t}\"></text>\n\t\t\t\t\t\t\t\t<text class=\"stats-text\">{{ momentDetail.likes }}人觉得很赞</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stats-item\" v-if=\"momentDetail.pv > 0\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t<text class=\"stats-text\">{{ momentDetail.pv }}次浏览</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 评论区域 -->\n\t\t\t\t\t<view class=\"comments-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">评论 {{ commentTotal || commentList.length }}</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 评论列表 -->\n\t\t\t\t\t\t<view class=\"comment-list\">\n\t\t\t\t\t\t\t<view class=\"comment-item\" v-for=\"comment in commentList\" :key=\"comment.id\">\n\t\t\t\t\t\t\t\t<view class=\"comment-avatar\">\n\t\t\t\t\t\t\t\t\t<image :src=\"comment.avatar\" mode=\"aspectFill\" class=\"avatar-img\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"comment-content\">\n\t\t\t\t\t\t\t\t\t<view class=\"comment-header\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"comment-username\">{{ comment.nickname }}</text>\n\t\t\t\t\t\t\t\t\t\t<view class=\"comment-menu\" v-if=\"comment.isMe\"\n\t\t\t\t\t\t\t\t\t\t\t@click=\"showCommentMenu(comment)\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"menu-dots\">...</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"comment-text\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"comment-content-text\">{{ comment.content }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"comment-meta\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"comment-time\">{{ comment.createTime }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t<!-- 评论操作按钮 -->\n\t\t\t\t\t\t\t\t\t<view class=\"comment-actions\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" @click=\"handleReplyComment(comment)\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"action-text\">回复</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" v-if=\"comment.childCount > 0\"\n\t\t\t\t\t\t\t\t\t\t\t@click=\"handleShowReplies(comment)\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons :type=\"expandedComments.has(comment.id) ? 'up' : 'down'\"\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"action-text reply-count-text\">{{ comment.childCount\n\t\t\t\t\t\t\t\t\t\t\t\t}}条回复</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t<!-- 子评论列表 -->\n\t\t\t\t\t\t\t\t\t<view class=\"child-comments\"\n\t\t\t\t\t\t\t\t\t\tv-if=\"expandedComments.has(comment.id) && childCommentsMap.get(comment.id)\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-item\"\n\t\t\t\t\t\t\t\t\t\t\tv-for=\"childComment in childCommentsMap.get(comment.id)\"\n\t\t\t\t\t\t\t\t\t\t\t:key=\"childComment.id\">\n\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-content\">\n\t\t\t\t\t\t\t\t\t\t\t\t<image class=\"child-avatar\" :src=\"childComment.avatar\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-main\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-header\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-header-left\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"child-nickname\">{{ childComment.nickname\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"child-comment-time\">{{ childComment.createTime\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-menu\" v-if=\"childComment.isMe\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"showCommentMenu(childComment)\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"menu-dots\">...</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"child-comment-text\">{{ childComment.content }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"child-comment-actions\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"action-btn\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@click=\"handleReplyComment(childComment)\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"12\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"action-text\">回复</text>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t\t\t<!-- 加载更多子评论 -->\n\t\t\t\t\t\t\t\t\t\t<view class=\"load-more-child\" v-if=\"childCommentLoading.get(comment.id)\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"spinner-cycle\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 底部操作栏 -->\n\t\t\t\t<view class=\"bottom-action-bar\">\n\t\t\t\t\t<view class=\"comment-input-area\" @click=\"focusCommentInput\">\n\t\t\t\t\t\t<!-- 回复前缀显示 -->\n\t\t\t\t\t\t<view class=\"reply-prefix\" v-if=\"replyPrefix\">\n\t\t\t\t\t\t\t<text class=\"prefix-text\">{{ replyPrefix }}</text>\n\t\t\t\t\t\t\t<view class=\"cancel-reply\" @click=\"cancelReply\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"close\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 输入框和发送按钮行 -->\n\t\t\t\t\t\t<view class=\"input-row\">\n\t\t\t\t\t\t\t<input ref=\"commentInputRef\" class=\"comment-input\"\n\t\t\t\t\t\t\t\t:placeholder=\"replyPrefix ? '输入回复内容...' : '说点什么吧'\" v-model=\"commentText\"\n\t\t\t\t\t\t\t\t@focus=\"handleInputFocus\" @blur=\"handleInputBlur\" @confirm=\"handleSendComment\"\n\t\t\t\t\t\t\t\tconfirm-type=\"send\" />\n\t\t\t\t\t\t\t<view class=\"send-btn\" v-if=\"commentText.trim()\" @click=\"handleSendComment\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"18\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-buttons\" v-if=\"!isInputFocused\">\n\t\t\t\t\t\t<view class=\"action-btn\" @click=\"handleShare\">\n\t\t\t\t\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"action-text\">分享</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"action-btn\" @click=\"handleLike\">\n\t\t\t\t\t\t\t<text class=\"iconfont\" :class=\"momentDetail.isLiked ? 'bani-dianzan-fill' : 'bani-dianzan'\"\n\t\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\t\tcolor: momentDetail.isLiked ? '#696CF3' : '#999',\n\t\t\t\t\t\t\t\t\tfontSize: '20px'\n\t\t\t\t\t\t\t\t}\"></text>\n\t\t\t\t\t\t\t<text class=\"action-text\" :style=\"{ color: momentDetail.isLiked ? '#696CF3' : '#999' }\">\n\t\t\t\t\t\t\t\t{{ momentDetail.likes }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\nimport { getRecommendMomentDetail, formatRecommendMoment } from '@/api/moment/recommend'\nimport { toggleMomentLike } from '@/api/my/like'\nimport { toggleUserFollow } from '@/api/my/follow'\nimport {\n\tgetRootComments,\n\tgetChildComments,\n\taddRootComment,\n\treplyComment,\n\tdeleteSingleComment,\n\tCOMMENT_TYPE\n} from '@/api/my/comment'\nimport $store from '@/store'\n\n// 页面滚动距离\nconst pageScrollTop = ref(0)\n// 导航栏高度\nconst navBarHeight = ref(0)\n\n// 动态详情数据\nconst momentDetail = ref({})\n// 评论列表\nconst commentList = ref([])\n// 评论输入内容\nconst commentText = ref('')\n// 输入框聚焦状态\nconst isInputFocused = ref(false)\n// 输入框引用\nconst commentInputRef = ref(null)\n\n// 评论相关状态\nconst commentLoading = ref(false)\nconst commentTotal = ref(0)\nconst commentPageNum = ref(1)\nconst commentPageSize = ref(10)\nconst hasMoreComments = ref(true)\n\n// 当前动态ID\nconst currentMomentId = ref(null)\n\n// 回复相关状态\nconst replyTarget = ref(null) // 当前回复的目标评论\nconst replyPrefix = ref('') // 回复前缀文本\n\n// 子评论相关状态\nconst childCommentsMap = ref(new Map()) // 存储每个根评论的子评论列表\nconst childCommentLoading = ref(new Map()) // 存储每个根评论的加载状态\nconst expandedComments = ref(new Set()) // 存储已展开的评论ID\n\n// 页面加载\nonLoad((options) => {\n\tconst momentId = options.id\n\tif (momentId) {\n\t\tcurrentMomentId.value = momentId\n\t\tloadMomentDetail(momentId)\n\t\tloadCommentList(momentId)\n\t}\n})\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 加载动态详情\nconst loadMomentDetail = async (momentId) => {\n\tgetRecommendMomentDetail(momentId).then(response => {\n\t\t// 格式化动态数据\n\t\tmomentDetail.value = formatRecommendMoment(response.data)\n\t})\n}\n\n\n// 加载评论列表\nconst loadCommentList = async (momentId, isRefresh = true) => {\n\tif (commentLoading.value) return\n\n\ttry {\n\t\tcommentLoading.value = true\n\n\t\t// 如果是刷新，重置分页\n\t\tif (isRefresh) {\n\t\t\tcommentPageNum.value = 1\n\t\t\tcommentList.value = []\n\t\t}\n\n\t\tconst params = {\n\t\t\ttype: COMMENT_TYPE.MOMENT,\n\t\t\tbusinessId: momentId,\n\t\t\tpageSize: commentPageSize.value,\n\t\t\tpageNum: commentPageNum.value,\n\t\t\torderByColumn: 'createTime',\n\t\t\tisAsc: 'desc'\n\t\t}\n\n\t\tconsole.log('加载评论列表，参数:', params)\n\t\tconst response = await getRootComments(params)\n\n\t\tif (response.code === 200 && response.data) {\n\t\t\tconst { total, rows } = response.data\n\n\t\t\t// 格式化评论数据\n\t\t\tconst formattedComments = rows.map(comment => ({\n\t\t\t\tid: comment.id,\n\t\t\t\trootId: comment.rootId || null, // 根评论ID，根评论为null\n\t\t\t\tavatar: comment.oppAvatar,\n\t\t\t\tnickname: comment.oppNickName,\n\t\t\t\tuid: comment.uid,\n\t\t\t\tcontent: comment.content,\n\t\t\t\timages: comment.images,\n\t\t\t\tcreateTime: comment.createTime,\n\t\t\t\tchildCount: comment.childCount || 0,\n\t\t\t\tisMe: $store.isMe(comment.uid) // 使用store判断是否为本人评论\n\t\t\t}))\n\n\t\t\tif (isRefresh) {\n\t\t\t\tcommentList.value = formattedComments\n\t\t\t} else {\n\t\t\t\tcommentList.value.push(...formattedComments)\n\t\t\t}\n\n\t\t\tcommentTotal.value = response.data.totalCount\n\t\t\thasMoreComments.value = commentList.value.length < total\n\n\t\t\tconsole.log('评论列表加载成功:', commentList.value)\n\t\t} else {\n\t\t\tconsole.error('加载评论列表失败:', response.msg)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: response.msg || '加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载评论列表异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络异常，请稍后重试',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tcommentLoading.value = false\n\t}\n}\n\n// 获取图片网格样式类\nconst getImageGridClass = (imageCount) => {\n\tif (imageCount === 1) return 'single-image'\n\tif (imageCount === 2) return 'two-images'\n\tif (imageCount === 3) return 'three-images'\n\treturn 'multiple-images'\n}\n\n// 图片预览\nconst previewImage = (images, current) => {\n\tuni.previewImage({\n\t\turls: images,\n\t\tcurrent: current\n\t})\n}\n\n// 处理导航栏高度变化\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 计算导航栏文字颜色\nconst getNavTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\n}\n\n// 计算导航栏图标颜色\nconst getNavIconColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? '#696CF3' : '#ffffff'\n}\n\n// 显示更多选项\nconst showMoreOptions = () => {\n\tuni.showActionSheet({\n\t\titemList: ['举报', '屏蔽'],\n\t\tsuccess: (res) => {\n\t\t\tconsole.log('选中了第' + (res.tapIndex + 1) + '个按钮')\n\t\t}\n\t})\n}\n\n// 点赞\nconst handleLike = async () => {\n\ttry {\n\t\tconsole.log('点赞动态:', momentDetail.value.id, '当前状态:', momentDetail.value.isLiked)\n\n\t\t// 调用点赞API\n\t\tconst response = await toggleMomentLike(momentDetail.value.id, momentDetail.value.isLiked)\n\n\t\tif (response.code === 200) {\n\t\t\t// 更新本地状态\n\t\t\tmomentDetail.value.isLiked = !momentDetail.value.isLiked\n\t\t\tif (momentDetail.value.isLiked) {\n\t\t\t\tmomentDetail.value.likes = (momentDetail.value.likes || 0) + 1\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '点赞成功',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tmomentDetail.value.likes = Math.max((momentDetail.value.likes || 0) - 1, 0)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '取消点赞',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 1000\n\t\t\t\t})\n\t\t\t}\n\t\t} else {\n\t\t\tconsole.error('点赞操作失败:', response.msg || '未知错误')\n\t\t\tuni.showToast({\n\t\t\t\ttitle: response.msg || '操作失败，请重试',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 2000\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('点赞操作异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络错误，请检查网络连接',\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t})\n\t}\n}\n\n// 关注用户\nconst handleFollow = async () => {\n\tconsole.log('关注用户:', momentDetail.value.user.id, '当前状态:', momentDetail.value.isFollowed)\n\n\t// 调用关注/取消关注API\n\tconst response = await toggleUserFollow(momentDetail.value.user.id, momentDetail.value.isFollowed)\n\n\tif (response.code === 200) {\n\t\t// 更新本地状态\n\t\tmomentDetail.value.isFollowed = !momentDetail.value.isFollowed\n\t\tuni.showToast({\n\t\t\ttitle: momentDetail.value.isFollowed ? '关注成功' : '已取消关注',\n\t\t\ticon: 'none',\n\t\t\tduration: 1500\n\t\t})\n\t} else {\n\t\tconsole.error('关注操作失败:', response.msg || '未知错误')\n\t\tuni.showToast({\n\t\t\ttitle: response.msg || '操作失败，请重试',\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t})\n\t}\n}\n\n// 分享\nconst handleShare = () => {\n\tuni.showToast({\n\t\ttitle: '分享功能开发中',\n\t\ticon: 'none'\n\t})\n}\n\n// 点击输入区域聚焦输入框\nconst focusCommentInput = () => {\n\tif (commentInputRef.value) {\n\t\tcommentInputRef.value.focus()\n\t}\n}\n\n// 输入框聚焦\nconst handleInputFocus = () => {\n\tisInputFocused.value = true\n}\n\n// 输入框失焦\nconst handleInputBlur = () => {\n\tisInputFocused.value = false\n}\n\n// 发送评论\nconst handleSendComment = async () => {\n\tif (!commentText.value.trim()) {\n\t\treturn\n\t}\n\n\tif (!currentMomentId.value) {\n\t\tuni.showToast({\n\t\t\ttitle: '动态ID不能为空',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\ttry {\n\t\tlet response\n\n\t\tif (replyTarget.value) {\n\t\t\t// 回复评论模式\n\t\t\tconst rootId = replyTarget.value.rootId || replyTarget.value.id\n\t\t\tconst parentId = replyTarget.value.id\n\n\t\t\t// 拼接回复前缀和回复内容\n\t\t\tconst fullReplyContent = replyPrefix.value + commentText.value.trim()\n\n\t\t\tconsole.log('发送回复:', {\n\t\t\t\tbusinessId: currentMomentId.value,\n\t\t\t\ttype: COMMENT_TYPE.MOMENT,\n\t\t\t\trootId: rootId,\n\t\t\t\tparentId: parentId,\n\t\t\t\tcontent: fullReplyContent,\n\t\t\t\treplyTarget: replyTarget.value.nickname,\n\t\t\t\toriginalContent: commentText.value.trim(),\n\t\t\t\tprefix: replyPrefix.value\n\t\t\t})\n\n\t\t\tresponse = await replyComment(\n\t\t\t\tcurrentMomentId.value,\n\t\t\t\tCOMMENT_TYPE.MOMENT,\n\t\t\t\trootId,\n\t\t\t\tparentId,\n\t\t\t\tfullReplyContent\n\t\t\t)\n\t\t} else {\n\t\t\t// 普通评论模式\n\t\t\tconsole.log('发送评论:', {\n\t\t\t\tbusinessId: currentMomentId.value,\n\t\t\t\ttype: COMMENT_TYPE.MOMENT,\n\t\t\t\tcontent: commentText.value.trim()\n\t\t\t})\n\n\t\t\tresponse = await addRootComment(\n\t\t\t\tcurrentMomentId.value,\n\t\t\t\tCOMMENT_TYPE.MOMENT,\n\t\t\t\tcommentText.value.trim()\n\t\t\t)\n\t\t}\n\n\t\tif (response.code === 200) {\n\t\t\t// 清空输入框和回复状态\n\t\t\tcommentText.value = ''\n\t\t\treplyTarget.value = null\n\t\t\treplyPrefix.value = ''\n\n\t\t\t// 显示成功提示\n\t\t\tuni.showToast({\n\t\t\t\ttitle: replyTarget.value ? '回复成功' : '评论成功',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\n\t\t\t// 重新加载评论列表\n\t\t\tawait loadCommentList(currentMomentId.value, true)\n\n\t\t\t// 更新动态的评论数量\n\t\t\tif (momentDetail.value.comments !== undefined) {\n\t\t\t\tmomentDetail.value.comments = commentList.value.length\n\t\t\t}\n\t\t} else {\n\t\t\tconsole.error('发送失败:', response.msg)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: response.msg || '发送失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('发送异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络异常，请稍后重试',\n\t\t\ticon: 'none'\n\t\t})\n\t}\n}\n\n// 回复评论\nconst handleReplyComment = (comment) => {\n\t// 设置回复目标\n\treplyTarget.value = comment\n\treplyPrefix.value = `回复${comment.nickname}: `\n\n\t// 聚焦输入框\n\tfocusCommentInput()\n\n\tconsole.log('设置回复目标:', {\n\t\ttarget: comment,\n\t\tprefix: replyPrefix.value\n\t})\n}\n\n// 取消回复\nconst cancelReply = () => {\n\treplyTarget.value = null\n\treplyPrefix.value = ''\n\tcommentText.value = ''\n\n\tconsole.log('取消回复')\n}\n\n// 显示评论菜单\nconst showCommentMenu = (comment) => {\n\t// 验证是否为本人评论\n\tif (!$store.isMe(comment.uid)) {\n\t\treturn\n\t}\n\n\tuni.showActionSheet({\n\t\titemList: ['删除评论'],\n\t\titemColor: '#ff4757',\n\t\tsuccess: (res) => {\n\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t// 删除评论\n\t\t\t\thandleDeleteComment(comment)\n\t\t\t}\n\t\t},\n\t\tfail: (err) => {\n\t\t\tconsole.log('取消操作:', err)\n\t\t}\n\t})\n}\n\n// 显示回复列表\nconst handleShowReplies = async (comment) => {\n\tconst commentId = comment.id\n\n\t// 如果已经展开，则收起\n\tif (expandedComments.value.has(commentId)) {\n\t\texpandedComments.value.delete(commentId)\n\t\treturn\n\t}\n\n\t// 如果正在加载，直接返回\n\tif (childCommentLoading.value.get(commentId)) {\n\t\treturn\n\t}\n\n\ttry {\n\t\t// 设置加载状态\n\t\tchildCommentLoading.value.set(commentId, true)\n\n\t\tconst params = {\n\t\t\ttype: COMMENT_TYPE.MOMENT,\n\t\t\tbusinessId: currentMomentId.value,\n\t\t\trootId: commentId, // 根评论ID\n\t\t\tpageSize: 10,\n\t\t\tpageNum: 1,\n\t\t\torderByColumn: 'createTime',\n\t\t\tisAsc: 'asc' // 子评论按时间正序排列\n\t\t}\n\n\t\tconsole.log('加载子评论，参数:', params)\n\t\tconst response = await getChildComments(params)\n\n\t\tif (response.code === 200 && response.data) {\n\t\t\tconst { rows } = response.data\n\n\t\t\t// 格式化子评论数据\n\t\t\tconst formattedChildComments = rows.map(childComment => ({\n\t\t\t\tid: childComment.id,\n\t\t\t\trootId: childComment.rootId,\n\t\t\t\tavatar: childComment.oppAvatar,\n\t\t\t\tnickname: childComment.oppNickName,\n\t\t\t\tuid: childComment.uid,\n\t\t\t\tcontent: childComment.content,\n\t\t\t\timages: childComment.images,\n\t\t\t\tcreateTime: childComment.createTime,\n\t\t\t\tpublishTime: childComment.createTime,\n\t\t\t\tisMe: $store.isMe(childComment.uid) // 使用store判断是否为本人评论\n\t\t\t}))\n\n\t\t\t// 存储子评论列表\n\t\t\tchildCommentsMap.value.set(commentId, formattedChildComments)\n\n\t\t\t// 标记为已展开\n\t\t\texpandedComments.value.add(commentId)\n\n\t\t\tconsole.log('子评论加载成功:', {\n\t\t\t\trootCommentId: commentId,\n\t\t\t\tchildComments: formattedChildComments\n\t\t\t})\n\t\t} else {\n\t\t\tconsole.error('加载子评论失败:', response.msg)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: response.msg || '加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载子评论异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络异常，请稍后重试',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\t// 清除加载状态\n\t\tchildCommentLoading.value.set(commentId, false)\n\t}\n}\n\n// 删除评论\nconst handleDeleteComment = async (comment) => {\n\t// 验证是否为本人评论\n\tif (!$store.isMe(comment.uid)) {\n\t\tuni.showToast({\n\t\t\ttitle: '只能删除自己的评论',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\ttry {\n\t\tconst result = await uni.showModal({\n\t\t\ttitle: '确认删除',\n\t\t\tcontent: '确定要删除这条评论吗？',\n\t\t\tconfirmText: '删除',\n\t\t\tconfirmColor: '#ff4757'\n\t\t})\n\n\t\tif (result.confirm) {\n\t\t\tconsole.log('删除评论:', {\n\t\t\t\tcommentId: comment.id,\n\t\t\t\tuid: comment.uid,\n\t\t\t\tisMe: $store.isMe(comment.uid)\n\t\t\t})\n\n\t\t\tconst response = await deleteSingleComment(comment.id)\n\n\t\t\tif (response.code === 200) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 重新加载评论列表\n\t\t\t\tawait loadCommentList(currentMomentId.value, true)\n\n\t\t\t\t// 更新动态的评论数量\n\t\t\t\tif (momentDetail.value.comments !== undefined) {\n\t\t\t\t\tmomentDetail.value.comments = commentList.value.length\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.error('删除评论失败:', response.msg)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: response.msg || '删除失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('删除评论异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络异常，请稍后重试',\n\t\t\ticon: 'none'\n\t\t})\n\t}\n}\n\n// 话题标签点击\nconst handleTopicTagClick = (tag) => {\n\tuni.showToast({\n\t\ttitle: `点击了话题: #${tag.name}`,\n\t\ticon: 'none'\n\t})\n\t// TODO: 跳转到话题详情页面\n\t// uni.navigateTo({\n\t//   url: `/pages/topic/detail?title=${tag.name}&id=${tag.id}`\n\t// })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/static/fonts/iconfont.css';\n\n.moment-detail-page {\n\tmin-height: 100vh;\n\tbackground: #f8f9fa;\n}\n\n.nav-more-btn {\n\tpadding: 8rpx;\n\tborder-radius: 50%;\n\ttransition: all 0.3s ease;\n}\n\n.nav-more-btn:active {\n\tbackground-color: rgba(255, 255, 255, 0.1);\n}\n\n.main-container {\n\tmargin-top: 20rpx;\n\tpadding: 20rpx;\n\tpadding-bottom: 120rpx;\n\t/* 为底部操作栏留出空间 */\n}\n\n/* 动态内容 */\n.moment-content {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n}\n\n/* 用户信息 */\n.user-info {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tmargin-bottom: 20rpx;\n}\n\n.user-avatar {\n\tposition: relative;\n\tmargin-right: 16rpx;\n}\n\n.avatar-img {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n}\n\n.verified-badge {\n\tposition: absolute;\n\tbottom: -2rpx;\n\tright: -2rpx;\n\twidth: 24rpx;\n\theight: 24rpx;\n\tbackground: #696CF3;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder: 2rpx solid #fff;\n}\n\n.user-details {\n\tflex: 1;\n}\n\n.follow-btn {\n\tpadding: 12rpx 24rpx;\n\tbackground: linear-gradient(135deg, #696CF3 0%, #5A5FE8 100%);\n\tborder-radius: 50rpx;\n\tmargin-left: 16rpx;\n}\n\n.follow-text {\n\tfont-size: 24rpx;\n\tcolor: #fff;\n\tfont-weight: 500;\n}\n\n.user-name-row {\n\tdisplay: flex;\n\talign-items: center;\n\tflex-wrap: wrap;\n\tgap: 12rpx;\n}\n\n.username {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.user-tags {\n\tdisplay: flex;\n\tgap: 8rpx;\n}\n\n.age-tag,\n.location-tag,\n.occupation-tag {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tbackground: #f5f5f5;\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 动态文本 */\n.moment-text {\n\tmargin-bottom: 20rpx;\n}\n\n.content-text {\n\tfont-size: 30rpx;\n\tline-height: 1.6;\n\tcolor: #333;\n}\n\n/* 话题标签 */\n.topic-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 12rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.topic-tag {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tbackground: rgba(105, 108, 243, 0.08);\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.topic-tag:active {\n\ttransform: scale(0.96);\n\tbackground: rgba(105, 108, 243, 0.12);\n}\n\n.topic-tag-text {\n\tfont-size: 24rpx;\n\tcolor: #696CF3;\n\tfont-weight: 500;\n\tline-height: 1.2;\n}\n\n/* 动态图片 */\n.moment-images {\n\tmargin-bottom: 20rpx;\n}\n\n.image-grid {\n\tdisplay: grid;\n\tgap: 8rpx;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n}\n\n.single-image {\n\tgrid-template-columns: 1fr;\n\tmax-width: 400rpx;\n}\n\n.two-images {\n\tgrid-template-columns: 1fr 1fr;\n}\n\n.three-images {\n\tgrid-template-columns: 1fr 1fr 1fr;\n}\n\n.multiple-images {\n\tgrid-template-columns: 1fr 1fr 1fr;\n}\n\n.image-item {\n\tposition: relative;\n\taspect-ratio: 1;\n\toverflow: hidden;\n\tborder-radius: 8rpx;\n}\n\n.moment-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n/* 动态元信息 */\n.moment-meta {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.publish-time,\n.location {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 互动统计 */\n.interaction-stats {\n\tborder-top: 1rpx solid #f0f0f0;\n\tpadding-top: 20rpx;\n}\n\n.stats-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.stats-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n/* 评论区域 */\n.comments-section {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n}\n\n.section-header {\n\tmargin-bottom: 20rpx;\n}\n\n.section-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n/* 评论列表 */\n.comment-item {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f5f5f5;\n}\n\n.comment-item:last-child {\n\tborder-bottom: none;\n}\n\n.comment-avatar {\n\tmargin-right: 16rpx;\n}\n\n.comment-content {\n\tflex: 1;\n}\n\n.comment-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 8rpx;\n}\n\n.comment-menu {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 50%;\n\ttransition: all 0.3s ease;\n}\n\n.comment-menu:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n}\n\n.menu-dots {\n\tfont-size: 32rpx;\n\tcolor: #999;\n\tfont-weight: bold;\n\tline-height: 1;\n\ttransform: rotate(90deg);\n}\n\n.comment-username {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.comment-tags {\n\tdisplay: flex;\n\tgap: 6rpx;\n}\n\n.comment-verified {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 20rpx;\n\theight: 20rpx;\n\tbackground: #696CF3;\n\tborder-radius: 50%;\n}\n\n.comment-text {\n\tmargin-bottom: 8rpx;\n}\n\n.comment-content-text {\n\tfont-size: 28rpx;\n\tline-height: 1.5;\n\tcolor: #333;\n}\n\n.comment-meta {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.comment-time,\n.comment-location {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n/* 评论操作按钮 */\n.comment-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 24rpx;\n\tmargin-top: 8rpx;\n}\n\n.comment-actions .action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n\tpadding: 8rpx 12rpx;\n\tborder-radius: 16rpx;\n\tbackground: transparent;\n\ttransition: all 0.3s ease;\n}\n\n.comment-actions .action-btn:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n}\n\n.comment-actions .action-text {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n.comment-actions .delete-btn:active {\n\tbackground: rgba(255, 71, 87, 0.1);\n}\n\n.comment-actions .delete-text {\n\tcolor: #ff4757;\n}\n\n.comment-actions .reply-count-text {\n\tcolor: #696CF3;\n}\n\n/* 底部操作栏 */\n.bottom-action-bar {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: #fff;\n\tpadding: 16rpx 20rpx;\n\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n\tborder-top: 1rpx solid #f0f0f0;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tz-index: 100;\n}\n\n.comment-input-area {\n\tflex: 1;\n\tposition: relative;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: #f5f5f5;\n\tborder-radius: 32rpx;\n\tpadding: 0 20rpx;\n\ttransition: all 0.3s ease;\n}\n\n.comment-input-area:active {\n\tbackground: #eeeeee;\n}\n\n/* 回复前缀样式 */\n.reply-prefix {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 8rpx 0 4rpx 0;\n\tborder-bottom: 1rpx solid #e0e0e0;\n\tmargin-bottom: 4rpx;\n}\n\n.prefix-text {\n\tfont-size: 24rpx;\n\tcolor: #696CF3;\n\tfont-weight: 500;\n\tflex: 1;\n}\n\n.cancel-reply {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 32rpx;\n\theight: 32rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s ease;\n}\n\n.cancel-reply:active {\n\tbackground: rgba(0, 0, 0, 0.1);\n}\n\n/* 输入框行样式 */\n.input-row {\n\tdisplay: flex;\n\talign-items: center;\n\twidth: 100%;\n}\n\n.comment-input {\n\tflex: 1;\n\theight: 64rpx;\n\tbackground: transparent;\n\tborder: none;\n\toutline: none;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tpadding: 0;\n\tmargin: 0;\n}\n\n.send-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 56rpx;\n\theight: 56rpx;\n\tbackground: linear-gradient(135deg, #696CF3 0%, #8B7CF6 100%);\n\tborder-radius: 50%;\n\tmargin-left: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.send-btn:active {\n\ttransform: scale(0.95);\n\topacity: 0.8;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tgap: 24rpx;\n}\n\n.action-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 6rpx;\n\tpadding: 8rpx;\n\tborder-radius: 8rpx;\n\ttransition: all 0.3s ease;\n}\n\n.action-btn:active {\n\tbackground: #f5f5f5;\n}\n\n.action-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 点赞图标样式 */\n.action-btn .bani-dianzan,\n.action-btn .bani-dianzan-fill,\n.stats-item .bani-dianzan {\n\ttransition: all 0.3s ease;\n\ttransform-origin: center;\n}\n\n.action-btn .bani-dianzan:active,\n.action-btn .bani-dianzan-fill:active {\n\ttransform: scale(1.2);\n}\n\n/* 子评论样式 */\n.child-comments {\n\tmargin-top: 16rpx;\n\tpadding-left: 20rpx;\n\tborder-left: 2rpx solid #f0f0f0;\n\tmargin-left: 10rpx;\n}\n\n.child-comment-item {\n\tmargin-bottom: 16rpx;\n}\n\n.child-comment-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.child-comment-content {\n\tdisplay: flex;\n\tgap: 12rpx;\n}\n\n.child-avatar {\n\twidth: 48rpx;\n\theight: 48rpx;\n\tborder-radius: 50%;\n\tflex-shrink: 0;\n}\n\n.child-comment-main {\n\tflex: 1;\n}\n\n.child-comment-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 8rpx;\n}\n\n.child-header-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n}\n\n.child-comment-menu {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 32rpx;\n\theight: 32rpx;\n\tborder-radius: 50%;\n\ttransition: all 0.3s ease;\n}\n\n.child-comment-menu:active {\n\tbackground: rgba(0, 0, 0, 0.05);\n}\n\n.child-comment-menu .menu-dots {\n\tfont-size: 28rpx;\n}\n\n.child-nickname {\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.child-comment-time {\n\tfont-size: 20rpx;\n\tcolor: #999;\n}\n\n.child-comment-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tline-height: 1.5;\n\tmargin-bottom: 8rpx;\n}\n\n.child-comment-actions {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n}\n\n.child-comment-actions .action-btn {\n\tpadding: 4rpx 8rpx;\n}\n\n.child-comment-actions .action-text {\n\tfont-size: 20rpx;\n}\n\n/* 加载更多子评论 */\n.load-more-child {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 8rpx;\n\tpadding: 16rpx;\n\tcolor: #999;\n}\n\n.loading-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/moment/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "momentDetail", "commentList", "commentText", "isInputFocused", "commentInputRef", "commentLoading", "commentTotal", "commentPageNum", "commentPageSize", "hasMoreComments", "currentMomentId", "reply<PERSON>arget", "replyPrefix", "childCommentsMap", "childCommentLoading", "expandedComments", "onLoad", "options", "momentId", "loadMomentDetail", "loadCommentList", "onPageScroll", "e", "getRecommendMomentDetail", "response", "formatRecommendMoment", "isRefresh", "params", "COMMENT_TYPE", "uni", "getRootComments", "total", "rows", "formattedComments", "comment", "$store", "error", "getImageGridClass", "imageCount", "previewImage", "images", "current", "handleLike", "toggleMomentLike", "handleFollow", "toggle<PERSON>ser<PERSON><PERSON>ow", "handleShare", "focusCommentInput", "handleInputFocus", "handleInputBlur", "handleSendComment", "rootId", "parentId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replyComment", "addRootComment", "handleReplyComment", "cancelReply", "showCommentMenu", "res", "handleDeleteComment", "err", "handleShowReplies", "commentId", "getChildComments", "formattedChildComments", "childComment", "deleteSingleComment", "handleTopicTagClick", "tag", "MiniProgramPage"], "mappings": "0gBAkOA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,EAAA,EAEAG,EAAAH,EAAA,IAAA,EAAA,EAEAI,EAAAJ,EAAA,IAAA,EAAA,EAEAK,EAAAL,EAAA,IAAA,EAAA,EAEAM,EAAAN,EAAA,IAAA,IAAA,EAGAO,EAAAP,EAAA,IAAA,EAAA,EACAQ,EAAAR,EAAA,IAAA,CAAA,EACAS,EAAAT,EAAA,IAAA,CAAA,EACAU,EAAAV,EAAA,IAAA,EAAA,EACAW,EAAAX,EAAA,IAAA,EAAA,EAGAY,EAAAZ,EAAA,IAAA,IAAA,EAGAa,EAAAb,EAAA,IAAA,IAAA,EACAc,EAAAd,EAAA,IAAA,EAAA,EAGAe,EAAAf,EAAAA,IAAA,IAAA,GAAA,EACAgB,EAAAhB,EAAAA,IAAA,IAAA,GAAA,EACAiB,EAAAjB,EAAAA,IAAA,IAAA,GAAA,EAGAkB,EAAA,OAAAC,GAAA,CACA,MAAAC,EAAAD,EAAA,GACAC,IACAR,EAAA,MAAAQ,EACAC,EAAAD,CAAA,EACAE,EAAAF,CAAA,EAEA,CAAA,EAGAG,EAAA,aAAAC,GAAA,CACAzB,EAAA,MAAAyB,EAAA,SACA,CAAA,EAGA,MAAAH,EAAA,MAAAD,GAAA,CACAK,EAAAA,yBAAAL,CAAA,EAAA,KAAAM,GAAA,CAEAxB,EAAA,MAAAyB,wBAAAD,EAAA,IAAA,CACA,CAAA,CACA,EAIAJ,EAAA,MAAAF,EAAAQ,EAAA,KAAA,CACA,GAAA,CAAArB,EAAA,MAEA,GAAA,CACAA,EAAA,MAAA,GAGAqB,IACAnB,EAAA,MAAA,EACAN,EAAA,MAAA,CAAA,GAGA,MAAA0B,EAAA,CACA,KAAAC,EAAA,aAAA,OACA,WAAAV,EACA,SAAAV,EAAA,MACA,QAAAD,EAAA,MACA,cAAA,aACA,MAAA,MACA,EAEAsB,EAAAA,MAAA,MAAA,MAAA,oCAAA,aAAAF,CAAA,EACA,MAAAH,EAAA,MAAAM,EAAA,gBAAAH,CAAA,EAEA,GAAAH,EAAA,OAAA,KAAAA,EAAA,KAAA,CACA,KAAA,CAAA,MAAAO,EAAA,KAAAC,CAAA,EAAAR,EAAA,KAGAS,EAAAD,EAAA,IAAAE,IAAA,CACA,GAAAA,EAAA,GACA,OAAAA,EAAA,QAAA,KACA,OAAAA,EAAA,UACA,SAAAA,EAAA,YACA,IAAAA,EAAA,IACA,QAAAA,EAAA,QACA,OAAAA,EAAA,OACA,WAAAA,EAAA,WACA,WAAAA,EAAA,YAAA,EACA,KAAAC,EAAA,OAAA,KAAAD,EAAA,GAAA,CACA,EAAA,EAEAR,EACAzB,EAAA,MAAAgC,EAEAhC,EAAA,MAAA,KAAA,GAAAgC,CAAA,EAGA3B,EAAA,MAAAkB,EAAA,KAAA,WACAf,EAAA,MAAAR,EAAA,MAAA,OAAA8B,EAEAF,EAAA,MAAA,MAAA,MAAA,oCAAA,YAAA5B,EAAA,KAAA,CACA,MACA4B,EAAA,MAAA,MAAA,QAAA,oCAAA,YAAAL,EAAA,GAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,OACA,KAAA,MACA,CAAA,CAEA,OAAAY,EAAA,CACAP,EAAAA,MAAA,MAAA,QAAA,oCAAA,YAAAO,CAAA,EACAP,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,QAAA,CACAxB,EAAA,MAAA,EACA,CACA,EAGAgC,EAAAC,GACAA,IAAA,EAAA,eACAA,IAAA,EAAA,aACAA,IAAA,EAAA,eACA,kBAIAC,EAAA,CAAAC,EAAAC,IAAA,CACAZ,EAAAA,MAAA,aAAA,CACA,KAAAW,EACA,QAAAC,CACA,CAAA,CACA,EA8BAC,EAAA,SAAA,CACA,GAAA,CACAb,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAA7B,EAAA,MAAA,GAAA,QAAAA,EAAA,MAAA,OAAA,EAGA,MAAAwB,EAAA,MAAAmB,EAAAA,iBAAA3C,EAAA,MAAA,GAAAA,EAAA,MAAA,OAAA,EAEAwB,EAAA,OAAA,KAEAxB,EAAA,MAAA,QAAA,CAAAA,EAAA,MAAA,QACAA,EAAA,MAAA,SACAA,EAAA,MAAA,OAAAA,EAAA,MAAA,OAAA,GAAA,EACA6B,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,OACA,SAAA,GACA,CAAA,IAEA7B,EAAA,MAAA,MAAA,KAAA,KAAAA,EAAA,MAAA,OAAA,GAAA,EAAA,CAAA,EACA6B,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,OACA,SAAA,GACA,CAAA,KAGAA,EAAA,MAAA,MAAA,QAAA,oCAAA,UAAAL,EAAA,KAAA,MAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,WACA,KAAA,OACA,SAAA,GACA,CAAA,EAEA,OAAAY,EAAA,CACAP,EAAAA,MAAA,MAAA,QAAA,oCAAA,UAAAO,CAAA,EACAP,EAAAA,MAAA,UAAA,CACA,MAAA,eACA,KAAA,OACA,SAAA,GACA,CAAA,CACA,CACA,EAGAe,EAAA,SAAA,CACAf,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAA7B,EAAA,MAAA,KAAA,GAAA,QAAAA,EAAA,MAAA,UAAA,EAGA,MAAAwB,EAAA,MAAAqB,EAAA,iBAAA7C,EAAA,MAAA,KAAA,GAAAA,EAAA,MAAA,UAAA,EAEAwB,EAAA,OAAA,KAEAxB,EAAA,MAAA,WAAA,CAAAA,EAAA,MAAA,WACA6B,EAAAA,MAAA,UAAA,CACA,MAAA7B,EAAA,MAAA,WAAA,OAAA,QACA,KAAA,OACA,SAAA,IACA,CAAA,IAEA6B,EAAA,MAAA,MAAA,QAAA,oCAAA,UAAAL,EAAA,KAAA,MAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,WACA,KAAA,OACA,SAAA,GACA,CAAA,EAEA,EAGAsB,EAAA,IAAA,CACAjB,EAAAA,MAAA,UAAA,CACA,MAAA,UACA,KAAA,MACA,CAAA,CACA,EAGAkB,EAAA,IAAA,CACA3C,EAAA,OACAA,EAAA,MAAA,MAAA,CAEA,EAGA4C,EAAA,IAAA,CACA7C,EAAA,MAAA,EACA,EAGA8C,EAAA,IAAA,CACA9C,EAAA,MAAA,EACA,EAGA+C,EAAA,SAAA,CACA,GAAAhD,EAAA,MAAA,OAIA,IAAA,CAAAQ,EAAA,MAAA,CACAmB,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,GAAA,CACA,IAAAL,EAEA,GAAAb,EAAA,MAAA,CAEA,MAAAwC,EAAAxC,EAAA,MAAA,QAAAA,EAAA,MAAA,GACAyC,EAAAzC,EAAA,MAAA,GAGA0C,EAAAzC,EAAA,MAAAV,EAAA,MAAA,KAAA,EAEA2B,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAA,CACA,WAAAnB,EAAA,MACA,KAAAkB,EAAA,aAAA,OACA,OAAAuB,EACA,SAAAC,EACA,QAAAC,EACA,YAAA1C,EAAA,MAAA,SACA,gBAAAT,EAAA,MAAA,KAAA,EACA,OAAAU,EAAA,KACA,CAAA,EAEAY,EAAA,MAAA8B,EAAA,aACA5C,EAAA,MACAkB,EAAAA,aAAA,OACAuB,EACAC,EACAC,CACA,CACA,MAEAxB,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAA,CACA,WAAAnB,EAAA,MACA,KAAAkB,EAAA,aAAA,OACA,QAAA1B,EAAA,MAAA,KAAA,CACA,CAAA,EAEAsB,EAAA,MAAA+B,EAAA,eACA7C,EAAA,MACAkB,EAAAA,aAAA,OACA1B,EAAA,MAAA,KAAA,CACA,EAGAsB,EAAA,OAAA,KAEAtB,EAAA,MAAA,GACAS,EAAA,MAAA,KACAC,EAAA,MAAA,GAGAiB,EAAAA,MAAA,UAAA,CACA,MAAAlB,EAAA,MAAA,OAAA,OACA,KAAA,SACA,CAAA,EAGA,MAAAS,EAAAV,EAAA,MAAA,EAAA,EAGAV,EAAA,MAAA,WAAA,SACAA,EAAA,MAAA,SAAAC,EAAA,MAAA,UAGA4B,EAAA,MAAA,MAAA,QAAA,oCAAA,QAAAL,EAAA,GAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,OACA,KAAA,MACA,CAAA,EAEA,OAAAY,EAAA,CACAP,EAAAA,MAAA,MAAA,QAAA,oCAAA,QAAAO,CAAA,EACAP,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,EACA,EAGA2B,EAAAtB,GAAA,CAEAvB,EAAA,MAAAuB,EACAtB,EAAA,MAAA,KAAAsB,EAAA,QAAA,KAGAa,EAAA,EAEAlB,EAAAA,MAAA,MAAA,MAAA,oCAAA,UAAA,CACA,OAAAK,EACA,OAAAtB,EAAA,KACA,CAAA,CACA,EAGA6C,EAAA,IAAA,CACA9C,EAAA,MAAA,KACAC,EAAA,MAAA,GACAV,EAAA,MAAA,GAEA2B,EAAAA,MAAA,MAAA,MAAA,oCAAA,MAAA,CACA,EAGA6B,EAAAxB,GAAA,CAEAC,EAAAA,OAAA,KAAAD,EAAA,GAAA,GAIAL,EAAAA,MAAA,gBAAA,CACA,SAAA,CAAA,MAAA,EACA,UAAA,UACA,QAAA8B,GAAA,CACAA,EAAA,WAAA,GAEAC,EAAA1B,CAAA,CAEA,EACA,KAAA2B,GAAA,CACAhC,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAAgC,CAAA,CACA,CACA,CAAA,CACA,EAGAC,EAAA,MAAA5B,GAAA,CACA,MAAA6B,EAAA7B,EAAA,GAGA,GAAAnB,EAAA,MAAA,IAAAgD,CAAA,EAAA,CACAhD,EAAA,MAAA,OAAAgD,CAAA,EACA,MACA,CAGA,GAAA,CAAAjD,EAAA,MAAA,IAAAiD,CAAA,EAIA,GAAA,CAEAjD,EAAA,MAAA,IAAAiD,EAAA,EAAA,EAEA,MAAApC,EAAA,CACA,KAAAC,EAAA,aAAA,OACA,WAAAlB,EAAA,MACA,OAAAqD,EACA,SAAA,GACA,QAAA,EACA,cAAA,aACA,MAAA,KACA,EAEAlC,EAAAA,MAAA,MAAA,MAAA,oCAAA,YAAAF,CAAA,EACA,MAAAH,EAAA,MAAAwC,EAAA,iBAAArC,CAAA,EAEA,GAAAH,EAAA,OAAA,KAAAA,EAAA,KAAA,CACA,KAAA,CAAA,KAAAQ,GAAAR,EAAA,KAGAyC,EAAAjC,EAAA,IAAAkC,IAAA,CACA,GAAAA,EAAA,GACA,OAAAA,EAAA,OACA,OAAAA,EAAA,UACA,SAAAA,EAAA,YACA,IAAAA,EAAA,IACA,QAAAA,EAAA,QACA,OAAAA,EAAA,OACA,WAAAA,EAAA,WACA,YAAAA,EAAA,WACA,KAAA/B,EAAA,OAAA,KAAA+B,EAAA,GAAA,CACA,EAAA,EAGArD,EAAA,MAAA,IAAAkD,EAAAE,CAAA,EAGAlD,EAAA,MAAA,IAAAgD,CAAA,EAEAlC,EAAAA,MAAA,MAAA,MAAA,oCAAA,WAAA,CACA,cAAAkC,EACA,cAAAE,CACA,CAAA,CACA,MACApC,EAAA,MAAA,MAAA,QAAA,oCAAA,WAAAL,EAAA,GAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,OACA,KAAA,MACA,CAAA,CAEA,OAAAY,EAAA,CACAP,EAAAA,MAAA,MAAA,QAAA,oCAAA,WAAAO,CAAA,EACAP,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,QAAA,CAEAf,EAAA,MAAA,IAAAiD,EAAA,EAAA,CACA,CACA,EAGAH,EAAA,MAAA1B,GAAA,CAEA,GAAA,CAAAC,EAAAA,OAAA,KAAAD,EAAA,GAAA,EAAA,CACAL,EAAAA,MAAA,UAAA,CACA,MAAA,YACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,GAAA,CAQA,IAPA,MAAAA,EAAA,MAAA,UAAA,CACA,MAAA,OACA,QAAA,cACA,YAAA,KACA,aAAA,SACA,CAAA,GAEA,QAAA,CACAA,EAAAA,MAAA,MAAA,MAAA,oCAAA,QAAA,CACA,UAAAK,EAAA,GACA,IAAAA,EAAA,IACA,KAAAC,EAAA,OAAA,KAAAD,EAAA,GAAA,CACA,CAAA,EAEA,MAAAV,EAAA,MAAA2C,sBAAAjC,EAAA,EAAA,EAEAV,EAAA,OAAA,KACAK,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,EAGA,MAAAT,EAAAV,EAAA,MAAA,EAAA,EAGAV,EAAA,MAAA,WAAA,SACAA,EAAA,MAAA,SAAAC,EAAA,MAAA,UAGA4B,EAAA,MAAA,MAAA,QAAA,oCAAA,UAAAL,EAAA,GAAA,EACAK,EAAAA,MAAA,UAAA,CACA,MAAAL,EAAA,KAAA,OACA,KAAA,MACA,CAAA,EAEA,CACA,OAAAY,EAAA,CACAP,EAAAA,MAAA,MAAA,QAAA,oCAAA,UAAAO,CAAA,EACAP,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,CACA,EAGAuC,EAAAC,GAAA,CACAxC,EAAAA,MAAA,UAAA,CACA,MAAA,WAAAwC,EAAA,IAAA,GACA,KAAA,MACA,CAAA,CAKA,mgFCvwBA,GAAG,WAAWC,EAAe"}