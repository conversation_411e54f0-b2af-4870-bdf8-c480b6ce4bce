package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageTagListVo;
import com.gzhuxn.personals.domain.manage.bo.ManageTagBoToManageTagMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageTagBoToManageTagMapper.class,ManageTagToManageTagVoMapper.class,ManageTagToAdminActTagVoMapper.class,ManageTagToAppManageTagVoMapper.class},
    imports = {}
)
public interface ManageTagToAppManageTagListVoMapper extends BaseMapper<ManageTag, AppManageTagListVo> {
}
