package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserGiftVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserGiftToUserGiftVoMapperImpl implements UserGiftToUserGiftVoMapper {

    @Override
    public UserGiftVo convert(UserGift arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGiftVo userGiftVo = new UserGiftVo();

        userGiftVo.setId( arg0.getId() );
        userGiftVo.setUserId( arg0.getUserId() );
        userGiftVo.setOppositeUserId( arg0.getOppositeUserId() );
        userGiftVo.setGiftId( arg0.getGiftId() );
        userGiftVo.setGiftName( arg0.getGiftName() );
        userGiftVo.setGiftPrice( arg0.getGiftPrice() );
        userGiftVo.setGiftNum( arg0.getGiftNum() );
        userGiftVo.setCoin( arg0.getCoin() );

        return userGiftVo;
    }

    @Override
    public UserGiftVo convert(UserGift arg0, UserGiftVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setGiftId( arg0.getGiftId() );
        arg1.setGiftName( arg0.getGiftName() );
        arg1.setGiftPrice( arg0.getGiftPrice() );
        arg1.setGiftNum( arg0.getGiftNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
