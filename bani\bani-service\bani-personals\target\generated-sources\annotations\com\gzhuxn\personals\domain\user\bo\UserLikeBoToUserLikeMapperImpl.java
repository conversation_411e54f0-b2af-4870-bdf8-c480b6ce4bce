package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserLike;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLikeBoToUserLikeMapperImpl implements UserLikeBoToUserLikeMapper {

    @Override
    public UserLike convert(UserLikeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLike userLike = new UserLike();

        userLike.setSearchValue( arg0.getSearchValue() );
        userLike.setCreateBy( arg0.getCreateBy() );
        userLike.setCreateTime( arg0.getCreateTime() );
        userLike.setUpdateBy( arg0.getUpdateBy() );
        userLike.setUpdateTime( arg0.getUpdateTime() );
        userLike.setDelFlag( arg0.getDelFlag() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userLike.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userLike.setCreateDept( arg0.getCreateDept() );
        userLike.setId( arg0.getId() );
        userLike.setUserId( arg0.getUserId() );
        userLike.setType( arg0.getType() );
        userLike.setBusinessId( arg0.getBusinessId() );

        return userLike;
    }

    @Override
    public UserLike convert(UserLikeBo arg0, UserLike arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setDelFlag( arg0.getDelFlag() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
