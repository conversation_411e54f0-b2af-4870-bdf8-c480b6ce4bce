package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageGiftVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageGiftToAppManageGiftVoMapperImpl implements ManageGiftToAppManageGiftVoMapper {

    @Override
    public AppManageGiftVo convert(ManageGift arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppManageGiftVo appManageGiftVo = new AppManageGiftVo();

        appManageGiftVo.setId( arg0.getId() );
        appManageGiftVo.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            appManageGiftVo.setIcon( String.valueOf( arg0.getIcon() ) );
        }
        appManageGiftVo.setPrice( arg0.getPrice() );

        return appManageGiftVo;
    }

    @Override
    public AppManageGiftVo convert(ManageGift arg0, AppManageGiftVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            arg1.setIcon( String.valueOf( arg0.getIcon() ) );
        }
        else {
            arg1.setIcon( null );
        }
        arg1.setPrice( arg0.getPrice() );

        return arg1;
    }
}
