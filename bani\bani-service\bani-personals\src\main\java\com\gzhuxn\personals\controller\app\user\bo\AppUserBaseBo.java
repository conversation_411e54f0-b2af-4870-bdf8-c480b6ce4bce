package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.common.core.validate.EditGroup;
import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * 用户-用户详情业务对象 user_detail
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
@AutoMappers({
    @AutoMapper(target = RemoteUserBo.class, reverseConvertGenerate = false),
    @AutoMapper(target = UserDetail.class, reverseConvertGenerate = false)
})
public class AppUserBaseBo {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 昵称
     */
    @NotBlank(message = "昵称不能为空", groups = {EditGroup.class})
    private String nickName;
    /**
     * 性别,编码;字典：user_sex
     */
    @NotBlank(message = "性别不能为空", groups = {EditGroup.class})
    private String sex;

    /**
     * 生日/出生日期
     */
    @NotNull(message = "出生日期不能为空", groups = {EditGroup.class})
    private LocalDate birthday;

    /**
     * 身高,cm
     */
    @NotNull(message = "身高不能为空", groups = {EditGroup.class})
    private Integer height;

    /**
     * 体重,kg
     */
    @NotNull(message = "体重不能为空", groups = {EditGroup.class})
    private Integer weight;

    /**
     * 学历,编码;字典：user_edu
     */
    @NotBlank(message = "学历不能为空", groups = {EditGroup.class})
    private String edu;

    /**
     * 职业,编码;字典：user_job
     */
    @NotBlank(message = "职业不能为空", groups = {EditGroup.class})
    private String job;

    /**
     * 个人情感状况,编码;字典：user_affective_status
     */
    private String affectiveStatus;

    /**
     * 收入,编码;字典：user_revenue
     */
    private String revenue;

    /**
     * 户籍地省编码
     */
    @NotBlank(message = "户籍地省编码不能为空", groups = {EditGroup.class})
    private String addrProvinceCode;

    /**
     * 户籍地市编码
     */
    @NotBlank(message = "户籍地市编码不能为空", groups = {EditGroup.class})
    private String addrCityCode;

    /**
     * 户籍地区/县编码
     */
    @NotBlank(message = "户籍地区/县编码不能为空", groups = {EditGroup.class})
    private String addrDistrictCode;

    /**
     * 户籍地街道/镇/乡编码
     */
    @NotBlank(message = "户籍地街道/镇/乡编码不能为空", groups = {EditGroup.class})
    private String addrStreetCode;

    /**
     * 户籍详细地址
     */
    @NotBlank(message = "户籍详细地址不能为空", groups = {EditGroup.class})
    private String addr;

    /**
     * 现居地省编码
     */
    @NotBlank(message = "现居地省编码不能为空", groups = {EditGroup.class})
    private String addrNewProvinceCode;

    /**
     * 现居地市编码
     */
    @NotBlank(message = "现居地市编码不能为空", groups = {EditGroup.class})
    private String addrNewCityCode;

    /**
     * 现居地区/县编码
     */
    @NotBlank(message = "现居地区/县编码不能为空", groups = {EditGroup.class})
    private String addrNewDistrictCode;

    /**
     * 现居地街道/镇/乡编码
     */
    @NotBlank(message = "现居地街道/镇/乡编码不能为空", groups = {EditGroup.class})
    private String addrNewStreetCode;

    /**
     * 现居详细地址
     */
    @NotBlank(message = "现居详细地址不能为空", groups = {EditGroup.class})
    private String addrNew;
}
