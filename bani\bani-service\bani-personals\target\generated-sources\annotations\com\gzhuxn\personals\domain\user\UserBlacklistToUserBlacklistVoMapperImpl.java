package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserBlacklistVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBlacklistToUserBlacklistVoMapperImpl implements UserBlacklistToUserBlacklistVoMapper {

    @Override
    public UserBlacklistVo convert(UserBlacklist arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserBlacklistVo userBlacklistVo = new UserBlacklistVo();

        userBlacklistVo.setId( arg0.getId() );
        userBlacklistVo.setUserId( arg0.getUserId() );
        userBlacklistVo.setOppositeUserId( arg0.getOppositeUserId() );

        return userBlacklistVo;
    }

    @Override
    public UserBlacklistVo convert(UserBlacklist arg0, UserBlacklistVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
