package com.gzhuxn.personals.domain.audit;

import com.gzhuxn.personals.domain.audit.vo.ContentAuditVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ContentAuditRecordToContentAuditVoMapperImpl implements ContentAuditRecordToContentAuditVoMapper {

    @Override
    public ContentAuditVo convert(ContentAuditRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ContentAuditVo contentAuditVo = new ContentAuditVo();

        contentAuditVo.setId( arg0.getId() );
        contentAuditVo.setBusinessId( arg0.getBusinessId() );
        contentAuditVo.setType( arg0.getType() );
        contentAuditVo.setAuditStatus( arg0.getAuditStatus() );
        contentAuditVo.setAuditTime( arg0.getAuditTime() );
        contentAuditVo.setAuditDesc( arg0.getAuditDesc() );
        contentAuditVo.setAuditUserId( arg0.getAuditUserId() );
        contentAuditVo.setAuditUserName( arg0.getAuditUserName() );
        contentAuditVo.setUserId( arg0.getUserId() );
        contentAuditVo.setUserName( arg0.getUserName() );
        contentAuditVo.setCreateTime( arg0.getCreateTime() );

        return contentAuditVo;
    }

    @Override
    public ContentAuditVo convert(ContentAuditRecord arg0, ContentAuditVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setType( arg0.getType() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setAuditTime( arg0.getAuditTime() );
        arg1.setAuditDesc( arg0.getAuditDesc() );
        arg1.setAuditUserId( arg0.getAuditUserId() );
        arg1.setAuditUserName( arg0.getAuditUserName() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
