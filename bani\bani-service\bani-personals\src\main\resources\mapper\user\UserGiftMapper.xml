<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gzhuxn.personals.mapper.user.UserGiftMapper">
    <select id="queryUsedByIds" resultType="java.lang.String">
        SELECT
        DISTINCT gift_name
        FROM
        user_gift
        WHERE
        del_flag = 0
        AND gift_id IN
        <foreach collection="giftIds" item="giftId" open="(" separator="," close=")">
            #{giftId}
        </foreach>
    </select>

    <!-- 根据查询类型分页查询礼物列表 -->
    <select id="selectAppPageListByQueryType"
            resultType="com.gzhuxn.personals.controller.app.user.vo.gift.AppUserGiftVo">
        SELECT
        ug.id,
        ug.user_id,
        ug.opposite_user_id AS uid,
        ug.gift_id,
        ug.gift_name,
        ug.gift_price,
        ug.gift_num,
        ug.coin,
        ug.create_time time
        FROM user_gift ug
        WHERE ug.del_flag = 0
        <!-- 根据查询类型决定查询条件 -->
        <choose>
            <!-- 我送给他人的礼物 -->
            <when test="bo.queryType != null and bo.queryType == 1">
                AND ug.user_id = #{bo.userId}
            </when>
            <!-- 送给我的礼物 -->
            <when test="bo.queryType != null and bo.queryType == 2">
                AND ug.opposite_user_id = #{bo.userId}
            </when>
            <!-- 默认查询我送给他人的礼物 -->
            <otherwise>
                AND ug.user_id = #{bo.userId}
            </otherwise>
        </choose>
        ORDER BY ug.create_time DESC
    </select>

    <!-- 查询用户收到的礼物总数量 -->
    <select id="selectReceivedGiftTotalCount" resultType="java.lang.Integer">
        SELECT SUM(1) AS totalCount
        FROM user_gift ug
        WHERE ug.del_flag = 0
          AND ug.opposite_user_id = #{userId}
    </select>

    <!-- 分页查询用户收到的礼物墙列表（按礼物类型聚合） -->
    <select id="selectGiftWallPageList"
            resultType="com.gzhuxn.personals.controller.app.user.vo.gift.AppUserGiftWallItemVo">
        SELECT ug.id        AS id,
               ug.gift_name AS giftName,
               mg.icon      AS giftIcon,
               ug.gift_num  AS giftNum
        FROM user_gift ug
                 LEFT JOIN manage_gift mg ON ug.gift_id = mg.id
        WHERE ug.del_flag = 0
          AND ug.opposite_user_id = #{userId}
          AND mg.del_flag = 0
        ORDER BY ug.create_time DESC
    </select>

    <!-- 查询用户收到的礼物列表（按礼物类型聚合） -->
    <select id="selectReceivedGiftListByUserId"
            resultType="com.gzhuxn.personals.controller.app.user.vo.gift.AppUserLitGiftItemVo">
        SELECT mg.id            AS id,
               mg.name          AS name,
               mg.icon          AS icon,
               mg.price         AS price,
               SUM(ug.gift_num) AS receivedCount
        FROM user_gift ug
                 INNER JOIN manage_gift mg ON ug.gift_id = mg.id
        WHERE ug.del_flag = 0
          AND ug.opposite_user_id = #{userId}
          AND mg.del_flag = 0
          AND mg.status = 1
        GROUP BY mg.id, mg.name, mg.icon, mg.price
        ORDER BY receivedCount DESC
    </select>
</mapper>
