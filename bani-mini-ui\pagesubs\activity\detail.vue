<template>
	<scroll-nav-page title="活动详情" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 活动图片 -->
				<image class="activity-image" :src="activityDetail.image" mode="aspectFill"></image>

				<!-- 活动信息 -->
				<view class="activity-info">
					<!-- 标题和状态 -->
					<view class="header">
						<text class="title">{{ activityDetail.title }}</text>
						<view class="status" :class="activityDetail.status">{{ getStatusText(activityDetail.status) }}
						</view>
					</view>

					<!-- 发起人信息（仅搭子活动显示） -->
					<view v-if="type === 'buddy'" class="organizer">
						<image class="avatar" :src="activityDetail.avatar" mode="aspectFill"></image>
						<view class="user-info">
							<text class="name">{{ activityDetail.name }}</text>
							<text class="type-tag">{{ getTypeLabel(activityDetail.type) }}</text>
						</view>
					</view>

					<!-- 活动详情 -->
					<view class="detail-list">
						<view class="detail-item">
							<uni-icons type="location" size="16" color="$text-secondary"></uni-icons>
							<text class="label">活动地点：</text>
							<text class="value">{{ activityDetail.location }}</text>
						</view>
						<view class="detail-item">
							<uni-icons type="calendar" size="16" color="$text-secondary"></uni-icons>
							<text class="label">开始时间：</text>
							<text class="value">{{ activityDetail.startTime }}</text>
						</view>
						<view class="detail-item">
							<uni-icons type="clock" size="16" color="$text-secondary"></uni-icons>
							<text class="label">活动时长：</text>
							<text class="value">{{ activityDetail.duration }}</text>
						</view>
						<view class="detail-item">
							<uni-icons type="wallet" size="16" color="$text-secondary"></uni-icons>
							<text class="label">活动费用：</text>
							<text class="value price">¥{{ activityDetail.price }}</text>
						</view>
					</view>

					<!-- 活动介绍 -->
					<view class="description">
						<text class="section-title">活动介绍</text>
						<text class="content">{{ activityDetail.description }}</text>
					</view>

					<!-- 报名须知 -->
					<view class="notice">
						<text class="section-title">报名须知</text>
						<view class="notice-list">
							<view class="notice-item" v-for="(item, index) in activityDetail.notices" :key="index">
								<text class="dot">·</text>
								<text class="text">{{ item }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 底部操作栏 -->
				<view class="bottom-bar">
					<view class="left">
						<button class="share-btn" open-type="share">
							<uni-icons type="redo" size="20" color="$text-secondary"></uni-icons>
							<text>分享</text>
						</button>
						<button class="contact-btn" @click="contactOrganizer">
							<uni-icons type="chat" size="20" color="$text-secondary"></uni-icons>
							<text>联系</text>
						</button>
					</view>
					<view class="right">
						<button class="enroll-btn" :class="{ disabled: !canEnroll }" @click="handleEnroll">
							{{ getEnrollButtonText() }}
						</button>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onPageScroll } from '@dcloudio/uni-app'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)

// 活动类型
const type = ref('official')
// 活动ID
const id = ref(null)

// 活动详情
const activityDetail = ref({
	id: 1,
	title: '2024春季相亲交友会',
	image: 'https://p3.toutiaoimg.com/large/tos-cn-i-qvj2lq49k0/b66de365c7614b24af852e21b4271976.jpg',
	name: '小明',
	avatar: '/static/image/avatar/1.png',
	type: 1,
	location: '北京市朝阳区某咖啡厅',
	startTime: '2024-04-20 14:00',
	duration: '3小时',
	price: 99,
	status: 'enrolling',
	description: '本次活动旨在为单身人士提供一个轻松愉快的交友平台。通过精心设计的互动环节，帮助参与者更好地了解彼此，找到心仪的对象。',
	notices: [
		'请准时到达活动地点',
		'活动期间请保持手机静音',
		'请着装整洁得体',
		'活动费用包含场地费和饮品'
	]
})

// 是否可以报名
const canEnroll = ref(true)

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		notStarted: '未开始',
		enrolling: '报名中',
		enrollEnd: '报名已结束',
		inProgress: '活动进行中',
		ended: '活动已结束'
	}
	return statusMap[status] || ''
}

// 获取类型标签
const getTypeLabel = (type) => {
	const typeMap = {
		1: '相亲会',
		2: '聊天',
		3: '干饭',
		4: '户外',
		5: '看展',
		6: '运动',
		7: '学习',
		8: '喝酒',
		9: '打游戏',
		10: '其他'
	}
	return typeMap[type] || ''
}

// 获取报名按钮文本
const getEnrollButtonText = () => {
	const status = activityDetail.value.status
	const textMap = {
		notStarted: '预约报名',
		enrolling: '立即报名',
		enrollEnd: '报名已结束',
		inProgress: '活动进行中',
		ended: '活动已结束'
	}
	return textMap[status] || '立即报名'
}

// 处理报名
const handleEnroll = () => {
	if (!canEnroll.value) return
	uni.navigateTo({
		url: `/pages/activity/enroll/enroll?id=${activityDetail.value.id}`
	})
}

// 联系发起人
const contactOrganizer = () => {
	uni.navigateTo({
		url: `/pages/message/chat/chat?id=${activityDetail.value.id}&name=${activityDetail.value.name}`
	})
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 分享
const onShareAppMessage = () => {
	return {
		title: activityDetail.value.title,
		path: `/pages/activity/detail/detail?id=${activityDetail.value.id}&type=${type.value}`,
		imageUrl: activityDetail.value.image
	}
}

// 处理页面滚动
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
}

// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏图标颜色
const getNavIconColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}

// 页面滚动监听
onPageScroll(handlePageScroll)

// 页面加载
onMounted((options) => {
	id.value = options.id
	type.value = options.type
	// TODO: 根据id和type获取活动详情
})
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 120rpx;
	position: relative;
}

.nav-back-btn {
	width: 88rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	margin-left: 20rpx;
}

.activity-image {
	width: 100%;
	height: 400rpx;
}

.activity-info {
	background: #fff;
	padding: 30rpx;
	margin-top: -30rpx;
	border-radius: 30rpx 30rpx 0 0;
	position: relative;

	.header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 30rpx;

		.title {
			font-size: $font-size-xl;
			font-weight: $font-weight-bold;
			color: $text-primary;
			flex: 1;
			margin-right: $spacing-md;
		}

		.status {
			font-size: $font-size-xs;
			padding: $spacing-xs $spacing-md;
			border-radius: $radius-full;

			&.notStarted {
				background: $primary-light;
				color: $primary-color;
			}

			&.enrolling {
				background: $success-light;
				color: $success-color;
			}

			&.enrollEnd {
				background: $warning-light;
				color: $warning-color;
			}

			&.inProgress {
				background: $info-light;
				color: $info-color;
			}

			&.ended {
				background: rgba(0, 0, 0, 0.1);
				color: $text-tertiary;
			}
		}
	}

	.organizer {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-lg;
		padding: $spacing-md;
		background: $primary-lighter;
		border-radius: $radius-md;

		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: $spacing-md;
		}

		.user-info {
			.name {
				font-size: $font-size-md;
				color: $text-primary;
				font-weight: $font-weight-medium;
				margin-bottom: $spacing-xs;
			}

			.type-tag {
				font-size: $font-size-xs;
				color: $primary-color;
				background: $primary-light;
				padding: $spacing-xs $spacing-sm;
				border-radius: $radius-full;
			}
		}
	}

	.detail-list {
		margin-bottom: $spacing-lg;

		.detail-item {
			display: flex;
			align-items: center;
			margin-bottom: $spacing-md;

			.label {
				font-size: $font-size-md;
				color: $text-secondary;
				margin: 0 $spacing-xs;
			}

			.value {
				font-size: $font-size-md;
				color: $text-primary;

				&.price {
					color: $primary-color;
					font-weight: $font-weight-bold;
				}
			}
		}
	}

	.description,
	.notice {
		margin-bottom: $spacing-lg;

		.section-title {
			font-size: $font-size-lg;
			font-weight: $font-weight-bold;
			color: $text-primary;
			margin-bottom: $spacing-md;
			display: block;
		}

		.content {
			font-size: $font-size-md;
			color: $text-secondary;
			line-height: 1.6;
		}

		.notice-list {
			.notice-item {
				display: flex;
				margin-bottom: $spacing-md;

				.dot {
					color: $primary-color;
					margin-right: $spacing-xs;
				}

				.text {
					font-size: $font-size-md;
					color: $text-secondary;
					line-height: 1.6;
				}
			}
		}
	}
}

.bottom-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 100rpx;
	background: $bg-primary;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 $spacing-lg;
	box-shadow: $shadow-sm;

	.left {
		display: flex;
		align-items: center;

		button {
			background: none;
			padding: 0;
			margin: 0;
			line-height: 1;
			border: none;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-right: $spacing-xl;

			&::after {
				border: none;
			}

			text {
				font-size: $font-size-xs;
				color: $text-secondary;
				margin-top: $spacing-xs;
			}
		}
	}

	.right {
		.enroll-btn {
			background: $primary-gradient;
			color: $text-white;
			font-size: $font-size-md;
			padding: $spacing-md $spacing-xl;
			border-radius: $radius-full;
			border: none;

			&.disabled {
				background: $text-tertiary;
			}

			&::after {
				border: none;
			}
		}
	}
}
</style>