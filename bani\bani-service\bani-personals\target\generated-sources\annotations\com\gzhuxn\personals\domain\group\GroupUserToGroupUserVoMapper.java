package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.controller.app.group.bo.AppGroupUserBoToGroupUserMapper;
import com.gzhuxn.personals.domain.group.bo.GroupUserBoToGroupUserMapper;
import com.gzhuxn.personals.domain.group.vo.GroupUserVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppGroupUserBoToGroupUserMapper.class,GroupUserBoToGroupUserMapper.class},
    imports = {}
)
public interface GroupUserToGroupUserVoMapper extends BaseMapper<GroupUser, GroupUserVo> {
}
