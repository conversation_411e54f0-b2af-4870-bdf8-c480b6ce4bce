<template>
	<scroll-nav-page title="我的问答" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 主要内容 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<!-- 顶部菜单 -->
					<view class="top-menu">
						<uni-segmented-control :current="segmentedIndex" :values="['我的提问', '我的回答']"
							@clickItem="switchTab" styleType="text" activeColor="#696CF3"></uni-segmented-control>
					</view>

					<!-- z-paging分页组件 -->
					<z-paging ref="paging" v-model="datas" :fixed="false" :use-page-scroll="false" @query="queryList">

						<!-- 问答记录列表 -->
						<view class="content">
							<view class="question-item" v-for="(item, index) in datas" :key="item.id">
								<view class="question-header">
									<view class="question-type">
										<uni-icons :type="segmentedIndex === 0 ? 'help' : 'chatbubble'" size="16"
											:color="segmentedIndex === 0 ? '#ff6b6b' : '#4ecb73'">
										</uni-icons>
										<text class="type-text">{{ segmentedIndex === 0 ? '提问' : '回答' }}</text>
									</view>
									<text class="create-time">{{ item.createTime }}</text>
								</view>

								<view class="question-content">
									<text class="question-title">{{ item.title || item.question }}</text>
									<text class="question-desc" v-if="item.content">{{ item.content }}</text>
								</view>

								<view class="question-footer">
									<view class="stats">
										<view class="stat-item">
											<uni-icons type="eye" size="14" color="#999"></uni-icons>
											<text class="stat-text">{{ item.viewCount || 0 }}次查看</text>
										</view>
										<view class="stat-item" v-if="segmentedIndex === 0">
											<uni-icons type="chatbubble" size="14" color="#999"></uni-icons>
											<text class="stat-text">{{ item.answerCount || 0 }}个回答</text>
										</view>
										<view class="stat-item" v-if="item.isResolved">
											<uni-icons type="checkmarkempty" size="14" color="#4ecb73"></uni-icons>
											<text class="stat-text resolved">已解决</text>
										</view>
									</view>
									<view class="action-btn" @click="viewDetail(item)">
										<text>查看详情</text>
										<uni-icons type="right" size="12" color="#696CF3"></uni-icons>
									</view>
								</view>
							</view>
						</view>

						<!-- 空状态 -->
						<template #empty>
							<view class="empty-state">
								<view class="empty-icon">
									<uni-icons :type="segmentedIndex === 0 ? 'help' : 'chatbubble'" size="80"
										color="#ddd">
									</uni-icons>
								</view>
								<text class="empty-text">{{ segmentedIndex === 0 ? '暂无提问记录' : '暂无回答记录' }}</text>
								<text class="empty-desc">{{ segmentedIndex === 0 ? '您还没有提出过问题' : '您还没有回答过问题' }}</text>
							</view>
						</template>
					</z-paging>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onPageScroll } from "@dcloudio/uni-app"

// 导航栏相关
const pageScrollTop = ref(0)
const navBarHeight = ref(0)

// 计算属性
const getNavTextColor = computed(() => {
	return pageScrollTop.value > 50 ? '#333' : '#fff'
})

// 页面数据
const segmentedIndex = ref(0)
const type = ref('')

// z-paging组件
const paging = ref(null)
const datas = ref([])

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

onLoad((param) => {
	if (!param.type) {
		param.type = 1
	}
	segmentedIndex.value = parseInt(param.type) - 1
	type.value = param.type
})

function queryList(pageNum, pageSize) {
	// TODO: 调用问答记录API
	// getQuestionAnswerPage({
	// 	pageNum: pageNum,
	// 	pageSize: pageSize,
	// 	type: type.value,
	// }).then(res => {
	// 	paging.value.complete(res.rows);
	// }).catch(() => {
	// 	paging.value.complete(false);
	// })

	// 模拟数据
	const mockData = [
		{
			id: 1,
			title: '如何提高个人魅力？',
			content: '想要在交友中更有吸引力，有什么好的建议吗？',
			createTime: '2024-01-15 14:30',
			viewCount: 25,
			answerCount: 3,
			isResolved: true
		},
		{
			id: 2,
			title: '第一次约会应该注意什么？',
			content: '马上要和心仪的人第一次见面，有点紧张，求建议！',
			createTime: '2024-01-14 10:20',
			viewCount: 18,
			answerCount: 5,
			isResolved: false
		}
	]

	setTimeout(() => {
		paging.value.complete(pageNum === 1 ? mockData : [])
	}, 500)
}

const switchTab = (e) => {
	if (segmentedIndex.value !== e.currentIndex) {
		segmentedIndex.value = e.currentIndex
		type.value = segmentedIndex.value + 1
		paging.value.reload()
	}
}

const viewDetail = (item) => {
	// TODO: 跳转到问答详情页面
	uni.navigateTo({
		url: `/pagesubs/my/question/detail?id=${item.id}&type=${type.value}`
	})
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.main-container {
	min-height: 100vh;
	box-sizing: border-box;
	padding: 0 20rpx 120rpx;
}

// z-paging组件样式
:deep(.z-paging-content) {
	min-height: calc(100vh - 200px);
}

.top-menu {
	background: rgba(255, 255, 255, 0.95);
	padding: 24rpx 32rpx;
	margin: 20rpx 0 16rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);

	:deep(.uni-segmented-control) {
		background-color: transparent;

		.segmented-control__item {
			color: #666;
			font-weight: 500;

			&.segmented-control__item--button--active {
				color: $primary-color;
				font-weight: 600;
			}
		}
	}
}

.content {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	overflow: hidden;

	.question-item {
		padding: 32rpx 24rpx;
		border-bottom: 1rpx solid rgba($primary-color, 0.08);
		transition: all 0.3s ease;

		&:last-child {
			border-bottom: none;
		}

		&:hover {
			background: rgba($primary-color, 0.02);
		}

		.question-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;

			.question-type {
				display: flex;
				align-items: center;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				background: rgba($primary-color, 0.1);

				.type-text {
					font-size: 24rpx;
					color: $primary-color;
					font-weight: 600;
					margin-left: 8rpx;
				}
			}

			.create-time {
				font-size: 24rpx;
				color: #999;
			}
		}

		.question-content {
			margin-bottom: 20rpx;

			.question-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.5;
				display: block;
				margin-bottom: 12rpx;
			}

			.question-desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.6;
				display: block;
			}
		}

		.question-footer {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.stats {
				display: flex;
				gap: 24rpx;

				.stat-item {
					display: flex;
					align-items: center;

					.stat-text {
						font-size: 24rpx;
						color: #999;
						margin-left: 6rpx;

						&.resolved {
							color: #4ecb73;
							font-weight: 500;
						}
					}
				}
			}

			.action-btn {
				display: flex;
				align-items: center;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				background: rgba($primary-color, 0.1);
				border: 1rpx solid rgba($primary-color, 0.2);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.95);
					background: rgba($primary-color, 0.15);
				}

				text {
					font-size: 24rpx;
					color: $primary-color;
					font-weight: 500;
					margin-right: 6rpx;
				}
			}
		}
	}
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;

	.empty-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba($primary-color, 0.05);
		border-radius: 50%;
		border: 2rpx dashed rgba($primary-color, 0.2);
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		font-weight: 600;
		margin-bottom: 12rpx;
	}

	.empty-desc {
		font-size: 26rpx;
		color: #999;
		text-align: center;
		line-height: 1.5;
	}
}
</style>
