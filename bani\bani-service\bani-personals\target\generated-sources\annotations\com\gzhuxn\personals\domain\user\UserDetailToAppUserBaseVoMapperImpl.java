package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.AppUserBaseVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAppUserBaseVoMapperImpl implements UserDetailToAppUserBaseVoMapper {

    @Override
    public AppUserBaseVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserBaseVo appUserBaseVo = new AppUserBaseVo();

        appUserBaseVo.setUserId( arg0.getUserId() );
        appUserBaseVo.setNickName( arg0.getNickName() );
        appUserBaseVo.setSex( arg0.getSex() );
        appUserBaseVo.setPhoneNumber( arg0.getPhoneNumber() );
        appUserBaseVo.setBirthday( arg0.getBirthday() );
        appUserBaseVo.setHeight( arg0.getHeight() );
        appUserBaseVo.setWeight( arg0.getWeight() );
        appUserBaseVo.setEdu( arg0.getEdu() );
        appUserBaseVo.setJob( arg0.getJob() );
        appUserBaseVo.setAffectiveStatus( arg0.getAffectiveStatus() );
        appUserBaseVo.setRevenue( arg0.getRevenue() );
        appUserBaseVo.setWechat( arg0.getWechat() );
        appUserBaseVo.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        appUserBaseVo.setAddrCityCode( arg0.getAddrCityCode() );
        appUserBaseVo.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        appUserBaseVo.setAddrStreetCode( arg0.getAddrStreetCode() );
        appUserBaseVo.setAddr( arg0.getAddr() );
        appUserBaseVo.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        appUserBaseVo.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        appUserBaseVo.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        appUserBaseVo.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        appUserBaseVo.setAddrNew( arg0.getAddrNew() );

        return appUserBaseVo;
    }

    @Override
    public AppUserBaseVo convert(UserDetail arg0, AppUserBaseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setAffectiveStatus( arg0.getAffectiveStatus() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        arg1.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        arg1.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        arg1.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        arg1.setAddrNew( arg0.getAddrNew() );

        return arg1;
    }
}
