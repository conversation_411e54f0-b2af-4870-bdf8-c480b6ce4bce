<template>
	<!-- 自定义导航栏 -->
	<scroll-nav-page :enableScrollGradient="false" @scroll="handlePageScroll" @heightChange="handleNavHeightChange">
		<template v-if="$store.isUserShort()" #nav-center>
			<text class="nav-title">消息中心</text>
		</template>
		<template v-else #nav-left>
			<nav-tabs v-model="currentTab" :tabs="messageTabs" :text-color="getNavTextColor()"
				@change="handleTabChange" />
		</template>
		<template #content>
			<!-- 未注册 -->
			<unregistered-user v-if="$store.isUserShort()"></unregistered-user>
			<!-- 已注册 -->
			<view v-else class="main-container">
				<!-- 消息标签页内容 -->
				<view v-if="currentTab === 'message'" class="message-list">
					<!-- 加载状态 -->
					<view v-if="loading" class="loading-container">
						<view class="loading-spinner"></view>
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 消息内容 -->
					<template v-else>
						<!-- 新关注的 -->
						<view class="message-item system-message" @click="navigateTo('/pagesubs/message/follow')">
							<view class="avatar-container">
								<view class="avatar system-avatar follow-avatar">
									<uni-icons type="staff-filled" size="32" color="#ffffff"
										class="avatar-icon"></uni-icons>
								</view>
								<view class="badge" v-if="followCount > 0">{{ followCount }}</view>
							</view>
							<view class="content">
								<view class="top-row">
									<text class="title">新关注的</text>
									<view class="time-badge-container">
										<text class="time">{{ followMessage.time }}</text>
									</view>
								</view>
								<text class="subtitle">{{ followMessage.content }}</text>
							</view>
						</view>

						<!-- 互动消息 -->
						<view class="message-item system-message" @click="navigateTo('/pagesubs/message/interaction')">
							<view class="avatar-container">
								<view class="avatar system-avatar interaction-avatar">
									<uni-icons type="notification-filled" size="32" color="#ffffff"
										class="avatar-icon"></uni-icons>
								</view>
								<view class="badge" v-if="interactionCount > 0">{{ interactionCount }}</view>
							</view>
							<view class="content">
								<view class="top-row">
									<text class="title">互动消息</text>
									<view class="time-badge-container">
										<text class="time">{{ interactionMessage.time }}</text>
									</view>
								</view>
								<text class="subtitle">{{ interactionMessage.content }}</text>
							</view>
						</view>

						<!-- 系统消息 -->
						<view class="message-item system-message" @click="navigateTo('/pagesubs/message/system')">
							<view class="avatar-container">
								<view class="avatar system-avatar system-avatar-red">
									<uni-icons type="gear" size="32" color="#ffffff" class="avatar-icon"></uni-icons>
								</view>
								<view class="badge" v-if="systemCount > 0">{{ systemCount }}</view>
							</view>
							<view class="content">
								<view class="top-row">
									<text class="title">系统消息</text>
									<view class="time-badge-container">
										<text class="time">{{ systemMessage.time }}</text>
									</view>
								</view>
								<text class="subtitle">{{ systemMessage.content }}</text>
							</view>
						</view>

						<!-- 用户消息列表 -->
						<view class="message-item user-message" v-for="(item, index) in userMessages" :key="index"
							@click="navigateToChat(item)">
							<view class="avatar-container">
								<image class="avatar" :src="item.avatar" mode="aspectFill"></image>
								<view class="badge" v-if="item.unreadCount > 0">{{ item.unreadCount }}</view>
							</view>
							<view class="content">
								<view class="top-row">
									<text class="title">{{ item.name }}</text>
									<view class="time-badge-container">
										<text class="time">{{ item.time }}</text>
									</view>
								</view>
								<text class="subtitle">{{ item.lastMessage }}</text>
							</view>
						</view>

						<!-- 空状态 -->
						<view class="empty-state" v-if="userMessages.length === 0">
							<text class="empty-text">暂时没有更多了</text>
						</view>
					</template>
				</view>

				<!-- 好友标签页内容 -->
				<view v-else-if="currentTab === 'friend'" class="friend-list message-list">
					<follow-user :navBarHeight="navBarHeight" :initial-category="currentFriendCategory"
						@userClick="handleUserClick" @userAction="handleUserAction"
						@categoryChange="handleCategoryChange">
					</follow-user>
				</view>
			</view>
			<!-- 关注订阅号组件 -->
			<mp-subscribe :navBarHeight="navBarHeight" />
		</template>
	</scroll-nav-page>
</template>

<script setup>
import {
	ref,
	computed,
	onMounted
} from 'vue'
import { onPageScroll, onShow } from '@dcloudio/uni-app'
import { getMessageGroup } from '@/api/message/message'

import $store from '@/store'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)

// 当前标签页
const currentTab = ref('message')
// 当前好友分类
const currentFriendCategory = ref('following')

// 消息页面标签数据
const messageTabs = ref([
	{ label: '消息', value: 'message' },
	{ label: '好友', value: 'friend' }
])

// 消息计数
const followCount = ref(0)
const interactionCount = ref(0)
const systemCount = ref(0)

// 系统消息内容和时间
const followMessage = ref({
	content: '没有新通知',
	time: null,
	unreadNum: 0
})
const interactionMessage = ref({
	content: '暂无互动消息',
	time: null,
	unreadNum: 0
})
const systemMessage = ref({
	content: '暂无系统消息',
	time: null,
	unreadNum: 0
})

// 加载状态
const loading = ref(false)

// 用户消息列表
const userMessages = ref([])

// 好友列表数据
// 加载消息数据
const loadMessageData = () => {
	if (loading.value) return
	getMessageGroup().then(response => {
		const messageData = response.data
		console.log('消息数据详情:', messageData)

		// 更新系统消息计数和内容
		if (messageData.follow) {
			console.log('关注消息数据:', messageData.follow)
			followCount.value = messageData.follow.unreadNum || 0
			followMessage.value = {
				content: messageData.follow.content || '没有新通知',
				time: messageData.follow.time,
				unreadNum: messageData.follow.unreadNum
			}
			console.log('处理后的关注消息:', followMessage.value)
		}
		if (messageData.related) {
			console.log('互动消息数据:', messageData.related)
			interactionCount.value = messageData.related.unreadNum || 0
			interactionMessage.value = {
				content: messageData.related.content || '暂无互动消息',
				time: messageData.related.time,
				unreadNum: messageData.related.unreadNum
			}
			console.log('处理后的互动消息:', interactionMessage.value)
		}
		if (messageData.system) {
			console.log('系统消息数据:', messageData.system)
			systemCount.value = messageData.system.unreadNum || 0
			systemMessage.value = {
				content: messageData.system.content || '暂无系统消息',
				time: messageData.system.time,
				unreadNum: messageData.system.unreadNum
			}
			console.log('处理后的系统消息:', systemMessage.value)
		}

		// 更新用户消息列表
		if (messageData.uMsgList && Array.isArray(messageData.uMsgList)) {
			console.log('用户消息列表原始数据:', messageData.uMsgList)

			userMessages.value = messageData.uMsgList.map(item => {
				console.log('处理消息项:', {
					uid: item.uid,
					uName: item.uName,
					content: item.content,
					time: item.time,
					type: item.type
				})

				return {
					id: item.uid,
					name: item.uName,
					avatar: item.uAvatar,
					time: item.time,
					lastMessage: item.content,
					unreadCount: item.unreadNum || 0,
					groupId: item.groupId,
					sendUserId: item.sendUserId,
					type: item.type,
					subType: item.subType,
					contentId: item.contentId,
					read: item.read
				}
			})

			console.log('处理后的用户消息列表:', userMessages.value)
		}

		console.log('消息数据加载成功，用户消息数量:', userMessages.value.length)
	}).finally(() => {

		loading.value = false
	})
}

// 页面跳转
const navigateTo = (url) => {
	uni.navigateTo({
		url
	})
}

// 跳转到聊天页面
const navigateToChat = (item) => {
	uni.navigateTo({
		url: `/pages/message/chat/chat?id=${item.id}&name=${item.name}`
	})
}

// 处理页面滚动
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
}

// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}

// 标签切换处理
const handleTabChange = (tab) => {
	currentTab.value = tab
}

// follow-user组件事件处理
const handleUserClick = (user) => {
	console.log('点击用户:', user)
	// 跳转到用户详情页
	uni.navigateTo({
		url: `/pages/user/profile?id=${user.id}`
	})
}

const handleUserAction = (user) => {
	console.log('用户操作:', user)
	// 根据关系类型执行不同操作
	switch (user.relationship) {
		case 'mutual':
			// 互关用户，可以发消息
			uni.navigateTo({
				url: `/pages/message/chat/chat?id=${user.id}&name=${user.name}`
			})
			break
		case 'following':
			// 已关注用户，可以取消关注
			uni.showModal({
				title: '确认',
				content: `确定要取消关注 ${user.name} 吗？`,
				success: (res) => {
					if (res.confirm) {
						// TODO: 调用取消关注API
						uni.showToast({
							title: '已取消关注',
							icon: 'success'
						})
					}
				}
			})
			break
		case 'followers':
			// 粉丝，可以回关
			uni.showModal({
				title: '确认',
				content: `确定要关注 ${user.name} 吗？`,
				success: (res) => {
					if (res.confirm) {
						// TODO: 调用关注API
						uni.showToast({
							title: '关注成功',
							icon: 'success'
						})
					}
				}
			})
			break
	}
}

const handleCategoryChange = (category) => {
	console.log('分类变化:', category)
	currentFriendCategory.value = category
}


// 页面滚动监听
onPageScroll(handlePageScroll)

// 页面加载时获取数据
onMounted(() => {
	
})

// 页面显示时重新加载数据（tabBar重复点击时会触发）
onShow(() => {
	console.log('消息页面显示，重新加载数据')
	if (!$store.isUserShort()) {
		loadMessageData()
	}
})
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			$primary-lighter 0%,
			rgba(105, 108, 243, 0.03) 30%,
			rgba(105, 108, 243, 0.01) 60%,
			$bg-primary 100%);
}

// nav-tabs 样式已移至独立组件中

// 好友页面样式
.friend-list {
	padding: $spacing-md;
	box-sizing: border-box;

	.friend-category {
		background: $bg-primary;
		border-radius: $radius-md;
		padding: $spacing-md $spacing-lg;
		margin-bottom: $spacing-md;
		box-shadow: $shadow-md;
		backdrop-filter: blur(10rpx);
		border: 1px solid $border-color-light;
		display: flex;
		justify-content: space-around;

		.category-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: $spacing-md $spacing-sm;
			border-radius: $radius-sm;
			transition: all 0.3s ease;
			cursor: pointer;

			text {
				font-size: $font-size-sm;
				color: $text-secondary;
				font-weight: $font-weight-medium;
				margin-bottom: $spacing-xs;
			}

			.count {
				background: $primary-light;
				color: $primary-color;
				font-size: $font-size-xs;
				font-weight: $font-weight-bold;
				padding: 4rpx $spacing-sm;
				border-radius: $radius-full;
				min-width: 40rpx;
				text-align: center;
			}

			&.active {
				background: $primary-lighter;

				text {
					color: $primary-color;
					font-weight: $font-weight-bold;
				}

				.count {
					background: $primary-color;
					color: $text-white;
				}
			}

			&:hover {
				background: $primary-lighter;
			}
		}
	}

	.user-list {
		.user-item {
			background: $bg-primary;
			border-radius: $radius-md;
			padding: $spacing-md $spacing-lg;
			margin-bottom: $spacing-md;
			box-shadow: $shadow-md;
			backdrop-filter: blur(10rpx);
			border: 1px solid $border-color-light;
			display: flex;
			align-items: center;
			transition: all 0.3s ease;

			&:hover {
				background: $primary-lighter;
				transform: translateX(4rpx);
			}

			.avatar {
				width: 88rpx;
				height: 88rpx;
				border-radius: $radius-full;
				margin-right: $spacing-lg;
				box-shadow: $shadow-sm;
				border: 2rpx solid $border-color-light;
			}

			.user-info {
				flex: 1;
				min-width: 0;

				.top {
					display: flex;
					align-items: center;
					margin-bottom: $spacing-xs;

					.name {
						font-size: $title-size-md;
						color: $text-primary;
						font-weight: $font-weight-bold;
						margin-right: $spacing-sm;
					}

					.status {
						background: $success-color;
						color: $text-white;
						font-size: $font-size-xs;
						padding: 2rpx $spacing-xs;
						border-radius: $radius-sm;
					}
				}

				.bio {
					font-size: $font-size-xs;
					color: $text-secondary;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.action-btn {
				background: $primary-gradient;
				color: $text-white;
				font-size: $font-size-xs;
				font-weight: $font-weight-medium;
				padding: $spacing-sm $spacing-lg;
				border-radius: $radius-lg;
				box-shadow: $primary-shadow;
				transition: all 0.3s ease;

				&:hover {
					transform: scale(1.05);
					box-shadow: 0 6rpx 16rpx $primary-shadow;
				}

				&:active {
					transform: scale(0.95);
				}
			}
		}
	}
}

.message-list {
	padding: 0;
	background: $bg-primary;

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;

		.loading-spinner {
			width: 60rpx;
			height: 60rpx;
			border: 4rpx solid #f3f3f3;
			border-top: 4rpx solid $primary-color;
			border-radius: 50%;
			animation: spin 1s linear infinite;
			margin-bottom: 24rpx;
		}

		.loading-text {
			font-size: $font-size-md;
			color: #666;
		}
	}

	.message-item {
		display: flex;
		align-items: center;
		padding: $spacing-lg $spacing-xl;
		border-bottom: 1rpx solid $bg-secondary;
		position: relative;
		transition: all 0.3s ease;

		&:active {
			background-color: $primary-lighter;
		}

		.avatar-container {
			position: relative;
			margin-right: $spacing-lg;

			.avatar {
				width: 96rpx;
				height: 96rpx;
				border-radius: $radius-full;
				background: $bg-secondary;
				box-shadow: $shadow-sm;
			}

			.badge {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				background: $error-color;
				color: $text-white;
				font-size: $font-size-xs;
				font-weight: $font-weight-medium;
				padding: 2rpx $spacing-xs;
				border-radius: $radius-full;
				min-width: 28rpx;
				height: 28rpx;
				text-align: center;
				line-height: 24rpx;
				box-shadow: $shadow-sm;
				z-index: 10;
			}
		}

		.content {
			flex: 1;
			min-width: 0;

			.top-row {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 8rpx;

				.title {
					font-size: $title-size-md;
					font-weight: $font-weight-medium;
					color: $text-primary;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					max-width: 400rpx;
					flex: 1;
				}

				.time-badge-container {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					flex-shrink: 0;
					margin-left: $spacing-md;

					.time {
						font-size: $font-size-xs;
						color: $text-tertiary;
					}
				}
			}

			.subtitle {
				font-size: $font-size-sm;
				color: $text-secondary;
				line-height: 1.4;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-bottom: 0;
			}

			.status-tags {
				display: flex;
				gap: $spacing-xs;

				.tag {
					font-size: $font-size-xs;
					color: $text-tertiary;
					background: $bg-secondary;
					padding: 4rpx $spacing-xs;
					border-radius: $radius-sm;
					border: 1rpx solid $border-color-light;
				}
			}
		}



		// 系统消息特殊样式
		&.system-message {
			.avatar-container .avatar {
				background: $primary-gradient;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: $shadow-md;

				&.system-avatar {
					.avatar-icon {
						display: flex;
						align-items: center;
						justify-content: center;
					}

					&.follow-avatar {
						background: $primary-gradient;
					}

					&.interaction-avatar {
						background: $primary-gradient;
					}

					&.system-avatar-red {
						background: $primary-gradient;
					}
				}
			}
		}

		// 用户消息特殊样式
		&.user-message {
			.avatar-container .avatar {
				border: 2rpx solid $border-color-light;
			}
		}
	}

	.empty-state {
		padding: 120rpx $spacing-xl;
		text-align: center;

		.empty-text {
			font-size: $font-size-sm;
			color: $text-tertiary;
		}
	}
}

// 响应式设计 - 针对不同屏幕尺寸优化
@media screen and (max-width: 750rpx) {
	.message-list {
		padding: 16rpx;

		.message-section {
			padding: 16rpx 20rpx;
			margin-bottom: 16rpx;

			.section-header .title {
				font-size: 32rpx;
			}

			.message-item {
				padding: 14rpx 8rpx;

				.avatar {
					width: 68rpx;
					height: 68rpx;
					margin-right: 16rpx;
				}

				.content {
					.top .name {
						font-size: 28rpx;
					}

					.message {
						font-size: 26rpx;
					}
				}
			}
		}
	}
}

// 深色模式适配（预留）
@media (prefers-color-scheme: dark) {
	.page-container {
		background: linear-gradient(135deg,
				rgba(105, 108, 243, 0.15) 0%,
				rgba(105, 108, 243, 0.10) 30%,
				rgba(105, 108, 243, 0.05) 60%,
				rgba(30, 30, 30, 1) 100%);
	}

	.message-list .message-section {
		background: rgba(40, 40, 40, 0.95);
		border: 1px solid rgba(255, 255, 255, 0.1);

		.section-header .title {
			color: #fff;
		}

		.message-item {
			border-bottom-color: rgba(255, 255, 255, 0.1);

			&:hover {
				background: rgba(105, 108, 243, 0.1);
			}

			.content {
				.top .name {
					color: #fff;
				}

				.message {
					color: #ccc;
				}
			}
		}
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>