"use strict";const e=require("../../../common/vendor.js"),s=require("../../../utils/common.js"),n=require("../../../api/my/config.js");Array||e.resolveComponent("uni-icons")();const y=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";Math||(y+u)();const u=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",f={__name:"privacy",setup(l){const c=e.ref(0);e.ref(!1);const o=e.reactive({privacy_online_invisible:!1,privacy_profile_visibility:0,privacy_location_show:!0}),t=["所有人","关注我的","互相关注","仅自己"],r=i=>{c.value=i},_=i=>{o.privacy_online_invisible=i.detail.value,s.toast(i.detail.value?"已开启隐身模式":"已关闭隐身模式"),n.updateUserConfig({configKey:"privacy_online_invisible",val:i.detail.value?1:0})},v=i=>{o.privacy_profile_visibility=i.detail.value,s.toast(`资料可见性已设置为：${t[i.detail.value]}`),n.updateUserConfig({configKey:"privacy_profile_visibility",val:i.detail.value})},p=i=>{o.privacy_location_show=i.detail.value,s.toast(i.detail.value?"已开启位置显示":"已关闭位置显示"),n.updateUserConfig({configKey:"privacy_location_show",val:i.detail.value?1:0})};return e.onLoad(()=>{n.getUserConfigMap(3).then(i=>{if(i.data)for(let a in i.data)o[a]instanceof Boolean||typeof o[a]=="boolean"?o[a]=i.data[a]==="1":o[a]=i.data[a]})}),(i,a)=>({a:e.p({type:"eye-slash",size:"20",color:"#696CF3"}),b:o.privacy_online_invisible,c:e.o(_),d:e.p({type:"person",size:"20",color:"#696CF3"}),e:e.t(t[o.privacy_profile_visibility]),f:e.o(v),g:t,h:o.privacy_profile_visibility,i:e.p({type:"location",size:"20",color:"#696CF3"}),j:o.privacy_location_show,k:e.o(p),l:e.o(r),m:e.p({title:"隐私设置","show-back":!0})})}},d=e._export_sfc(f,[["__scopeId","data-v-7d1e5486"]]);wx.createPage(d);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/setting/privacy.js.map
