package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.vo.AppActivityPageVo;
import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppActivityPageVoMapperImpl implements ActivityToAppActivityPageVoMapper {

    @Override
    public AppActivityPageVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppActivityPageVo appActivityPageVo = new AppActivityPageVo();

        appActivityPageVo.setId( arg0.getId() );
        appActivityPageVo.setGroupId( arg0.getGroupId() );
        appActivityPageVo.setName( arg0.getName() );
        appActivityPageVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        appActivityPageVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            appActivityPageVo.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        appActivityPageVo.setEndTime( arg0.getEndTime() );
        appActivityPageVo.setTimeLength( arg0.getTimeLength() );
        appActivityPageVo.setRefundTime( arg0.getRefundTime() );
        appActivityPageVo.setOfficialFlag( arg0.getOfficialFlag() );
        appActivityPageVo.setIntroduce( arg0.getIntroduce() );
        appActivityPageVo.setOriginalAmount( arg0.getOriginalAmount() );
        appActivityPageVo.setAmount( arg0.getAmount() );
        appActivityPageVo.setStatus( arg0.getStatus() );
        appActivityPageVo.setAuditStatus( arg0.getAuditStatus() );
        appActivityPageVo.setType( arg0.getType() );
        appActivityPageVo.setLon( arg0.getLon() );
        appActivityPageVo.setLat( arg0.getLat() );
        appActivityPageVo.setCreateByName( arg0.getCreateByName() );

        return appActivityPageVo;
    }

    @Override
    public AppActivityPageVo convert(Activity arg0, AppActivityPageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            arg1.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setStartTime( null );
        }
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
