package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailUserVo;
import com.gzhuxn.personals.domain.message.bo.MsgGroupUserBoToMsgGroupUserMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupUserBoToMsgGroupUserMapper.class,MsgGroupUserToMsgGroupUserVoMapper.class,MsgGroupUserToAppMsgGroupUserVoMapper.class},
    imports = {}
)
public interface MsgGroupUserToAppMsgGroupDetailUserVoMapper extends BaseMapper<MsgGroupUser, AppMsgGroupDetailUserVo> {
  @Mapping(
      target = "uid",
      source = "userId"
  )
  AppMsgGroupDetailUserVo convert(MsgGroupUser source);

  @Mapping(
      target = "uid",
      source = "userId"
  )
  AppMsgGroupDetailUserVo convert(MsgGroupUser source,
      @MappingTarget AppMsgGroupDetailUserVo target);
}
