package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserLike;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserLikeCreateBoToUserLikeMapperImpl implements AppUserLikeCreateBoToUserLikeMapper {

    @Override
    public UserLike convert(AppUserLikeCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLike userLike = new UserLike();

        userLike.setType( arg0.getType() );
        userLike.setBusinessId( arg0.getBusinessId() );

        return userLike;
    }

    @Override
    public UserLike convert(AppUserLikeCreateBo arg0, UserLike arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
