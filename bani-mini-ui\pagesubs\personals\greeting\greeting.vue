<template>
	<!-- 自定义导航栏 -->
	<scroll-nav-page title="打招呼" :show-back="true">
		<template #content>
			<view class="greeting-container">
				<!-- 背景装饰 -->
				<view class="background-decoration">
					<!-- Hi 气泡 -->
					<view class="hi-bubble">
						<text class="hi-text">Hi</text>
					</view>
				</view>

				<!-- 用户信息区域 -->
				<view class="user-info-section">
					<view class="greeting-title">
						<uni-icons type="hand" size="20" color="#696CF3" />
						<text class="title-text">打招呼</text>
					</view>
					<text class="greeting-subtitle">私信对方，让TA马上看到你</text>
				</view>

				<!-- 输入区域 -->
				<view class="input-section">
					<view class="input-container">
						<textarea 
							v-model="greetingContent" 
							class="greeting-input" 
							placeholder="嗨，想和你认识一下～"
							:maxlength="60"
							auto-height
						/>
						<view class="char-count">{{ greetingContent.length }}/60</view>
					</view>
				</view>

				<!-- 底部按钮区域 -->
				<view class="bottom-actions">
					<view class="action-btn secondary-btn" @click="goBack">
						<text class="btn-text">再想想</text>
					</view>
					<view class="action-btn primary-btn" @click="sendGreeting" :class="{ 'disabled': !canSend }">
						<text class="btn-text">发送</text>
					</view>
				</view>

				<!-- VIP 提示 -->
				<view class="vip-tip">
					<uni-icons type="vip-filled" size="16" color="#FFD700" />
					<text class="vip-text">开通会员每天免费打招呼10次 ></text>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import { sendUserGreeting, checkUserGreeted } from '@/api/user/greeting'

// 页面参数
let oppositeUserId = ref(null)
let oppositeUserInfo = ref({})

// 打招呼内容
const greetingContent = ref('嗨，想和你认识一下～')

// 是否可以发送
const canSend = computed(() => {
	return greetingContent.value.trim().length > 0 && greetingContent.value.trim().length <= 60
})

// 页面加载时获取参数
onLoad((options) => {
	console.log('打招呼页面参数:', options)
	if (options && options.userId) {
		oppositeUserId.value = options.userId
		oppositeUserInfo.value = {
			userId: options.userId,
			nickName: options.nickName || '用户',
			avatar: options.avatar || ''
		}
		checkIfAlreadyGreeted()
	}
})

// 检查是否已经打过招呼
const checkIfAlreadyGreeted = async () => {
	try {
		const response = await checkUserGreeted(oppositeUserId.value)
		if (response.data) {
			uni.showModal({
				title: '提示',
				content: '您已经向该用户打过招呼了，请等待对方回复',
				showCancel: false,
				success: () => {
					uni.navigateBack()
				}
			})
		}
	} catch (error) {
		console.error('检查打招呼状态失败:', error)
	}
}

// 发送打招呼
const sendGreeting = async () => {
	if (!canSend.value) {
		uni.showToast({
			title: '请输入打招呼内容',
			icon: 'none'
		})
		return
	}

	if (!oppositeUserId.value) {
		uni.showToast({
			title: '用户信息错误',
			icon: 'none'
		})
		return
	}

	try {
		uni.showLoading({
			title: '发送中...'
		})

		await sendUserGreeting({
			oppositeUserId: oppositeUserId.value,
			content: greetingContent.value.trim()
		})

		uni.hideLoading()
		uni.showToast({
			title: '打招呼发送成功',
			icon: 'success'
		})

		// 延迟返回上一页
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)

	} catch (error) {
		uni.hideLoading()
		console.error('发送打招呼失败:', error)
		uni.showToast({
			title: error.message || '发送失败，请重试',
			icon: 'none'
		})
	}
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
// 引入uni.scss变量
@import '@/uni.scss';

.greeting-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #E3F2FD 0%, #BBDEFB 100%);
	padding: 40rpx 32rpx;
	position: relative;
	display: flex;
	flex-direction: column;
}

.background-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 400rpx;
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	padding: 80rpx 60rpx 0 0;
}

.hi-bubble {
	width: 120rpx;
	height: 120rpx;
	background: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.2);
}

.hi-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #696CF3;
}

.user-info-section {
	margin-top: 200rpx;
	margin-bottom: 60rpx;
	z-index: 2;
}

.greeting-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 16rpx;
}

.title-text {
	font-size: 48rpx;
	font-weight: 600;
	color: #333;
}

.greeting-subtitle {
	font-size: 28rpx;
	color: #666;
	margin-left: 32rpx;
}

.input-section {
	margin-bottom: 40rpx;
}

.input-container {
	background: white;
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	position: relative;
}

.greeting-input {
	width: 100%;
	min-height: 200rpx;
	font-size: 32rpx;
	color: #333;
	line-height: 1.6;
	border: none;
	outline: none;
	resize: none;
}

.char-count {
	position: absolute;
	bottom: 16rpx;
	right: 24rpx;
	font-size: 24rpx;
	color: #999;
}

.bottom-actions {
	display: flex;
	gap: 24rpx;
	margin-bottom: 40rpx;
}

.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
}

.secondary-btn {
	background: white;
	border: 2rpx solid #E0E0E0;
}

.secondary-btn .btn-text {
	color: #666;
	font-size: 32rpx;
	font-weight: 500;
}

.primary-btn {
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
	box-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.3);
}

.primary-btn .btn-text {
	color: white;
	font-size: 32rpx;
	font-weight: 600;
}

.primary-btn.disabled {
	background: #CCCCCC;
	box-shadow: none;
}

.vip-tip {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 16rpx;
}

.vip-text {
	font-size: 24rpx;
	color: #999;
}
</style>
