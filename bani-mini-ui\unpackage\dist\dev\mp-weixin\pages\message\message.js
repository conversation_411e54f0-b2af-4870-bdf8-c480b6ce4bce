"use strict";const e=require("../../common/vendor.js"),j=require("../../api/message/message.js"),u=require("../../store/index.js");if(!Array){const y=e.resolveComponent("nav-tabs"),r=e.resolveComponent("unregistered-user"),l=e.resolveComponent("uni-icons"),o=e.resolveComponent("follow-user"),c=e.resolveComponent("mp-subscribe"),p=e.resolveComponent("scroll-nav-page");(y+r+l+o+c+p)()}const k=()=>"../../components/nav-tabs/nav-tabs.js",A=()=>"../../components/unregistered-user/unregistered-user.js",H=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",P=()=>"../../components/follow-user/follow-user.js",q=()=>"../../components/mp-subscribe/mp-subscribe.js",z=()=>"../../components/scroll-nav-page/scroll-nav-page.js";Math||(k+A+H+P+q+z)();const w={__name:"message",setup(y){const r=e.ref(0),l=e.ref(0),o=e.ref("message"),c=e.ref("following"),p=e.ref([{label:"消息",value:"message"},{label:"好友",value:"friend"}]),g=e.ref(0),i=e.ref(0),_=e.ref(0),m=e.ref({content:"没有新通知",time:null,unreadNum:0}),f=e.ref({content:"暂无互动消息",time:null,unreadNum:0}),v=e.ref({content:"暂无系统消息",time:null,unreadNum:0}),d=e.ref(!1),t=e.ref([]),C=()=>{d.value||j.getMessageGroup().then(n=>{const a=n.data;e.index.__f__("log","at pages/message/message.vue:185","消息数据详情:",a),a.follow&&(e.index.__f__("log","at pages/message/message.vue:189","关注消息数据:",a.follow),g.value=a.follow.unreadNum||0,m.value={content:a.follow.content||"没有新通知",time:a.follow.time,unreadNum:a.follow.unreadNum},e.index.__f__("log","at pages/message/message.vue:196","处理后的关注消息:",m.value)),a.related&&(e.index.__f__("log","at pages/message/message.vue:199","互动消息数据:",a.related),i.value=a.related.unreadNum||0,f.value={content:a.related.content||"暂无互动消息",time:a.related.time,unreadNum:a.related.unreadNum},e.index.__f__("log","at pages/message/message.vue:206","处理后的互动消息:",f.value)),a.system&&(e.index.__f__("log","at pages/message/message.vue:209","系统消息数据:",a.system),_.value=a.system.unreadNum||0,v.value={content:a.system.content||"暂无系统消息",time:a.system.time,unreadNum:a.system.unreadNum},e.index.__f__("log","at pages/message/message.vue:216","处理后的系统消息:",v.value)),a.uMsgList&&Array.isArray(a.uMsgList)&&(e.index.__f__("log","at pages/message/message.vue:221","用户消息列表原始数据:",a.uMsgList),t.value=a.uMsgList.map(s=>(e.index.__f__("log","at pages/message/message.vue:224","处理消息项:",{uid:s.uid,uName:s.uName,content:s.content,time:s.time,type:s.type}),{id:s.uid,name:s.uName,avatar:s.uAvatar,time:s.time,lastMessage:s.content,unreadCount:s.unreadNum||0,groupId:s.groupId,sendUserId:s.sendUserId,type:s.type,subType:s.subType,contentId:s.contentId,read:s.read})),e.index.__f__("log","at pages/message/message.vue:248","处理后的用户消息列表:",t.value)),e.index.__f__("log","at pages/message/message.vue:251","消息数据加载成功，用户消息数量:",t.value.length)}).finally(()=>{d.value=!1})},h=n=>{e.index.navigateTo({url:n})},M=n=>{e.index.navigateTo({url:`/pages/message/chat/chat?id=${n.id}&name=${n.name}`})},b=n=>{r.value=n.scrollTop},N=n=>{l.value=n},T=()=>Math.min(r.value/100,1)>.5?"#333333":"#ffffff",$=n=>{o.value=n},S=n=>{e.index.__f__("log","at pages/message/message.vue:295","点击用户:",n),e.index.navigateTo({url:`/pages/user/profile?id=${n.id}`})},U=n=>{switch(e.index.__f__("log","at pages/message/message.vue:303","用户操作:",n),n.relationship){case"mutual":e.index.navigateTo({url:`/pages/message/chat/chat?id=${n.id}&name=${n.name}`});break;case"following":e.index.showModal({title:"确认",content:`确定要取消关注 ${n.name} 吗？`,success:a=>{a.confirm&&e.index.showToast({title:"已取消关注",icon:"success"})}});break;case"followers":e.index.showModal({title:"确认",content:`确定要关注 ${n.name} 吗？`,success:a=>{a.confirm&&e.index.showToast({title:"关注成功",icon:"success"})}});break}},I=n=>{e.index.__f__("log","at pages/message/message.vue:348","分类变化:",n),c.value=n};return e.onPageScroll(b),e.onMounted(()=>{}),e.onShow(()=>{e.index.__f__("log","at pages/message/message.vue:363","消息页面显示，重新加载数据"),u.$store.isUserShort()||C()}),(n,a)=>e.e({a:e.unref(u.$store).isUserShort()},e.unref(u.$store).isUserShort()?{}:{b:e.o($),c:e.o(s=>o.value=s),d:e.p({tabs:p.value,"text-color":T(),modelValue:o.value})},{e:e.unref(u.$store).isUserShort()},e.unref(u.$store).isUserShort()?{}:e.e({f:o.value==="message"},o.value==="message"?e.e({g:d.value},d.value?{}:e.e({h:e.p({type:"staff-filled",size:"32",color:"#ffffff"}),i:g.value>0},g.value>0?{j:e.t(g.value)}:{},{k:e.t(m.value.time),l:e.t(m.value.content),m:e.o(s=>h("/pagesubs/message/follow")),n:e.p({type:"notification-filled",size:"32",color:"#ffffff"}),o:i.value>0},i.value>0?{p:e.t(i.value)}:{},{q:e.t(f.value.time),r:e.t(f.value.content),s:e.o(s=>h("/pagesubs/message/interaction")),t:e.p({type:"gear",size:"32",color:"#ffffff"}),v:_.value>0},_.value>0?{w:e.t(_.value)}:{},{x:e.t(v.value.time),y:e.t(v.value.content),z:e.o(s=>h("/pagesubs/message/system")),A:e.f(t.value,(s,x,L)=>e.e({a:s.avatar,b:s.unreadCount>0},s.unreadCount>0?{c:e.t(s.unreadCount)}:{},{d:e.t(s.name),e:e.t(s.time),f:e.t(s.lastMessage),g:x,h:e.o(D=>M(s),x)})),B:t.value.length===0},t.value.length===0?{}:{})):o.value==="friend"?{D:e.o(S),E:e.o(U),F:e.o(I),G:e.p({navBarHeight:l.value,"initial-category":c.value})}:{},{C:o.value==="friend"}),{H:e.p({navBarHeight:l.value}),I:e.o(b),J:e.o(N),K:e.p({enableScrollGradient:!1})})}},B=e._export_sfc(w,[["__scopeId","data-v-4c1b26cf"]]);w.__runtimeHooks=1;wx.createPage(B);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/message/message.js.map
