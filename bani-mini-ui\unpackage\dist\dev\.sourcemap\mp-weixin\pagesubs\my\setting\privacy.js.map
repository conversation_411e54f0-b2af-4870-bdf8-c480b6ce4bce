{"version": 3, "file": "privacy.js", "sources": ["pagesubs/my/setting/privacy.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcc2V0dGluZ1xwcml2YWN5LnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"隐私设置\" :show-back=\"true\" @heightChange=\"handleNavHeightChange\">\n\t\t<template #content>\n\t\t\t<!-- 主要内容 -->\n\t\t\t<view class=\"main-container\">\n\t\t\t<!-- 页面描述 -->\n\t\t\t<view class=\"page-description\">\n\t\t\t\t<view class=\"desc-content\">\n\t\t\t\t\t<text>管理您的隐私设置，保护个人信息安全</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 隐私设置列表 -->\n\t\t\t<view class=\"privacy-list\">\n\t\t\t\t<!-- 隐身设置 -->\n\t\t\t\t<view class=\"privacy-section\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"section-title\">在线状态</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"privacy-item\">\n\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t<uni-icons type=\"eye-slash\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t<text class=\"item-title\">隐身模式</text>\n\t\t\t\t\t\t\t\t<text class=\"item-desc\">开启后，其他用户无法看到您的在线状态</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t<switch :checked=\"privacySettings.privacy_online_invisible\" @change=\"handleInvisibleChange\"\n\t\t\t\t\t\t\t\tcolor=\"#696CF3\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 资料可见性 -->\n\t\t\t\t<view class=\"privacy-section\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"section-title\">资料可见性</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"privacy-item\">\n\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t<uni-icons type=\"person\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t<text class=\"item-title\">个人资料</text>\n\t\t\t\t\t\t\t\t<text class=\"item-desc\">设置谁可以查看您的详细资料</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t<picker @change=\"handleProfileVisibilityChange\" :range=\"profileVisibilityOptions\"\n\t\t\t\t\t\t\t\t:value=\"privacySettings.privacy_profile_visibility\">\n\t\t\t\t\t\t\t\t<view class=\"picker-text\">{{\n\t\t\t\t\t\t\t\t\tprofileVisibilityOptions[privacySettings.privacy_profile_visibility] }}</view>\n\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"privacy-item\">\n\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t<text class=\"item-title\">位置信息</text>\n\t\t\t\t\t\t\t\t<text class=\"item-desc\">是否显示您的位置信息</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t<switch :checked=\"privacySettings.privacy_location_show\" @change=\"handleLocationChange\"\n\t\t\t\t\t\t\t\tcolor=\"#696CF3\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, reactive } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { toast } from '@/utils/common'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport globalConfig from '@/config'\nimport { getUserConfigMap, updateUserConfig } from '@/api/my/config'\n\n// 页面状态\nconst navBarHeight = ref(0)\nconst saving = ref(false)\n\n// 隐私设置数据\nconst privacySettings = reactive({\n\tprivacy_online_invisible: false,        // 隐身模式\n\tprivacy_profile_visibility: 0,      // 资料可见性 0-所有人 1-仅好友 2-仅自己\n\tprivacy_location_show: true,        // 显示位置\n})\n\n// 资料可见性选项\nconst profileVisibilityOptions = ['所有人', '关注我的', '互相关注', '仅自己']\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 隐身模式切换\nconst handleInvisibleChange = (e) => {\n\tprivacySettings.privacy_online_invisible = e.detail.value\n\ttoast(e.detail.value ? '已开启隐身模式' : '已关闭隐身模式')\n\tupdateUserConfig({\n\t\tconfigKey: 'privacy_online_invisible',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 资料可见性切换\nconst handleProfileVisibilityChange = (e) => {\n\tprivacySettings.privacy_profile_visibility = e.detail.value\n\ttoast(`资料可见性已设置为：${profileVisibilityOptions[e.detail.value]}`)\n\tupdateUserConfig({\n\t\tconfigKey: 'privacy_profile_visibility',\n\t\tval: e.detail.value\n\t})\n}\n\n// 位置信息切换\nconst handleLocationChange = (e) => {\n\tprivacySettings.privacy_location_show = e.detail.value\n\ttoast(e.detail.value ? '已开启位置显示' : '已关闭位置显示')\n\tupdateUserConfig({\n\t\tconfigKey: 'privacy_location_show',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 页面加载时获取当前设置\nonLoad(() => {\n\tgetUserConfigMap(3).then(result => {\n\t\tif (result.data) {\n\t\t\tfor (let key in result.data) {\n\t\t\t\tif (privacySettings[key] instanceof Boolean || typeof privacySettings[key] === 'boolean') {\n\t\t\t\t\tprivacySettings[key] = result.data[key] === '1' ? true : false\n\t\t\t\t} else {\n\t\t\t\t\tprivacySettings[key] = result.data[key]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t})\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: #f5f5f5;\n}\n\n.main-container {\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx 120rpx;\n}\n\n.page-description {\n\ttext-align: center;\n\tmargin: 20rpx 0 32rpx;\n\tpadding: 20rpx 16rpx;\n\tbackground: rgba($primary-color, 0.03);\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba($primary-color, 0.08);\n\n\t.desc-content {\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n}\n\n.privacy-list {\n\t.privacy-section {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tborder-radius: 20rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tbox-shadow: 0 6rpx 24rpx rgba($primary-color, 0.08);\n\t\tbackdrop-filter: blur(10rpx);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\n\t\t.section-header {\n\t\t\tpadding: 24rpx 30rpx 16rpx;\n\t\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\n\t\t\t.section-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tletter-spacing: 0.5rpx;\n\t\t\t}\n\t\t}\n\n\t\t.privacy-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 24rpx 30rpx;\n\t\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.05);\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tbackground: rgba($primary-color, 0.02);\n\t\t\t}\n\n\t\t\t.item-left {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\t\t\t\tflex: 1;\n\n\t\t\t\t.item-info {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t.item-title {\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-desc {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.item-right {\n\t\t\t\t.picker-text {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\t\tbackground: rgba($primary-color, 0.1);\n\t\t\t\t\tborder-radius: 16rpx;\n\t\t\t\t\tborder: 1rpx solid rgba($primary-color, 0.2);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.save-section {\n\tmargin-top: 40rpx;\n\tpadding: 0 20rpx;\n\n\t.save-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tline-height: 88rpx;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tbox-shadow: 0 6rpx 20rpx rgba($primary-color, 0.2);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.15);\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/setting/privacy.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "navBarHeight", "ref", "privacySettings", "reactive", "profileVisibilityOptions", "handleNavHeightChange", "height", "handleInvisibleChange", "e", "toast", "updateUserConfig", "handleProfileVisibilityChange", "handleLocationChange", "onLoad", "getUserConfigMap", "result", "key", "MiniProgramPage"], "mappings": "6QA+EA,MAAMA,EAAgB,IAAW,sFAKjC,MAAMC,EAAeC,EAAG,IAAC,CAAC,EACXA,EAAG,IAAC,EAAK,EAGxB,MAAMC,EAAkBC,EAAAA,SAAS,CAChC,yBAA0B,GAC1B,2BAA4B,EAC5B,sBAAuB,EACxB,CAAC,EAGKC,EAA2B,CAAC,MAAO,OAAQ,OAAQ,KAAK,EAGxDC,EAAyBC,GAAW,CACzCN,EAAa,MAAQM,CACtB,EAGMC,EAAyBC,GAAM,CACpCN,EAAgB,yBAA2BM,EAAE,OAAO,MACpDC,EAAAA,MAAMD,EAAE,OAAO,MAAQ,UAAY,SAAS,EAC5CE,mBAAiB,CAChB,UAAW,2BACX,IAAKF,EAAE,OAAO,MAAQ,EAAI,CAC5B,CAAE,CACF,EAGMG,EAAiCH,GAAM,CAC5CN,EAAgB,2BAA6BM,EAAE,OAAO,MACtDC,EAAK,MAAC,aAAaL,EAAyBI,EAAE,OAAO,KAAK,CAAC,EAAE,EAC7DE,mBAAiB,CAChB,UAAW,6BACX,IAAKF,EAAE,OAAO,KAChB,CAAE,CACF,EAGMI,EAAwBJ,GAAM,CACnCN,EAAgB,sBAAwBM,EAAE,OAAO,MACjDC,EAAAA,MAAMD,EAAE,OAAO,MAAQ,UAAY,SAAS,EAC5CE,mBAAiB,CAChB,UAAW,wBACX,IAAKF,EAAE,OAAO,MAAQ,EAAI,CAC5B,CAAE,CACF,EAGAK,OAAAA,EAAAA,OAAO,IAAM,CACZC,EAAAA,iBAAiB,CAAC,EAAE,KAAKC,GAAU,CAClC,GAAIA,EAAO,KACV,QAASC,KAAOD,EAAO,KAClBb,EAAgBc,CAAG,YAAa,SAAW,OAAOd,EAAgBc,CAAG,GAAM,UAC9Ed,EAAgBc,CAAG,EAAID,EAAO,KAAKC,CAAG,IAAM,IAE5Cd,EAAgBc,CAAG,EAAID,EAAO,KAAKC,CAAG,CAI3C,CAAE,CACF,CAAC,saChJD,GAAG,WAAWC,CAAe"}