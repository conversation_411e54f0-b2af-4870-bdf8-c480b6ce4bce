package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserMomentTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentTagToUserMomentTagVoMapperImpl implements UserMomentTagToUserMomentTagVoMapper {

    @Override
    public UserMomentTagVo convert(UserMomentTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMomentTagVo userMomentTagVo = new UserMomentTagVo();

        userMomentTagVo.setId( arg0.getId() );
        userMomentTagVo.setMomentId( arg0.getMomentId() );
        if ( arg0.getTagVal() != null ) {
            userMomentTagVo.setTagVal( String.valueOf( arg0.getTagVal() ) );
        }
        userMomentTagVo.setTagValName( arg0.getTagValName() );

        return userMomentTagVo;
    }

    @Override
    public UserMomentTagVo convert(UserMomentTag arg0, UserMomentTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMomentId( arg0.getMomentId() );
        if ( arg0.getTagVal() != null ) {
            arg1.setTagVal( String.valueOf( arg0.getTagVal() ) );
        }
        else {
            arg1.setTagVal( null );
        }
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
