package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.vo.AppDaziActivityDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppDaziActivityDetailVoMapperImpl implements ActivityToAppDaziActivityDetailVoMapper {

    @Override
    public AppDaziActivityDetailVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppDaziActivityDetailVo appDaziActivityDetailVo = new AppDaziActivityDetailVo();

        appDaziActivityDetailVo.setId( arg0.getId() );
        appDaziActivityDetailVo.setGroupId( arg0.getGroupId() );
        appDaziActivityDetailVo.setName( arg0.getName() );
        appDaziActivityDetailVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        appDaziActivityDetailVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        appDaziActivityDetailVo.setStartTime( arg0.getStartTime() );
        appDaziActivityDetailVo.setEndTime( arg0.getEndTime() );
        appDaziActivityDetailVo.setTimeLength( arg0.getTimeLength() );
        appDaziActivityDetailVo.setRefundTime( arg0.getRefundTime() );
        appDaziActivityDetailVo.setOfficialFlag( arg0.getOfficialFlag() );
        appDaziActivityDetailVo.setIntroduce( arg0.getIntroduce() );
        appDaziActivityDetailVo.setOriginalAmount( arg0.getOriginalAmount() );
        appDaziActivityDetailVo.setAmount( arg0.getAmount() );
        appDaziActivityDetailVo.setStatus( arg0.getStatus() );
        appDaziActivityDetailVo.setAuditStatus( arg0.getAuditStatus() );
        appDaziActivityDetailVo.setType( arg0.getType() );
        appDaziActivityDetailVo.setLon( arg0.getLon() );
        appDaziActivityDetailVo.setLat( arg0.getLat() );
        appDaziActivityDetailVo.setCreateByName( arg0.getCreateByName() );

        return appDaziActivityDetailVo;
    }

    @Override
    public AppDaziActivityDetailVo convert(Activity arg0, AppDaziActivityDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
