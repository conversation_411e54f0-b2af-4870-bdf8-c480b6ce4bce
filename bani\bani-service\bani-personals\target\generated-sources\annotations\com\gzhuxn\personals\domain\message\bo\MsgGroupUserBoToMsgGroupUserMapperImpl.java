package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.domain.message.MsgGroupUser;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupUserBoToMsgGroupUserMapperImpl implements MsgGroupUserBoToMsgGroupUserMapper {

    @Override
    public MsgGroupUser convert(MsgGroupUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroupUser msgGroupUser = new MsgGroupUser();

        msgGroupUser.setSearchValue( arg0.getSearchValue() );
        msgGroupUser.setCreateBy( arg0.getCreateBy() );
        msgGroupUser.setCreateTime( arg0.getCreateTime() );
        msgGroupUser.setUpdateBy( arg0.getUpdateBy() );
        msgGroupUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            msgGroupUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        msgGroupUser.setCreateDept( arg0.getCreateDept() );
        msgGroupUser.setUserId( arg0.getUserId() );
        msgGroupUser.setId( arg0.getId() );
        msgGroupUser.setGroupId( arg0.getGroupId() );
        msgGroupUser.setName( arg0.getName() );
        msgGroupUser.setType( arg0.getType() );
        msgGroupUser.setDisturb( arg0.getDisturb() );
        msgGroupUser.setStatus( arg0.getStatus() );

        return msgGroupUser;
    }

    @Override
    public MsgGroupUser convert(MsgGroupUserBo arg0, MsgGroupUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setDisturb( arg0.getDisturb() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
