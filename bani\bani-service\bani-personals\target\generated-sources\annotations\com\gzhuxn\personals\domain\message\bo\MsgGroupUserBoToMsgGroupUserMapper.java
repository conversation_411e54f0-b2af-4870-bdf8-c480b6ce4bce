package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.controller.app.message.bo.AppMsgGroupUserUpdateBoToMsgGroupUserBoMapper;
import com.gzhuxn.personals.domain.message.MsgGroupUser;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppMsgGroupUserUpdateBoToMsgGroupUserBoMapper.class},
    imports = {}
)
public interface MsgGroupUserBoToMsgGroupUserMapper extends BaseMapper<MsgGroupUserBo, MsgGroupUser> {
}
