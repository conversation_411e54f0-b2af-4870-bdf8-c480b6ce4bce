package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.bo.AppMsgContentUserPageReqBoToMsgContentUserMapper;
import com.gzhuxn.personals.controller.app.message.bo.AppMsgContentUserReadBoToMsgContentUserMapper;
import com.gzhuxn.personals.controller.app.message.vo.AppMsgContentUserVo;
import com.gzhuxn.personals.domain.message.bo.MsgContentUserBoToMsgContentUserMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgContentUserBoToMsgContentUserMapper.class,AppMsgContentUserPageReqBoToMsgContentUserMapper.class,AppMsgContentUserReadBoToMsgContentUserMapper.class,MsgContentUserToMsgContentUserVoMapper.class,MsgContentUserToAppMsgContentUserPageReqBoMapper.class},
    imports = {}
)
public interface MsgContentUserToAppMsgContentUserVoMapper extends BaseMapper<MsgContentUser, AppMsgContentUserVo> {
}
