package com.gzhuxn.personals.controller.app.user.vo.blacklist;

import com.gzhuxn.personals.domain.user.UserBlacklist;
import com.gzhuxn.personals.domain.user.UserBlacklistToAppUserBlacklistVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserBlacklistToAppUserBlacklistVoMapper.class},
    imports = {}
)
public interface AppUserBlacklistVoToUserBlacklistMapper extends BaseMapper<AppUserBlacklistVo, UserBlacklist> {
  @Mapping(
      target = "oppositeUserId",
      source = "uid"
  )
  UserBlacklist convert(AppUserBlacklistVo source);

  @Mapping(
      target = "oppositeUserId",
      source = "uid"
  )
  UserBlacklist convert(AppUserBlacklistVo source, @MappingTarget UserBlacklist target);
}
