package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.admin.activity.vo.AdminActTagVo;
import com.gzhuxn.personals.domain.manage.bo.ManageTagBoToManageTagMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageTagBoToManageTagMapper.class,ManageTagToManageTagVoMapper.class,ManageTagToAppManageTagListVoMapper.class,ManageTagToAppManageTagVoMapper.class},
    imports = {}
)
public interface ManageTagToAdminActTagVoMapper extends BaseMapper<ManageTag, AdminActTagVo> {
}
