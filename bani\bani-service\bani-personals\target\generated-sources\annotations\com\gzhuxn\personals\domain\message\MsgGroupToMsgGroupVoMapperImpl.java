package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.vo.MsgGroupVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupToMsgGroupVoMapperImpl implements MsgGroupToMsgGroupVoMapper {

    @Override
    public MsgGroupVo convert(MsgGroup arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroupVo msgGroupVo = new MsgGroupVo();

        msgGroupVo.setId( arg0.getId() );
        msgGroupVo.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            msgGroupVo.setNum( arg0.getNum().longValue() );
        }
        msgGroupVo.setType( arg0.getType() );
        msgGroupVo.setBusinessId( arg0.getBusinessId() );
        msgGroupVo.setStatus( arg0.getStatus() );
        msgGroupVo.setRemark( arg0.getRemark() );

        return msgGroupVo;
    }

    @Override
    public MsgGroupVo convert(MsgGroup arg0, MsgGroupVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            arg1.setNum( arg0.getNum().longValue() );
        }
        else {
            arg1.setNum( null );
        }
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
