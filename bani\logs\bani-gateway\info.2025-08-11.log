2025-08-11 04:40:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Server check success, currentServer is 127.0.0.1:8848 
2025-08-11 07:50:17 [boundedElastic-283] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 07:50:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[351]毫秒
2025-08-11 07:50:18 [boundedElastic-283] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 07:50:19 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[688]毫秒
2025-08-11 07:50:48 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 07:50:48 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[292]毫秒
2025-08-11 07:50:50 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:50:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[103]毫秒
2025-08-11 07:50:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:50:52 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[139]毫秒
2025-08-11 07:50:54 [boundedElastic-284] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:50:54 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[151]毫秒
2025-08-11 07:51:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:51:01 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[149]毫秒
2025-08-11 07:51:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:51:05 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[108]毫秒
2025-08-11 07:52:06 [boundedElastic-283] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/wechatApply/page],参数类型[json],参数:[null]
2025-08-11 07:52:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/wechatApply/page],耗时:[141]毫秒
2025-08-11 07:54:55 [boundedElastic-284] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 07:54:55 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[230]毫秒
2025-08-11 07:55:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1QuJ0w38uir53EF73w3QrAzk0QuJ0O","grantType":"mini","tenantId":"1"}]
2025-08-11 07:55:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[2128]毫秒
2025-08-11 07:55:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 07:55:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 07:55:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[119]毫秒
2025-08-11 07:55:12 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[810]毫秒
2025-08-11 07:55:12 [boundedElastic-290] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 07:55:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[524]毫秒
2025-08-11 07:55:17 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 07:55:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[197]毫秒
2025-08-11 07:55:23 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:55:23 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[272]毫秒
2025-08-11 07:55:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 07:55:27 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[153]毫秒
2025-08-11 07:55:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:55:32 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[194]毫秒
2025-08-11 07:55:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:55:36 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[143]毫秒
2025-08-11 07:55:37 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:55:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[193]毫秒
2025-08-11 07:56:14 [boundedElastic-290] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 07:56:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[197]毫秒
2025-08-11 07:56:21 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:56:21 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[186]毫秒
2025-08-11 07:56:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:56:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[144]毫秒
2025-08-11 07:57:09 [nacos-grpc-client-executor-127.0.0.1-7062] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Receive server push request, request = NotifySubscriberRequest, requestId = 19
2025-08-11 07:57:09 [nacos-grpc-client-executor-127.0.0.1-7062] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 07:57:09 [nacos-grpc-client-executor-127.0.0.1-7062] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@bani-personals -> []
2025-08-11 07:57:09 [nacos-grpc-client-executor-127.0.0.1-7062] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Ack server push request, request = NotifySubscriberRequest, requestId = 19
2025-08-11 07:57:52 [nacos-grpc-client-executor-127.0.0.1-7071] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Receive server push request, request = NotifySubscriberRequest, requestId = 21
2025-08-11 07:57:52 [nacos-grpc-client-executor-127.0.0.1-7071] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 07:57:52 [nacos-grpc-client-executor-127.0.0.1-7071] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 07:57:52 [nacos-grpc-client-executor-127.0.0.1-7071] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Ack server push request, request = NotifySubscriberRequest, requestId = 21
2025-08-11 07:58:10 [boundedElastic-295] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:58:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:58:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[652]毫秒
2025-08-11 07:58:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:58:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[538]毫秒
2025-08-11 07:58:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:58:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[601]毫秒
2025-08-11 07:58:44 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 07:58:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[541]毫秒
2025-08-11 08:00:49 [boundedElastic-296] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:00:49 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[524]毫秒
2025-08-11 08:00:50 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:00:51 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[333]毫秒
2025-08-11 08:00:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:00:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[482]毫秒
2025-08-11 08:00:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:00:59 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[392]毫秒
2025-08-11 08:01:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[366]毫秒
2025-08-11 08:01:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[344]毫秒
2025-08-11 08:01:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[457]毫秒
2025-08-11 08:01:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:05 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[367]毫秒
2025-08-11 08:01:10 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:11 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[421]毫秒
2025-08-11 08:01:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 08:01:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[347]毫秒
2025-08-11 08:01:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:01:23 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[513]毫秒
2025-08-11 08:01:24 [boundedElastic-295] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:01:25 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[351]毫秒
2025-08-11 08:01:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:01:35 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[453]毫秒
2025-08-11 08:03:01 [boundedElastic-301] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:03:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[903]毫秒
2025-08-11 08:03:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:03:06 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[336]毫秒
2025-08-11 08:03:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:03:08 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[436]毫秒
2025-08-11 08:03:14 [boundedElastic-295] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:03:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[149]毫秒
2025-08-11 08:03:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:03:16 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[876]毫秒
2025-08-11 08:03:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:03:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[481]毫秒
2025-08-11 08:03:22 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:03:22 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[432]毫秒
2025-08-11 08:05:31 [boundedElastic-290] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:05:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[505]毫秒
2025-08-11 08:07:07 [boundedElastic-292] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[4874]毫秒
2025-08-11 08:07:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[450]毫秒
2025-08-11 08:07:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[473]毫秒
2025-08-11 08:07:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[422]毫秒
2025-08-11 08:07:22 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[421]毫秒
2025-08-11 08:07:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[496]毫秒
2025-08-11 08:07:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[462]毫秒
2025-08-11 08:07:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:07:38 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[357]毫秒
2025-08-11 08:07:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:07:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[354]毫秒
2025-08-11 08:07:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[336]毫秒
2025-08-11 08:07:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:07:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[363]毫秒
2025-08-11 08:07:43 [boundedElastic-299] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:07:43 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[487]毫秒
2025-08-11 08:07:47 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:47 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[413]毫秒
2025-08-11 08:07:51 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/greeting/ignore/1954564506755682305],参数类型[json],参数:[null]
2025-08-11 08:07:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/greeting/ignore/1954564506755682305],耗时:[912]毫秒
2025-08-11 08:07:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[388]毫秒
2025-08-11 08:07:54 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:07:54 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[356]毫秒
2025-08-11 08:08:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[441]毫秒
2025-08-11 08:08:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[354]毫秒
2025-08-11 08:08:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:08:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[405]毫秒
2025-08-11 08:08:10 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:08:10 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[455]毫秒
2025-08-11 08:08:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[324]毫秒
2025-08-11 08:08:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[504]毫秒
2025-08-11 08:08:19 [boundedElastic-292] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[345]毫秒
2025-08-11 08:08:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[334]毫秒
2025-08-11 08:08:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:08:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[420]毫秒
2025-08-11 08:08:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 08:08:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 08:08:30 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 08:08:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 08:08:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 08:08:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[135]毫秒
2025-08-11 08:08:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[291]毫秒
2025-08-11 08:08:30 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[319]毫秒
2025-08-11 08:08:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[863]毫秒
2025-08-11 08:08:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[1183]毫秒
2025-08-11 08:08:31 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 08:08:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 08:08:32 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[359]毫秒
2025-08-11 08:08:32 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[622]毫秒
2025-08-11 08:08:33 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:08:33 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[95]毫秒
2025-08-11 08:08:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:08:39 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[131]毫秒
2025-08-11 08:08:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:08:51 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[134]毫秒
2025-08-11 08:09:41 [boundedElastic-292] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:09:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[148]毫秒
2025-08-11 08:09:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:09:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[127]毫秒
2025-08-11 08:10:04 [boundedElastic-292] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1L6rll2lfA6g4Z6Hkl2rbI3H2L6rly","grantType":"mini","tenantId":"1"}]
2025-08-11 08:10:06 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1707]毫秒
2025-08-11 08:10:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:10:06 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 08:10:06 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[206]毫秒
2025-08-11 08:10:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[799]毫秒
2025-08-11 08:10:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:10:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[537]毫秒
2025-08-11 08:10:10 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:10:10 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[221]毫秒
2025-08-11 08:10:14 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 08:10:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 08:10:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 08:10:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 08:10:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 08:10:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[130]毫秒
2025-08-11 08:10:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[184]毫秒
2025-08-11 08:10:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[184]毫秒
2025-08-11 08:10:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[471]毫秒
2025-08-11 08:10:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[530]毫秒
2025-08-11 08:10:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 08:10:14 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 08:10:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[195]毫秒
2025-08-11 08:10:15 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[358]毫秒
2025-08-11 08:10:24 [boundedElastic-296] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:10:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[148]毫秒
2025-08-11 08:10:25 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:10:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[95]毫秒
2025-08-11 08:10:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:10:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[101]毫秒
2025-08-11 08:10:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:10:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[139]毫秒
2025-08-11 08:10:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 08:10:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 08:10:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 08:10:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 08:10:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 08:10:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[86]毫秒
2025-08-11 08:10:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[135]毫秒
2025-08-11 08:10:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[195]毫秒
2025-08-11 08:10:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 08:10:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[470]毫秒
2025-08-11 08:10:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[516]毫秒
2025-08-11 08:10:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 08:10:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[350]毫秒
2025-08-11 08:10:36 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[151]毫秒
2025-08-11 08:10:37 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:10:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[89]毫秒
2025-08-11 08:10:49 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:10:49 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[145]毫秒
2025-08-11 08:11:00 [boundedElastic-304] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/greeting/send],参数类型[json],参数:[{"oppositeUserId":"1938886425951789058","content":"uuuuu天天tt还能"}]
2025-08-11 08:11:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/greeting/send],耗时:[831]毫秒
2025-08-11 08:11:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 08:11:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[134]毫秒
2025-08-11 08:11:16 [boundedElastic-290] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:11:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[219]毫秒
2025-08-11 08:11:17 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:11:18 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[509]毫秒
2025-08-11 08:11:20 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:11:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[140]毫秒
2025-08-11 08:11:22 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:11:22 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[362]毫秒
2025-08-11 08:11:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:11:24 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[370]毫秒
2025-08-11 08:11:28 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/greeting/reply/1954697018513543170],参数类型[json],参数:[null]
2025-08-11 08:11:29 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/greeting/reply/1954697018513543170],耗时:[666]毫秒
2025-08-11 08:11:29 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:11:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[271]毫秒
2025-08-11 08:11:31 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:11:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[318]毫秒
2025-08-11 08:11:33 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:11:33 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[317]毫秒
2025-08-11 08:19:12 [boundedElastic-309] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:19:16 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[4425]毫秒
2025-08-11 08:21:00 [boundedElastic-311] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:21:01 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[577]毫秒
2025-08-11 08:21:01 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:21:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[396]毫秒
2025-08-11 08:21:08 [boundedElastic-296] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:21:08 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[150]毫秒
2025-08-11 08:21:09 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:21:10 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[701]毫秒
2025-08-11 08:21:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:21:11 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[184]毫秒
2025-08-11 08:21:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 08:21:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[294]毫秒
2025-08-11 08:21:31 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 08:21:31 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[381]毫秒
2025-08-11 08:21:39 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:21:40 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[216]毫秒
2025-08-11 08:21:40 [boundedElastic-312] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:21:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[577]毫秒
2025-08-11 08:21:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:21:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[161]毫秒
2025-08-11 08:21:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:21:46 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[468]毫秒
2025-08-11 08:21:48 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:21:48 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[378]毫秒
2025-08-11 08:21:49 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:21:49 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[377]毫秒
2025-08-11 08:21:51 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:21:51 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[346]毫秒
2025-08-11 08:22:37 [boundedElastic-312] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:22:37 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[276]毫秒
2025-08-11 08:22:38 [boundedElastic-312] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:22:39 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[517]毫秒
2025-08-11 08:22:40 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:22:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[215]毫秒
2025-08-11 08:22:47 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:22:48 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[449]毫秒
2025-08-11 08:22:49 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:22:49 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[405]毫秒
2025-08-11 08:23:19 [nacos-grpc-client-executor-127.0.0.1-7417] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Receive server push request, request = NotifySubscriberRequest, requestId = 23
2025-08-11 08:23:19 [nacos-grpc-client-executor-127.0.0.1-7417] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 08:23:19 [nacos-grpc-client-executor-127.0.0.1-7417] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@bani-personals -> []
2025-08-11 08:23:19 [nacos-grpc-client-executor-127.0.0.1-7417] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Ack server push request, request = NotifySubscriberRequest, requestId = 23
2025-08-11 08:23:53 [nacos-grpc-client-executor-127.0.0.1-7424] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Receive server push request, request = NotifySubscriberRequest, requestId = 25
2025-08-11 08:23:53 [nacos-grpc-client-executor-127.0.0.1-7424] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 08:23:53 [nacos-grpc-client-executor-127.0.0.1-7424] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@bani-personals -> [{"instanceId":"**********#9211##DEFAULT_GROUP@@bani-personals","ip":"**********","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@bani-personals","metadata":{"preserved.register.source":"SPRING_CLOUD","username":"bani","userpassword":"123456"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-11 08:23:53 [nacos-grpc-client-executor-127.0.0.1-7424] INFO  c.alibaba.nacos.common.remote.client - [3eecde17-8e8d-49fb-968b-912107b0fd97] Ack server push request, request = NotifySubscriberRequest, requestId = 25
2025-08-11 08:24:06 [boundedElastic-306] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 08:24:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[212]毫秒
2025-08-11 08:24:07 [boundedElastic-306] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:24:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[2023]毫秒
2025-08-11 08:24:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:24:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[339]毫秒
2025-08-11 08:24:20 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:24:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[395]毫秒
2025-08-11 08:24:23 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:24:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[450]毫秒
2025-08-11 08:24:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:24:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[364]毫秒
2025-08-11 08:24:25 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 08:24:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[363]毫秒
2025-08-11 08:24:27 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:24:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[358]毫秒
2025-08-11 08:24:29 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 08:24:29 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[459]毫秒
2025-08-11 08:27:48 [boundedElastic-304] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:27:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[320]毫秒
2025-08-11 08:27:48 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 08:27:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[109]毫秒
2025-08-11 08:28:38 [boundedElastic-318] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 08:28:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[676]毫秒
2025-08-11 09:21:20 [boundedElastic-341] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 09:21:21 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[239]毫秒
2025-08-11 09:21:21 [boundedElastic-341] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 09:21:22 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[546]毫秒
2025-08-11 09:21:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:21:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[147]毫秒
2025-08-11 09:21:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:21:25 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[107]毫秒
2025-08-11 09:21:26 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 09:21:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[236]毫秒
2025-08-11 09:21:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 09:21:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[188]毫秒
2025-08-11 09:21:28 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:21:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[187]毫秒
2025-08-11 09:21:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 09:21:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[190]毫秒
2025-08-11 09:21:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:21:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[140]毫秒
2025-08-11 09:21:32 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 09:21:32 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[232]毫秒
2025-08-11 09:21:33 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:21:33 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[115]毫秒
2025-08-11 09:22:13 [boundedElastic-323] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 09:22:13 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[309]毫秒
2025-08-11 09:22:46 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:22:47 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[609]毫秒
2025-08-11 09:22:47 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:22:48 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[342]毫秒
2025-08-11 09:22:49 [boundedElastic-346] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:22:50 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[344]毫秒
2025-08-11 09:22:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:22:54 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[435]毫秒
2025-08-11 09:22:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 09:22:55 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[329]毫秒
2025-08-11 09:22:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:22:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[321]毫秒
2025-08-11 09:23:01 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:23:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[416]毫秒
2025-08-11 09:23:05 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 09:23:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[321]毫秒
2025-08-11 09:23:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 09:23:07 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[530]毫秒
2025-08-11 09:23:08 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 09:23:09 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[416]毫秒
2025-08-11 09:23:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:23:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[411]毫秒
2025-08-11 09:23:16 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-11 09:23:17 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[474]毫秒
2025-08-11 09:23:25 [boundedElastic-349] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:23:25 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[422]毫秒
2025-08-11 09:23:28 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-11 09:23:29 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[331]毫秒
2025-08-11 09:23:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 09:23:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[407]毫秒
2025-08-11 09:24:42 [boundedElastic-323] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-11 09:24:42 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[517]毫秒
2025-08-11 09:24:42 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-11 09:24:43 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[180]毫秒
2025-08-11 09:24:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 09:24:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[382]毫秒
2025-08-11 09:24:51 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-11 09:24:52 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[421]毫秒
2025-08-11 10:06:26 [boundedElastic-372] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:06:26 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[448]毫秒
2025-08-11 10:06:29 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:06:30 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[365]毫秒
2025-08-11 10:06:31 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:06:32 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[413]毫秒
2025-08-11 10:06:34 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:06:34 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[318]毫秒
2025-08-11 10:06:36 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:06:36 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[322]毫秒
2025-08-11 10:06:38 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:06:38 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[227]毫秒
2025-08-11 10:06:41 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:06:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[346]毫秒
2025-08-11 10:06:43 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:06:43 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[378]毫秒
2025-08-11 10:06:48 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:06:48 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[182]毫秒
2025-08-11 10:06:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:06:54 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[538]毫秒
2025-08-11 10:08:00 [boundedElastic-370] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:08:00 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[634]毫秒
2025-08-11 10:08:22 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:08:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[499]毫秒
2025-08-11 10:08:31 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:08:32 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[625]毫秒
2025-08-11 10:08:48 [boundedElastic-372] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:08:48 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[502]毫秒
2025-08-11 10:08:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:08:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[510]毫秒
2025-08-11 10:41:41 [boundedElastic-385] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:41:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[320]毫秒
2025-08-11 10:41:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:41:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[394]毫秒
2025-08-11 10:41:48 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:41:48 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[325]毫秒
2025-08-11 10:41:52 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:41:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[220]毫秒
2025-08-11 10:41:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:41:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[328]毫秒
2025-08-11 10:41:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:41:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[408]毫秒
2025-08-11 10:41:59 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:42:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[324]毫秒
2025-08-11 10:42:01 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-11 10:42:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[309]毫秒
2025-08-11 10:42:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[224]毫秒
2025-08-11 10:42:08 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 10:42:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[316]毫秒
2025-08-11 10:42:12 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:13 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[303]毫秒
2025-08-11 10:42:16 [boundedElastic-385] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 10:42:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[118]毫秒
2025-08-11 10:42:17 [boundedElastic-385] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:42:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[532]毫秒
2025-08-11 10:42:21 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[137]毫秒
2025-08-11 10:42:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:42:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[209]毫秒
2025-08-11 10:42:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:42:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[128]毫秒
2025-08-11 10:42:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[186]毫秒
2025-08-11 10:42:26 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:42:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[164]毫秒
2025-08-11 10:42:27 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[132]毫秒
2025-08-11 10:42:29 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:42:29 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[206]毫秒
2025-08-11 10:42:30 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:42:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[178]毫秒
2025-08-11 10:42:31 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:42:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[170]毫秒
2025-08-11 10:44:10 [boundedElastic-377] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:44:10 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[210]毫秒
2025-08-11 10:44:10 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:44:10 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[128]毫秒
2025-08-11 10:45:18 [boundedElastic-384] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:45:18 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[208]毫秒
2025-08-11 10:45:24 [boundedElastic-384] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 10:45:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[225]毫秒
2025-08-11 10:45:25 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:45:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[485]毫秒
2025-08-11 10:45:25 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 10:45:26 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[99]毫秒
2025-08-11 10:45:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:45:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[184]毫秒
2025-08-11 10:45:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:45:30 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:45:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[358]毫秒
2025-08-11 10:45:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:45:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[100]毫秒
2025-08-11 10:45:31 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[585]毫秒
2025-08-11 10:45:32 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 10:45:34 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[1319]毫秒
2025-08-11 10:45:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 10:45:35 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 10:45:36 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[602]毫秒
2025-08-11 10:45:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[780]毫秒
2025-08-11 10:45:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:45:37 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:45:38 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[99]毫秒
2025-08-11 10:45:38 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[492]毫秒
2025-08-11 10:45:44 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:45:44 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:45:44 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[175]毫秒
2025-08-11 10:45:44 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[483]毫秒
2025-08-11 10:45:48 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 10:45:48 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[173]毫秒
2025-08-11 10:45:50 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:45:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:45:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[179]毫秒
2025-08-11 10:45:50 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[487]毫秒
2025-08-11 10:46:26 [boundedElastic-361] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 10:46:26 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:46:26 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[263]毫秒
2025-08-11 10:46:27 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[489]毫秒
2025-08-11 10:46:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 10:46:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[522]毫秒
2025-08-11 10:46:40 [boundedElastic-361] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 10:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[99]毫秒
2025-08-11 10:46:42 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:46:43 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[517]毫秒
2025-08-11 10:48:28 [boundedElastic-390] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0a1f7H000PvZLU1wi3200sBF2l1f7H0T","grantType":"mini","tenantId":"1"}]
2025-08-11 10:48:30 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[2161]毫秒
2025-08-11 10:48:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 10:48:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 10:48:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[172]毫秒
2025-08-11 10:48:31 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[803]毫秒
2025-08-11 10:48:31 [boundedElastic-390] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 10:48:32 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[438]毫秒
2025-08-11 11:11:43 [boundedElastic-399] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0d1ItV0w3ZYZq53p5g1w3blNVv0ItV0b","grantType":"mini","tenantId":"1"}]
2025-08-11 11:11:44 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1715]毫秒
2025-08-11 11:11:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 11:11:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 11:11:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[183]毫秒
2025-08-11 11:11:45 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[747]毫秒
2025-08-11 11:11:46 [boundedElastic-399] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 11:11:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[476]毫秒
2025-08-11 11:11:50 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 11:11:50 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:11:50 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 11:11:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 11:11:50 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 11:11:50 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[107]毫秒
2025-08-11 11:11:51 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[310]毫秒
2025-08-11 11:11:51 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[312]毫秒
2025-08-11 11:11:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[567]毫秒
2025-08-11 11:11:51 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 11:11:51 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[757]毫秒
2025-08-11 11:11:51 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[298]毫秒
2025-08-11 11:11:51 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 11:11:52 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[593]毫秒
2025-08-11 11:11:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:11:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/create],耗时:[732]毫秒
2025-08-11 11:17:18 [boundedElastic-397] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 11:17:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[230]毫秒
2025-08-11 11:17:19 [boundedElastic-397] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 11:17:19 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[533]毫秒
2025-08-11 11:25:04 [boundedElastic-407] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 11:25:04 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[245]毫秒
2025-08-11 11:25:05 [boundedElastic-407] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 11:25:05 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[531]毫秒
2025-08-11 11:25:08 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 11:25:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 11:25:08 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:25:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 11:25:08 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 11:25:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[141]毫秒
2025-08-11 11:25:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[143]毫秒
2025-08-11 11:25:08 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[53]毫秒
2025-08-11 11:25:08 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[480]毫秒
2025-08-11 11:25:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 11:25:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[847]毫秒
2025-08-11 11:25:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[346]毫秒
2025-08-11 11:25:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 11:25:09 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[238]毫秒
2025-08-11 11:25:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/cancel],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:25:33 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/cancel],耗时:[469]毫秒
2025-08-11 11:25:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:25:37 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/create],耗时:[504]毫秒
2025-08-11 11:43:52 [boundedElastic-412] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1GZOkl2zT36g4zTlll2tzF7p0GZOkB","grantType":"mini","tenantId":"1"}]
2025-08-11 11:43:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1761]毫秒
2025-08-11 11:43:54 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 11:43:54 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 11:43:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[174]毫秒
2025-08-11 11:43:55 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[739]毫秒
2025-08-11 11:43:56 [boundedElastic-412] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 11:43:56 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[448]毫秒
2025-08-11 11:56:11 [boundedElastic-427] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 11:56:11 [boundedElastic-423] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 11:56:11 [boundedElastic-431] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 11:56:11 [boundedElastic-432] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 11:56:11 [boundedElastic-426] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:56:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[137]毫秒
2025-08-11 11:56:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[263]毫秒
2025-08-11 11:56:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[264]毫秒
2025-08-11 11:56:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 11:56:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[722]毫秒
2025-08-11 11:56:12 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[963]毫秒
2025-08-11 11:56:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[592]毫秒
2025-08-11 11:56:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 11:56:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[191]毫秒
2025-08-11 11:56:24 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/cancel],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 11:56:25 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/cancel],耗时:[473]毫秒
2025-08-11 12:03:39 [boundedElastic-423] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 12:03:39 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[218]毫秒
2025-08-11 12:03:41 [boundedElastic-423] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 12:03:42 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[502]毫秒
2025-08-11 12:05:48 [boundedElastic-431] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 12:05:48 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[220]毫秒
2025-08-11 12:05:49 [boundedElastic-431] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 12:05:50 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[505]毫秒
2025-08-11 12:05:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 12:05:53 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 12:05:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 12:05:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 12:05:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 12:05:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[85]毫秒
2025-08-11 12:05:53 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[135]毫秒
2025-08-11 12:05:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[135]毫秒
2025-08-11 12:05:54 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[460]毫秒
2025-08-11 12:05:54 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 12:05:54 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[854]毫秒
2025-08-11 12:05:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[333]毫秒
2025-08-11 12:05:54 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 12:05:54 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[215]毫秒
2025-08-11 12:05:56 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 12:05:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/create],耗时:[503]毫秒
2025-08-11 12:06:55 [boundedElastic-432] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 12:06:55 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 12:06:55 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 12:06:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 12:06:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 12:06:55 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 12:06:55 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[47]毫秒
2025-08-11 12:06:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[200]毫秒
2025-08-11 12:06:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[203]毫秒
2025-08-11 12:06:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[513]毫秒
2025-08-11 12:06:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 12:06:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[689]毫秒
2025-08-11 12:06:56 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[936]毫秒
2025-08-11 12:06:56 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[344]毫秒
2025-08-11 12:06:56 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 12:06:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[184]毫秒
2025-08-11 12:07:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 12:07:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 12:07:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 12:07:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 12:07:05 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 12:07:05 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[130]毫秒
2025-08-11 12:07:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[176]毫秒
2025-08-11 12:07:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[177]毫秒
2025-08-11 12:07:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 12:07:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[463]毫秒
2025-08-11 12:07:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 12:07:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[602]毫秒
2025-08-11 12:07:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[343]毫秒
2025-08-11 12:07:06 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[147]毫秒
2025-08-11 12:07:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 12:07:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/gift/list],参数类型[json],参数:[null]
2025-08-11 12:07:26 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[145]毫秒
2025-08-11 12:07:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/gift/list],耗时:[238]毫秒
2025-08-11 12:11:03 [boundedElastic-432] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1VW90w3kzIq53DiS2w3WAc0F0VW90W","grantType":"mini","tenantId":"1"}]
2025-08-11 12:11:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1715]毫秒
2025-08-11 12:11:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 12:11:05 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[171]毫秒
2025-08-11 12:11:05 [boundedElastic-432] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 12:11:05 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[194]毫秒
2025-08-11 12:23:05 [boundedElastic-446] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 12:23:05 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[500]毫秒
2025-08-11 13:10:02 [boundedElastic-461] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 13:10:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[492]毫秒
2025-08-11 14:05:38 [boundedElastic-476] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 14:05:39 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[505]毫秒
2025-08-11 14:06:28 [boundedElastic-495] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 14:06:29 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[480]毫秒
2025-08-11 14:06:48 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-11 14:06:49 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[396]毫秒
2025-08-11 14:09:36 [boundedElastic-495] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1DnT000cOFLU1JVd000T51Zs1DnT0C","grantType":"mini","tenantId":"1"}]
2025-08-11 14:09:38 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[2200]毫秒
2025-08-11 14:09:38 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:09:38 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 14:09:39 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[270]毫秒
2025-08-11 14:09:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[747]毫秒
2025-08-11 14:09:40 [boundedElastic-495] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:09:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[496]毫秒
2025-08-11 14:09:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:09:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[222]毫秒
2025-08-11 14:09:48 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:09:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[391]毫秒
2025-08-11 14:10:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1jpT000uOFLU1OH5400vF5MY1jpT02","grantType":"mini","tenantId":"1"}]
2025-08-11 14:10:04 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1512]毫秒
2025-08-11 14:10:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:10:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[90]毫秒
2025-08-11 14:10:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:10:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[438]毫秒
2025-08-11 14:10:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:10:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[181]毫秒
2025-08-11 14:10:27 [boundedElastic-493] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1gb00w3nhJq53YOd3w36ACsF1gb00g","grantType":"mini","tenantId":"1"}]
2025-08-11 14:10:28 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1528]毫秒
2025-08-11 14:10:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:10:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[174]毫秒
2025-08-11 14:10:29 [boundedElastic-493] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:10:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[497]毫秒
2025-08-11 14:10:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:10:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[445]毫秒
2025-08-11 14:10:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:10:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[135]毫秒
2025-08-11 14:10:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:10:42 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[455]毫秒
2025-08-11 14:10:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:10:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[135]毫秒
2025-08-11 14:10:49 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:10:49 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[141]毫秒
2025-08-11 14:10:50 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:10:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[445]毫秒
2025-08-11 14:10:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:10:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[214]毫秒
2025-08-11 14:11:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:11:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[220]毫秒
2025-08-11 14:11:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:11:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[217]毫秒
2025-08-11 14:11:06 [boundedElastic-497] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 14:11:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[138]毫秒
2025-08-11 14:11:13 [boundedElastic-497] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1chh0w3ncsq538NS2w38rmNV2chh0y","grantType":"mini","tenantId":"1"}]
2025-08-11 14:11:14 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1559]毫秒
2025-08-11 14:11:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 14:11:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[101]毫秒
2025-08-11 14:11:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 14:11:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[490]毫秒
2025-08-11 20:09:34 [boundedElastic-665] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:09:34 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[791]毫秒
2025-08-11 20:10:34 [boundedElastic-662] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:10:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[677]毫秒
2025-08-11 20:11:31 [boundedElastic-670] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 20:11:31 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:11:32 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[424]毫秒
2025-08-11 20:11:32 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[683]毫秒
2025-08-11 20:11:56 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 20:11:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:11:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[233]毫秒
2025-08-11 20:11:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[506]毫秒
2025-08-11 20:12:08 [boundedElastic-662] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:08 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 20:12:08 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[106]毫秒
2025-08-11 20:12:08 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[576]毫秒
2025-08-11 20:12:08 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[354]毫秒
2025-08-11 20:12:10 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[608]毫秒
2025-08-11 20:12:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[506]毫秒
2025-08-11 20:12:16 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:17 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[443]毫秒
2025-08-11 20:12:29 [boundedElastic-667] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 20:12:29 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[177]毫秒
2025-08-11 20:12:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:12:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[514]毫秒
2025-08-11 20:12:34 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 20:12:34 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:12:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[234]毫秒
2025-08-11 20:12:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[527]毫秒
2025-08-11 20:14:59 [boundedElastic-671] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 20:14:59 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 20:15:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[267]毫秒
2025-08-11 20:15:00 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[515]毫秒
2025-08-11 20:15:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:15:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[156]毫秒
2025-08-11 20:15:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:15:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[219]毫秒
2025-08-11 20:15:16 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:15:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[301]毫秒
2025-08-11 20:15:50 [boundedElastic-667] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:15:51 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[332]毫秒
2025-08-11 20:16:01 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:16:01 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[311]毫秒
2025-08-11 20:16:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 20:16:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[223]毫秒
2025-08-11 20:16:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:16:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[180]毫秒
2025-08-11 20:16:22 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:22 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[249]毫秒
2025-08-11 20:16:24 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[153]毫秒
2025-08-11 20:16:26 [boundedElastic-668] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:26 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[163]毫秒
2025-08-11 20:16:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[167]毫秒
2025-08-11 20:16:38 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:38 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[220]毫秒
2025-08-11 20:16:56 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-11 20:16:56 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[494]毫秒
2025-08-11 20:17:16 [boundedElastic-667] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 20:17:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[238]毫秒
2025-08-11 20:17:18 [boundedElastic-667] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:17:19 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[530]毫秒
2025-08-11 20:17:20 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:17:20 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[156]毫秒
2025-08-11 20:17:25 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-11 20:17:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-11 20:17:25 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-11 20:17:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 20:17:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[141]毫秒
2025-08-11 20:17:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[151]毫秒
2025-08-11 20:17:25 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[200]毫秒
2025-08-11 20:17:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[286]毫秒
2025-08-11 20:18:02 [boundedElastic-673] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 20:18:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-11 20:18:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-11 20:18:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-11 20:18:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[188]毫秒
2025-08-11 20:18:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[197]毫秒
2025-08-11 20:18:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[154]毫秒
2025-08-11 20:18:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[232]毫秒
2025-08-11 20:18:10 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-11 20:18:10 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 20:18:10 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-11 20:18:10 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-11 20:18:11 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[133]毫秒
2025-08-11 20:18:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[105]毫秒
2025-08-11 20:18:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[108]毫秒
2025-08-11 20:18:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[216]毫秒
2025-08-11 20:18:42 [boundedElastic-675] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 20:18:42 [boundedElastic-674] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-11 20:18:42 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-11 20:18:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-11 20:18:42 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[128]毫秒
2025-08-11 20:18:42 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[124]毫秒
2025-08-11 20:18:42 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[125]毫秒
2025-08-11 20:18:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[226]毫秒
2025-08-11 20:18:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 20:18:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-11 20:18:54 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-11 20:18:54 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-11 20:18:54 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[141]毫秒
2025-08-11 20:18:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[141]毫秒
2025-08-11 20:18:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[142]毫秒
2025-08-11 20:18:54 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[211]毫秒
2025-08-11 20:19:08 [boundedElastic-676] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 20:19:08 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[325]毫秒
2025-08-11 20:19:09 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:19:10 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[714]毫秒
2025-08-11 20:19:12 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:19:12 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[410]毫秒
2025-08-11 20:19:17 [boundedElastic-668] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 20:19:17 [boundedElastic-673] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 20:19:17 [boundedElastic-665] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 20:19:17 [boundedElastic-676] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 20:19:17 [boundedElastic-674] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 20:19:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[141]毫秒
2025-08-11 20:19:17 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[357]毫秒
2025-08-11 20:19:17 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[383]毫秒
2025-08-11 20:19:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[779]毫秒
2025-08-11 20:19:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[975]毫秒
2025-08-11 20:19:18 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 20:19:18 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 20:19:18 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[367]毫秒
2025-08-11 20:19:19 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[596]毫秒
2025-08-11 20:19:26 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/basic-info],参数类型[json],参数:[null]
2025-08-11 20:19:26 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/lit-list],参数类型[json],参数:[null]
2025-08-11 20:19:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/basic-info],耗时:[246]毫秒
2025-08-11 20:19:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/lit-list],耗时:[252]毫秒
2025-08-11 20:19:57 [boundedElastic-668] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:19:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[635]毫秒
2025-08-11 20:20:58 [boundedElastic-674] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:20:59 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[621]毫秒
2025-08-11 20:20:59 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 20:20:59 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[146]毫秒
2025-08-11 20:21:14 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/data],参数类型[json],参数:[null]
2025-08-11 20:21:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/data],耗时:[313]毫秒
2025-08-11 20:21:19 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/data],参数类型[json],参数:[null]
2025-08-11 20:21:19 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/data],耗时:[151]毫秒
2025-08-11 20:21:37 [boundedElastic-672] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 20:21:37 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[232]毫秒
2025-08-11 20:21:38 [boundedElastic-672] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 20:21:38 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[503]毫秒
2025-08-11 22:14:22 [boundedElastic-722] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:14:22 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:14:22 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[467]毫秒
2025-08-11 22:14:23 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[860]毫秒
2025-08-11 22:15:38 [boundedElastic-741] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:15:38 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:15:38 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[106]毫秒
2025-08-11 22:15:39 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[683]毫秒
2025-08-11 22:15:39 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:15:40 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[437]毫秒
2025-08-11 22:15:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:15:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:15:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[349]毫秒
2025-08-11 22:15:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[532]毫秒
2025-08-11 22:16:01 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:16:01 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[93]毫秒
2025-08-11 22:16:01 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:16:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[404]毫秒
2025-08-11 22:16:13 [boundedElastic-722] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 22:16:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[267]毫秒
2025-08-11 22:16:14 [boundedElastic-722] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:16:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[597]毫秒
2025-08-11 22:16:17 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:16:17 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[106]毫秒
2025-08-11 22:16:17 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:16:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[475]毫秒
2025-08-11 22:16:34 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:16:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[526]毫秒
2025-08-11 22:17:14 [boundedElastic-739] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:17:15 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:17:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 22:17:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[1117]毫秒
2025-08-11 22:17:16 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[431]毫秒
2025-08-11 22:17:16 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[679]毫秒
2025-08-11 22:17:20 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:17:20 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:17:20 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[193]毫秒
2025-08-11 22:17:21 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[513]毫秒
2025-08-11 22:18:29 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:18:29 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:18:29 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[156]毫秒
2025-08-11 22:18:29 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[586]毫秒
2025-08-11 22:18:38 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:18:38 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:18:38 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[163]毫秒
2025-08-11 22:18:38 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[443]毫秒
2025-08-11 22:19:14 [boundedElastic-739] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:19:14 [boundedElastic-743] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:19:14 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[140]毫秒
2025-08-11 22:19:14 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:19:14 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[62]毫秒
2025-08-11 22:19:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[572]毫秒
2025-08-11 22:19:14 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:19:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[276]毫秒
2025-08-11 22:20:06 [boundedElastic-739] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 22:20:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[220]毫秒
2025-08-11 22:20:07 [boundedElastic-739] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:20:08 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[484]毫秒
2025-08-11 22:20:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:20:10 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[775]毫秒
2025-08-11 22:20:10 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:20:10 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[565]毫秒
2025-08-11 22:20:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:20:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:20:13 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[159]毫秒
2025-08-11 22:20:13 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[459]毫秒
2025-08-11 22:22:06 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:22:06 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:22:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[144]毫秒
2025-08-11 22:22:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[562]毫秒
2025-08-11 22:22:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:22:16 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:22:16 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[154]毫秒
2025-08-11 22:22:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[440]毫秒
2025-08-11 22:22:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:22:33 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:22:34 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[141]毫秒
2025-08-11 22:22:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[435]毫秒
2025-08-11 22:22:39 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:22:39 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:22:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[174]毫秒
2025-08-11 22:22:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[912]毫秒
2025-08-11 22:23:42 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:23:42 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:23:42 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[154]毫秒
2025-08-11 22:23:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[591]毫秒
2025-08-11 22:24:47 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:24:47 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:24:47 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[158]毫秒
2025-08-11 22:24:47 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[575]毫秒
2025-08-11 22:24:50 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:24:50 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:24:51 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[119]毫秒
2025-08-11 22:24:51 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[358]毫秒
2025-08-11 22:25:15 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:25:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:25:15 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[158]毫秒
2025-08-11 22:25:16 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[434]毫秒
2025-08-11 22:25:54 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:25:54 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:25:54 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[154]毫秒
2025-08-11 22:25:55 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[558]毫秒
2025-08-11 22:28:56 [boundedElastic-747] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:28:56 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:28:56 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[150]毫秒
2025-08-11 22:28:56 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[573]毫秒
2025-08-11 22:30:02 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:30:02 [boundedElastic-748] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:30:03 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[159]毫秒
2025-08-11 22:30:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[589]毫秒
2025-08-11 22:31:09 [boundedElastic-744] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:31:09 [boundedElastic-748] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:31:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[144]毫秒
2025-08-11 22:31:10 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[555]毫秒
2025-08-11 22:31:36 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:31:36 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:31:36 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[174]毫秒
2025-08-11 22:31:37 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[439]毫秒
2025-08-11 22:33:08 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:33:08 [boundedElastic-746] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:33:08 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[155]毫秒
2025-08-11 22:33:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[571]毫秒
2025-08-11 22:33:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:33:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:33:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[145]毫秒
2025-08-11 22:33:24 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[437]毫秒
2025-08-11 22:34:14 [boundedElastic-743] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:34:14 [boundedElastic-748] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:34:14 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[147]毫秒
2025-08-11 22:34:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[561]毫秒
2025-08-11 22:37:55 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:37:55 [boundedElastic-743] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:37:55 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[166]毫秒
2025-08-11 22:37:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[579]毫秒
2025-08-11 22:38:40 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:38:40 [boundedElastic-752] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:38:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[159]毫秒
2025-08-11 22:38:41 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[588]毫秒
2025-08-11 22:39:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:39:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:39:13 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[136]毫秒
2025-08-11 22:39:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[565]毫秒
2025-08-11 22:39:37 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:39:37 [boundedElastic-752] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:39:37 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[156]毫秒
2025-08-11 22:39:37 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[453]毫秒
2025-08-11 22:40:41 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:40:41 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:40:41 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[134]毫秒
2025-08-11 22:40:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[574]毫秒
2025-08-11 22:41:09 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:41:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:41:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[95]毫秒
2025-08-11 22:41:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[454]毫秒
2025-08-11 22:42:02 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0a1hnv0w35hgq53n0L2w3rlg0F0hnv0y","grantType":"mini","tenantId":"1"}]
2025-08-11 22:42:04 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[2203]毫秒
2025-08-11 22:42:04 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 22:42:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 22:42:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[187]毫秒
2025-08-11 22:42:05 [boundedElastic-742] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:05 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[804]毫秒
2025-08-11 22:42:06 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[481]毫秒
2025-08-11 22:42:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:42:11 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:42:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:42:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:42:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:42:11 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[139]毫秒
2025-08-11 22:42:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[362]毫秒
2025-08-11 22:42:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[240]毫秒
2025-08-11 22:42:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[489]毫秒
2025-08-11 22:42:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:42:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[792]毫秒
2025-08-11 22:42:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[318]毫秒
2025-08-11 22:42:12 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:42:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[608]毫秒
2025-08-11 22:42:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[492]毫秒
2025-08-11 22:42:23 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 22:42:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 22:42:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[225]毫秒
2025-08-11 22:42:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[452]毫秒
2025-08-11 22:42:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:27 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[802]毫秒
2025-08-11 22:42:30 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 22:42:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[433]毫秒
2025-08-11 22:42:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[674]毫秒
2025-08-11 22:42:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:42:37 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:42:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[143]毫秒
2025-08-11 22:42:38 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[586]毫秒
2025-08-11 22:42:47 [boundedElastic-743] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:48 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[586]毫秒
2025-08-11 22:42:51 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:51 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[398]毫秒
2025-08-11 22:42:53 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[493]毫秒
2025-08-11 22:42:55 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:55 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[407]毫秒
2025-08-11 22:42:56 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:57 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[405]毫秒
2025-08-11 22:42:58 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:58 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[440]毫秒
2025-08-11 22:42:59 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:42:59 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[482]毫秒
2025-08-11 22:43:01 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 22:43:02 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[517]毫秒
2025-08-11 22:43:03 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 22:43:04 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[505]毫秒
2025-08-11 22:43:05 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 22:43:05 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[465]毫秒
2025-08-11 22:43:06 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 22:43:07 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[456]毫秒
2025-08-11 22:43:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:43:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[586]毫秒
2025-08-11 22:43:16 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:43:16 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:43:16 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:43:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:43:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:43:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[129]毫秒
2025-08-11 22:43:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[96]毫秒
2025-08-11 22:43:17 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[224]毫秒
2025-08-11 22:43:17 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[533]毫秒
2025-08-11 22:43:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:43:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[352]毫秒
2025-08-11 22:43:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[1011]毫秒
2025-08-11 22:43:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:43:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[193]毫秒
2025-08-11 22:46:35 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:46:35 [boundedElastic-755] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:46:35 [boundedElastic-752] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:46:35 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:46:35 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:46:36 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[86]毫秒
2025-08-11 22:46:36 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[275]毫秒
2025-08-11 22:46:36 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[184]毫秒
2025-08-11 22:46:36 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[495]毫秒
2025-08-11 22:46:36 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:46:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[965]毫秒
2025-08-11 22:46:36 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[378]毫秒
2025-08-11 22:46:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:46:37 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[186]毫秒
2025-08-11 22:46:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:46:41 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:46:41 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[132]毫秒
2025-08-11 22:46:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[172]毫秒
2025-08-11 22:46:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[59]毫秒
2025-08-11 22:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:46:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[472]毫秒
2025-08-11 22:46:41 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[535]毫秒
2025-08-11 22:46:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:46:42 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[346]毫秒
2025-08-11 22:46:42 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[148]毫秒
2025-08-11 22:47:06 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:47:06 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:47:06 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:47:06 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:47:06 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[124]毫秒
2025-08-11 22:47:06 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:47:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[258]毫秒
2025-08-11 22:47:06 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[267]毫秒
2025-08-11 22:47:06 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[534]毫秒
2025-08-11 22:47:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:47:07 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[845]毫秒
2025-08-11 22:47:07 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[349]毫秒
2025-08-11 22:47:07 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:47:08 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[233]毫秒
2025-08-11 22:47:17 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:47:17 [boundedElastic-755] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:47:17 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:47:17 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:47:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:47:17 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[126]毫秒
2025-08-11 22:47:17 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[172]毫秒
2025-08-11 22:47:17 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[86]毫秒
2025-08-11 22:47:18 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:47:18 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[452]毫秒
2025-08-11 22:47:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[487]毫秒
2025-08-11 22:47:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:47:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[344]毫秒
2025-08-11 22:47:18 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[158]毫秒
2025-08-11 22:47:46 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:47:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:47:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:47:46 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:47:46 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[125]毫秒
2025-08-11 22:47:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:47:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[252]毫秒
2025-08-11 22:47:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[125]毫秒
2025-08-11 22:47:46 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[495]毫秒
2025-08-11 22:47:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:47:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[951]毫秒
2025-08-11 22:47:47 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[337]毫秒
2025-08-11 22:47:47 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:47:47 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[200]毫秒
2025-08-11 22:48:00 [boundedElastic-757] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:48:00 [boundedElastic-752] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:48:00 [boundedElastic-755] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:48:00 [boundedElastic-723] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:48:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:48:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[176]毫秒
2025-08-11 22:48:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[217]毫秒
2025-08-11 22:48:00 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[107]毫秒
2025-08-11 22:48:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:48:00 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[457]毫秒
2025-08-11 22:48:00 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[523]毫秒
2025-08-11 22:48:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:48:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[138]毫秒
2025-08-11 22:48:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[340]毫秒
2025-08-11 22:50:22 [boundedElastic-759] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1LhTFa1xkE5K0iNzHa1RBnx81LhTFV","grantType":"mini","tenantId":"1"}]
2025-08-11 22:50:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1767]毫秒
2025-08-11 22:50:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 22:50:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 22:50:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[205]毫秒
2025-08-11 22:50:25 [boundedElastic-759] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:50:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[816]毫秒
2025-08-11 22:50:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[438]毫秒
2025-08-11 22:53:23 [boundedElastic-757] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1Kg8100EKBMU1VHl100qb9Sm0Kg81F","grantType":"mini","tenantId":"1"}]
2025-08-11 22:53:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1632]毫秒
2025-08-11 22:53:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 22:53:25 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 22:53:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[190]毫秒
2025-08-11 22:53:26 [boundedElastic-755] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:53:26 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[786]毫秒
2025-08-11 22:53:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[435]毫秒
2025-08-11 22:53:34 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 22:53:34 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 22:53:34 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 22:53:34 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 22:53:35 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 22:53:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[213]毫秒
2025-08-11 22:53:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[213]毫秒
2025-08-11 22:53:35 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[98]毫秒
2025-08-11 22:53:35 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[452]毫秒
2025-08-11 22:53:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[500]毫秒
2025-08-11 22:53:35 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 22:53:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 22:53:35 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[233]毫秒
2025-08-11 22:53:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[360]毫秒
2025-08-11 22:55:26 [boundedElastic-752] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:55:27 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[632]毫秒
2025-08-11 22:55:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:55:31 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[823]毫秒
2025-08-11 22:55:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:55:33 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 22:55:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[448]毫秒
2025-08-11 22:55:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[740]毫秒
2025-08-11 22:55:37 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:55:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:55:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[134]毫秒
2025-08-11 22:55:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[370]毫秒
2025-08-11 22:59:19 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 22:59:19 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:59:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 22:59:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 22:59:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[98]毫秒
2025-08-11 22:59:20 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[576]毫秒
2025-08-11 22:59:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[344]毫秒
2025-08-11 22:59:20 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[840]毫秒
2025-08-11 22:59:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 22:59:21 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[730]毫秒
2025-08-11 23:00:11 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:00:11 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:00:11 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[147]毫秒
2025-08-11 23:00:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[576]毫秒
2025-08-11 23:01:09 [boundedElastic-761] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:01:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[227]毫秒
2025-08-11 23:01:10 [boundedElastic-761] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:01:10 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[499]毫秒
2025-08-11 23:02:49 [boundedElastic-760] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:02:49 [boundedElastic-762] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:02:49 [boundedElastic-758] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:02:49 [boundedElastic-760] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:02:49 [boundedElastic-762] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:02:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[139]毫秒
2025-08-11 23:02:49 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[263]毫秒
2025-08-11 23:02:49 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[266]毫秒
2025-08-11 23:02:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[516]毫秒
2025-08-11 23:02:50 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:02:50 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[959]毫秒
2025-08-11 23:02:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[377]毫秒
2025-08-11 23:02:50 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:02:51 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[185]毫秒
2025-08-11 23:03:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:03:05 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:03:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:03:05 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:03:05 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:03:05 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:03:05 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-11 23:03:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[124]毫秒
2025-08-11 23:03:05 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[124]毫秒
2025-08-11 23:03:05 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[433]毫秒
2025-08-11 23:03:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:03:05 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[397]毫秒
2025-08-11 23:03:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[452]毫秒
2025-08-11 23:03:05 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:03:06 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[360]毫秒
2025-08-11 23:03:06 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[212]毫秒
2025-08-11 23:03:11 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:03:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:03:11 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:03:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:03:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:03:12 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[158]毫秒
2025-08-11 23:03:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:03:12 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-11 23:03:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[175]毫秒
2025-08-11 23:03:12 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:03:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[392]毫秒
2025-08-11 23:03:12 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[435]毫秒
2025-08-11 23:03:12 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[503]毫秒
2025-08-11 23:03:12 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:03:12 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[339]毫秒
2025-08-11 23:03:12 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[141]毫秒
2025-08-11 23:04:23 [boundedElastic-763] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:04:23 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:04:23 [boundedElastic-762] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:04:23 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:04:23 [boundedElastic-758] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:04:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[129]毫秒
2025-08-11 23:04:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[259]毫秒
2025-08-11 23:04:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[259]毫秒
2025-08-11 23:04:23 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[509]毫秒
2025-08-11 23:04:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:04:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[956]毫秒
2025-08-11 23:04:24 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[371]毫秒
2025-08-11 23:04:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:04:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[184]毫秒
2025-08-11 23:05:41 [boundedElastic-762] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:05:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[224]毫秒
2025-08-11 23:05:42 [boundedElastic-762] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:05:42 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[506]毫秒
2025-08-11 23:05:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:05:46 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:05:46 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:05:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:05:46 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:05:46 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[85]毫秒
2025-08-11 23:05:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[141]毫秒
2025-08-11 23:05:46 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[149]毫秒
2025-08-11 23:05:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[457]毫秒
2025-08-11 23:05:47 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:05:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[916]毫秒
2025-08-11 23:05:47 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[328]毫秒
2025-08-11 23:05:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:05:47 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[241]毫秒
2025-08-11 23:07:11 [boundedElastic-763] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:07:11 [boundedElastic-760] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:07:11 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:07:11 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:07:11 [boundedElastic-758] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:07:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[136]毫秒
2025-08-11 23:07:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[268]毫秒
2025-08-11 23:07:11 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[268]毫秒
2025-08-11 23:07:12 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[505]毫秒
2025-08-11 23:07:12 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:07:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[972]毫秒
2025-08-11 23:07:12 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[392]毫秒
2025-08-11 23:07:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:07:13 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[173]毫秒
2025-08-11 23:07:13 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:07:13 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:07:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:07:13 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:07:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:07:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[83]毫秒
2025-08-11 23:07:13 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[93]毫秒
2025-08-11 23:07:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[102]毫秒
2025-08-11 23:07:13 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[143]毫秒
2025-08-11 23:07:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:07:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[340]毫秒
2025-08-11 23:07:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[46]毫秒
2025-08-11 23:07:13 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:07:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[151]毫秒
2025-08-11 23:07:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:07:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:07:15 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:07:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:07:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:07:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[50]毫秒
2025-08-11 23:07:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[99]毫秒
2025-08-11 23:07:15 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[91]毫秒
2025-08-11 23:07:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[108]毫秒
2025-08-11 23:07:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:07:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[57]毫秒
2025-08-11 23:07:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[403]毫秒
2025-08-11 23:07:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:07:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[156]毫秒
2025-08-11 23:08:24 [boundedElastic-766] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0a1ZYw0w36Khq5348U0w3zeHd90ZYw05","grantType":"mini","tenantId":"1"}]
2025-08-11 23:08:26 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1739]毫秒
2025-08-11 23:08:26 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 23:08:26 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:08:26 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[188]毫秒
2025-08-11 23:08:27 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[763]毫秒
2025-08-11 23:08:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:08:45 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[139]毫秒
2025-08-11 23:08:46 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:08:46 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[450]毫秒
2025-08-11 23:08:49 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:08:49 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[390]毫秒
2025-08-11 23:08:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:08:52 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:08:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:08:52 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:08:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:08:52 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[132]毫秒
2025-08-11 23:08:52 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[220]毫秒
2025-08-11 23:08:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[220]毫秒
2025-08-11 23:08:53 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[463]毫秒
2025-08-11 23:08:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[513]毫秒
2025-08-11 23:08:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:08:53 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:08:53 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[191]毫秒
2025-08-11 23:08:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[339]毫秒
2025-08-11 23:09:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/gift/list],参数类型[json],参数:[null]
2025-08-11 23:09:02 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-11 23:09:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[129]毫秒
2025-08-11 23:09:02 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/gift/list],耗时:[229]毫秒
2025-08-11 23:09:05 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 23:09:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[97]毫秒
2025-08-11 23:09:09 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 23:09:09 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[132]毫秒
2025-08-11 23:09:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 23:09:14 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[98]毫秒
2025-08-11 23:09:22 [boundedElastic-758] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500498118180865"}]
2025-08-11 23:09:22 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[766]毫秒
2025-08-11 23:09:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500515973332994"}]
2025-08-11 23:09:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500498118180865"}]
2025-08-11 23:09:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[49]毫秒
2025-08-11 23:09:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[609]毫秒
2025-08-11 23:10:50 [boundedElastic-765] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/cancel],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:10:51 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/cancel],耗时:[484]毫秒
2025-08-11 23:10:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:10:53 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/create],耗时:[520]毫秒
2025-08-11 23:10:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-11 23:10:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[133]毫秒
2025-08-11 23:11:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[624]毫秒
2025-08-11 23:11:16 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:11:17 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[140]毫秒
2025-08-11 23:11:17 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:11:17 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[482]毫秒
2025-08-11 23:11:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:21 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[823]毫秒
2025-08-11 23:11:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 23:11:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[391]毫秒
2025-08-11 23:11:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[643]毫秒
2025-08-11 23:11:28 [boundedElastic-767] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:29 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[720]毫秒
2025-08-11 23:11:31 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:11:31 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:11:31 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[102]毫秒
2025-08-11 23:11:31 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[373]毫秒
2025-08-11 23:11:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[488]毫秒
2025-08-11 23:11:46 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 23:11:46 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[570]毫秒
2025-08-11 23:11:47 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:47 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[498]毫秒
2025-08-11 23:11:48 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-11 23:11:48 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[515]毫秒
2025-08-11 23:11:49 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:11:49 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[416]毫秒
2025-08-11 23:12:15 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:12:15 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[228]毫秒
2025-08-11 23:12:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:12:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:16 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[458]毫秒
2025-08-11 23:12:16 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[483]毫秒
2025-08-11 23:12:16 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[628]毫秒
2025-08-11 23:12:16 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:17 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[627]毫秒
2025-08-11 23:12:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:12:42 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:12:43 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:43 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[257]毫秒
2025-08-11 23:12:43 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[529]毫秒
2025-08-11 23:12:43 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[452]毫秒
2025-08-11 23:12:43 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[698]毫秒
2025-08-11 23:12:43 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:12:44 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[676]毫秒
2025-08-11 23:15:45 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:15:45 [boundedElastic-770] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:15:45 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:15:45 [boundedElastic-765] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:15:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:15:46 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[93]毫秒
2025-08-11 23:15:46 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[279]毫秒
2025-08-11 23:15:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[279]毫秒
2025-08-11 23:15:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:15:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[725]毫秒
2025-08-11 23:15:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[995]毫秒
2025-08-11 23:15:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[596]毫秒
2025-08-11 23:15:47 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:15:47 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[191]毫秒
2025-08-11 23:16:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:16:07 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:16:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:16:07 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:16:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:16:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[181]毫秒
2025-08-11 23:16:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-11 23:16:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[231]毫秒
2025-08-11 23:16:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:16:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[498]毫秒
2025-08-11 23:16:07 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[507]毫秒
2025-08-11 23:16:07 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:16:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[347]毫秒
2025-08-11 23:16:08 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[154]毫秒
2025-08-11 23:16:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:16:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[403]毫秒
2025-08-11 23:16:14 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:16:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:16:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:16:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:16:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[85]毫秒
2025-08-11 23:16:14 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:16:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[130]毫秒
2025-08-11 23:16:14 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[52]毫秒
2025-08-11 23:16:14 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:16:14 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[447]毫秒
2025-08-11 23:16:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[473]毫秒
2025-08-11 23:16:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:16:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[341]毫秒
2025-08-11 23:16:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[168]毫秒
2025-08-11 23:16:18 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:16:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:16:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:16:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:16:18 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[87]毫秒
2025-08-11 23:16:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:16:18 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-11 23:16:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[211]毫秒
2025-08-11 23:16:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[136]毫秒
2025-08-11 23:16:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:16:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[45]毫秒
2025-08-11 23:16:19 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[987]毫秒
2025-08-11 23:16:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:16:20 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[192]毫秒
2025-08-11 23:17:00 [boundedElastic-772] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[603]毫秒
2025-08-11 23:17:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:17:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:17:03 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:17:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:17:03 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[85]毫秒
2025-08-11 23:17:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:17:03 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[134]毫秒
2025-08-11 23:17:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[92]毫秒
2025-08-11 23:17:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[460]毫秒
2025-08-11 23:17:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:17:04 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[846]毫秒
2025-08-11 23:17:04 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[333]毫秒
2025-08-11 23:17:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:17:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[185]毫秒
2025-08-11 23:17:09 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:17:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:17:09 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:17:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:17:10 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:17:10 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[134]毫秒
2025-08-11 23:17:10 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[184]毫秒
2025-08-11 23:17:10 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[98]毫秒
2025-08-11 23:17:10 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:17:10 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[494]毫秒
2025-08-11 23:17:10 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[514]毫秒
2025-08-11 23:17:10 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:17:10 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[334]毫秒
2025-08-11 23:17:10 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[140]毫秒
2025-08-11 23:17:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:13 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[389]毫秒
2025-08-11 23:17:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:17:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:17:14 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:17:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:17:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[127]毫秒
2025-08-11 23:17:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:17:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[169]毫秒
2025-08-11 23:17:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[170]毫秒
2025-08-11 23:17:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:17:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[341]毫秒
2025-08-11 23:17:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:17:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[636]毫秒
2025-08-11 23:17:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[335]毫秒
2025-08-11 23:17:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[155]毫秒
2025-08-11 23:17:16 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:17 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[402]毫秒
2025-08-11 23:17:26 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:26 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[855]毫秒
2025-08-11 23:17:27 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:17:27 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[389]毫秒
2025-08-11 23:17:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[530]毫秒
2025-08-11 23:18:20 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:18:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[919]毫秒
2025-08-11 23:18:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:18:25 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[467]毫秒
2025-08-11 23:18:26 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:18:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[415]毫秒
2025-08-11 23:18:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:18:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:18:30 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:18:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:18:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[86]毫秒
2025-08-11 23:18:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:18:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[97]毫秒
2025-08-11 23:18:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[307]毫秒
2025-08-11 23:18:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[551]毫秒
2025-08-11 23:18:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:18:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[942]毫秒
2025-08-11 23:18:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[386]毫秒
2025-08-11 23:18:31 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:18:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[183]毫秒
2025-08-11 23:18:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/basic-info],参数类型[json],参数:[null]
2025-08-11 23:18:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/lit-list],参数类型[json],参数:[null]
2025-08-11 23:18:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/lit-list],耗时:[284]毫秒
2025-08-11 23:18:52 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/basic-info],耗时:[285]毫秒
2025-08-11 23:21:34 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:21:34 [boundedElastic-770] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:21:34 [boundedElastic-773] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:21:34 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:21:34 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[122]毫秒
2025-08-11 23:21:34 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:21:34 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[254]毫秒
2025-08-11 23:21:34 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[128]毫秒
2025-08-11 23:21:35 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[500]毫秒
2025-08-11 23:21:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:21:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[962]毫秒
2025-08-11 23:21:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[336]毫秒
2025-08-11 23:21:35 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:21:36 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[197]毫秒
2025-08-11 23:21:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:21:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:21:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:21:57 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:21:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[129]毫秒
2025-08-11 23:21:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:21:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[179]毫秒
2025-08-11 23:21:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[58]毫秒
2025-08-11 23:21:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:21:58 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[461]毫秒
2025-08-11 23:21:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[487]毫秒
2025-08-11 23:21:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:21:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[338]毫秒
2025-08-11 23:21:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[144]毫秒
2025-08-11 23:22:12 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:22:12 [boundedElastic-773] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:22:12 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:22:12 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:22:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[124]毫秒
2025-08-11 23:22:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:22:12 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[249]毫秒
2025-08-11 23:22:12 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[124]毫秒
2025-08-11 23:22:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[501]毫秒
2025-08-11 23:22:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:22:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[938]毫秒
2025-08-11 23:22:13 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[342]毫秒
2025-08-11 23:22:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:22:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[184]毫秒
2025-08-11 23:22:53 [boundedElastic-753] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:22:54 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[615]毫秒
2025-08-11 23:22:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:22:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[784]毫秒
2025-08-11 23:22:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:22:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 23:22:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[385]毫秒
2025-08-11 23:22:58 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[658]毫秒
2025-08-11 23:23:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:23:00 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:23:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[161]毫秒
2025-08-11 23:23:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[453]毫秒
2025-08-11 23:27:38 [boundedElastic-770] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:27:38 [boundedElastic-777] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:27:39 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[147]毫秒
2025-08-11 23:27:39 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[593]毫秒
2025-08-11 23:28:35 [boundedElastic-778] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:28:35 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:28:35 [boundedElastic-772] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:28:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:28:35 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:28:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[127]毫秒
2025-08-11 23:28:36 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[262]毫秒
2025-08-11 23:28:36 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[136]毫秒
2025-08-11 23:28:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[496]毫秒
2025-08-11 23:28:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:28:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[330]毫秒
2025-08-11 23:28:36 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[966]毫秒
2025-08-11 23:28:37 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:28:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[232]毫秒
2025-08-11 23:28:39 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:28:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[418]毫秒
2025-08-11 23:28:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:28:42 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[830]毫秒
2025-08-11 23:28:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:28:42 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:28:42 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[131]毫秒
2025-08-11 23:28:42 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:28:43 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[558]毫秒
2025-08-11 23:28:43 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[410]毫秒
2025-08-11 23:28:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:28:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:28:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[96]毫秒
2025-08-11 23:28:47 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[451]毫秒
2025-08-11 23:30:04 [boundedElastic-778] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:30:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[617]毫秒
2025-08-11 23:30:06 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:30:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:30:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:30:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:30:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-11 23:30:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[145]毫秒
2025-08-11 23:30:06 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[144]毫秒
2025-08-11 23:30:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:30:06 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:30:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[652]毫秒
2025-08-11 23:30:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[393]毫秒
2025-08-11 23:30:07 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[813]毫秒
2025-08-11 23:30:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:30:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[195]毫秒
2025-08-11 23:30:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:30:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[466]毫秒
2025-08-11 23:30:16 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:30:16 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:30:16 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[93]毫秒
2025-08-11 23:30:17 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[423]毫秒
2025-08-11 23:30:28 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:30:29 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[479]毫秒
2025-08-11 23:30:30 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:30:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:30:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:30:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:30:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[82]毫秒
2025-08-11 23:30:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[139]毫秒
2025-08-11 23:30:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:30:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[52]毫秒
2025-08-11 23:30:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:30:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[404]毫秒
2025-08-11 23:30:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[456]毫秒
2025-08-11 23:30:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:30:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[351]毫秒
2025-08-11 23:30:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[149]毫秒
2025-08-11 23:30:32 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:30:32 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[392]毫秒
2025-08-11 23:33:04 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:33:04 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[624]毫秒
2025-08-11 23:33:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:33:06 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[402]毫秒
2025-08-11 23:33:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-11 23:33:09 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[173]毫秒
2025-08-11 23:33:10 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 23:33:10 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[214]毫秒
2025-08-11 23:33:48 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 23:33:48 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[293]毫秒
2025-08-11 23:35:03 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:35:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 23:35:04 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:35:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[881]毫秒
2025-08-11 23:35:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[393]毫秒
2025-08-11 23:35:04 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[499]毫秒
2025-08-11 23:35:07 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:35:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:35:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[91]毫秒
2025-08-11 23:35:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[355]毫秒
2025-08-11 23:35:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:35:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:35:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[142]毫秒
2025-08-11 23:35:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[467]毫秒
2025-08-11 23:35:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:35:27 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:35:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[103]毫秒
2025-08-11 23:35:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[344]毫秒
2025-08-11 23:39:39 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:39:39 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:39:40 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[142]毫秒
2025-08-11 23:39:40 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[556]毫秒
2025-08-11 23:40:40 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-11 23:40:40 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[315]毫秒
2025-08-11 23:40:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:40:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:40:41 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:40:41 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[95]毫秒
2025-08-11 23:40:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[347]毫秒
2025-08-11 23:40:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[717]毫秒
2025-08-11 23:40:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:40:42 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[548]毫秒
2025-08-11 23:42:55 [boundedElastic-784] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0e1oooFa1YNe6K0XybGa1sP4Fy1oooFs","grantType":"mini","tenantId":"1"}]
2025-08-11 23:42:57 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1742]毫秒
2025-08-11 23:42:57 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 23:42:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:42:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[173]毫秒
2025-08-11 23:42:58 [boundedElastic-751] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:42:58 [boundedElastic-780] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:42:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:42:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:42:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:42:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[101]毫秒
2025-08-11 23:42:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[180]毫秒
2025-08-11 23:42:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[178]毫秒
2025-08-11 23:42:58 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[740]毫秒
2025-08-11 23:42:58 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[391]毫秒
2025-08-11 23:42:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[533]毫秒
2025-08-11 23:42:58 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:42:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:42:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[189]毫秒
2025-08-11 23:42:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[339]毫秒
2025-08-11 23:43:32 [boundedElastic-785] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0e198500055yLU1XrC100lD32l19850b","grantType":"mini","tenantId":"1"}]
2025-08-11 23:43:33 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1550]毫秒
2025-08-11 23:43:33 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:43:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[169]毫秒
2025-08-11 23:43:34 [boundedElastic-785] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:43:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:43:34 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:43:34 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:43:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[121]毫秒
2025-08-11 23:43:34 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:43:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[171]毫秒
2025-08-11 23:43:34 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[88]毫秒
2025-08-11 23:43:34 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[498]毫秒
2025-08-11 23:43:34 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:43:35 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[941]毫秒
2025-08-11 23:43:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[352]毫秒
2025-08-11 23:43:35 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:43:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[230]毫秒
2025-08-11 23:44:16 [boundedElastic-781] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:44:17 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[628]毫秒
2025-08-11 23:44:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:44:20 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:44:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:44:20 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:44:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:44:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[84]毫秒
2025-08-11 23:44:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[135]毫秒
2025-08-11 23:44:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[203]毫秒
2025-08-11 23:44:20 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[474]毫秒
2025-08-11 23:44:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:44:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[816]毫秒
2025-08-11 23:44:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[332]毫秒
2025-08-11 23:44:21 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:44:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[192]毫秒
2025-08-11 23:44:22 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:44:22 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[483]毫秒
2025-08-11 23:44:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:44:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[137]毫秒
2025-08-11 23:44:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:44:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[460]毫秒
2025-08-11 23:44:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:44:25 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[727]毫秒
2025-08-11 23:44:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:44:27 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 23:44:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[428]毫秒
2025-08-11 23:44:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[704]毫秒
2025-08-11 23:44:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:44:30 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:44:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[95]毫秒
2025-08-11 23:44:31 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[373]毫秒
2025-08-11 23:46:14 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:14 [boundedElastic-780] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:46:14 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:46:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[278]毫秒
2025-08-11 23:46:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[543]毫秒
2025-08-11 23:46:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:46:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[539]毫秒
2025-08-11 23:46:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:46:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[102]毫秒
2025-08-11 23:46:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[331]毫秒
2025-08-11 23:46:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[645]毫秒
2025-08-11 23:46:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:16 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[507]毫秒
2025-08-11 23:46:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:46:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:46:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:24 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:24 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[171]毫秒
2025-08-11 23:46:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[467]毫秒
2025-08-11 23:46:25 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:46:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:46:25 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[464]毫秒
2025-08-11 23:46:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[109]毫秒
2025-08-11 23:46:25 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[646]毫秒
2025-08-11 23:46:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[264]毫秒
2025-08-11 23:46:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:26 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[518]毫秒
2025-08-11 23:46:51 [boundedElastic-784] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:51 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-11 23:46:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-11 23:46:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[285]毫秒
2025-08-11 23:46:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[516]毫秒
2025-08-11 23:46:52 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:46:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:46:52 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[457]毫秒
2025-08-11 23:46:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[118]毫秒
2025-08-11 23:46:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[310]毫秒
2025-08-11 23:46:52 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[703]毫秒
2025-08-11 23:46:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:46:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[504]毫秒
2025-08-11 23:48:22 [boundedElastic-785] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1ZLFFa1BLv6K0ElyIa1nBqOu0ZLFFy","grantType":"mini","tenantId":"1"}]
2025-08-11 23:48:28 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1tw71w3nuXr53Nsv0w3lRtG62tw716","grantType":"mini","tenantId":"1"}]
2025-08-11 23:48:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1414]毫秒
2025-08-11 23:48:29 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 23:48:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:48:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[177]毫秒
2025-08-11 23:48:30 [boundedElastic-784] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:48:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:48:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:48:30 [boundedElastic-784] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:48:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:48:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[100]毫秒
2025-08-11 23:48:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[168]毫秒
2025-08-11 23:48:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[170]毫秒
2025-08-11 23:48:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[748]毫秒
2025-08-11 23:48:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[401]毫秒
2025-08-11 23:48:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:48:30 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:48:30 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[738]毫秒
2025-08-11 23:48:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[190]毫秒
2025-08-11 23:48:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[584]毫秒
2025-08-11 23:49:43 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:49:43 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[587]毫秒
2025-08-11 23:50:00 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1hzQ0w3jpAq534dF3w35WD7B4hzQ0w","grantType":"mini","tenantId":"1"}]
2025-08-11 23:50:02 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1589]毫秒
2025-08-11 23:50:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-11 23:50:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-11 23:50:02 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[86]毫秒
2025-08-11 23:50:02 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-11 23:50:02 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-11 23:50:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-11 23:50:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-11 23:50:02 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-11 23:50:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[128]毫秒
2025-08-11 23:50:03 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[208]毫秒
2025-08-11 23:50:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[210]毫秒
2025-08-11 23:50:03 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[765]毫秒
2025-08-11 23:50:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[406]毫秒
2025-08-11 23:50:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[551]毫秒
2025-08-11 23:50:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-11 23:50:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-11 23:50:03 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[204]毫秒
2025-08-11 23:50:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[377]毫秒
2025-08-11 23:50:07 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-11 23:50:08 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[493]毫秒
2025-08-11 23:50:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:50:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[716]毫秒
2025-08-11 23:50:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-11 23:50:13 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-11 23:50:14 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[429]毫秒
2025-08-11 23:50:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[789]毫秒
2025-08-11 23:50:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:50:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:50:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[99]毫秒
2025-08-11 23:50:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[362]毫秒
2025-08-11 23:54:53 [boundedElastic-788] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:54:53 [boundedElastic-776] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:54:53 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[146]毫秒
2025-08-11 23:54:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[566]毫秒
2025-08-11 23:55:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-11 23:55:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-11 23:55:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[136]毫秒
2025-08-11 23:55:03 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[448]毫秒
