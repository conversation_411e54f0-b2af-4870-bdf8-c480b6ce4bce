package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserBaseBoToUserDetailMapper.class},
    imports = {}
)
public interface AppUserBaseBoToRemoteUserBoMapper extends BaseMapper<AppUserBaseBo, RemoteUserBo> {
}
