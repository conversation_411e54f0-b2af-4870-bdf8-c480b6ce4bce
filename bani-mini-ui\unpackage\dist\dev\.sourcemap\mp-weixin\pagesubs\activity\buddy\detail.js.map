{"version": 3, "file": "detail.js", "sources": ["pagesubs/activity/buddy/detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcYWN0aXZpdHlcYnVkZHlcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"搭子详情\" :show-back=\"true\">\r\n\t\t<template #content>\r\n\t\t\t<view class=\"page-container\">\r\n\t\t\t\t<!-- 活动封面图 -->\r\n\t\t\t\t<image class=\"cover-image\" :src=\"activityDetail.backgroundImage\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<!-- 活动内容 -->\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<!-- 用户信息 -->\r\n\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t<view class=\"user-left\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"activityDetail.oppAvatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<view class=\"user-detail\">\r\n\t\t\t\t\t\t\t\t<text class=\"name\">{{ activityDetail.oppNickName }}</text>\r\n\t\t\t\t\t\t\t\t<view class=\"tags\">\r\n\t\t\t\t\t\t\t\t\t<uni-tag :text=\"activityTypes.find(t => t.value === activityDetail.classify)?.label\"\r\n\t\t\t\t\t\t\t\t\t\ttype=\"primary\" :inverted=\"false\" size=\"small\" :color=\"'#696CF3'\" />\r\n\t\t\t\t\t\t\t\t\t<uni-tag v-if=\"activityDetail.oppIsIdentity\" text=\"已实名\" type=\"success\"\r\n\t\t\t\t\t\t\t\t\t\t:inverted=\"false\" size=\"small\" :color=\"'#52c41a'\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"follow-btn\" :class=\"{\r\n\t\t\t\t\t\t\tfollowed: activityDetail.oppIsFollowed,\r\n\t\t\t\t\t\t\tloading: isFollowLoading\r\n\t\t\t\t\t\t}\" :disabled=\"isFollowLoading\" @click=\"handleFollow\">\r\n\t\t\t\t\t\t\t<view v-if=\"isFollowLoading\" class=\"loading-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"spinner-cycle\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text v-else>{{ activityDetail.oppIsFollowed ? '已关注' : '关注' }}</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 活动标题 -->\r\n\t\t\t\t\t<view class=\"title-section\">\r\n\t\t\t\t\t\t<view class=\"title-left\">\r\n\t\t\t\t\t\t\t<text class=\"title\">{{ activityDetail.name }}</text>\r\n\t\t\t\t\t\t\t<view class=\"status-tag\" :class=\"getStatusClass(activityDetail.status)\">\r\n\t\t\t\t\t\t\t\t{{ getStatusText(activityDetail.status) }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"title-right\">\r\n\t\t\t\t\t\t\t<text class=\"publish-time\">{{ activityDetail.createTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 活动信息 -->\r\n\t\t\t\t\t<view class=\"info-section\">\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-position info-icon\"></text>\r\n\t\t\t\t\t\t\t<text class=\"info-text\">地址：{{ activityDetail.address }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-rili info-icon\"></text>\r\n\t\t\t\t\t\t\t<text class=\"info-text\">活动开始时间：{{ activityDetail.startTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\" v-if=\"activityDetail.timeLength\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-time info-icon\"></text>\r\n\t\t\t\t\t\t\t<text class=\"info-text\">活动时长：{{ activityDetail.timeLength }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-jine info-icon\"></text>\r\n\t\t\t\t\t\t\t<text class=\"info-text\">费用：{{ activityDetail.amount > 0 ? `¥${activityDetail.amount}` : '免费'\r\n\t\t\t\t\t\t\t\t}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"info-item\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-yonghu info-icon\"></text>\r\n\t\t\t\t\t\t\t<text class=\"info-text\">已报名：{{ activityDetail.enrollNum }}{{\r\n\t\t\t\t\t\t\t\tactivityDetail.limitNum ? '/' + activityDetail.limitNum : '' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 活动详情 -->\r\n\t\t\t\t\t<view class=\"detail-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">活动详情</text>\r\n\t\t\t\t\t\t<text class=\"detail-text\">{{ activityDetail.introduce }}</text>\r\n\t\t\t\t\t\t<!-- 活动图片 -->\r\n\t\t\t\t\t\t<view v-if=\"activityDetail.introduceImages\" class=\"detail-images\">\r\n\t\t\t\t\t\t\t<image v-for=\"(image, index) in getIntroduceImages()\" :key=\"index\" :src=\"getImageUrl(image)\"\r\n\t\t\t\t\t\t\t\tmode=\"widthFix\" class=\"detail-image\" @error=\"onImageError\" @load=\"onImageLoad\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 报名列表 -->\r\n\t\t\t\t\t<view class=\"enroll-section\">\r\n\t\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">报名列表</text>\r\n\t\t\t\t\t\t\t<text class=\"enroll-count\">({{ enrollUserList.length }}人)</text>\r\n\t\t\t\t\t\t\t<view class=\"refresh-btn\" @click=\"loadEnrollUsers\" :class=\"{ loading: enrollLoading }\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"refreshempty\" size=\"16\"\r\n\t\t\t\t\t\t\t\t\t:color=\"enrollLoading ? '$text-tertiary' : '$primary-color'\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t\t\t<view v-if=\"enrollLoading\" class=\"loading-container\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"spinner-cycle\" size=\"20\" color=\"$primary-color\" />\r\n\t\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 报名用户列表 -->\r\n\t\t\t\t\t\t<view v-else-if=\"enrollUserList.length > 0\" class=\"enroll-list\">\r\n\t\t\t\t\t\t\t<view class=\"enroll-item\" v-for=\"(user, index) in enrollUserList\" :key=\"user.uid || index\">\r\n\t\t\t\t\t\t\t\t<image class=\"enroll-avatar\" :src=\"user.oppAvatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"enroll-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"enroll-name\">{{ user.oppNickName }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"enroll-time\">{{ user.enrollTime }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- 可以根据需要添加实名认证标识 -->\r\n\t\t\t\t\t\t\t\t<!-- <uni-tag v-if=\"user.isVerified\" text=\"已实名\" type=\"success\" :inverted=\"false\" size=\"small\"\r\n\t\t\t\t\t\t\t:color=\"'#52c41a'\" /> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 暂无报名用户 -->\r\n\t\t\t\t\t\t<view v-else class=\"empty-state\">\r\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无报名用户</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 底部操作栏 -->\r\n\t\t\t\t<view class=\"bottom-bar\">\r\n\t\t\t\t\t<view class=\"action-btns\">\r\n\t\t\t\t\t\t<button class=\"share-btn\" @click=\"shareActivity\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-fenxiang action-icon\"></text>\r\n\t\t\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button class=\"more-btn\" @click=\"showMoreActions\">\r\n\t\t\t\t\t\t\t<text class=\"iconfont bani-gengduo action-icon\"></text>\r\n\t\t\t\t\t\t\t<text>更多</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class=\"enroll-btn\" :class=\"{\r\n\t\t\t\t\t\tdisabled: activityDetail.status !== 3 || activityDetail.hasEnrolled,\r\n\t\t\t\t\t\tenrolled: activityDetail.hasEnrolled\r\n\t\t\t\t\t}\" @click=\"enrollActivity\">\r\n\t\t\t\t\t\t{{ getEnrollBtnText() }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onPageScroll, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'\r\nimport { getBuddyActivityDetail, getActivityTypeName, getActivityStatusName, BUDDY_ACTIVITY_TYPES } from '@/api/activity/buddy'\r\nimport {\r\n\tenrollFreeActivity,\r\n\tenrollPaidActivity,\r\n\tgetActivityEnrollUsers\r\n} from '@/api/activity/activityRecord'\r\nimport { toggleUserFollow } from '@/api/my/follow'\r\nimport { getCurrentPagePath } from '@/utils/common.js'\r\n\r\n// 页面参数\r\nconst activityId = ref(null)\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n\r\n// 加载状态\r\nconst loading = ref(false)\r\nconst isFollowLoading = ref(false)\r\n\r\n// 报名用户列表\r\nconst enrollUserList = ref([])\r\nconst enrollLoading = ref(false)\r\n\r\n// 防抖定时器\r\nlet followDebounceTimer = null\r\n\r\n// 活动类型列表\r\nconst activityTypes = [\r\n\t{ value: 0, label: '全部' },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.DATING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DATING) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.CHAT, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.CHAT) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.DINING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DINING) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.OUTDOOR, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.OUTDOOR) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.EXHIBITION, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.EXHIBITION) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.SPORTS, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.SPORTS) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.STUDY, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.STUDY) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.DRINKING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DRINKING) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.GAMING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.GAMING) },\r\n\t{ value: BUDDY_ACTIVITY_TYPES.OTHER, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.OTHER) }\r\n]\r\n\r\n// 活动详情数据\r\nconst activityDetail = ref({})\r\n\r\n// 加载活动详情\r\nconst loadActivityDetail = async () => {\r\n\tif (!activityId.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '活动ID不能为空',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tloading.value = true\r\n\r\n\ttry {\r\n\t\tconsole.log('加载活动详情，ID:', activityId.value)\r\n\t\tconst response = await getBuddyActivityDetail(activityId.value)\r\n\r\n\t\tconsole.log('API响应:', response)\r\n\r\n\t\tif (response.code === 200 && response.data) {\r\n\t\t\t// 直接使用API返回的数据更新活动详情\r\n\t\t\tactivityDetail.value = response.data\r\n\t\t\tconsole.log('活动详情加载成功:', activityDetail.value)\r\n\t\t\tconsole.log('介绍图片字段:', activityDetail.value.introduceImages)\r\n\t\t} else {\r\n\t\t\tconsole.error('加载活动详情失败:', response.msg)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: response.msg || '加载失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('加载活动详情异常:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '网络异常，请稍后重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t} finally {\r\n\t\tloading.value = false\r\n\t}\r\n}\r\n\r\n// 获取报名用户列表\r\nconst loadEnrollUsers = async () => {\r\n\tenrollLoading.value = true\r\n\tgetActivityEnrollUsers({\r\n\t\tactivityId: activityId.value,\r\n\t\tpageNum: 1,\r\n\t\tpageSize: 20\r\n\t}).then(response => {\r\n\t\tenrollUserList.value = response.rows || []\r\n\t}).finally(() => {\r\n\t\tenrollLoading.value = false\r\n\t})\r\n}\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n\t// 使用API中的状态枚举\r\n\tif (typeof status === 'number') {\r\n\t\treturn getActivityStatusName(status)\r\n\t}\r\n\r\n\t// 兼容旧版本的字符串状态\r\n\tconst statusMap = {\r\n\t\tnotStarted: '未开始',\r\n\t\tenrolling: '报名中',\r\n\t\tenrollEnd: '报名已结束',\r\n\t\tinProgress: '活动进行中',\r\n\t\tended: '活动已结束'\r\n\t}\r\n\treturn statusMap[status] || ''\r\n}\r\n\r\n// 获取状态样式类名\r\nconst getStatusClass = (status) => {\r\n\tif (typeof status === 'number') {\r\n\t\t// 根据数字状态返回对应的类名\r\n\t\tconst statusClassMap = {\r\n\t\t\t1: 'notStarted',    // 草稿\r\n\t\t\t2: 'notStarted',    // 已发布/未开始\r\n\t\t\t3: 'enrolling',     // 报名中\r\n\t\t\t4: 'enrollEnd',     // 报名已结束\r\n\t\t\t10: 'inProgress',   // 活动进行中\r\n\t\t\t11: 'ended'         // 活动已结束\r\n\t\t}\r\n\t\treturn statusClassMap[status] || 'notStarted'\r\n\t}\r\n\r\n\t// 兼容旧版本的字符串状态\r\n\treturn status || 'notStarted'\r\n}\r\n\r\n// 解析活动介绍图片\r\nconst getIntroduceImages = () => {\r\n\tconsole.log('getIntroduceImages 被调用')\r\n\tconsole.log('activityDetail.value:', activityDetail.value)\r\n\tconsole.log('introduceImages 字段值:', activityDetail.value.introduceImages)\r\n\r\n\tif (!activityDetail.value.introduceImages) {\r\n\t\tconsole.log('introduceImages 为空，返回空数组')\r\n\t\treturn []\r\n\t}\r\n\r\n\t// 图片用逗号分隔\r\n\tconst images = activityDetail.value.introduceImages.split(',').filter(img => img.trim())\r\n\tconsole.log('解析后的图片数组:', images)\r\n\treturn images\r\n}\r\n\r\n// 获取完整的图片URL\r\nconst getImageUrl = (imageUrl) => {\r\n\tif (!imageUrl) return ''\r\n\r\n\t// 如果已经是完整URL，直接返回\r\n\tif (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {\r\n\t\tconsole.log('完整URL:', imageUrl)\r\n\t\treturn imageUrl\r\n\t}\r\n\r\n\t// 如果是相对路径或OSS ID，需要拼接完整URL\r\n\t// 这里需要根据您的实际情况调整URL拼接逻辑\r\n\tconst baseUrl = 'https://your-oss-domain.com/' // 请替换为实际的OSS域名\r\n\tconst fullUrl = baseUrl + imageUrl\r\n\tconsole.log('拼接后的URL:', fullUrl)\r\n\treturn fullUrl\r\n}\r\n\r\n// 图片加载成功\r\nconst onImageLoad = (e) => {\r\n\tconsole.log('图片加载成功:', e)\r\n}\r\n\r\n// 图片加载失败\r\nconst onImageError = (e) => {\r\n\tconsole.error('图片加载失败:', e)\r\n\tconsole.error('失败的图片src:', e.target.src)\r\n}\r\n\r\n\r\n\r\n// 获取报名按钮文本\r\nconst getEnrollBtnText = () => {\r\n\t// 如果已报名，显示已报名状态\r\n\tif (activityDetail.value.hasEnrolled) {\r\n\t\treturn '已报名'\r\n\t}\r\n\r\n\tconst status = activityDetail.value.status\r\n\tconst statusMap = {\r\n\t\t0: '已下架',\r\n\t\t1: '草稿',\r\n\t\t2: '已发布',\r\n\t\t3: '立即报名',\r\n\t\t4: '报名已结束',\r\n\t\t10: '活动进行中',\r\n\t\t11: '活动已结束'\r\n\t}\r\n\treturn statusMap[status] || '未知状态'\r\n}\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n\tuni.navigateBack()\r\n}\r\n\r\n// 关注用户\r\nconst handleFollow = async () => {\r\n\t// 检查用户信息是否完整\r\n\tif (!activityDetail.value.oppUserId) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '用户信息不完整',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 防止重复点击\r\n\tif (isFollowLoading.value) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// 防抖处理\r\n\tif (followDebounceTimer) {\r\n\t\tclearTimeout(followDebounceTimer)\r\n\t}\r\n\r\n\tfollowDebounceTimer = setTimeout(async () => {\r\n\t\tawait performFollowAction()\r\n\t}, 300)\r\n}\r\n\r\n// 执行关注操作\r\nconst performFollowAction = async () => {\r\n\r\n\ttry {\r\n\t\tisFollowLoading.value = true\r\n\t\tconsole.log('切换关注状态，用户ID:', activityDetail.value.oppUserId)\r\n\r\n\t\tconst response = await toggleUserFollow(activityDetail.value.oppUserId, activityDetail.value.oppIsFollowed)\r\n\r\n\t\tif (response.code === 200) {\r\n\t\t\t// 切换关注状态\r\n\t\t\tconst wasFollowed = activityDetail.value.oppIsFollowed\r\n\t\t\tactivityDetail.value.oppIsFollowed = !activityDetail.value.oppIsFollowed\r\n\r\n\t\t\t// 显示成功提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: activityDetail.value.oppIsFollowed ? '关注成功' : '已取消关注',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 1500\r\n\t\t\t})\r\n\r\n\t\t\t// 如果是关注操作，可以添加一些额外的反馈\r\n\t\t\tif (!wasFollowed && activityDetail.value.oppIsFollowed) {\r\n\t\t\t\tconsole.log('用户关注了:', activityDetail.value.oppNickName)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.error('关注操作失败:', response.msg)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: response.msg || '操作失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('关注操作异常:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '网络异常，请稍后重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t} finally {\r\n\t\tisFollowLoading.value = false\r\n\t}\r\n}\r\n\r\n// 分享活动\r\nconst shareActivity = () => {\r\n\t// 检查是否在微信小程序环境\r\n\t// #ifdef MP-WEIXIN\r\n\tuni.showActionSheet({\r\n\t\titemList: ['分享给好友', '分享到朋友圈', '生成分享海报'],\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.tapIndex === 0) {\r\n\t\t\t\t// 分享给好友\r\n\t\t\t\tshareToFriend()\r\n\t\t\t} else if (res.tapIndex === 1) {\r\n\t\t\t\t// 分享到朋友圈\r\n\t\t\t\tshareToTimeline()\r\n\t\t\t} else if (res.tapIndex === 2) {\r\n\t\t\t\t// 生成分享海报\r\n\t\t\t\tgenerateSharePoster()\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: () => {\r\n\t\t\tconsole.log('取消分享')\r\n\t\t}\r\n\t})\r\n\t// #endif\r\n\r\n\t// #ifndef MP-WEIXIN\r\n\tuni.showToast({\r\n\t\ttitle: '当前环境不支持分享功能',\r\n\t\ticon: 'none'\r\n\t})\r\n\t// #endif\r\n}\r\n\r\n// 分享给好友\r\nconst shareToFriend = () => {\r\n\t// #ifdef MP-WEIXIN\r\n\tconst shareData = {\r\n\t\ttitle: activityDetail.value.name || '精彩活动邀请',\r\n\t\tpath: `/pagesubs/activity/buddy/detail?id=${activityId.value}`,\r\n\t\timageUrl: activityDetail.value.backgroundImage || '',\r\n\t\tsuccess: (res) => {\r\n\t\t\tconsole.log('分享成功', res)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('分享失败', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\t// 调用微信分享API\r\n\tuni.shareAppMessage(shareData)\r\n\t// #endif\r\n}\r\n\r\n// 分享到朋友圈\r\nconst shareToTimeline = () => {\r\n\t// #ifdef MP-WEIXIN\r\n\t// 微信小程序分享到朋友圈需要通过右上角菜单触发\r\n\t// 这里提示用户使用右上角分享功能\r\n\tuni.showModal({\r\n\t\ttitle: '分享到朋友圈',\r\n\t\tcontent: '请点击右上角的\"...\"按钮，选择\"分享到朋友圈\"',\r\n\t\tshowCancel: false,\r\n\t\tconfirmText: '知道了'\r\n\t})\r\n\t// #endif\r\n}\r\n\r\n// 生成分享海报\r\nconst generateSharePoster = () => {\r\n\tuni.showLoading({\r\n\t\ttitle: '生成中...'\r\n\t})\r\n\r\n\t// 这里可以调用海报生成API或使用canvas绘制\r\n\t// 暂时模拟生成过程\r\n\tsetTimeout(() => {\r\n\t\tuni.hideLoading()\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '海报生成功能开发中',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}, 1500)\r\n}\r\n\r\n// 显示更多操作\r\nconst showMoreActions = () => {\r\n\tuni.showActionSheet({\r\n\t\titemList: ['举报'],\r\n\t\tsuccess: (res) => {\r\n\t\t\tconsole.log('选择了第' + (res.tapIndex + 1) + '个按钮')\r\n\t\t\tif (res.tapIndex === 0) {\r\n\t\t\t\t// 举报\r\n\t\t\t\thandleReport()\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: () => {\r\n\t\t\tconsole.log('取消选择')\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 处理举报\r\nconst handleReport = () => {\r\n\t// 跳转到举报页面，传递举报类型为活动(2)和活动ID\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/my/report/report?type=2&targetId=${activityId.value}&targetName=${encodeURIComponent(activityDetail.value.name || '')}`\r\n\t})\r\n}\r\n\r\n// 报名活动\r\nconst enrollActivity = async () => {\r\n\t// 如果已报名，显示提示\r\n\tif (activityDetail.value.hasEnrolled) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '您已报名此活动',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 只有状态为3（报名中）时才能报名\r\n\tif (activityDetail.value.status !== 3) {\r\n\t\tconst statusText = getEnrollBtnText()\r\n\t\tuni.showToast({\r\n\t\t\ttitle: `当前状态：${statusText}，无法报名`,\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!activityId.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '活动ID不能为空',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\ttry {\r\n\t\t// 判断是否为付费活动\r\n\t\tconst amount = activityDetail.value.amount || 0\r\n\r\n\t\tif (amount > 0) {\r\n\t\t\t// 付费活动，需要支付\r\n\t\t\tawait handlePaidEnroll(amount)\r\n\t\t} else {\r\n\t\t\t// 免费活动，直接报名\r\n\t\t\tawait handleFreeEnroll()\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('报名活动异常:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '网络异常，请稍后重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 处理免费活动报名\r\nconst handleFreeEnroll = async () => {\r\n\ttry {\r\n\t\tconsole.log('免费活动报名，活动ID:', activityId.value)\r\n\t\tconst response = await enrollFreeActivity(activityId.value)\r\n\r\n\t\tif (response.code === 200) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '报名成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\r\n\t\t\t// 重新加载活动详情，更新报名人数等信息\r\n\t\t\tawait loadActivityDetail(activityId.value)\r\n\t\t\t// 重新加载报名用户列表\r\n\t\t\tawait loadEnrollUsers()\r\n\t\t} else {\r\n\t\t\tconsole.error('免费活动报名失败:', response.msg)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: response.msg || '报名失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('免费活动报名异常:', error)\r\n\t\tthrow error\r\n\t}\r\n}\r\n\r\n// 处理付费活动报名\r\nconst handlePaidEnroll = async (amount) => {\r\n\ttry {\r\n\t\t// 显示支付确认对话框\r\n\t\tconst result = await uni.showModal({\r\n\t\t\ttitle: '付费活动',\r\n\t\t\tcontent: `此活动需要支付 ¥${amount}，是否继续？`,\r\n\t\t\tconfirmText: '支付',\r\n\t\t\tcancelText: '取消'\r\n\t\t})\r\n\r\n\t\tif (!result.confirm) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tconsole.log('付费活动报名，活动ID:', activityId.value, '金额:', amount)\r\n\r\n\t\t// 计算花瓣数量（假设1元=10花瓣）\r\n\t\tconst coin = amount * 10\r\n\r\n\t\tconst response = await enrollPaidActivity(activityId.value, amount, coin)\r\n\r\n\t\tif (response.code === 200) {\r\n\t\t\t// 如果返回支付数据，需要调起支付\r\n\t\t\tif (response.data && response.data.payData) {\r\n\t\t\t\t// TODO: 调起支付流程\r\n\t\t\t\tconsole.log('支付数据:', response.data.payData)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '支付功能开发中',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '报名成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\r\n\t\t\t\t// 重新加载活动详情\r\n\t\t\t\tawait loadActivityDetail(activityId.value)\r\n\t\t\t\t// 重新加载报名用户列表\r\n\t\t\t\tawait loadEnrollUsers()\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.error('付费活动报名失败:', response.msg)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: response.msg || '报名失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('付费活动报名异常:', error)\r\n\t\tthrow error\r\n\t}\r\n}\r\n\r\n// 处理页面滚动\r\nconst handlePageScroll = (e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n}\r\n\r\n// 页面滚动监听\r\nonPageScroll(handlePageScroll)\r\n\r\n// 页面加载时接收参数\r\nonLoad((options) => {\r\n\tactivityId.value = options.id\r\n})\r\n\r\n// 页面加载时获取活动详情\r\nonMounted(() => {\r\n\t// 获取当前页面完整路径（包含参数）\r\n\tconst currentPagePath = getCurrentPagePath()\r\n\tconsole.log('当前页面完整路径:', currentPagePath)\r\n\r\n\tif (activityId.value) {\r\n\t\tloadActivityDetail()\r\n\t\tloadEnrollUsers()\r\n\t}\r\n})\r\n\r\n// 微信小程序分享给好友配置\r\n// #ifdef MP-WEIXIN\r\nonShareAppMessage(() => {\r\n\treturn {\r\n\t\ttitle: activityDetail.value.name || '精彩活动邀请',\r\n\t\tpath: `/pagesubs/activity/buddy/detail?id=${activityId.value}`,\r\n\t\timageUrl: activityDetail.value.backgroundImage || '',\r\n\t\tsuccess: (res) => {\r\n\t\t\tconsole.log('分享成功', res)\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('分享失败', err)\r\n\t\t}\r\n\t}\r\n})\r\n\r\n// 微信小程序分享到朋友圈配置\r\nonShareTimeline(() => {\r\n\treturn {\r\n\t\ttitle: `${activityDetail.value.name || '精彩活动'} - 快来一起参加吧！`,\r\n\t\tquery: `id=${activityId.value}`,\r\n\t\timageUrl: activityDetail.value.backgroundImage || '',\r\n\t\tsuccess: (res) => {\r\n\t\t\tconsole.log('分享到朋友圈成功', res)\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('分享到朋友圈失败', err)\r\n\t\t}\r\n\t}\r\n})\r\n// #endif\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.nav-left {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: rgba(0, 0, 0, 0.2);\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.nav-title {\r\n\tfont-size: $font-size-lg;\r\n\tfont-weight: $font-weight-bold;\r\n\ttext-align: center;\r\n}\r\n\r\n.cover-image {\r\n\twidth: 100%;\r\n\theight: 400rpx;\r\n}\r\n\r\n.content {\r\n\tpadding: $spacing-lg;\r\n\tbackground: $bg-primary;\r\n\tborder-radius: $radius-lg $radius-lg 0 0;\r\n\tmargin-top: -30rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.user-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: $spacing-lg;\r\n\r\n\t.user-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t\tmargin-right: $spacing-md;\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tmargin-right: $spacing-md;\r\n\t\t\tborder: 2rpx solid $border-color;\r\n\t\t}\r\n\r\n\t\t.user-detail {\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: $font-size-lg;\r\n\t\t\t\tcolor: $text-primary;\r\n\t\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\t\tmargin-bottom: $spacing-xs;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.tags {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tgap: $spacing-sm;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.follow-btn {\r\n\t\tbackground: $primary-gradient;\r\n\t\tcolor: $text-white;\r\n\t\tfont-size: $font-size-xs;\r\n\t\tpadding: $spacing-xs $spacing-md;\r\n\t\tborder-radius: $radius-full;\r\n\t\tborder: none;\r\n\t\tbox-shadow: $shadow-sm;\r\n\t\twhite-space: nowrap;\r\n\t\tmin-width: 100rpx;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\r\n\t\t&::after {\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t&:active:not(:disabled) {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t}\r\n\r\n\t\t&.followed {\r\n\t\t\tbackground: #f5f5f5;\r\n\t\t\tcolor: #666;\r\n\t\t\tborder: 2rpx solid #e0e0e0;\r\n\t\t\tbox-shadow: none;\r\n\t\t}\r\n\r\n\t\t&.loading,\r\n\t\t&:disabled {\r\n\t\t\topacity: 0.6;\r\n\t\t\ttransform: none !important;\r\n\t\t\tpointer-events: none;\r\n\t\t}\r\n\r\n\t\t.loading-icon {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tanimation: spin 1s linear infinite;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes spin {\r\n\t\t0% {\r\n\t\t\ttransform: rotate(0deg);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: rotate(360deg);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.title-section {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: flex-start;\r\n\tmargin-bottom: $spacing-lg;\r\n\r\n\t.title-left {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-start;\r\n\t\tflex: 1;\r\n\t\tmargin-right: $spacing-md;\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: $font-size-xl;\r\n\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\tcolor: $text-primary;\r\n\t\t\tmargin-bottom: $spacing-sm;\r\n\t\t}\r\n\t}\r\n\r\n\t.title-right {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: flex-end;\r\n\r\n\t\t.publish-time {\r\n\t\t\tfont-size: $font-size-sm;\r\n\t\t\tcolor: $text-secondary;\r\n\t\t\twhite-space: nowrap;\r\n\t\t}\r\n\t}\r\n\r\n\t.status-tag {\r\n\t\tfont-size: $font-size-xs;\r\n\t\tpadding: $spacing-xs $spacing-md;\r\n\t\tborder-radius: $radius-full;\r\n\r\n\t\t&.notStarted {\r\n\t\t\tbackground: $primary-light;\r\n\t\t\tcolor: $primary-color;\r\n\t\t}\r\n\r\n\t\t&.enrolling {\r\n\t\t\tbackground: $success-light;\r\n\t\t\tcolor: $success-color;\r\n\t\t}\r\n\r\n\t\t&.enrollEnd {\r\n\t\t\tbackground: $warning-light;\r\n\t\t\tcolor: $warning-color;\r\n\t\t}\r\n\r\n\t\t&.inProgress {\r\n\t\t\tbackground: $info-light;\r\n\t\t\tcolor: $info-color;\r\n\t\t}\r\n\r\n\t\t&.ended {\r\n\t\t\tbackground: rgba(0, 0, 0, 0.1);\r\n\t\t\tcolor: $text-tertiary;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.info-section {\r\n\tbackground: $bg-tertiary;\r\n\tborder-radius: $radius-md;\r\n\tpadding: $spacing-md;\r\n\tmargin-bottom: $spacing-lg;\r\n\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: $spacing-md;\r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\r\n\t\t.info-icon {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tcolor: $primary-color;\r\n\t\t\tmargin-right: $spacing-sm;\r\n\t\t\twidth: 36rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.info-text {\r\n\t\t\tfont-size: $font-size-md;\r\n\t\t\tcolor: $text-secondary;\r\n\t\t\tmargin-left: $spacing-sm;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.detail-section {\r\n\tmargin-bottom: $spacing-lg;\r\n\r\n\t.section-title {\r\n\t\tfont-size: $font-size-lg;\r\n\t\tfont-weight: $font-weight-bold;\r\n\t\tcolor: $text-primary;\r\n\t\tmargin-bottom: $spacing-md;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.detail-text {\r\n\t\tfont-size: $font-size-md;\r\n\t\tcolor: $text-secondary;\r\n\t\tline-height: 1.6;\r\n\t\tmargin-bottom: $spacing-md;\r\n\t}\r\n\r\n\t.detail-images {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-top: $spacing-md;\r\n\r\n\t\t.detail-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: auto;\r\n\t\t\tdisplay: block;\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t/* 添加背景色，便于调试 */\r\n\t\t\tmin-height: 200rpx;\r\n\t\t\t/* 最小高度，便于查看是否有图片元素 */\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.enroll-section {\r\n\t.section-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: $spacing-md;\r\n\r\n\t\t.section-title {\r\n\t\t\tfont-size: $font-size-lg;\r\n\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\tcolor: $text-primary;\r\n\t\t}\r\n\r\n\t\t.enroll-count {\r\n\t\t\tfont-size: $font-size-sm;\r\n\t\t\tcolor: $text-secondary;\r\n\t\t\tmargin-left: $spacing-xs;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\r\n\t\t.refresh-btn {\r\n\t\t\tpadding: $spacing-xs;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:active:not(.loading) {\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t}\r\n\r\n\t\t\t&.loading {\r\n\t\t\t\tanimation: spin 1s linear infinite;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.loading-container {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: $spacing-xl 0;\r\n\r\n\t\t.loading-text {\r\n\t\t\tfont-size: $font-size-sm;\r\n\t\t\tcolor: $text-secondary;\r\n\t\t\tmargin-left: $spacing-sm;\r\n\t\t}\r\n\t}\r\n\r\n\t.enroll-list {\r\n\t\t.enroll-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: $spacing-md 0;\r\n\t\t\tborder-bottom: 2rpx solid $bg-secondary;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tborder-bottom: none;\r\n\t\t\t}\r\n\r\n\t\t\t.enroll-avatar {\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tmargin-right: $spacing-md;\r\n\t\t\t}\r\n\r\n\t\t\t.enroll-info {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.enroll-name {\r\n\t\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\t\tcolor: $text-primary;\r\n\t\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\t\tmargin-bottom: $spacing-xs;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.enroll-time {\r\n\t\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\t\tcolor: $text-tertiary;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.empty-state {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: $spacing-xl 0;\r\n\r\n\t\t.empty-text {\r\n\t\t\tfont-size: $font-size-sm;\r\n\t\t\tcolor: $text-tertiary;\r\n\t\t\tmargin-top: $spacing-sm;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.bottom-bar {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\theight: 100rpx;\r\n\tbackground: $bg-primary;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 0 $spacing-lg;\r\n\tbox-shadow: $shadow-sm;\r\n\r\n\t.action-btns {\r\n\t\tdisplay: flex;\r\n\t\tgap: $spacing-lg;\r\n\t\tmargin-right: $spacing-lg;\r\n\r\n\t\tbutton {\r\n\t\t\tbackground: none;\r\n\t\t\tpadding: 0;\r\n\t\t\tline-height: 1;\r\n\t\t\tborder: none;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tgap: $spacing-xs;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\r\n\t\t\t.action-icon {\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t}\r\n\r\n\t\t\ttext:not(.action-icon) {\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.enroll-btn {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tbackground: $primary-gradient;\r\n\t\tcolor: $text-white;\r\n\t\tfont-size: $font-size-lg;\r\n\t\tborder-radius: $radius-full;\r\n\t\tborder: none;\r\n\r\n\t\t&::after {\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t&.disabled {\r\n\t\t\tbackground: $text-tertiary;\r\n\t\t\tcolor: $text-white;\r\n\t\t\tcursor: not-allowed;\r\n\t\t}\r\n\r\n\t\t&.enrolled {\r\n\t\t\tbackground: $text-tertiary;\r\n\t\t\tcolor: $text-white;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/activity/buddy/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["activityId", "ref", "pageScrollTop", "loading", "isFollowLoading", "enrollUserList", "enrollLoading", "followDebounceTimer", "activityTypes", "BUDDY_ACTIVITY_TYPES", "getActivityTypeName", "activityDetail", "loadActivityDetail", "uni", "response", "getBuddyActivityDetail", "error", "loadEnrollUsers", "getActivityEnrollUsers", "getStatusText", "status", "getActivityStatusName", "getStatusClass", "getIntroduceImages", "images", "img", "getImageUrl", "imageUrl", "fullUrl", "onImageLoad", "e", "onImageError", "getEnrollBtnText", "handleFollow", "performFollowAction", "toggle<PERSON>ser<PERSON><PERSON>ow", "wasFollowed", "shareActivity", "res", "shareToFriend", "shareToTimeline", "generateSharePoster", "shareData", "err", "showMoreActions", "handleReport", "enrollActivity", "statusText", "amount", "handlePaidEnroll", "handleFreeEnroll", "enrollFreeActivity", "coin", "enrollPaidActivity", "handlePageScroll", "onPageScroll", "onLoad", "options", "onMounted", "currentPagePath", "getCurrentPagePath", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": "gnBA6JA,MAAAA,EAAAC,EAAA,IAAA,IAAA,EAGAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,EAAA,EACAG,EAAAH,EAAA,IAAA,EAAA,EAGAI,EAAAJ,EAAA,IAAA,EAAA,EACAK,EAAAL,EAAA,IAAA,EAAA,EAGA,IAAAM,EAAA,KAGA,MAAAC,EAAA,CACA,CAAA,MAAA,EAAA,MAAA,IAAA,EACA,CAAA,MAAAC,EAAAA,qBAAA,OAAA,MAAAC,sBAAAD,EAAAA,qBAAA,MAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,KAAA,MAAAC,sBAAAD,EAAAA,qBAAA,IAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,OAAA,MAAAC,sBAAAD,EAAAA,qBAAA,MAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,QAAA,MAAAC,sBAAAD,EAAAA,qBAAA,OAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,WAAA,MAAAC,sBAAAD,EAAAA,qBAAA,UAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,OAAA,MAAAC,sBAAAD,EAAAA,qBAAA,MAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,MAAA,MAAAC,sBAAAD,EAAAA,qBAAA,KAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,SAAA,MAAAC,sBAAAD,EAAAA,qBAAA,QAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,OAAA,MAAAC,sBAAAD,EAAAA,qBAAA,MAAA,CAAA,EACA,CAAA,MAAAA,EAAAA,qBAAA,MAAA,MAAAC,sBAAAD,EAAAA,qBAAA,KAAA,CAAA,CACA,EAGAE,EAAAV,EAAA,IAAA,EAAA,EAGAW,EAAA,SAAA,CACA,GAAA,CAAAZ,EAAA,MAAA,CACAa,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,EACA,MACA,CAEAV,EAAA,MAAA,GAEA,GAAA,CACAU,EAAA,MAAA,MAAA,MAAA,4CAAA,aAAAb,EAAA,KAAA,EACA,MAAAc,EAAA,MAAAC,yBAAAf,EAAA,KAAA,EAEAa,EAAAA,MAAA,MAAA,MAAA,4CAAA,SAAAC,CAAA,EAEAA,EAAA,OAAA,KAAAA,EAAA,MAEAH,EAAA,MAAAG,EAAA,KACAD,EAAA,MAAA,MAAA,MAAA,4CAAA,YAAAF,EAAA,KAAA,EACAE,EAAA,MAAA,MAAA,MAAA,4CAAA,UAAAF,EAAA,MAAA,eAAA,IAEAE,EAAA,MAAA,MAAA,QAAA,4CAAA,YAAAC,EAAA,GAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAAC,EAAA,KAAA,OACA,KAAA,MACA,CAAA,EAEA,OAAAE,EAAA,CACAH,EAAAA,MAAA,MAAA,QAAA,4CAAA,YAAAG,CAAA,EACAH,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,QAAA,CACAV,EAAA,MAAA,EACA,CACA,EAGAc,EAAA,SAAA,CACAX,EAAA,MAAA,GACAY,yBAAA,CACA,WAAAlB,EAAA,MACA,QAAA,EACA,SAAA,EACA,CAAA,EAAA,KAAAc,GAAA,CACAT,EAAA,MAAAS,EAAA,MAAA,CAAA,CACA,CAAA,EAAA,QAAA,IAAA,CACAR,EAAA,MAAA,EACA,CAAA,CACA,EAEAa,EAAAC,GAEA,OAAAA,GAAA,SACAC,EAAAA,sBAAAD,CAAA,EAIA,CACA,WAAA,MACA,UAAA,MACA,UAAA,QACA,WAAA,QACA,MAAA,OACA,EACAA,CAAA,GAAA,GAIAE,EAAAF,GACA,OAAAA,GAAA,SAEA,CACA,EAAA,aACA,EAAA,aACA,EAAA,YACA,EAAA,YACA,GAAA,aACA,GAAA,OACA,EACAA,CAAA,GAAA,aAIAA,GAAA,aAIAG,EAAA,IAAA,CAKA,GAJAV,EAAAA,MAAA,MAAA,MAAA,4CAAA,wBAAA,EACAA,EAAA,MAAA,MAAA,MAAA,4CAAA,wBAAAF,EAAA,KAAA,EACAE,EAAA,MAAA,MAAA,MAAA,4CAAA,uBAAAF,EAAA,MAAA,eAAA,EAEA,CAAAA,EAAA,MAAA,gBACAE,OAAAA,EAAAA,MAAA,MAAA,MAAA,4CAAA,0BAAA,EACA,CAAA,EAIA,MAAAW,EAAAb,EAAA,MAAA,gBAAA,MAAA,GAAA,EAAA,OAAAc,GAAAA,EAAA,KAAA,CAAA,EACAZ,OAAAA,EAAAA,MAAA,MAAA,MAAA,4CAAA,YAAAW,CAAA,EACAA,CACA,EAGAE,EAAAC,GAAA,CACA,GAAA,CAAAA,EAAA,MAAA,GAGA,GAAAA,EAAA,WAAA,SAAA,GAAAA,EAAA,WAAA,UAAA,EACAd,OAAAA,EAAAA,MAAA,MAAA,MAAA,4CAAA,SAAAc,CAAA,EACAA,EAMA,MAAAC,EADA,+BACAD,EACAd,OAAAA,EAAAA,MAAA,MAAA,MAAA,4CAAA,WAAAe,CAAA,EACAA,CACA,EAGAC,EAAAC,GAAA,CACAjB,EAAAA,MAAA,MAAA,MAAA,4CAAA,UAAAiB,CAAA,CACA,EAGAC,EAAAD,GAAA,CACAjB,EAAAA,MAAA,MAAA,QAAA,4CAAA,UAAAiB,CAAA,EACAjB,EAAA,MAAA,MAAA,QAAA,4CAAA,YAAAiB,EAAA,OAAA,GAAA,CACA,EAKAE,EAAA,IAAA,CAEA,GAAArB,EAAA,MAAA,YACA,MAAA,MAGA,MAAAS,EAAAT,EAAA,MAAA,OAUA,MATA,CACA,EAAA,MACA,EAAA,KACA,EAAA,MACA,EAAA,OACA,EAAA,QACA,GAAA,QACA,GAAA,OACA,EACAS,CAAA,GAAA,MACA,EAQAa,EAAA,SAAA,CAEA,GAAA,CAAAtB,EAAA,MAAA,UAAA,CACAE,EAAAA,MAAA,UAAA,CACA,MAAA,UACA,KAAA,MACA,CAAA,EACA,MACA,CAGAT,EAAA,QAKAG,GACA,aAAAA,CAAA,EAGAA,EAAA,WAAA,SAAA,CACA,MAAA2B,EAAA,CACA,EAAA,GAAA,EACA,EAGAA,EAAA,SAAA,CAEA,GAAA,CACA9B,EAAA,MAAA,GACAS,EAAA,MAAA,MAAA,MAAA,4CAAA,eAAAF,EAAA,MAAA,SAAA,EAEA,MAAAG,EAAA,MAAAqB,EAAAA,iBAAAxB,EAAA,MAAA,UAAAA,EAAA,MAAA,aAAA,EAEA,GAAAG,EAAA,OAAA,IAAA,CAEA,MAAAsB,EAAAzB,EAAA,MAAA,cACAA,EAAA,MAAA,cAAA,CAAAA,EAAA,MAAA,cAGAE,EAAAA,MAAA,UAAA,CACA,MAAAF,EAAA,MAAA,cAAA,OAAA,QACA,KAAA,UACA,SAAA,IACA,CAAA,EAGA,CAAAyB,GAAAzB,EAAA,MAAA,eACAE,EAAA,MAAA,MAAA,MAAA,4CAAA,SAAAF,EAAA,MAAA,WAAA,CAEA,MACAE,EAAA,MAAA,MAAA,QAAA,4CAAA,UAAAC,EAAA,GAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAAC,EAAA,KAAA,OACA,KAAA,MACA,CAAA,CAEA,OAAAE,EAAA,CACAH,EAAAA,MAAA,MAAA,QAAA,4CAAA,UAAAG,CAAA,EACAH,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,QAAA,CACAT,EAAA,MAAA,EACA,CACA,EAGAiC,EAAA,IAAA,CAGAxB,EAAAA,MAAA,gBAAA,CACA,SAAA,CAAA,QAAA,SAAA,QAAA,EACA,QAAAyB,GAAA,CACAA,EAAA,WAAA,EAEAC,EAAA,EACAD,EAAA,WAAA,EAEAE,EAAA,EACAF,EAAA,WAAA,GAEAG,EAAA,CAEA,EACA,KAAA,IAAA,CACA5B,EAAAA,MAAA,MAAA,MAAA,4CAAA,MAAA,CACA,CACA,CAAA,CASA,EAGA0B,EAAA,IAAA,CAEA,MAAAG,EAAA,CACA,MAAA/B,EAAA,MAAA,MAAA,SACA,KAAA,sCAAAX,EAAA,KAAA,GACA,SAAAW,EAAA,MAAA,iBAAA,GACA,QAAA2B,GAAA,CACAzB,EAAAA,MAAA,MAAA,MAAA,4CAAA,OAAAyB,CAAA,EACAzB,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,CACA,EACA,KAAA8B,GAAA,CACA9B,EAAAA,MAAA,MAAA,QAAA,4CAAA,OAAA8B,CAAA,EACA9B,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,MACA,CAAA,CACA,CACA,EAGAA,EAAA,MAAA,gBAAA6B,CAAA,CAEA,EAGAF,EAAA,IAAA,CAIA3B,EAAAA,MAAA,UAAA,CACA,MAAA,SACA,QAAA,4BACA,WAAA,GACA,YAAA,KACA,CAAA,CAEA,EAGA4B,EAAA,IAAA,CACA5B,EAAAA,MAAA,YAAA,CACA,MAAA,QACA,CAAA,EAIA,WAAA,IAAA,CACAA,EAAAA,MAAA,YAAA,EACAA,EAAAA,MAAA,UAAA,CACA,MAAA,YACA,KAAA,MACA,CAAA,CACA,EAAA,IAAA,CACA,EAGA+B,EAAA,IAAA,CACA/B,EAAAA,MAAA,gBAAA,CACA,SAAA,CAAA,IAAA,EACA,QAAAyB,GAAA,CACAzB,QAAA,MAAA,MAAA,4CAAA,QAAAyB,EAAA,SAAA,GAAA,KAAA,EACAA,EAAA,WAAA,GAEAO,EAAA,CAEA,EACA,KAAA,IAAA,CACAhC,EAAAA,MAAA,MAAA,MAAA,4CAAA,MAAA,CACA,CACA,CAAA,CACA,EAGAgC,EAAA,IAAA,CAEAhC,EAAAA,MAAA,WAAA,CACA,IAAA,8CAAAb,EAAA,KAAA,eAAA,mBAAAW,EAAA,MAAA,MAAA,EAAA,CAAA,EACA,CAAA,CACA,EAGAmC,EAAA,SAAA,CAEA,GAAAnC,EAAA,MAAA,YAAA,CACAE,EAAAA,MAAA,UAAA,CACA,MAAA,UACA,KAAA,MACA,CAAA,EACA,MACA,CAGA,GAAAF,EAAA,MAAA,SAAA,EAAA,CACA,MAAAoC,EAAAf,EAAA,EACAnB,EAAAA,MAAA,UAAA,CACA,MAAA,QAAAkC,CAAA,QACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,GAAA,CAAA/C,EAAA,MAAA,CACAa,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,GAAA,CAEA,MAAAmC,EAAArC,EAAA,MAAA,QAAA,EAEAqC,EAAA,EAEA,MAAAC,EAAAD,CAAA,EAGA,MAAAE,EAAA,CAEA,OAAAlC,EAAA,CACAH,EAAAA,MAAA,MAAA,QAAA,4CAAA,UAAAG,CAAA,EACAH,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CACA,CACA,EAGAqC,EAAA,SAAA,CACA,GAAA,CACArC,EAAA,MAAA,MAAA,MAAA,4CAAA,eAAAb,EAAA,KAAA,EACA,MAAAc,EAAA,MAAAqC,qBAAAnD,EAAA,KAAA,EAEAc,EAAA,OAAA,KACAD,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,EAGA,MAAAD,EAAAZ,EAAA,KAAA,EAEA,MAAAiB,EAAA,IAEAJ,EAAA,MAAA,MAAA,QAAA,4CAAA,YAAAC,EAAA,GAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAAC,EAAA,KAAA,OACA,KAAA,MACA,CAAA,EAEA,OAAAE,EAAA,CACAH,MAAAA,EAAAA,MAAA,MAAA,QAAA,4CAAA,YAAAG,CAAA,EACAA,CACA,CACA,EAGAiC,EAAA,MAAAD,GAAA,CACA,GAAA,CASA,GAAA,EAPA,MAAAnC,EAAA,MAAA,UAAA,CACA,MAAA,OACA,QAAA,YAAAmC,CAAA,SACA,YAAA,KACA,WAAA,IACA,CAAA,GAEA,QACA,OAGAnC,QAAA,MAAA,MAAA,4CAAA,eAAAb,EAAA,MAAA,MAAAgD,CAAA,EAGA,MAAAI,EAAAJ,EAAA,GAEAlC,EAAA,MAAAuC,EAAA,mBAAArD,EAAA,MAAAgD,EAAAI,CAAA,EAEAtC,EAAA,OAAA,IAEAA,EAAA,MAAAA,EAAA,KAAA,SAEAD,EAAA,MAAA,MAAA,MAAA,4CAAA,QAAAC,EAAA,KAAA,OAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAA,UACA,KAAA,MACA,CAAA,IAEAA,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,EAGA,MAAAD,EAAAZ,EAAA,KAAA,EAEA,MAAAiB,EAAA,IAGAJ,EAAA,MAAA,MAAA,QAAA,4CAAA,YAAAC,EAAA,GAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAAC,EAAA,KAAA,OACA,KAAA,MACA,CAAA,EAEA,OAAAE,EAAA,CACAH,MAAAA,EAAAA,MAAA,MAAA,QAAA,4CAAA,YAAAG,CAAA,EACAA,CACA,CACA,EAGAsC,EAAAxB,GAAA,CACA5B,EAAA,MAAA4B,EAAA,SACA,EAGAyB,OAAAA,EAAA,aAAAD,CAAA,EAGAE,EAAA,OAAAC,GAAA,CACAzD,EAAA,MAAAyD,EAAA,EACA,CAAA,EAGAC,EAAAA,UAAA,IAAA,CAEA,MAAAC,EAAAC,EAAAA,mBAAA,EACA/C,EAAAA,MAAA,MAAA,MAAA,4CAAA,YAAA8C,CAAA,EAEA3D,EAAA,QACAY,EAAA,EACAK,EAAA,EAEA,CAAA,EAIA4C,EAAAA,kBAAA,KACA,CACA,MAAAlD,EAAA,MAAA,MAAA,SACA,KAAA,sCAAAX,EAAA,KAAA,GACA,SAAAW,EAAA,MAAA,iBAAA,GACA,QAAA2B,GAAA,CACAzB,EAAAA,MAAA,MAAA,MAAA,4CAAA,OAAAyB,CAAA,CACA,EACA,KAAAK,GAAA,CACA9B,EAAAA,MAAA,MAAA,QAAA,4CAAA,OAAA8B,CAAA,CACA,CACA,EACA,EAGAmB,EAAAA,gBAAA,KACA,CACA,MAAA,GAAAnD,EAAA,MAAA,MAAA,MAAA,cACA,MAAA,MAAAX,EAAA,KAAA,GACA,SAAAW,EAAA,MAAA,iBAAA,GACA,QAAA2B,GAAA,CACAzB,EAAAA,MAAA,MAAA,MAAA,4CAAA,WAAAyB,CAAA,CACA,EACA,KAAAK,GAAA,CACA9B,EAAAA,MAAA,MAAA,QAAA,4CAAA,WAAA8B,CAAA,CACA,CACA,EACA,ikDCttBA,GAAG,WAAWoB,CAAe"}