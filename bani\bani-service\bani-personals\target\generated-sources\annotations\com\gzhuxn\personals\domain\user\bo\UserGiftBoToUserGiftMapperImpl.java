package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserGift;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserGiftBoToUserGiftMapperImpl implements UserGiftBoToUserGiftMapper {

    @Override
    public UserGift convert(UserGiftBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGift userGift = new UserGift();

        userGift.setSearchValue( arg0.getSearchValue() );
        userGift.setCreateBy( arg0.getCreateBy() );
        userGift.setCreateTime( arg0.getCreateTime() );
        userGift.setUpdateBy( arg0.getUpdateBy() );
        userGift.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userGift.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userGift.setCreateDept( arg0.getCreateDept() );
        userGift.setId( arg0.getId() );
        userGift.setUserId( arg0.getUserId() );
        userGift.setOppositeUserId( arg0.getOppositeUserId() );
        userGift.setGiftId( arg0.getGiftId() );
        userGift.setGiftName( arg0.getGiftName() );
        userGift.setGiftPrice( arg0.getGiftPrice() );
        userGift.setGiftNum( arg0.getGiftNum() );
        userGift.setCoin( arg0.getCoin() );

        return userGift;
    }

    @Override
    public UserGift convert(UserGiftBo arg0, UserGift arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setGiftId( arg0.getGiftId() );
        arg1.setGiftName( arg0.getGiftName() );
        arg1.setGiftPrice( arg0.getGiftPrice() );
        arg1.setGiftNum( arg0.getGiftNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
