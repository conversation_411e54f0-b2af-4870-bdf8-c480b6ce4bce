-- 用户打招呼表
CREATE TABLE `user_greeting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `opposite_user_id` bigint(20) NOT NULL COMMENT '对方用户ID',
  `content` varchar(200) NOT NULL COMMENT '打招呼内容',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-待回复，1-已回复，2-已忽略',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_opposite_user_id` (`opposite_user_id`),
  KEY `idx_user_opposite` (`user_id`, `opposite_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户-打招呼';
