package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserDetail;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailBoToUserDetailMapperImpl implements UserDetailBoToUserDetailMapper {

    @Override
    public UserDetail convert(UserDetailBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetail userDetail = new UserDetail();

        userDetail.setSearchValue( arg0.getSearchValue() );
        userDetail.setCreateBy( arg0.getCreateBy() );
        userDetail.setCreateTime( arg0.getCreateTime() );
        userDetail.setUpdateBy( arg0.getUpdateBy() );
        userDetail.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userDetail.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userDetail.setCreateDept( arg0.getCreateDept() );
        userDetail.setUserId( arg0.getUserId() );
        userDetail.setPid( arg0.getPid() );
        userDetail.setBirthday( arg0.getBirthday() );
        userDetail.setStar( arg0.getStar() );
        userDetail.setAnimal( arg0.getAnimal() );
        userDetail.setHeight( arg0.getHeight() );
        userDetail.setWeight( arg0.getWeight() );
        userDetail.setEdu( arg0.getEdu() );
        userDetail.setJob( arg0.getJob() );
        userDetail.setRevenue( arg0.getRevenue() );
        userDetail.setWechat( arg0.getWechat() );
        userDetail.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        userDetail.setAddrCityCode( arg0.getAddrCityCode() );
        userDetail.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        userDetail.setAddrStreetCode( arg0.getAddrStreetCode() );
        userDetail.setAddr( arg0.getAddr() );
        userDetail.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        userDetail.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        userDetail.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        userDetail.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        userDetail.setAddrNew( arg0.getAddrNew() );
        userDetail.setProgress( arg0.getProgress() );
        userDetail.setAuditStatus( arg0.getAuditStatus() );
        userDetail.setLon( arg0.getLon() );
        userDetail.setLat( arg0.getLat() );

        return userDetail;
    }

    @Override
    public UserDetail convert(UserDetailBo arg0, UserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        arg1.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        arg1.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        arg1.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
