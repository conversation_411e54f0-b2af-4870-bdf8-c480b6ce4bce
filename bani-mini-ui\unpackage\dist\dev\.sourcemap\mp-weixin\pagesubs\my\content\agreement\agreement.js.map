{"version": 3, "file": "agreement.js", "sources": ["pagesubs/my/content/agreement/agreement.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcY29udGVudFxhZ3JlZW1lbnRcYWdyZWVtZW50LnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page :title=\"pageTitle\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<!-- 内容区域 -->\n\t\t\t<view class=\"content-wrapper\">\n\t\t\t<!-- 加载状态 -->\n\t\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t\t<uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n\t\t\t</view>\n\n\t\t\t<!-- 内容显示 -->\n\t\t\t<view v-else class=\"up-content\">\n\t\t\t\t<rich-text :nodes=\"content\"></rich-text>\n\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { toast } from '@/utils/common'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport { getAgreementByKey } from '@/api/content/agreement'\n\n// 导航栏高度\nconst navBarHeight = ref(0)\n// 页面标题\nconst pageTitle = ref('')\n// 页面内容\nconst content = ref('')\n// 加载状态\nconst loading = ref(true)\n// 加载文字\nconst loadingText = ref({\n\tcontentdown: '正在加载协议内容...',\n\tcontentrefresh: '正在加载协议内容...',\n\tcontentnomore: '加载完成'\n})\n\n// 页面加载生命周期\nonLoad((option) => {\n\t// 根据传入的 key 参数确定页面标题和内容\n\tif (!option.key) {\n\t\ttoast('不存在的页面Key')\n\t\tuni.navigateBack({\n\t\t\tdelta: 1\n\t\t})\n\t\treturn\n\t}\n\tpageTitle.value = ''\n\tloading.value = true\n\n\t// 获取协议内容\n\tgetAgreementByKey(option.key).then(res => {\n\t\tif (res && res.data && res.data.content) {\n\t\t\tcontent.value = res.data.content\n\t\t\tpageTitle.value = res.data.name\n\t\t} else {\n\t\t\tcontent.value = getDefaultContent(option.key)\n\t\t}\n\t\tloading.value = false\n\t}).catch(err => {\n\t\t// 显示默认内容\n\t\tcontent.value = getDefaultContent(option.key)\n\t\tloading.value = false\n\t})\n})\n\n// 获取默认内容（当接口失败时）\nconst getDefaultContent = (key) => {\n\tconst defaultContents = {\n\t\tuserAgreement: `\n\t\t\t<h2>用户协议</h2>\n\t\t\t<p>欢迎使用伴你有约！</p>\n\t\t\t<p>本协议是您与伴你有约之间关于使用伴你有约服务的法律协议。</p>\n\t\t\t<h3>1. 服务条款</h3>\n\t\t\t<p>用户在使用本服务时，应当遵守相关法律法规...</p>\n\t\t`,\n\t\tprivacyAgreement: `\n\t\t\t<h2>隐私政策</h2>\n\t\t\t<p>我们非常重视您的隐私保护。</p>\n\t\t\t<p>本隐私政策说明了我们如何收集、使用和保护您的个人信息。</p>\n\t\t\t<h3>1. 信息收集</h3>\n\t\t\t<p>我们可能收集以下类型的信息...</p>\n\t\t`,\n\t\tserviceAgreement: `\n\t\t\t<h2>服务协议</h2>\n\t\t\t<p>本服务协议规定了服务的具体条款和条件。</p>\n\t\t`,\n\t\tcommunityRules: `\n\t\t\t<h2>社区规范</h2>\n\t\t\t<p>为了维护良好的社区环境，请遵守以下规范。</p>\n\t\t`\n\t}\n\n\treturn defaultContents[key] || '<p>内容加载中...</p>'\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg,\n\t\t\trgba(105, 108, 243, 0.08) 0%,\n\t\t\trgba(105, 108, 243, 0.05) 30%,\n\t\t\trgba(105, 108, 243, 0.02) 60%,\n\t\t\trgba(255, 255, 255, 1) 100%);\n}\n\n.content-wrapper {\n\tmargin-top: 20rpx;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n}\n\n.loading-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmin-height: 400rpx;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 20rpx;\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\n}\n\n.up-content {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 20rpx;\n\tpadding: 40rpx 30rpx;\n\tfont-size: 30rpx;\n\tline-height: 1.8;\n\tcolor: #333;\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n\n\t// 富文本内容样式优化\n\t:deep(p) {\n\t\tmargin-bottom: 24rpx;\n\t\ttext-align: justify;\n\t}\n\n\t:deep(h1),\n\t:deep(h2),\n\t:deep(h3) {\n\t\tcolor: $primary-color;\n\t\tfont-weight: 600;\n\t\tmargin: 40rpx 0 20rpx 0;\n\t}\n\n\t:deep(h1) {\n\t\tfont-size: 36rpx;\n\t}\n\n\t:deep(h2) {\n\t\tfont-size: 34rpx;\n\t}\n\n\t:deep(h3) {\n\t\tfont-size: 32rpx;\n\t}\n\n\t:deep(ul),\n\t:deep(ol) {\n\t\tpadding-left: 40rpx;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t:deep(li) {\n\t\tmargin-bottom: 12rpx;\n\t\tline-height: 1.6;\n\t}\n\n\t:deep(strong) {\n\t\tcolor: $primary-color;\n\t\tfont-weight: 600;\n\t}\n\n\t:deep(a) {\n\t\tcolor: $primary-color;\n\t\ttext-decoration: underline;\n\t}\n}\n\n// 响应式设计\n@media screen and (max-width: 750rpx) {\n\t.up-content {\n\t\tpadding: 30rpx 24rpx;\n\t\tfont-size: 28rpx;\n\n\t\t:deep(h1) {\n\t\t\tfont-size: 34rpx;\n\t\t}\n\n\t\t:deep(h2) {\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t:deep(h3) {\n\t\t\tfont-size: 30rpx;\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/content/agreement/agreement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "ref", "pageTitle", "content", "loading", "loadingText", "onLoad", "option", "toast", "uni", "getAgreementByKey", "res", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "key", "MiniProgramPage"], "mappings": "iTAuBA,MAAMA,EAAgB,IAAW,2FAIZC,EAAG,IAAC,CAAC,EAE1B,MAAMC,EAAYD,EAAG,IAAC,EAAE,EAElBE,EAAUF,EAAG,IAAC,EAAE,EAEhBG,EAAUH,EAAG,IAAC,EAAI,EAElBI,EAAcJ,EAAAA,IAAI,CACvB,YAAa,cACb,eAAgB,cAChB,cAAe,MAChB,CAAC,EAGDK,EAAM,OAAEC,GAAW,CAElB,GAAI,CAACA,EAAO,IAAK,CAChBC,EAAAA,MAAM,WAAW,EACjBC,EAAAA,MAAI,aAAa,CAChB,MAAO,CACV,CAAG,EACD,MACA,CACDP,EAAU,MAAQ,GAClBE,EAAQ,MAAQ,GAGhBM,EAAAA,kBAAkBH,EAAO,GAAG,EAAE,KAAKI,GAAO,CACrCA,GAAOA,EAAI,MAAQA,EAAI,KAAK,SAC/BR,EAAQ,MAAQQ,EAAI,KAAK,QACzBT,EAAU,MAAQS,EAAI,KAAK,MAE3BR,EAAQ,MAAQS,EAAkBL,EAAO,GAAG,EAE7CH,EAAQ,MAAQ,EAClB,CAAE,EAAE,MAAMS,GAAO,CAEfV,EAAQ,MAAQS,EAAkBL,EAAO,GAAG,EAC5CH,EAAQ,MAAQ,EAClB,CAAE,CACF,CAAC,EAGD,MAAMQ,EAAqBE,IACF,CACvB,cAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOf,iBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOlB,iBAAkB;AAAA;AAAA;AAAA,IAIlB,eAAgB;AAAA;AAAA;AAAA,GAIhB,GAEsBA,CAAG,GAAK,sNChGhC,GAAG,WAAWC,CAAe"}