package com.gzhuxn.personals.controller.app.user.vo.gift;

import com.gzhuxn.personals.domain.user.UserGift;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserGiftVoToUserGiftMapperImpl implements AppUserGiftVoToUserGiftMapper {

    @Override
    public UserGift convert(AppUserGiftVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGift userGift = new UserGift();

        userGift.setId( arg0.getId() );
        userGift.setUserId( arg0.getUserId() );
        userGift.setGiftId( arg0.getGiftId() );
        userGift.setGiftName( arg0.getGiftName() );
        userGift.setGiftPrice( arg0.getGiftPrice() );
        userGift.setGiftNum( arg0.getGiftNum() );
        userGift.setCoin( arg0.getCoin() );

        return userGift;
    }

    @Override
    public UserGift convert(AppUserGiftVo arg0, UserGift arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setGiftId( arg0.getGiftId() );
        arg1.setGiftName( arg0.getGiftName() );
        arg1.setGiftPrice( arg0.getGiftPrice() );
        arg1.setGiftNum( arg0.getGiftNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
