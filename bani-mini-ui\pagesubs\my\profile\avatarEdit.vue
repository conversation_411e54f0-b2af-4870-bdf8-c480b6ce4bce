<template>
	<scroll-nav-page title="上传头像" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 主要内容 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<view v-if="avatarType === 0" class="steps-wrapper">
						<uni-steps :options="[{ title: '基础信息' }, { title: '上传头像' }]" :active="1"></uni-steps>
					</view>
					<view class="title">
						<text class="upload-tip">点击上传本人真实照片做头像</text>
					</view>
					<view class="content-wrapper">
						<view class="upload-area">
							<images-select :limit="1" type="user_detail.avatar" :small="true" v-model="imageList"
								@change="handleImageChange">
							</images-select>
						</view>
						<view class="finishBtn">
							<button type="primary" :disabled="submitBtnDisabled" @click="submit">完成</button>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { updateUserAvatar } from '@/api/my/my'
import { toast } from '@/utils/common'
import $store from '@/store'
import globalConfig from '@/config'

// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const imageList = ref([])
const avatarType = ref(0)

const submitBtnDisabled = computed(() => {
	return imageList.value.length > 0 ? false : true
})

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor
}
onLoad((param) => {
	if (param.type) {
		avatarType.value = parseInt(param.type)
	}
})

// 处理图片变化
const handleImageChange = (event) => {
	console.log('头像图片变化:', event)
}

const submit = () => {
	if (imageList.value.length === 0) {
		uni.showToast({
			title: '请先上传头像',
			icon: 'none'
		})
		return
	}

	var data = {
		avatar: imageList.value[0].ossId
	}
	updateUserAvatar(data).then(res => {
		$store.user.refreshUserInfo();
		toast("保存成功！")
		if (avatarType.value === 1) {
			uni.navigateBack()
			return
		}
		uni.reLaunch({
			url: '/pages/my/my'
		})
	})
}
</script>

<style lang="scss">
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			rgba($primary-color, 0.08) 0%,
			rgba($primary-color, 0.04) 30%,
			rgba(255, 255, 255, 0.98) 60%,
			rgba(248, 249, 255, 1) 100%);
}

.main-container {
	padding: 24rpx 20rpx 120rpx;
	min-height: 100vh;
	box-sizing: border-box;

	.steps-wrapper {
		margin-top: 20rpx;
		padding: 0 10rpx;
		margin-bottom: 50rpx;

		:deep(.uni-steps) {
			.uni-steps__column-line-container {
				.uni-steps__column-line {
					background-color: rgba($primary-color, 0.3);
					height: 4rpx !important;
					border-radius: 2rpx;
				}
			}

			.uni-steps__column-container {
				.uni-steps__column-text-container {
					.uni-steps__column-title {
						color: #666;
						font-size: 26rpx;
						font-weight: 500;
					}
				}
			}
		}
	}

	.title {
		text-align: center;
		margin: 50rpx 0 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		position: relative;

		.upload-tip {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #1a1a1a;
			font-size: 32rpx;
			font-weight: 600;
			position: relative;
			padding: 20rpx 40rpx;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba($primary-color, 0.05));
			border-radius: 32rpx;
			box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.1);
			border: 1rpx solid rgba($primary-color, 0.1);
			letter-spacing: 1rpx;

			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				width: 40rpx;
				height: 3rpx;
				background: linear-gradient(90deg, transparent, rgba($primary-color, 0.4), transparent);
				border-radius: 2rpx;
			}

			&::before {
				left: -50rpx;
			}

			&::after {
				right: -50rpx;
			}

			// 添加光泽效果
			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
				transition: left 0.8s ease;
				border-radius: 32rpx;
			}

			&:hover::after {
				left: 100%;
			}
		}
	}

	.content-wrapper {
		background: linear-gradient(135deg, #fff 0%, #fafbff 100%);
		padding: 50rpx 40rpx;
		margin: 0 4rpx;
		border-radius: 32rpx;
		box-shadow: 0 12rpx 40rpx rgba($primary-color, 0.12);
		border: 1rpx solid rgba($primary-color, 0.08);
		position: relative;
		overflow: hidden;

		.upload-area {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 60rpx auto;
			width: 100%;
			max-width: 450rpx;

			// 为ImagesSelect组件添加样式
			:deep(.images-select) {
				width: 100%;

				.images-grid {
					display: flex;
					justify-content: center;
					align-items: center;
					grid-template-columns: 1fr;
				}

				.image-item,
				.add-image-btn {
					width: 450rpx;
					height: 550rpx;
					border-radius: 24rpx;
					box-shadow: 0 8rpx 32rpx rgba($primary-color, 0.15);
					transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

					&:active {
						transform: translateY(-4rpx) scale(1.02);
						box-shadow: 0 16rpx 48rpx rgba($primary-color, 0.25);
					}
				}

				.add-image-btn {
					background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
					border: 3rpx dashed rgba($primary-color, 0.3);
					position: relative;

					.add-icon {
						font-size: 120rpx;
						color: $primary-color;
					}

					// 添加上传提示文字
					&::after {
						content: '点击上传头像';
						position: absolute;
						bottom: 80rpx;
						left: 50%;
						transform: translateX(-50%);
						color: rgba($primary-color, 0.8);
						font-size: 28rpx;
						font-weight: 500;
						white-space: nowrap;
					}
				}
			}
		}

		.finishBtn {
			width: 100%;
			max-width: 600rpx;
			margin: 80rpx auto 20rpx;

			button {
				width: 100%;
				height: 100rpx;
				line-height: 100rpx;
				border-radius: 50rpx;
				font-size: 36rpx;
				font-weight: 700;
				background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
				color: #fff;
				border: none;
				box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.3);
				transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
				position: relative;
				overflow: hidden;
				letter-spacing: 2rpx;

				// 光泽效果
				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 100%;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
					transition: left 0.6s ease;
				}

				&:hover::before {
					left: 100%;
				}

				&:active {
					transform: scale(0.96);
					box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.25);
					background: linear-gradient(135deg, darken($primary-color, 3%), $primary-color);
				}

				&[disabled] {
					background: linear-gradient(135deg, #ccc, #ddd);
					box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
					transform: none;
					color: #999;

					&::before {
						display: none;
					}
				}
			}
		}
	}
}
</style>