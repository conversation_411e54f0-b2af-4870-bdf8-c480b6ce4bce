package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailFeeVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityFeeToAppActivityDetailFeeVoMapperImpl implements ActivityFeeToAppActivityDetailFeeVoMapper {

    @Override
    public AppActivityDetailFeeVo convert(ActivityFee arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppActivityDetailFeeVo appActivityDetailFeeVo = new AppActivityDetailFeeVo();

        appActivityDetailFeeVo.setId( arg0.getId() );
        appActivityDetailFeeVo.setActivityId( arg0.getActivityId() );
        appActivityDetailFeeVo.setType( arg0.getType() );
        appActivityDetailFeeVo.setBusinessId( arg0.getBusinessId() );
        appActivityDetailFeeVo.setAmount( arg0.getAmount() );

        return appActivityDetailFeeVo;
    }

    @Override
    public AppActivityDetailFeeVo convert(ActivityFee arg0, AppActivityDetailFeeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setAmount( arg0.getAmount() );

        return arg1;
    }
}
