<template>
	<scroll-nav-page title="推广中心" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 收益概览卡片 -->
				<view class="earnings-card">
					<view class="earnings-header">
						<view class="header-left">
							<uni-icons type="wallet-filled" size="28" color="#696CF3"></uni-icons>
							<text class="header-title">我的收益</text>
						</view>
						<view class="header-right" @click="navigateToRecords">
							<text class="view-records">查看明细</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>

					<view class="earnings-content">
						<view class="earnings-item">
							<text class="earnings-label">累计收益</text>
							<text class="earnings-amount">¥{{ earningsData.totalEarnings || '0.00' }}</text>
						</view>
						<view class="earnings-item">
							<text class="earnings-label">可提现余额</text>
							<text class="earnings-amount highlight">¥{{ earningsData.availableAmount || '0.00' }}</text>
						</view>
						<view class="earnings-item">
							<text class="earnings-label">已提现</text>
							<text class="earnings-amount">¥{{ earningsData.withdrawnAmount || '0.00' }}</text>
						</view>
					</view>

					<view class="earnings-actions">
						<button class="withdraw-btn" @click="navigateToWithdraw">
							<uni-icons type="wallet" size="20" color="white"></uni-icons>
							<text>立即提现</text>
						</button>
					</view>
				</view>

				<!-- 推广数据卡片 -->
				<view class="promotion-card">
					<view class="card-header">
						<uni-icons type="person-filled" size="24" color="#696CF3"></uni-icons>
						<text class="card-title">推广数据</text>
					</view>

					<view class="promotion-stats">
						<view class="stat-item">
							<text class="stat-number">{{ promotionData.inviteCount || 0 }}</text>
							<text class="stat-label">邀请人数</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">{{ promotionData.activeCount || 0 }}</text>
							<text class="stat-label">活跃用户</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">{{ promotionData.todayEarnings || '0.00' }}</text>
							<text class="stat-label">今日收益</text>
						</view>
					</view>
				</view>

				<!-- 推广工具 -->
				<view class="tools-card">
					<view class="card-header">
						<uni-icons type="gear-filled" size="24" color="#696CF3"></uni-icons>
						<text class="card-title">推广工具</text>
					</view>

					<view class="tools-list">
						<view class="tool-item" @click="shareInviteCode">
							<view class="tool-icon">
								<uni-icons type="share" size="24" color="#696CF3"></uni-icons>
							</view>
							<view class="tool-content">
								<text class="tool-title">分享邀请码</text>
								<text class="tool-desc">分享您的专属邀请码给好友</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>

						<view class="tool-item" @click="generatePoster">
							<view class="tool-icon">
								<uni-icons type="image" size="24" color="#696CF3"></uni-icons>
							</view>
							<view class="tool-content">
								<text class="tool-title">生成推广海报</text>
								<text class="tool-desc">生成专属推广海报分享</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 底部导航 -->
				<nav-tabbar tab-index="1"></nav-tabbar>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getUserBalance } from '@/api/my/withdraw'
import { toast } from '@/utils/common'

// 收益数据
const earningsData = ref({
	totalEarnings: 0,
	availableAmount: 0,
	withdrawnAmount: 0
})

// 推广数据
const promotionData = ref({
	inviteCount: 0,
	activeCount: 0,
	todayEarnings: 0
})

// 页面加载
onLoad(() => {
	loadEarningsData()
	loadPromotionData()
})

// 页面显示时刷新数据
onShow(() => {
	loadEarningsData()
})

// 加载收益数据
const loadEarningsData = async () => {
	try {
		const res = await getUserBalance()
		earningsData.value = res.data
	} catch (error) {
		console.error('获取收益数据失败:', error)
	}
}

// 加载推广数据
const loadPromotionData = async () => {
	// TODO: 实现推广数据API调用
	// 这里先使用模拟数据
	promotionData.value = {
		inviteCount: 15,
		activeCount: 8,
		todayEarnings: 12.50
	}
}

// 跳转到提现页面
const navigateToWithdraw = () => {
	if (earningsData.value.availableAmount <= 0) {
		toast('暂无可提现余额')
		return
	}
	uni.navigateTo({
		url: '/pagesubs/promotion-center/withdraw-apply'
	})
}

// 跳转到收益明细
const navigateToRecords = () => {
	// TODO: 实现收益明细页面
	toast('功能开发中')
}

// 分享邀请码
const shareInviteCode = () => {
	// TODO: 实现分享邀请码功能
	toast('功能开发中')
}

// 生成推广海报
const generatePoster = () => {
	// TODO: 实现生成推广海报功能
	toast('功能开发中')
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx; // 为底部导航留出空间
}

.earnings-card {
	margin: 20rpx;
	padding: 40rpx;
	background: linear-gradient(135deg, #696CF3 0%, #9B59B6 100%);
	border-radius: 20rpx;
	color: white;

	.earnings-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;

		.header-left {
			display: flex;
			align-items: center;

			.header-title {
				margin-left: 10rpx;
				font-size: 36rpx;
				font-weight: 600;
			}
		}

		.header-right {
			display: flex;
			align-items: center;
			opacity: 0.8;

			.view-records {
				font-size: 26rpx;
				margin-right: 5rpx;
			}
		}
	}

	.earnings-content {
		display: flex;
		justify-content: space-between;
		margin-bottom: 40rpx;

		.earnings-item {
			text-align: center;

			.earnings-label {
				display: block;
				font-size: 24rpx;
				opacity: 0.8;
				margin-bottom: 10rpx;
			}

			.earnings-amount {
				display: block;
				font-size: 32rpx;
				font-weight: 600;

				&.highlight {
					font-size: 40rpx;
					color: #FFE066;
				}
			}
		}
	}

	.earnings-actions {
		text-align: center;

		.withdraw-btn {
			display: inline-flex;
			align-items: center;
			padding: 20rpx 60rpx;
			background-color: rgba(255, 255, 255, 0.2);
			border: 2rpx solid rgba(255, 255, 255, 0.3);
			border-radius: 50rpx;
			color: white;
			font-size: 28rpx;
			font-weight: 500;

			text {
				margin-left: 10rpx;
			}

			&:active {
				background-color: rgba(255, 255, 255, 0.1);
			}
		}
	}
}

.promotion-card,
.tools-card {
	margin: 20rpx;
	padding: 30rpx;
	background-color: white;
	border-radius: 20rpx;

	.card-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;

		.card-title {
			margin-left: 10rpx;
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}
}

.promotion-stats {
	display: flex;
	justify-content: space-around;

	.stat-item {
		text-align: center;

		.stat-number {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #696CF3;
			margin-bottom: 10rpx;
		}

		.stat-label {
			font-size: 26rpx;
			color: #666;
		}
	}
}

.tools-list {
	.tool-item {
		display: flex;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		.tool-icon {
			width: 80rpx;
			height: 80rpx;
			background-color: #f8f9ff;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 20rpx;
		}

		.tool-content {
			flex: 1;

			.tool-title {
				display: block;
				font-size: 30rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 5rpx;
			}

			.tool-desc {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
}
</style>
