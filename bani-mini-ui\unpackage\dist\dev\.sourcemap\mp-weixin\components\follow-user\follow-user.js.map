{"version": 3, "file": "follow-user.js", "sources": ["components/follow-user/follow-user.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovYmFuaS9jb2RlL2JhbmktbWluaS11aS9jb21wb25lbnRzL2ZvbGxvdy11c2VyL2ZvbGxvdy11c2VyLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"follow-user-container\">\n\t\t<!-- 用户列表 -->\n\t\t<z-paging ref=\"paging\" v-model=\"userList\" @query=\"queryUserList\" :auto=\"true\" :refresher-enabled=\"true\"\n\t\t\t:loading-more-enabled=\"true\" :empty-view-text=\"emptyText\" empty-view-img=\"\" :data-key=\"currentCategory\">\n\n\t\t\t<!-- 自定义刷新组件 -->\n\t\t\t<template #refresher=\"{ refresherStatus }\">\n\t\t\t\t<custom-refresher :refresher-status=\"refresherStatus\" />\n\t\t\t</template>\n\n\t\t\t<template #top>\n\t\t\t\t<!-- 好友分类标签 -->\n\t\t\t\t<view class=\"top-menu margin-split\" v-if=\"showCategory\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<uni-segmented-control :current=\"segmentedIndex\" :values=\"segmentedValues\" @clickItem=\"switchTab\"\n\t\t\t\t\t\tstyleType=\"text\" activeColor=\"#696CF3\">\n\t\t\t\t\t</uni-segmented-control>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t\t<view class=\"user-list-container margin-split\">\n\t\t\t\t<view class=\"user-item\" v-for=\"(item, index) in userList\" :key=\"index\" @click=\"handleUserClick(item)\">\n\t\t\t\t\t<view class=\"avatar-container\">\n\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.oppAvatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"online-status\" v-if=\"item.isOnline\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<view class=\"name-with-gender\">\n\t\t\t\t\t\t\t<text class=\"title\">{{ item.oppNickName }}</text>\n\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\n\t\t\t\t\t\t\t\t:class=\"item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\n\t\t\t\t\t\t\t\t:style=\"{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }\"></text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 用户信息行 -->\n\t\t\t\t\t\t<view class=\"user-info-row\">\n\t\t\t\t\t\t\t<text class=\"info-text\">{{ item.oppAge }} · {{ item.oppHeight }}{{\n\t\t\t\t\t\t\t\titem.oppCity ? ' · ' + item.oppCity : ''\n\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 关注时间行 -->\n\t\t\t\t\t\t<view class=\"follow-time-row\">\n\t\t\t\t\t\t\t<text class=\"time-text\">{{ item.createTime }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn\" @click.stop=\"handleUserAction(item)\">\n\t\t\t\t\t\t<text class=\"action-text\">{{ getUserActionText(item) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</z-paging>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted } from 'vue'\nimport { followUserPage, toggleUserFollow } from '@/api/my/follow'\nimport CustomRefresher from '@/components/custom-refresher/custom-refresher.vue'\n\n// Props\nconst props = defineProps({\n\t// 导航栏高度\n\tnavBarHeight: {\n\t\ttype: Number,\n\t\tdefault: 0\n\t},\n\t// 初始分类\n\tinitialCategory: {\n\t\ttype: String,\n\t\tdefault: 'mutual'\n\t},\n\t// 是否显示分类标签\n\tshowCategory: {\n\t\ttype: Boolean,\n\t\tdefault: true\n\t},\n\t// 主题色\n\tthemeColor: {\n\t\ttype: String,\n\t\tdefault: '#696CF3'\n\t}\n})\n\n// Emits\nconst emit = defineEmits(['userClick', 'userAction', 'categoryChange'])\n\n// 状态管理\nconst currentCategory = ref(props.initialCategory)\nconst userList = ref([])\nconst paging = ref(null)\n\n// 计数\nconst mutualCount = ref(0)\nconst followingCount = ref(0)\nconst followersCount = ref(0)\n\n// uni-segmented-control相关\nconst segmentedIndex = ref(0)\nconst segmentedValues = computed(() => {\n\tconst values = []\n\t// 关注\n\tif (followingCount.value > 0) {\n\t\tvalues.push(`关注(${followingCount.value})`)\n\t} else {\n\t\tvalues.push('关注')\n\t}\n\n\t// 粉丝\n\tif (followersCount.value > 0) {\n\t\tvalues.push(`粉丝(${followersCount.value})`)\n\t} else {\n\t\tvalues.push('粉丝')\n\t}\n\n\t// 互关\n\tif (mutualCount.value > 0) {\n\t\tvalues.push(`互关(${mutualCount.value})`)\n\t} else {\n\t\tvalues.push('互关')\n\t}\n\n\treturn values\n})\n\nconst emptyText = computed(() => {\n\tswitch (currentCategory.value) {\n\t\tcase 'mutual':\n\t\t\treturn '暂无互关好友'\n\t\tcase 'following':\n\t\t\treturn '暂无关注用户'\n\t\tcase 'followers':\n\t\t\treturn '暂无粉丝'\n\t\tdefault:\n\t\t\treturn '暂无数据'\n\t}\n})\n\n// 监听分类变化\nwatch(currentCategory, (newCategory) => {\n\temit('categoryChange', newCategory)\n\t// 更新segmentedIndex\n\tupdateSegmentedIndex()\n\t// 重新加载数据\n\tif (paging.value) {\n\t\tpaging.value.reload()\n\t}\n})\n\n// 更新segmentedIndex\nconst updateSegmentedIndex = () => {\n\tswitch (currentCategory.value) {\n\t\tcase 'following':\n\t\t\tsegmentedIndex.value = 0\n\t\t\tbreak\n\t\tcase 'followers':\n\t\t\tsegmentedIndex.value = 1\n\t\t\tbreak\n\t\tcase 'mutual':\n\t\t\tsegmentedIndex.value = 2\n\t\t\tbreak\n\t}\n}\n\n// 分类切换\nconst switchCategory = (category) => {\n\tif (currentCategory.value !== category) {\n\t\tcurrentCategory.value = category\n\t}\n}\n\n// uni-segmented-control点击处理\nconst switchTab = (e) => {\n\tif (segmentedIndex.value !== e.currentIndex) {\n\t\tsegmentedIndex.value = e.currentIndex\n\n\t\t// 根据索引设置分类\n\t\tswitch (e.currentIndex) {\n\t\t\tcase 0:\n\t\t\t\tcurrentCategory.value = 'following'\n\t\t\t\tbreak\n\t\t\tcase 1:\n\t\t\t\tcurrentCategory.value = 'followers'\n\t\t\t\tbreak\n\t\t\tcase 2:\n\t\t\t\tcurrentCategory.value = 'mutual'\n\t\t\t\tbreak\n\t\t}\n\t}\n}\n\n// 获取关系类型对应的API参数\nconst getRelationshipType = (category) => {\n\tswitch (category) {\n\t\tcase 'following':\n\t\t\treturn 2 // 我关注的\n\t\tcase 'followers':\n\t\t\treturn 1 // 关注我的\n\t\tcase 'mutual':\n\t\t\treturn 3 // 互关\n\t\tdefault:\n\t\t\treturn 2\n\t}\n}\n\n// z-paging查询数据\nconst queryUserList = (pageNo, pageSize) => {\n\tconst relationshipType = getRelationshipType(currentCategory.value)\n\tfollowUserPage({\n\t\ttype: relationshipType,\n\t\tpageSize: pageSize,\n\t\tpageNum: pageNo\n\t}).then(response => {\n\t\tconst formattedList = response.rows\n\t\tconsole.log('用户列表加载成功:', formattedList.length, '条数据')\n\n\t\t// 通知z-paging数据加载完成\n\t\tpaging.value.complete(formattedList)\n\t})\n}\n\n// 处理用户点击\nconst handleUserClick = (user) => {\n\tconsole.log('点击用户:', user)\n\temit('userClick', user)\n}\n\n// 处理用户操作\nconst handleUserAction = async (user) => {\n\tconsole.log('用户操作:', user)\n\n\tswitch (currentCategory.value) {\n\t\tcase 'mutual':\n\t\t\t// 跳转到打招呼页面\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pagesubs/personals/greeting/greeting?userId=${user.oppUserId}&nickName=${encodeURIComponent(user.oppNickName)}&avatar=${user.oppAvatar}`,\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('跳转打招呼页面失败:', err)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'following':\n\t\t\t// 已关注用户，取消关注\n\t\t\tawait handleUnfollow(user)\n\t\t\tbreak\n\t\tcase 'followers':\n\t\t\t// 粉丝，回关\n\t\t\tawait handleFollow(user)\n\t\t\tbreak\n\t\tdefault:\n\t\t\t// 默认关注\n\t\t\tawait handleFollow(user)\n\t\t\tbreak\n\t}\n\n\temit('userAction', user)\n}\n\n// 关注用户\nconst handleFollow = async (user) => {\n\tconsole.log('关注用户:', user.oppUserId)\n\n\tconst response = await toggleUserFollow(user.oppUserId, false)\n\n\tif (response.code === 1) {\n\t\tuni.showToast({\n\t\t\ttitle: '关注成功',\n\t\t\ticon: 'none',\n\t\t\tduration: 1500\n\t\t})\n\t\t// 刷新列表\n\t\tif (paging.value) {\n\t\t\tpaging.value.reload()\n\t\t}\n\t} else {\n\t\tconsole.error('关注操作失败:', response.msg || '未知错误')\n\t\tuni.showToast({\n\t\t\ttitle: response.msg || '关注失败，请重试',\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t})\n\t}\n}\n\n// 取消关注用户\nconst handleUnfollow = (user) => {\n\tconsole.log('取消关注用户:', user.oppUserId)\n\n\t// 显示确认对话框\n\tuni.showModal({\n\t\ttitle: '确认',\n\t\tcontent: `确定要取消关注 ${user.oppNickName} 吗？`,\n\t\tconfirmText: '取消关注',\n\t\tcancelText: '再想想',\n\t\tsuccess: (result) => {\n\t\t\tif (result.confirm) {\n\t\t\t\ttoggleUserFollow(user.oppUserId, true).then(response => {\n\t\t\t\t\t// 显示取消关注成功提示\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '取消关注成功',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n\n// 获取用户操作按钮文字\nconst getUserActionText = (user) => {\n\tswitch (currentCategory.value) {\n\t\tcase 'mutual':\n\t\t\treturn '打招呼'\n\t\tcase 'following':\n\t\t\treturn '取消关注'\n\t\tcase 'followers':\n\t\t\tif (user.oppIsFollowed) {\n\t\t\t\treturn '打招呼';\n\t\t\t}\n\t\t\treturn '回关'\n\t\tdefault:\n\t\t\treturn '关注'\n\t}\n}\n\n// 刷新数据\nconst refresh = () => {\n\tif (paging.value) {\n\t\tpaging.value.reload()\n\t}\n}\n\n// 暴露方法给父组件\ndefineExpose({\n\trefresh,\n\tswitchCategory\n})\n\n// 组件挂载时初始化\nonMounted(() => {\n\tconsole.log('follow-user组件mounted')\n\t// 初始化segmentedIndex\n\tupdateSegmentedIndex()\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.follow-user-container {\n\theight: 100%;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.user-list-container {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\toverflow: hidden;\n}\n\n.user-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 32rpx 24rpx;\n\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\tbackground: rgba(255, 255, 255, 0.95);\n\n\t&:active {\n\t\tbackground-color: rgba($primary-color, 0.05);\n\t}\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.avatar-container {\n\t\tposition: relative;\n\t\tmargin-right: $spacing-lg;\n\n\t\t.avatar {\n\t\t\twidth: 110rpx;\n\t\t\theight: 110rpx;\n\t\t\tborder-radius: $radius-full;\n\t\t\tbackground: $bg-secondary;\n\t\t\tbox-shadow: $shadow-sm;\n\t\t}\n\n\t\t.online-status {\n\t\t\tposition: absolute;\n\t\t\tbottom: 4rpx;\n\t\t\tright: 4rpx;\n\t\t\twidth: 20rpx;\n\t\t\theight: 20rpx;\n\t\t\tbackground: #4CAF50;\n\t\t\tborder: 2rpx solid $bg-primary;\n\t\t\tborder-radius: 50%;\n\t\t}\n\t}\n\n\t.content {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t\theight: 110rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: space-between;\n\t\tmargin-top: -20rpx;\n\n\t\t.name-with-gender {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tflex: 1;\n\t\t\tmin-width: 0;\n\n\t\t\t.title {\n\t\t\t\tfont-size: $title-size-md;\n\t\t\t\tfont-weight: $font-weight-medium;\n\t\t\t\tcolor: $text-primary;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t}\n\n\t\t\t.gender-icon {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\t\t}\n\n\t\t.user-info-row {\n\t\t\tmargin-bottom: 6rpx;\n\n\t\t\t.info-text {\n\t\t\t\tfont-size: $font-size-sm;\n\t\t\t\tcolor: $text-secondary;\n\t\t\t\tline-height: 1.4;\n\t\t\t}\n\t\t}\n\n\t\t.follow-time-row {\n\t\t\tmargin-bottom: 8rpx;\n\n\t\t\t.time-text {\n\t\t\t\tfont-size: $font-size-xs;\n\t\t\t\tcolor: $text-tertiary;\n\t\t\t\tline-height: 1.4;\n\t\t\t}\n\t\t}\n\n\t\t.subtitle {\n\t\t\tfont-size: $font-size-sm;\n\t\t\tcolor: $text-secondary;\n\t\t\tline-height: 1.4;\n\t\t\toverflow: hidden;\n\t\t\ttext-overflow: ellipsis;\n\t\t\twhite-space: nowrap;\n\t\t\tmargin-bottom: 8rpx;\n\t\t}\n\n\t\t.user-tags {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tgap: 8rpx;\n\n\t\t\t.tag {\n\t\t\t\tpadding: 4rpx 8rpx;\n\t\t\t\tbackground: $bg-secondary;\n\t\t\t\tcolor: $text-secondary;\n\t\t\t\tfont-size: $font-size-xs;\n\t\t\t\tborder-radius: $radius-sm;\n\t\t\t}\n\t\t}\n\t}\n\n\t.action-btn {\n\t\tpadding: 12rpx 20rpx;\n\t\tbackground: linear-gradient(135deg, #696CF3 0%, #5A5FE8 100%);\n\t\tcolor: $text-white;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 24rpx;\n\t\tmargin-left: 20rpx;\n\t\tflex-shrink: 0;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.3);\n\t\tborder: none;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.4);\n\t\t}\n\n\t\t.action-text {\n\t\t\tcolor: $text-white;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n}\n</style>\n", "import Component from 'E:/bani/code/bani-mini-ui/components/follow-user/follow-user.vue'\nwx.createComponent(Component)"], "names": ["CustomRefresher", "props", "__props", "emit", "__emit", "currentCategory", "ref", "userList", "paging", "mutualCount", "followingCount", "followersCount", "segmentedIndex", "segmentedValues", "computed", "values", "emptyText", "watch", "newCategory", "updateSegmentedIndex", "switchCategory", "category", "switchTab", "e", "getRelationshipType", "queryUserList", "pageNo", "pageSize", "relationshipType", "followUserPage", "response", "formattedList", "uni", "handleUserClick", "user", "handleUserAction", "err", "handleUnfollow", "handleFollow", "toggle<PERSON>ser<PERSON><PERSON>ow", "result", "getUserActionText", "__expose", "onMounted", "Component"], "mappings": "sYAuDA,MAAMA,EAAkB,IAAW,6TAGnC,MAAMC,EAAQC,EAwBRC,EAAOC,EAGPC,EAAkBC,EAAAA,IAAIL,EAAM,eAAe,EAC3CM,EAAWD,EAAG,IAAC,EAAE,EACjBE,EAASF,EAAG,IAAC,IAAI,EAGjBG,EAAcH,EAAG,IAAC,CAAC,EACnBI,EAAiBJ,EAAG,IAAC,CAAC,EACtBK,EAAiBL,EAAG,IAAC,CAAC,EAGtBM,EAAiBN,EAAG,IAAC,CAAC,EACtBO,EAAkBC,EAAQ,SAAC,IAAM,CACtC,MAAMC,EAAS,CAAE,EAEjB,OAAIL,EAAe,MAAQ,EAC1BK,EAAO,KAAK,MAAML,EAAe,KAAK,GAAG,EAEzCK,EAAO,KAAK,IAAI,EAIbJ,EAAe,MAAQ,EAC1BI,EAAO,KAAK,MAAMJ,EAAe,KAAK,GAAG,EAEzCI,EAAO,KAAK,IAAI,EAIbN,EAAY,MAAQ,EACvBM,EAAO,KAAK,MAAMN,EAAY,KAAK,GAAG,EAEtCM,EAAO,KAAK,IAAI,EAGVA,CACR,CAAC,EAEKC,EAAYF,EAAQ,SAAC,IAAM,CAChC,OAAQT,EAAgB,MAAK,CAC5B,IAAK,SACJ,MAAO,SACR,IAAK,YACJ,MAAO,SACR,IAAK,YACJ,MAAO,OACR,QACC,MAAO,MACR,CACF,CAAC,EAGDY,EAAAA,MAAMZ,EAAkBa,GAAgB,CACvCf,EAAK,iBAAkBe,CAAW,EAElCC,EAAsB,EAElBX,EAAO,OACVA,EAAO,MAAM,OAAQ,CAEvB,CAAC,EAGD,MAAMW,EAAuB,IAAM,CAClC,OAAQd,EAAgB,MAAK,CAC5B,IAAK,YACJO,EAAe,MAAQ,EACvB,MACD,IAAK,YACJA,EAAe,MAAQ,EACvB,MACD,IAAK,SACJA,EAAe,MAAQ,EACvB,KACD,CACF,EAGMQ,EAAkBC,GAAa,CAChChB,EAAgB,QAAUgB,IAC7BhB,EAAgB,MAAQgB,EAE1B,EAGMC,EAAaC,GAAM,CACxB,GAAIX,EAAe,QAAUW,EAAE,aAI9B,OAHAX,EAAe,MAAQW,EAAE,aAGjBA,EAAE,aAAY,CACrB,IAAK,GACJlB,EAAgB,MAAQ,YACxB,MACD,IAAK,GACJA,EAAgB,MAAQ,YACxB,MACD,IAAK,GACJA,EAAgB,MAAQ,SACxB,KACD,CAEH,EAGMmB,EAAuBH,GAAa,CACzC,OAAQA,EAAQ,CACf,IAAK,YACJ,MAAO,GACR,IAAK,YACJ,MAAO,GACR,IAAK,SACJ,MAAO,GACR,QACC,MAAO,EACR,CACF,EAGMI,EAAgB,CAACC,EAAQC,IAAa,CAC3C,MAAMC,EAAmBJ,EAAoBnB,EAAgB,KAAK,EAClEwB,iBAAe,CACd,KAAMD,EACN,SAAUD,EACV,QAASD,CACX,CAAE,EAAE,KAAKI,GAAY,CACnB,MAAMC,EAAgBD,EAAS,KAC/BE,EAAY,MAAA,MAAA,MAAA,gDAAA,YAAaD,EAAc,OAAQ,KAAK,EAGpDvB,EAAO,MAAM,SAASuB,CAAa,CACrC,CAAE,CACF,EAGME,EAAmBC,GAAS,CACjCF,EAAAA,MAAY,MAAA,MAAA,gDAAA,QAASE,CAAI,EACzB/B,EAAK,YAAa+B,CAAI,CACvB,EAGMC,EAAmB,MAAOD,GAAS,CAGxC,OAFAF,EAAAA,MAAY,MAAA,MAAA,gDAAA,QAASE,CAAI,EAEjB7B,EAAgB,MAAK,CAC5B,IAAK,SAEJ2B,EAAAA,MAAI,WAAW,CACd,IAAK,gDAAgDE,EAAK,SAAS,aAAa,mBAAmBA,EAAK,WAAW,CAAC,WAAWA,EAAK,SAAS,GAC7I,KAAOE,GAAQ,CACdJ,EAAAA,oEAAc,aAAcI,CAAG,EAC/BJ,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACZ,CAAM,CACD,CACL,CAAI,EACD,MACD,IAAK,YAEJ,MAAMK,EAAeH,CAAI,EACzB,MACD,IAAK,YAEJ,MAAMI,EAAaJ,CAAI,EACvB,MACD,QAEC,MAAMI,EAAaJ,CAAI,EACvB,KACD,CAED/B,EAAK,aAAc+B,CAAI,CACxB,EAGMI,EAAe,MAAOJ,GAAS,CACpCF,EAAA,MAAA,MAAA,MAAA,gDAAY,QAASE,EAAK,SAAS,EAEnC,MAAMJ,EAAW,MAAMS,EAAAA,iBAAiBL,EAAK,UAAW,EAAK,EAEzDJ,EAAS,OAAS,GACrBE,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACN,SAAU,IACb,CAAG,EAEGxB,EAAO,OACVA,EAAO,MAAM,OAAQ,IAGtBwB,EAAA,MAAA,MAAA,QAAA,gDAAc,UAAWF,EAAS,KAAO,MAAM,EAC/CE,EAAAA,MAAI,UAAU,CACb,MAAOF,EAAS,KAAO,WACvB,KAAM,OACN,SAAU,GACb,CAAG,EAEH,EAGMO,EAAkBH,GAAS,CAChCF,EAAA,MAAA,MAAA,MAAA,gDAAY,UAAWE,EAAK,SAAS,EAGrCF,EAAAA,MAAI,UAAU,CACb,MAAO,KACP,QAAS,WAAWE,EAAK,WAAW,MACpC,YAAa,OACb,WAAY,MACZ,QAAUM,GAAW,CAChBA,EAAO,SACVD,EAAgB,iBAACL,EAAK,UAAW,EAAI,EAAE,KAAKJ,GAAY,CAEvDE,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,OACN,SAAU,IAChB,CAAM,CACN,CAAK,CAEF,CACH,CAAE,CACF,EAGMS,EAAqBP,GAAS,CACnC,OAAQ7B,EAAgB,MAAK,CAC5B,IAAK,SACJ,MAAO,MACR,IAAK,YACJ,MAAO,OACR,IAAK,YACJ,OAAI6B,EAAK,cACD,MAED,KACR,QACC,MAAO,IACR,CACF,EAUA,OAAAQ,EAAa,CACZ,QARe,IAAM,CACjBlC,EAAO,OACVA,EAAO,MAAM,OAAQ,CAEvB,EAKC,eAAAY,CACD,CAAC,EAGDuB,EAAAA,UAAU,IAAM,CACfX,EAAAA,MAAA,MAAA,MAAA,gDAAY,sBAAsB,EAElCb,EAAsB,CACvB,CAAC,k5BCxVD,GAAG,gBAAgByB,CAAS"}