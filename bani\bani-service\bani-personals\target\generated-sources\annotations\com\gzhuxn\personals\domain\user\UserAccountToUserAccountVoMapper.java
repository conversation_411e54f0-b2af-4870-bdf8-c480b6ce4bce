package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserAccountBoToUserAccountMapper;
import com.gzhuxn.personals.domain.user.vo.UserAccountVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAccountBoToUserAccountMapper.class,UserAccountToAppUserAccountDetailVoMapper.class},
    imports = {}
)
public interface UserAccountToUserAccountVoMapper extends BaseMapper<UserAccount, UserAccountVo> {
}
