package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAuthApplyToUserAuthApplyVoMapperImpl implements UserAuthApplyToUserAuthApplyVoMapper {

    @Override
    public UserAuthApplyVo convert(UserAuthApply arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAuthApplyVo userAuthApplyVo = new UserAuthApplyVo();

        userAuthApplyVo.setId( arg0.getId() );
        userAuthApplyVo.setUserId( arg0.getUserId() );
        userAuthApplyVo.setType( arg0.getType() );
        userAuthApplyVo.setAuditDesc( arg0.getAuditDesc() );
        userAuthApplyVo.setAuthUserId( arg0.getAuthUserId() );
        userAuthApplyVo.setAuthTime( arg0.getAuthTime() );
        userAuthApplyVo.setContentJs( arg0.getContentJs() );

        return userAuthApplyVo;
    }

    @Override
    public UserAuthApplyVo convert(UserAuthApply arg0, UserAuthApplyVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setAuditDesc( arg0.getAuditDesc() );
        arg1.setAuthUserId( arg0.getAuthUserId() );
        arg1.setAuthTime( arg0.getAuthTime() );
        arg1.setContentJs( arg0.getContentJs() );

        return arg1;
    }
}
