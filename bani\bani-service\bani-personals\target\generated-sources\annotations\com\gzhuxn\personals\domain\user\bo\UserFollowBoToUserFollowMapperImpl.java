package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserFollow;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserFollowBoToUserFollowMapperImpl implements UserFollowBoToUserFollowMapper {

    @Override
    public UserFollow convert(UserFollowBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserFollow userFollow = new UserFollow();

        userFollow.setType( arg0.getType() );
        userFollow.setBusinessId( arg0.getBusinessId() );

        return userFollow;
    }

    @Override
    public UserFollow convert(UserFollowBo arg0, UserFollow arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
