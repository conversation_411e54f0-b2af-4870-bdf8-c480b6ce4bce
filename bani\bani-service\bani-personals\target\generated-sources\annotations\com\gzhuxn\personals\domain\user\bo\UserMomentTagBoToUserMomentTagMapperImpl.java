package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserMomentTag;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentTagBoToUserMomentTagMapperImpl implements UserMomentTagBoToUserMomentTagMapper {

    @Override
    public UserMomentTag convert(UserMomentTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMomentTag userMomentTag = new UserMomentTag();

        userMomentTag.setSearchValue( arg0.getSearchValue() );
        userMomentTag.setCreateBy( arg0.getCreateBy() );
        userMomentTag.setCreateTime( arg0.getCreateTime() );
        userMomentTag.setUpdateBy( arg0.getUpdateBy() );
        userMomentTag.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userMomentTag.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userMomentTag.setCreateDept( arg0.getCreateDept() );
        userMomentTag.setId( arg0.getId() );
        userMomentTag.setMomentId( arg0.getMomentId() );
        if ( arg0.getTagVal() != null ) {
            userMomentTag.setTagVal( Long.parseLong( arg0.getTagVal() ) );
        }
        userMomentTag.setTagValName( arg0.getTagValName() );

        return userMomentTag;
    }

    @Override
    public UserMomentTag convert(UserMomentTagBo arg0, UserMomentTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setMomentId( arg0.getMomentId() );
        if ( arg0.getTagVal() != null ) {
            arg1.setTagVal( Long.parseLong( arg0.getTagVal() ) );
        }
        else {
            arg1.setTagVal( null );
        }
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
