package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.bo.MsgGroupUserBoToMsgGroupUserMapper;
import com.gzhuxn.personals.domain.message.vo.MsgGroupUserVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupUserBoToMsgGroupUserMapper.class,MsgGroupUserToAppMsgGroupUserVoMapper.class,MsgGroupUserToAppMsgGroupDetailUserVoMapper.class},
    imports = {}
)
public interface MsgGroupUserToMsgGroupUserVoMapper extends BaseMapper<MsgGroupUser, MsgGroupUserVo> {
}
