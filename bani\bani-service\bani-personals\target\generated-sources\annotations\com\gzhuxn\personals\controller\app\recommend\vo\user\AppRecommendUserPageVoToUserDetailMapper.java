package com.gzhuxn.personals.controller.app.recommend.vo.user;

import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.UserDetailToAppRecommendUserPageVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserDetailToAppRecommendUserPageVoMapper.class},
    imports = {}
)
public interface AppRecommendUserPageVoToUserDetailMapper extends BaseMapper<AppRecommendUserPageVo, UserDetail> {
  @Mapping(
      target = "addrProvinceCode",
      source = "addrProvince"
  )
  @Mapping(
      target = "addrNewProvinceCode",
      source = "addrNewProvince"
  )
  UserDetail convert(AppRecommendUserPageVo source);

  @Mapping(
      target = "addrProvinceCode",
      source = "addrProvince"
  )
  @Mapping(
      target = "addrNewProvinceCode",
      source = "addrNewProvince"
  )
  UserDetail convert(AppRecommendUserPageVo source, @MappingTarget UserDetail target);
}
