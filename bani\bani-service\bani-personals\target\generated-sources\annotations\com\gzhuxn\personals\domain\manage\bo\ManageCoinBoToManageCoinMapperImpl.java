package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageCoin;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageCoinBoToManageCoinMapperImpl implements ManageCoinBoToManageCoinMapper {

    @Override
    public ManageCoin convert(ManageCoinBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageCoin manageCoin = new ManageCoin();

        manageCoin.setSearchValue( arg0.getSearchValue() );
        manageCoin.setCreateBy( arg0.getCreateBy() );
        manageCoin.setCreateTime( arg0.getCreateTime() );
        manageCoin.setUpdateBy( arg0.getUpdateBy() );
        manageCoin.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            manageCoin.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        manageCoin.setCreateDept( arg0.getCreateDept() );
        manageCoin.setId( arg0.getId() );
        manageCoin.setType( arg0.getType() );
        manageCoin.setSubType( arg0.getSubType() );
        manageCoin.setName( arg0.getName() );
        manageCoin.setCoin( arg0.getCoin() );

        return manageCoin;
    }

    @Override
    public ManageCoin convert(ManageCoinBo arg0, ManageCoin arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );
        arg1.setName( arg0.getName() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
