package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.user.AppRecommendUserPageVoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserDetailBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserFullBaseBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserLocationBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.vo.AppUserBaseVoToUserDetailMapper;
import com.gzhuxn.personals.domain.audit.vo.UserDetailAuditVo;
import com.gzhuxn.personals.domain.user.bo.UserDetailBoToUserDetailMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserDetailBoToUserDetailMapper.class,AppUserFullBaseBoToUserDetailMapper.class,AppUserLocationBoToUserDetailMapper.class,AppRecommendUserPageVoToUserDetailMapper.class,UserDetailBoToUserDetailMapper.class,AppUserBaseVoToUserDetailMapper.class,AppUserBaseBoToUserDetailMapper.class,UserDetailToAppUserFullBaseVoMapper.class,UserDetailToAppUserMyProfileVoMapper.class,UserDetailToUserDetailVoMapper.class,UserDetailToAppRecommendUserPageVoMapper.class,UserDetailToAppUserEditDetailVoMapper.class,UserDetailToAdminUserDetailInfoVoMapper.class,UserDetailToAdminContentAuditUserDetailVoMapper.class,UserDetailToAppUserBaseVoMapper.class,UserDetailToAdminUserDetailPageVoMapper.class},
    imports = {}
)
public interface UserDetailToUserDetailAuditVoMapper extends BaseMapper<UserDetail, UserDetailAuditVo> {
}
