package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.personals.domain.activity.ActivityFee;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityFeeBoToActivityFeeMapperImpl implements ActivityFeeBoToActivityFeeMapper {

    @Override
    public ActivityFee convert(ActivityFeeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActivityFee activityFee = new ActivityFee();

        activityFee.setSearchValue( arg0.getSearchValue() );
        activityFee.setCreateBy( arg0.getCreateBy() );
        activityFee.setCreateTime( arg0.getCreateTime() );
        activityFee.setUpdateBy( arg0.getUpdateBy() );
        activityFee.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            activityFee.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        activityFee.setCreateDept( arg0.getCreateDept() );
        activityFee.setId( arg0.getId() );
        activityFee.setActivityId( arg0.getActivityId() );
        activityFee.setType( arg0.getType() );
        activityFee.setBusinessId( arg0.getBusinessId() );
        activityFee.setAmount( arg0.getAmount() );
        activityFee.setContentJs( arg0.getContentJs() );

        return activityFee;
    }

    @Override
    public ActivityFee convert(ActivityFeeBo arg0, ActivityFee arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setContentJs( arg0.getContentJs() );

        return arg1;
    }
}
