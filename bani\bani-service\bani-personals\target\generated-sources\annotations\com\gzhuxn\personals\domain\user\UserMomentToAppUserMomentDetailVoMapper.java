package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentVoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentCreateBoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentUpdateBoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.vo.moment.AppUserMomentDetailVo;
import com.gzhuxn.personals.domain.user.bo.UserMomentBoToUserMomentMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserMomentCreateBoToUserMomentMapper.class,AppUserMomentUpdateBoToUserMomentMapper.class,AppRecommendMomentVoToUserMomentMapper.class,UserMomentBoToUserMomentMapper.class,UserMomentToAppUserMomentPageVoMapper.class,UserMomentToAppRecommendMomentPageVoMapper.class,UserMomentToAppRecommendMomentDetailVoMapper.class,UserMomentToUserMomentVoMapper.class,UserMomentToAppRecommendMomentVoMapper.class},
    imports = {}
)
public interface UserMomentToAppUserMomentDetailVoMapper extends BaseMapper<UserMoment, AppUserMomentDetailVo> {
  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserMomentDetailVo convert(UserMoment source);

  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserMomentDetailVo convert(UserMoment source, @MappingTarget AppUserMomentDetailVo target);
}
