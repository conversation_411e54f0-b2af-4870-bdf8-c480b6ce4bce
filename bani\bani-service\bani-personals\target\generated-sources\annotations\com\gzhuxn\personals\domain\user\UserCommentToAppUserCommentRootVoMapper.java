package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.comment.AppUserCommentCreateBoToUserCommentMapper;
import com.gzhuxn.personals.controller.app.user.vo.comment.AppUserCommentRootVo;
import com.gzhuxn.personals.domain.user.bo.UserCommentBoToUserCommentMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserCommentCreateBoToUserCommentMapper.class,UserCommentBoToUserCommentMapper.class,UserCommentToUserCommentVoMapper.class,UserCommentToAdminContentAuditUserCommentVoMapper.class,UserCommentToAppUserCommentItemVoMapper.class},
    imports = {}
)
public interface UserCommentToAppUserCommentRootVoMapper extends BaseMapper<UserComment, AppUserCommentRootVo> {
}
