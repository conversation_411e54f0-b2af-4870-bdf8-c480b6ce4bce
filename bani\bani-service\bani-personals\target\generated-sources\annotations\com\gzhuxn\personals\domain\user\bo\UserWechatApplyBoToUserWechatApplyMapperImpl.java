package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserWechatApply;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserWechatApplyBoToUserWechatApplyMapperImpl implements UserWechatApplyBoToUserWechatApplyMapper {

    @Override
    public UserWechatApply convert(UserWechatApplyBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWechatApply userWechatApply = new UserWechatApply();

        userWechatApply.setSearchValue( arg0.getSearchValue() );
        userWechatApply.setCreateBy( arg0.getCreateBy() );
        userWechatApply.setCreateTime( arg0.getCreateTime() );
        userWechatApply.setUpdateBy( arg0.getUpdateBy() );
        userWechatApply.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userWechatApply.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userWechatApply.setCreateDept( arg0.getCreateDept() );
        userWechatApply.setId( arg0.getId() );
        userWechatApply.setUserId( arg0.getUserId() );
        userWechatApply.setOppositeUserId( arg0.getOppositeUserId() );
        userWechatApply.setContent( arg0.getContent() );
        userWechatApply.setStatus( arg0.getStatus() );

        return userWechatApply;
    }

    @Override
    public UserWechatApply convert(UserWechatApplyBo arg0, UserWechatApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
