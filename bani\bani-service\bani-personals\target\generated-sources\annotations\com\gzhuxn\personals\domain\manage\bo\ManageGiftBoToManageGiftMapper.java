package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageGift;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface ManageGiftBoToManageGiftMapper extends BaseMapper<ManageGiftBo, ManageGift> {
}
