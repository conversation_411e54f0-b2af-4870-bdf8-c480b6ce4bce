package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserInvitation;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserInvitationBoToUserInvitationMapperImpl implements UserInvitationBoToUserInvitationMapper {

    @Override
    public UserInvitation convert(UserInvitationBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserInvitation userInvitation = new UserInvitation();

        userInvitation.setSearchValue( arg0.getSearchValue() );
        userInvitation.setCreateBy( arg0.getCreateBy() );
        userInvitation.setCreateTime( arg0.getCreateTime() );
        userInvitation.setUpdateBy( arg0.getUpdateBy() );
        userInvitation.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userInvitation.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userInvitation.setCreateDept( arg0.getCreateDept() );
        userInvitation.setId( arg0.getId() );
        userInvitation.setUserId( arg0.getUserId() );
        userInvitation.setOppositeUserId( arg0.getOppositeUserId() );

        return userInvitation;
    }

    @Override
    public UserInvitation convert(UserInvitationBo arg0, UserInvitation arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
