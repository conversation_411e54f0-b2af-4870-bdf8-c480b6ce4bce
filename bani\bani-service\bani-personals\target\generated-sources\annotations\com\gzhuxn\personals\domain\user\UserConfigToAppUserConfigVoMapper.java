package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.config.AppUserConfigBoToUserConfigMapper;
import com.gzhuxn.personals.controller.app.user.vo.config.AppUserConfigVo;
import com.gzhuxn.personals.domain.user.bo.UserConfigBoToUserConfigMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserConfigBoToUserConfigMapper.class,AppUserConfigBoToUserConfigMapper.class,UserConfigToUserConfigVoMapper.class},
    imports = {}
)
public interface UserConfigToAppUserConfigVoMapper extends BaseMapper<UserConfig, AppUserConfigVo> {
}
