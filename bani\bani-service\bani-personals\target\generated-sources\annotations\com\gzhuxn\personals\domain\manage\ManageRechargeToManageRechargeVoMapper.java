package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageRechargeVoToManageRechargeMapper;
import com.gzhuxn.personals.domain.manage.bo.ManageRechargeBoToManageRechargeMapper;
import com.gzhuxn.personals.domain.manage.vo.ManageRechargeVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppManageRechargeVoToManageRechargeMapper.class,ManageRechargeBoToManageRechargeMapper.class,ManageRechargeToAppManageRechargeVoMapper.class},
    imports = {}
)
public interface ManageRechargeToManageRechargeVoMapper extends BaseMapper<ManageRecharge, ManageRechargeVo> {
}
