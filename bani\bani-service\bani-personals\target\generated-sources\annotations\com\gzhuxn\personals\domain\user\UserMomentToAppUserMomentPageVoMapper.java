package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentVoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentCreateBoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.bo.moment.AppUserMomentUpdateBoToUserMomentMapper;
import com.gzhuxn.personals.controller.app.user.vo.moment.AppUserMomentPageVo;
import com.gzhuxn.personals.domain.user.bo.UserMomentBoToUserMomentMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserMomentCreateBoToUserMomentMapper.class,AppUserMomentUpdateBoToUserMomentMapper.class,AppRecommendMomentVoToUserMomentMapper.class,UserMomentBoToUserMomentMapper.class,UserMomentToAppRecommendMomentPageVoMapper.class,UserMomentToAppRecommendMomentDetailVoMapper.class,UserMomentToUserMomentVoMapper.class,UserMomentToAppUserMomentDetailVoMapper.class,UserMomentToAppRecommendMomentVoMapper.class},
    imports = {}
)
public interface UserMomentToAppUserMomentPageVoMapper extends BaseMapper<UserMoment, AppUserMomentPageVo> {
  @Mapping(
      target = "cityName",
      source = "cityCode"
  )
  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserMomentPageVo convert(UserMoment source);

  @Mapping(
      target = "cityName",
      source = "cityCode"
  )
  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserMomentPageVo convert(UserMoment source, @MappingTarget AppUserMomentPageVo target);
}
