<template>
	<scroll-nav-page title="资料等级" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 主要内容 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<!-- 当前等级展示 -->
					<view class="current-level">
						<!-- 背景文字 -->
						<view class="bg-text">CURRENT LEVEL</view>

						<view class="level-info">
							<text class="level-label">当前等级：</text>
							<text class="level-name">{{ getCurrentLevelName() }}</text>
							<view class="level-desc" v-if="userLevelInfo.userLevelDesc">
							</view>
						</view>
						<view class="level-icon">
							<view class="cat-icon">
								<uni-icons :type="getCurrentLevelIcon()" size="40" color="#696CF3"></uni-icons>
							</view>
							<text class="icon-label">{{ getCurrentLevelName() }}</text>
							<view class="vip-badge" v-if="userLevelInfo.isVip">
								<text class="vip-text">VIP</text>
							</view>
						</view>
					</view>

					<!-- 等级进度卡片 -->
					<view class="level-progress-card">
						<!-- 等级图标进度条 -->
						<view class="level-icons">
							<view class="level-step"
								:class="{ active: userLevelInfo.userLevel >= 1, selected: selectedLevel === 1, current: userLevelInfo.userLevel === 1 }"
								@click="selectLevel(1)">
								<view class="step-icon">
									<uni-icons type="star" size="24"
										:color="selectedLevel === 1 ? '#696CF3' : '#fff'"></uni-icons>
								</view>
								<text class="step-name">萌新</text>
								<view class="current-badge" v-if="userLevelInfo.userLevel === 1">
									<text class="badge-text">当前</text>
								</view>
							</view>
							<view class="progress-line" :class="{ active: userLevelInfo.userLevel >= 2 }"></view>
							<view class="level-step"
								:class="{ active: userLevelInfo.userLevel >= 2, selected: selectedLevel === 2, current: userLevelInfo.userLevel === 2 }"
								@click="selectLevel(2)">
								<view class="step-icon">
									<uni-icons type="heart" size="24"
										:color="selectedLevel === 2 ? '#696CF3' : '#fff'"></uni-icons>
								</view>
								<text class="step-name">缘友</text>
								<view class="current-badge" v-if="userLevelInfo.userLevel === 2">
									<text class="badge-text">当前</text>
								</view>
							</view>
							<view class="progress-line" :class="{ active: userLevelInfo.userLevel >= 3 }"></view>
							<view class="level-step"
								:class="{ active: userLevelInfo.userLevel >= 3, selected: selectedLevel === 3, current: userLevelInfo.userLevel === 3 }"
								@click="selectLevel(3)">
								<view class="step-icon">
									<uni-icons type="fire" size="24"
										:color="selectedLevel === 3 ? '#696CF3' : '#fff'"></uni-icons>
								</view>
								<text class="step-name">情咖</text>
								<view class="current-badge" v-if="userLevelInfo.userLevel === 3">
									<text class="badge-text">当前</text>
								</view>
							</view>
							<view class="progress-line" :class="{ active: userLevelInfo.userLevel >= 4 }"></view>
							<view class="level-step"
								:class="{ active: userLevelInfo.userLevel >= 4, selected: selectedLevel === 4, current: userLevelInfo.userLevel === 4 }"
								@click="selectLevel(4)">
								<view class="step-icon">
									<uni-icons type="medal" size="24"
										:color="selectedLevel === 4 ? '#696CF3' : '#fff'"></uni-icons>
								</view>
								<text class="step-name">牵缘</text>
								<view class="current-badge" v-if="userLevelInfo.userLevel === 4">
									<text class="badge-text">当前</text>
								</view>
							</view>
						</view>

						<!-- 升级提示 -->
						<view class="upgrade-tip">
							<text>升级至“牵缘”，认识更优质的Ta</text>
						</view>
					</view>

					<!-- 任务列表 -->
					<view class="task-section">
						<view class="section-title">
							<text>完成以下任务即可升级为“牵缘”</text>
						</view>

						<!-- 加载状态 -->
						<view class="loading-container" v-if="isLoadingTasks">
							<text class="loading-text">加载中...</text>
						</view>

						<!-- 任务列表 -->
						<view class="task-list" v-else-if="taskList.length > 0">
							<view class="task-item" v-for="task in taskList" :key="task.taskId">
								<view class="task-info">
									<view class="task-title">{{ task.taskName }}</view>
									<view class="task-reward">
										<image src="/static/image/icons/coin.png" class="coin-icon"></image>
										<text class="reward-text">+{{ task.coin }}花瓣</text>
									</view>
								</view>
								<view class="task-action">
									<button class="action-btn" :class="task.isCompleted ? 'completed' : 'primary'"
										@click="handleTaskAction(task)">
										{{ task.isCompleted ? '已完成' : '去完成' }}
									</button>
								</view>
							</view>
						</view>

						<!-- 空状态 -->
						<view class="empty-tasks" v-else>
							<view class="empty-icon">
								<uni-icons type="info" size="48" color="#ccc"></uni-icons>
							</view>
							<text class="empty-text">暂无任务</text>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { onPageScroll } from '@dcloudio/uni-app'
import globalConfig from '@/config'
import { getTaskListByLevel, USER_LEVELS, TASK_STATUS } from '@/api/my/coin'
import { getUserLevel } from '@/api/my/my'


// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const currentLevel = ref(1) // 当前等级：1-萌新, 2-缘友, 3-情咖, 4-牵缘
const selectedLevel = ref(1) // 选中的等级，默认选中当前等级

// 用户等级信息
const userLevelInfo = ref({
	userId: null,
	userLevel: 1,
	userLevelDesc: '',
	nickName: '',
	isVip: false
})
const isLoadingUserLevel = ref(false)

// 任务列表数据
const taskList = ref([])
const isLoadingTasks = ref(false)

// 等级名称映射
const levelNames = {
	[USER_LEVELS.NEWBIE]: '萌新',
	[USER_LEVELS.FRIEND]: '缘友',
	[USER_LEVELS.EXPERT]: '情咖',
	[USER_LEVELS.MASTER]: '牵缘'
}

// 等级图标映射
const levelIcons = {
	[USER_LEVELS.NEWBIE]: 'star',
	[USER_LEVELS.FRIEND]: 'heart',
	[USER_LEVELS.EXPERT]: 'fire',
	[USER_LEVELS.MASTER]: 'medal'
}

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 加载用户等级信息
const loadUserLevel = async () => {
	try {
		isLoadingUserLevel.value = true
		const response = await getUserLevel()
		if (response.code === 200 && response.data) {
			userLevelInfo.value = response.data
			// 更新当前等级和选中等级
			currentLevel.value = response.data.userLevel
			selectedLevel.value = response.data.userLevel
			console.log('用户等级信息加载成功:', response.data)
		}
	} catch (error) {
		console.error('加载用户等级信息失败:', error)
		uni.showToast({
			title: '加载用户等级失败',
			icon: 'none'
		})
	} finally {
		isLoadingUserLevel.value = false
	}
}

// 页面挂载时初始化
onMounted(async () => {
	// 先加载用户等级信息
	await loadUserLevel()
	// 然后加载对应等级的任务列表
	loadTaskList(selectedLevel.value)
})

// 监听选中等级变化
watch(selectedLevel, (newLevel) => {
	loadTaskList(newLevel)
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor
}

// 加载任务列表
const loadTaskList = async (level) => {
	try {
		isLoadingTasks.value = true
		const response = await getTaskListByLevel(level)
		if (response.code === 200 && response.data) {
			taskList.value = response.data
			console.log('任务列表加载成功:', response.data)

			// 检查每个任务的path字段
			response.data.forEach((task, index) => {
				console.log(`任务${index + 1}:`, {
					id: task.id,
					title: task.title,
					isCompleted: task.isCompleted,
					path: task.path,
					taskType: task.taskType
				})
			})
		} else {
			taskList.value = []
			console.log('任务列表为空或加载失败:', response)
		}
	} catch (error) {
		console.error('加载任务列表失败:', error)
		taskList.value = []
		uni.showToast({
			title: '加载任务列表失败',
			icon: 'none'
		})
	} finally {
		isLoadingTasks.value = false
	}
}

// 选择等级
const selectLevel = (level) => {
	selectedLevel.value = level
	// 根据选中的等级加载对应的任务列表
	loadTaskList(level)
	console.log('选中等级:', level, '等级名称:', levelNames[level])
}

// 获取当前等级名称
const getCurrentLevelName = () => {
	return levelNames[userLevelInfo.value.userLevel] || '萌新'
}

// 获取当前等级图标
const getCurrentLevelIcon = () => {
	return levelIcons[userLevelInfo.value.userLevel] || 'star'
}

// 获取任务区域标题
const getTaskSectionTitle = () => {
	const nextLevel = selectedLevel.value + 1
	const nextLevelName = levelNames[nextLevel] || '最高等级'
	if (selectedLevel.value >= 4) {
		return '您已达到最高等级'
	}
	return `完成以下任务即可升级为"${nextLevelName}"`
}

// 处理任务操作
const handleTaskAction = (task) => {
	if (task.isCompleted) {
		return
	}
	// 检查任务是否有跳转路径
	if (task.path && task.path.trim()) {
		const targetPath = task.path.trim()
		uni.navigateTo({
			url: targetPath
		})
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #f0f2ff 0%, #fafbff 50%, #fff 100%);
}

.main-container {
	min-height: 100vh;
	box-sizing: border-box;
	padding: 0 20rpx 120rpx;
}

// 当前等级展示
.current-level {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 30rpx;
	margin: 20rpx 0 30rpx;
	background: linear-gradient(135deg, rgba($primary-color, 0.08), rgba($primary-color, 0.15));
	border-radius: 20rpx;
	border: 1rpx solid rgba($primary-color, 0.2);
	backdrop-filter: blur(10rpx);
	box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.1);
	position: relative;
	overflow: hidden;

	// 背景文字
	.bg-text {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%) rotate(-15deg);
		font-size: 48rpx;
		font-weight: 900;
		color: rgba($primary-color, 0.08);
		letter-spacing: 4rpx;
		z-index: 0;
		pointer-events: none;
		user-select: none;
		white-space: nowrap;
		text-transform: uppercase;
		font-family: 'Arial Black', Arial, sans-serif;
	}

	.level-info {
		position: relative;
		z-index: 1;

		.level-label {
			font-size: 32rpx;
			color: #666;
			margin-right: 16rpx;
		}

		.level-name {
			font-size: 36rpx;
			font-weight: 600;
			color: $primary-color;
		}

		.level-desc {
			margin-top: 8rpx;

			.desc-text {
				font-size: 24rpx;
				color: #999;
				line-height: 1.4;
			}
		}
	}

	.level-icon {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		z-index: 1;

		.cat-icon {
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #fff;
			border-radius: 50%;
			box-shadow: 0 6rpx 20rpx rgba($primary-color, 0.4);
			border: 2rpx solid rgba($primary-color, 0.2);
		}

		.icon-label {
			font-size: 24rpx;
			color: $primary-color;
			font-weight: 500;
		}

		.vip-badge {
			position: absolute;
			top: -5rpx;
			right: -10rpx;
			background: linear-gradient(135deg, #FFD700, #FFA500);
			border-radius: 20rpx;
			padding: 4rpx 8rpx;
			transform: scale(0.8);
			z-index: 10;

			.vip-text {
				color: #fff;
				font-size: 18rpx;
				font-weight: 700;
				text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
			}
		}
	}
}

// 等级进度卡片
.level-progress-card {
	background: linear-gradient(135deg, $primary-color, lighten($primary-color, 15%));
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba($primary-color, 0.3);

	.level-icons {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;

		.level-step {
			display: flex;
			flex-direction: column;
			align-items: center;
			flex: 1;
			cursor: pointer;
			transition: all 0.3s ease;

			.step-icon {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.3);
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;
				transition: all 0.3s ease;

				image {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.step-name {
				font-size: 24rpx;
				color: rgba(255, 255, 255, 0.8);
				text-align: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 80rpx;
			}

			// 选中状态优先级最高
			&.selected {
				transform: scale(1.05);

				.step-icon {
					background: #fff !important;
					box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.4);
					transform: scale(1.2);
				}

				.step-name {
					color: #fff;
					font-weight: 700;
					text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
				}
			}

			// 未选中的状态（包括激活和未激活）
			&:not(.selected) {
				.step-icon {
					background: rgba(255, 255, 255, 0.3);
				}

				&.active {
					.step-icon {
						background: rgba(255, 255, 255, 0.5);
						transform: scale(1.05);
					}

					.step-name {
						color: #fff;
						font-weight: 600;
					}
				}

				&.current {
					.step-icon {
						background: rgba(255, 255, 255, 0.8);
						border: 2px solid rgba(255, 255, 255, 0.9);
						box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.3);
					}
				}
			}
		}

		.current-badge {
			position: absolute;
			top: -8rpx;
			right: -15rpx;
			background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
			border-radius: 20rpx;
			padding: 4rpx 12rpx;
			transform: scale(0.8);
			z-index: 10;
		}

		.badge-text {
			color: white;
			font-size: 20rpx;
			font-weight: 600;
		}

		&:active {
			transform: scale(0.95);
		}

		.progress-line {
			flex: 1;
			height: 4rpx;
			background: rgba(255, 255, 255, 0.3);
			margin: 0 20rpx;
			border-radius: 2rpx;
			margin-top: -30rpx;

			&.active {
				background: rgba(255, 255, 255, 0.8);
			}
		}
	}

	.upgrade-tip {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 20rpx;
		padding: 16rpx 24rpx;
		text-align: center;

		text {
			color: #fff;
			font-size: 26rpx;
			font-weight: 500;
		}
	}
}

// 任务部分
.task-section {
	.section-title {
		margin-bottom: 30rpx;

		text {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	// 加载状态样式
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	// 空状态样式
	.empty-tasks {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
	}

	.empty-icon {
		margin-bottom: 24rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	.task-list {
		.task-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #fff;
			border-radius: 16rpx;
			padding: 32rpx 24rpx;
			margin-bottom: 16rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

			.task-info {
				flex: 1;

				.task-title {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 12rpx;
				}

				.task-reward {
					display: flex;
					align-items: center;

					.coin-icon {
						width: 24rpx;
						height: 24rpx;
						margin-right: 8rpx;
					}

					.reward-text {
						font-size: 24rpx;
						color: #ff9500;
						font-weight: 600;
					}
				}
			}

			.task-action {
				.action-btn {
					height: $btn-sm-height;
					padding: $btn-sm-padding;
					border-radius: $btn-sm-border-radius;
					font-size: $btn-sm-font-size;
					font-weight: 600;
					border: none;
					min-width: 120rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&.completed {
						background: #e5e7eb;
						color: #9ca3af;
					}

					&.primary {
						background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
						color: #fff;
						box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.3);
						border: 1rpx solid rgba($primary-color, 0.2);

						&:active {
							transform: scale(0.95);
							box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);
						}
					}
				}
			}
		}
	}
}
</style>
