{"version": 3, "file": "moment.js", "sources": ["pages/moment/moment.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbW9tZW50L21vbWVudC52dWU"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"发现\" @heightChange=\"handleNavHeightChange\">\r\n\t\t<template #nav-left>\r\n\t\t\t<text class=\"petal-text with-red-dot\" :style=\"{ color: getNavTextColor() }\" @click=\"showPetalModal\">签到</text>\r\n\t\t</template>\r\n\t\t<template #content>\r\n\t\t\t<view class=\"moment-container\">\r\n\t\t\t\t<!-- 内容区域 - 使用z-paging-swiper作为根节点免计算高度 -->\r\n\t\t\t\t<view class=\"content-wrapper\">\r\n\t\t\t\t\t<!-- z-paging主体内容 -->\r\n\t\t\t\t\t<z-paging ref=\"paging\" v-model=\"momentList\" @query=\"queryMomentList\">\r\n\t\t\t\t\t\t<template #top>\r\n\t\t\t\t\t\t\t<!-- 顶部间距，避免被导航栏覆盖 -->\r\n\t\t\t\t\t\t\t<view class=\"nav-spacer\" :style=\"{ paddingTop: navBarHeight + 'px' }\"></view>\r\n\r\n\t\t\t\t\t\t\t<!-- 标签导航区域 -->\r\n\t\t\t\t\t\t\t<z-tabs :list=\"tabList\" :current=\"currentTab\" @change=\"handleTabChange\"\r\n\t\t\t\t\t\t\t\tactive-color=\"#696CF3\" inactive-color=\"#666666\">\r\n\t\t\t\t\t\t\t</z-tabs>\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t<!-- 自定义刷新组件 -->\r\n\t\t\t\t\t\t<template #refresher=\"{ refresherStatus }\">\r\n\t\t\t\t\t\t\t<custom-refresher :refresher-status=\"refresherStatus\" />\r\n\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t<!-- 热门话题区域 -->\r\n\t\t\t\t\t\t<view class=\"hot-topics-section\">\r\n\t\t\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"topic-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"icon-text\">#</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"section-title\">热门话题</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"more-btn\" @click=\"handleMoreTopics\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"more-text\">更多话题</text>\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"topics-grid\">\r\n\t\t\t\t\t\t\t\t<view class=\"topic-item\" v-for=\"(topic, index) in hotTopics\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t\t@click=\"handleTopicClick(topic)\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"topic.icon\" mode=\"aspectFill\" class=\"topic-avatar\"></image>\r\n\t\t\t\t\t\t\t\t\t<view class=\"topic-info\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"topic-title\">{{ topic.title }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"topic-count\">{{ topic.count }}条动态</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 动态列表组件 -->\r\n\t\t\t\t\t\t<MomentList :moment-list=\"momentList\" :show-follow-btn=\"true\" @moment-click=\"handleMomentClick\" />\r\n\t\t\t\t\t</z-paging>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 右下角浮动按钮 -->\r\n\t\t\t\t<view class=\"floating-btn\" @click=\"goToAddMoment\">\r\n\t\t\t\t\t<uni-icons type=\"plus\" size=\"28\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 签到弹窗 -->\r\n\t\t\t\t<SigninModal ref=\"signinModalRef\">\r\n\t\t\t\t</SigninModal>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onPageScroll, onShow } from '@dcloudio/uni-app'\r\nimport { getRecommendMomentPage, formatRecommendMoment, RECOMMEND_TYPE, getTopicList, formatTopic } from '@/api/moment/recommend'\r\nimport SigninModal from '@/components/signin/signin-modal.vue'\r\nimport MomentList from '@/components/moment-list/moment-list.vue'\r\n\r\n// 如果z-tabs没有自动识别，取消下面的注释\r\n// import zTabs from '@/uni_modules/z-tabs/components/z-tabs/z-tabs.vue'\r\n\r\n// 如果uni-popup没有自动识别，取消下面的注释\r\n// import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n// 导航栏高度\r\nconst navBarHeight = ref(0)\r\n// 当前选中的标签\r\nconst currentTab = ref(1) // 默认选中\"最新\"\r\n\r\n// 标签导航数据\r\nconst tabList = ref([\r\n\t'关注',\r\n\t'最新',\r\n\t'最热'\r\n])\r\n\r\n// 热门话题数据\r\nconst hotTopics = ref([])\r\nconst isLoadingTopics = ref(false)\r\n\r\n// 动态列表数据\r\nconst momentList = ref([])\r\n\r\n// z-paging引用\r\nconst paging = ref(null)\r\n\r\n// 签到弹窗引用\r\nconst signinModalRef = ref(null)\r\n\r\n\r\n\r\n// 处理标签切换\r\nconst handleTabChange = (index) => {\r\n\tconsole.log('Tab changed to:', index, tabList.value[index])\r\n\tcurrentTab.value = index\r\n\t// 当切换tab时请调用组件的reload方法，请勿直接调用：queryMomentList方法！！\r\n\tif (paging.value) {\r\n\t\tpaging.value.reload()\r\n\t}\r\n}\r\n\r\n// 话题点击\r\nconst handleTopicClick = (topic) => {\r\n\tconsole.log('点击话题:', topic)\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/moment/tag/detail?id=${topic.id}&name=${encodeURIComponent(topic.title)}`\r\n\t})\r\n}\r\n\r\n// 加载热门话题列表\r\nconst loadHotTopics = () => {\r\n\tisLoadingTopics.value = true\r\n\tgetTopicList().then(response => {\r\n\t\t// 格式化话题数据\r\n\t\tconst formattedTopics = response.data.map(item => formatTopic(item))\r\n\t\t// 只显示前4个话题\r\n\t\thotTopics.value = formattedTopics.slice(0, 4).map(topic => ({\r\n\t\t\tid: topic.id,\r\n\t\t\ttitle: topic.name,\r\n\t\t\tcount: topic.momentCount,\r\n\t\t\ticon: topic.icon\r\n\t\t}))\r\n\t}).finally(() => {\r\n\t\tisLoadingTopics.value = false\r\n\t})\r\n}\r\n\r\n// 更多话题\r\nconst handleMoreTopics = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pagesubs/moment/tag/list'\r\n\t})\r\n}\r\n\r\n// z-paging查询数据 - 必须使用async函数\r\nconst queryMomentList = async (pageNo, pageSize) => {\r\n\t// 根据当前标签页确定查询类型\r\n\tlet queryType = RECOMMEND_TYPE.LATEST // 默认最新\r\n\tswitch (currentTab.value) {\r\n\t\tcase 0:\r\n\t\t\tqueryType = RECOMMEND_TYPE.FOLLOWING // 关注\r\n\t\t\tbreak\r\n\t\tcase 1:\r\n\t\t\tqueryType = RECOMMEND_TYPE.LATEST // 最新\r\n\t\t\tbreak\r\n\t\tcase 2:\r\n\t\t\tqueryType = RECOMMEND_TYPE.HOT // 最热\r\n\t\t\tbreak\r\n\t}\r\n\r\n\t// 准备查询参数\r\n\tconst params = {\r\n\t\ttype: queryType,\r\n\t\tpageNum: pageNo,\r\n\t\tpageSize: pageSize,\r\n\t}\r\n\t// 调用API查询推荐动态列表\r\n\tgetRecommendMomentPage(params).then(response => {\r\n\t\t// 格式化数据\r\n\t\tconst formattedData = response.rows.map(item => formatRecommendMoment(item))\r\n\t\tpaging.value.complete(formattedData)\r\n\t})\r\n}\r\n\r\n// 显示签到弹窗\r\nconst showPetalModal = () => {\r\n\tif (signinModalRef.value) {\r\n\t\tsigninModalRef.value.openModal()\r\n\t}\r\n}\r\n\r\n// 跳转到新增动态页面\r\nconst goToAddMoment = () => {\r\n\tuni.navigateTo({\r\n\t\turl: '/pagesubs/moment/add'\r\n\t})\r\n}\r\n\r\n// 处理动态点击\r\nconst handleMomentClick = (item) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/moment/detail?id=${item.id}`\r\n\t})\r\n}\r\n\r\n\r\n// 处理导航栏高度变化\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 计算导航栏文字颜色\r\nconst getNavTextColor = () => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\r\n}\r\n\r\n// 页面挂载时加载话题\r\nonMounted(() => {\r\n\tloadHotTopics()\r\n})\r\n\r\n// 页面滚动监听\r\nonPageScroll((e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n})\r\n\r\n// 调试信息\r\nconsole.log('tabList:', tabList.value)\r\nconsole.log('currentTab:', currentTab.value)\r\n</script>\r\n\r\n<style>\r\n@import '@/static/fonts/iconfont.css';\r\n\r\n/* 标签导航样式优化 */\r\n.z-tabs {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tpadding: 0 60rpx;\r\n}\r\n\r\n/* 深度选择器调整z-tabs内部样式 */\r\n:deep(.z-tabs-scroll) {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n:deep(.z-tabs-scroll-view) {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n:deep(.z-tabs-item) {\r\n\tmargin: 0 16rpx !important;\r\n\t/* 减少标签间距 */\r\n\tpadding: 16rpx 20rpx !important;\r\n\t/* 调整内边距 */\r\n\tmin-width: auto !important;\r\n}\r\n\r\n:deep(.z-tabs-item-text) {\r\n\tfont-size: 30rpx !important;\r\n\tfont-weight: 500 !important;\r\n}\r\n\r\n.moment-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n}\r\n\r\n.content-wrapper {\r\n\theight: 100vh;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* 普通模式需要设置容器高度 */\r\n.content-wrapper z-paging {\r\n\theight: 100%;\r\n}\r\n\r\n/* 导航栏间距 */\r\n.nav-spacer {\r\n\twidth: 100%;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n/* 顶部导航栏样式 */\r\n.nav-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 10rpx 2rpx;\r\n\tcursor: pointer;\r\n\tmin-height: 44rpx;\r\n\tmin-width: 120rpx;\r\n\tposition: relative;\r\n\tz-index: 1000;\r\n\r\n\t&:active {\r\n\t\topacity: 0.7;\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n}\r\n\r\n.test-btn {\r\n\tpadding: 8rpx 16rpx;\r\n\tfont-size: 28rpx;\r\n\tcursor: pointer;\r\n\r\n\t&:active {\r\n\t\topacity: 0.7;\r\n\t}\r\n}\r\n\r\n\r\n\r\n.petal-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n}\r\n\r\n.petal-text {\r\n\tfont-size: 28rpx;\r\n\tmargin-left: 4rpx;\r\n\twhite-space: nowrap;\r\n\tposition: relative;\r\n}\r\n\r\n.petal-text.with-red-dot::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\ttop: -2rpx;\r\n\tright: -14rpx;\r\n\twidth: 10rpx;\r\n\theight: 10rpx;\r\n\tbackground-color: #ff4757;\r\n\tborder-radius: 50%;\r\n\tborder: 1rpx solid #fff;\r\n}\r\n\r\n.nav-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #696CF3;\r\n\ttext-align: center;\r\n\twidth: 100%;\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\tright: 0;\r\n}\r\n\r\n.nav-right {\r\n\twidth: 60rpx;\r\n}\r\n\r\n\r\n\r\n/* 热门话题样式 */\r\n.hot-topics-section {\r\n\tbackground-color: rgba(240, 248, 255, 0.8);\r\n\tmargin: 12rpx 16rpx;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 16rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.header-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.topic-icon {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tbackground: linear-gradient(135deg, #4285f4, #34a853);\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.icon-text {\r\n\tcolor: white;\r\n\tfont-size: 22rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.more-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.more-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 6rpx;\r\n}\r\n\r\n.topics-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: 1fr 1fr;\r\n\tgap: 12rpx;\r\n}\r\n\r\n.topic-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 10rpx;\r\n\tpadding: 14rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.topic-item:active {\r\n\ttransform: scale(0.98);\r\n\tbackground-color: rgba(66, 133, 244, 0.1);\r\n}\r\n\r\n.topic-avatar {\r\n\twidth: 50rpx;\r\n\theight: 50rpx;\r\n\tborder-radius: 10rpx;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.topic-info {\r\n\tflex: 1;\r\n}\r\n\r\n.topic-title {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 4rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.topic-count {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 使用z-paging-swiper时无需设置额外高度和样式覆盖 */\r\n\r\n/* 右下角浮动按钮 */\r\n.floating-btn {\r\n\tposition: fixed;\r\n\tright: 30rpx;\r\n\tbottom: 120rpx;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.4);\r\n\tz-index: 999;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.floating-btn:active {\r\n\ttransform: scale(0.9);\r\n\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.6);\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pages/moment/moment.vue'\nwx.createPage(MiniProgramPage)"], "names": ["SigninModal", "MomentList", "pageScrollTop", "ref", "navBarHeight", "currentTab", "tabList", "hotTopics", "isLoadingTopics", "momentList", "paging", "signinModalRef", "handleTabChange", "index", "uni", "handleTopicClick", "topic", "loadHotTopics", "getTopicList", "response", "formattedTopics", "item", "formatTopic", "handleMoreTopics", "queryMomentList", "pageNo", "pageSize", "queryType", "RECOMMEND_TYPE", "params", "getRecommendMomentPage", "formattedData", "formatRecommendMoment", "showPetalModal", "goToAddMoment", "handleMomentClick", "handleNavHeightChange", "height", "getNavTextColor", "onMounted", "onPageScroll", "e", "MiniProgramPage"], "mappings": "kpBAsEA,MAAAA,EAAA,IAAA,0CACAC,EAAA,IAAA,0EASA,MAAAC,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAEAE,EAAAF,EAAA,IAAA,CAAA,EAGAG,EAAAH,EAAAA,IAAA,CACA,KACA,KACA,IACA,CAAA,EAGAI,EAAAJ,EAAA,IAAA,EAAA,EACAK,EAAAL,EAAA,IAAA,EAAA,EAGAM,EAAAN,EAAA,IAAA,EAAA,EAGAO,EAAAP,EAAA,IAAA,IAAA,EAGAQ,EAAAR,EAAA,IAAA,IAAA,EAKAS,EAAAC,GAAA,CACAC,QAAA,MAAA,MAAA,iCAAA,kBAAAD,EAAAP,EAAA,MAAAO,CAAA,CAAA,EACAR,EAAA,MAAAQ,EAEAH,EAAA,OACAA,EAAA,MAAA,OAAA,CAEA,EAGAK,EAAAC,GAAA,CACAF,EAAAA,MAAA,MAAA,MAAA,iCAAA,QAAAE,CAAA,EACAF,EAAAA,MAAA,WAAA,CACA,IAAA,kCAAAE,EAAA,EAAA,SAAA,mBAAAA,EAAA,KAAA,CAAA,EACA,CAAA,CACA,EAGAC,EAAA,IAAA,CACAT,EAAA,MAAA,GACAU,eAAA,EAAA,KAAAC,GAAA,CAEA,MAAAC,EAAAD,EAAA,KAAA,IAAAE,GAAAC,EAAAA,YAAAD,CAAA,CAAA,EAEAd,EAAA,MAAAa,EAAA,MAAA,EAAA,CAAA,EAAA,IAAAJ,IAAA,CACA,GAAAA,EAAA,GACA,MAAAA,EAAA,KACA,MAAAA,EAAA,YACA,KAAAA,EAAA,IACA,EAAA,CACA,CAAA,EAAA,QAAA,IAAA,CACAR,EAAA,MAAA,EACA,CAAA,CACA,EAGAe,EAAA,IAAA,CACAT,EAAAA,MAAA,WAAA,CACA,IAAA,2BACA,CAAA,CACA,EAGAU,EAAA,MAAAC,EAAAC,IAAA,CAEA,IAAAC,EAAAC,EAAAA,eAAA,OACA,OAAAvB,EAAA,MAAA,CACA,IAAA,GACAsB,EAAAC,EAAA,eAAA,UACA,MACA,IAAA,GACAD,EAAAC,EAAA,eAAA,OACA,MACA,IAAA,GACAD,EAAAC,EAAA,eAAA,IACA,KACA,CAGA,MAAAC,EAAA,CACA,KAAAF,EACA,QAAAF,EACA,SAAAC,CACA,EAEAI,EAAAA,uBAAAD,CAAA,EAAA,KAAAV,GAAA,CAEA,MAAAY,EAAAZ,EAAA,KAAA,IAAAE,GAAAW,EAAAA,sBAAAX,CAAA,CAAA,EACAX,EAAA,MAAA,SAAAqB,CAAA,CACA,CAAA,CACA,EAGAE,EAAA,IAAA,CACAtB,EAAA,OACAA,EAAA,MAAA,UAAA,CAEA,EAGAuB,EAAA,IAAA,CACApB,EAAAA,MAAA,WAAA,CACA,IAAA,sBACA,CAAA,CACA,EAGAqB,EAAAd,GAAA,CACAP,EAAAA,MAAA,WAAA,CACA,IAAA,8BAAAO,EAAA,EAAA,EACA,CAAA,CACA,EAIAe,EAAAC,GAAA,CACAjC,EAAA,MAAAiC,CACA,EAGAC,EAAA,IACA,KAAA,IAAApC,EAAA,MAAA,IAAA,CAAA,EACA,GAAA,UAAA,UAIAqC,OAAAA,EAAAA,UAAA,IAAA,CACAtB,EAAA,CACA,CAAA,EAGAuB,EAAA,aAAAC,GAAA,CACAvC,EAAA,MAAAuC,EAAA,SACA,CAAA,EAGA3B,EAAAA,MAAA,MAAA,MAAA,iCAAA,WAAAR,EAAA,KAAA,EACAQ,EAAAA,MAAA,MAAA,MAAA,iCAAA,cAAAT,EAAA,KAAA,qwBCjOA,GAAG,WAAWqC,CAAe"}