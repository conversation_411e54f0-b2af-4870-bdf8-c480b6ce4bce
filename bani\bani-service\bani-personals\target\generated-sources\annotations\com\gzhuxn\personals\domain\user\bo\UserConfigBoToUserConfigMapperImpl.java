package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserConfig;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserConfigBoToUserConfigMapperImpl implements UserConfigBoToUserConfigMapper {

    @Override
    public UserConfig convert(UserConfigBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserConfig userConfig = new UserConfig();

        userConfig.setSearchValue( arg0.getSearchValue() );
        userConfig.setCreateBy( arg0.getCreateBy() );
        userConfig.setCreateTime( arg0.getCreateTime() );
        userConfig.setUpdateBy( arg0.getUpdateBy() );
        userConfig.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userConfig.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userConfig.setCreateDept( arg0.getCreateDept() );
        userConfig.setId( arg0.getId() );
        userConfig.setUserId( arg0.getUserId() );
        userConfig.setConfigKey( arg0.getConfigKey() );
        userConfig.setVal( arg0.getVal() );

        return userConfig;
    }

    @Override
    public UserConfig convert(UserConfigBo arg0, UserConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setConfigKey( arg0.getConfigKey() );
        arg1.setVal( arg0.getVal() );

        return arg1;
    }
}
