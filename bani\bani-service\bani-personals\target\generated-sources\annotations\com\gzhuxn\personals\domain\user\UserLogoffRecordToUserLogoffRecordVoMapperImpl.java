package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLogoffRecordToUserLogoffRecordVoMapperImpl implements UserLogoffRecordToUserLogoffRecordVoMapper {

    @Override
    public UserLogoffRecordVo convert(UserLogoffRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLogoffRecordVo userLogoffRecordVo = new UserLogoffRecordVo();

        userLogoffRecordVo.setId( arg0.getId() );
        userLogoffRecordVo.setUserId( arg0.getUserId() );
        userLogoffRecordVo.setStatus( arg0.getStatus() );
        userLogoffRecordVo.setLogoffTime( arg0.getLogoffTime() );
        userLogoffRecordVo.setSucceedTime( arg0.getSucceedTime() );

        return userLogoffRecordVo;
    }

    @Override
    public UserLogoffRecordVo convert(UserLogoffRecord arg0, UserLogoffRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLogoffTime( arg0.getLogoffTime() );
        arg1.setSucceedTime( arg0.getSucceedTime() );

        return arg1;
    }
}
