package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.vo.MsgContentUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentUserToMsgContentUserVoMapperImpl implements MsgContentUserToMsgContentUserVoMapper {

    @Override
    public MsgContentUserVo convert(MsgContentUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContentUserVo msgContentUserVo = new MsgContentUserVo();

        msgContentUserVo.setId( arg0.getId() );
        msgContentUserVo.setUserId( arg0.getUserId() );
        msgContentUserVo.setContentId( arg0.getContentId() );
        msgContentUserVo.setType( arg0.getType() );
        msgContentUserVo.setSubType( arg0.getSubType() );

        return msgContentUserVo;
    }

    @Override
    public MsgContentUserVo convert(MsgContentUser arg0, MsgContentUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setContentId( arg0.getContentId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );

        return arg1;
    }
}
