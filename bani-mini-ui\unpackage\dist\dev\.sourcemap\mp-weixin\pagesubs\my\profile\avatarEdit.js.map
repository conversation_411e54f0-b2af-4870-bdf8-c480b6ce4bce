{"version": 3, "file": "avatarEdit.js", "sources": ["pagesubs/my/profile/avatarEdit.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlccHJvZmlsZVxhdmF0YXJFZGl0LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"上传头像\" :show-back=\"true\">\r\n\t\t<template #content>\r\n\t\t\t<view class=\"page-container\">\r\n\t\t\t\t<!-- 主要内容 -->\r\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t\t\t<view v-if=\"avatarType === 0\" class=\"steps-wrapper\">\r\n\t\t\t\t\t\t<uni-steps :options=\"[{ title: '基础信息' }, { title: '上传头像' }]\" :active=\"1\"></uni-steps>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t<text class=\"upload-tip\">点击上传本人真实照片做头像</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"content-wrapper\">\r\n\t\t\t\t\t\t<view class=\"upload-area\">\r\n\t\t\t\t\t\t\t<images-select :limit=\"1\" type=\"user_detail.avatar\" :small=\"true\" v-model=\"imageList\"\r\n\t\t\t\t\t\t\t\t@change=\"handleImageChange\">\r\n\t\t\t\t\t\t\t</images-select>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"finishBtn\">\r\n\t\t\t\t\t\t\t<button type=\"primary\" :disabled=\"submitBtnDisabled\" @click=\"submit\">完成</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue'\r\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\r\nimport { updateUserAvatar } from '@/api/my/my'\r\nimport { toast } from '@/utils/common'\r\nimport $store from '@/store'\r\nimport globalConfig from '@/config'\r\n\r\n// 页面状态\r\nconst pageScrollTop = ref(0)\r\nconst navBarHeight = ref(0)\r\nconst imageList = ref([])\r\nconst avatarType = ref(0)\r\n\r\nconst submitBtnDisabled = computed(() => {\r\n\treturn imageList.value.length > 0 ? false : true\r\n})\r\n\r\n// 页面滚动监听\r\nonPageScroll((e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n})\r\n\r\n// 导航栏高度变化处理\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 计算导航栏文字颜色\r\nconst getNavTextColor = () => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor\r\n}\r\nonLoad((param) => {\r\n\tif (param.type) {\r\n\t\tavatarType.value = parseInt(param.type)\r\n\t}\r\n})\r\n\r\n// 处理图片变化\r\nconst handleImageChange = (event) => {\r\n\tconsole.log('头像图片变化:', event)\r\n}\r\n\r\nconst submit = () => {\r\n\tif (imageList.value.length === 0) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请先上传头像',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tvar data = {\r\n\t\tavatar: imageList.value[0].ossId\r\n\t}\r\n\tupdateUserAvatar(data).then(res => {\r\n\t\t$store.user.refreshUserInfo();\r\n\t\ttoast(\"保存成功！\")\r\n\t\tif (avatarType.value === 1) {\r\n\t\t\tuni.navigateBack()\r\n\t\t\treturn\r\n\t\t}\r\n\t\tuni.reLaunch({\r\n\t\t\turl: '/pages/my/my'\r\n\t\t})\r\n\t})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/uni.scss';\r\n\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(135deg,\r\n\t\t\trgba($primary-color, 0.08) 0%,\r\n\t\t\trgba($primary-color, 0.04) 30%,\r\n\t\t\trgba(255, 255, 255, 0.98) 60%,\r\n\t\t\trgba(248, 249, 255, 1) 100%);\r\n}\r\n\r\n.main-container {\r\n\tpadding: 24rpx 20rpx 120rpx;\r\n\tmin-height: 100vh;\r\n\tbox-sizing: border-box;\r\n\r\n\t.steps-wrapper {\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tmargin-bottom: 50rpx;\r\n\r\n\t\t:deep(.uni-steps) {\r\n\t\t\t.uni-steps__column-line-container {\r\n\t\t\t\t.uni-steps__column-line {\r\n\t\t\t\t\tbackground-color: rgba($primary-color, 0.3);\r\n\t\t\t\t\theight: 4rpx !important;\r\n\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.uni-steps__column-container {\r\n\t\t\t\t.uni-steps__column-text-container {\r\n\t\t\t\t\t.uni-steps__column-title {\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.title {\r\n\t\ttext-align: center;\r\n\t\tmargin: 50rpx 0 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\r\n\t\t.upload-tip {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tcolor: #1a1a1a;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 20rpx 40rpx;\r\n\t\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba($primary-color, 0.05));\r\n\t\t\tborder-radius: 32rpx;\r\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.1);\r\n\t\t\tborder: 1rpx solid rgba($primary-color, 0.1);\r\n\t\t\tletter-spacing: 1rpx;\r\n\r\n\t\t\t&::before,\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 3rpx;\r\n\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba($primary-color, 0.4), transparent);\r\n\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t}\r\n\r\n\t\t\t&::before {\r\n\t\t\t\tleft: -50rpx;\r\n\t\t\t}\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tright: -50rpx;\r\n\t\t\t}\r\n\r\n\t\t\t// 添加光泽效果\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: -100%;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\t\t\ttransition: left 0.8s ease;\r\n\t\t\t\tborder-radius: 32rpx;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover::after {\r\n\t\t\t\tleft: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.content-wrapper {\r\n\t\tbackground: linear-gradient(135deg, #fff 0%, #fafbff 100%);\r\n\t\tpadding: 50rpx 40rpx;\r\n\t\tmargin: 0 4rpx;\r\n\t\tborder-radius: 32rpx;\r\n\t\tbox-shadow: 0 12rpx 40rpx rgba($primary-color, 0.12);\r\n\t\tborder: 1rpx solid rgba($primary-color, 0.08);\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\r\n\t\t.upload-area {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin: 60rpx auto;\r\n\t\t\twidth: 100%;\r\n\t\t\tmax-width: 450rpx;\r\n\r\n\t\t\t// 为ImagesSelect组件添加样式\r\n\t\t\t:deep(.images-select) {\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.images-grid {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tgrid-template-columns: 1fr;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.image-item,\r\n\t\t\t\t.add-image-btn {\r\n\t\t\t\t\twidth: 450rpx;\r\n\t\t\t\t\theight: 550rpx;\r\n\t\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\t\tbox-shadow: 0 8rpx 32rpx rgba($primary-color, 0.15);\r\n\t\t\t\t\ttransition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n\t\t\t\t\t&:active {\r\n\t\t\t\t\t\ttransform: translateY(-4rpx) scale(1.02);\r\n\t\t\t\t\t\tbox-shadow: 0 16rpx 48rpx rgba($primary-color, 0.25);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.add-image-btn {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);\r\n\t\t\t\t\tborder: 3rpx dashed rgba($primary-color, 0.3);\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.add-icon {\r\n\t\t\t\t\t\tfont-size: 120rpx;\r\n\t\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 添加上传提示文字\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tcontent: '点击上传头像';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 80rpx;\r\n\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\t\tcolor: rgba($primary-color, 0.8);\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.finishBtn {\r\n\t\t\twidth: 100%;\r\n\t\t\tmax-width: 600rpx;\r\n\t\t\tmargin: 80rpx auto 20rpx;\r\n\r\n\t\t\tbutton {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tborder: none;\r\n\t\t\t\tbox-shadow: 0 8rpx 24rpx rgba($primary-color, 0.3);\r\n\t\t\t\ttransition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\r\n\t\t\t\t// 光泽效果\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: -100%;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n\t\t\t\t\ttransition: left 0.6s ease;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:hover::before {\r\n\t\t\t\t\tleft: 100%;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.96);\r\n\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.25);\r\n\t\t\t\t\tbackground: linear-gradient(135deg, darken($primary-color, 3%), $primary-color);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&[disabled] {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #ccc, #ddd);\r\n\t\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t\t\t\t\ttransform: none;\r\n\t\t\t\t\tcolor: #999;\r\n\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/profile/avatarEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "imageList", "avatarType", "submitBtnDisabled", "computed", "onPageScroll", "e", "onLoad", "param", "handleImageChange", "event", "uni", "submit", "data", "updateUserAvatar", "res", "$store", "toast", "MiniProgramPage"], "mappings": "kjBAqCA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EACAE,EAAAF,EAAA,IAAA,EAAA,EACAG,EAAAH,EAAA,IAAA,CAAA,EAEAI,EAAAC,EAAA,SAAA,IACA,EAAAH,EAAA,MAAA,OAAA,EACA,EAGAI,EAAA,aAAAC,GAAA,CACAR,EAAA,MAAAQ,EAAA,SACA,CAAA,EAYAC,EAAA,OAAAC,GAAA,CACAA,EAAA,OACAN,EAAA,MAAA,SAAAM,EAAA,IAAA,EAEA,CAAA,EAGA,MAAAC,EAAAC,GAAA,CACAC,EAAAA,MAAA,MAAA,MAAA,2CAAA,UAAAD,CAAA,CACA,EAEAE,EAAA,IAAA,CACA,GAAAX,EAAA,MAAA,SAAA,EAAA,CACAU,EAAAA,MAAA,UAAA,CACA,MAAA,SACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,IAAAE,EAAA,CACA,OAAAZ,EAAA,MAAA,CAAA,EAAA,KACA,EACAa,EAAAA,iBAAAD,CAAA,EAAA,KAAAE,GAAA,CAGA,GAFAC,SAAA,KAAA,kBACAC,EAAAA,MAAA,OAAA,EACAf,EAAA,QAAA,EAAA,CACAS,EAAAA,MAAA,aAAA,EACA,MACA,CACAA,EAAAA,MAAA,SAAA,CACA,IAAA,cACA,CAAA,CACA,CAAA,CACA,iTC9FA,GAAG,WAAWO,CAAe"}