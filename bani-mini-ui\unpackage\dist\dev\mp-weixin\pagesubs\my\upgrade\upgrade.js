"use strict";const e=require("../../../common/vendor.js"),C=require("../../../common/assets.js"),t=require("../../../api/my/coin.js"),T=require("../../../api/my/my.js");if(!Array){const p=e.resolveComponent("uni-icons"),c=e.resolveComponent("scroll-nav-page");(p+c)()}const R=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",I=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(R+I)();const g={__name:"upgrade",setup(p){const c=e.ref(0),m=e.ref(0),E=e.ref(1),u=e.ref(1),a=e.ref({userId:null,userLevel:1,userLevelDesc:"",nickName:"",isVip:!1}),_=e.ref(!1),o=e.ref([]),n=e.ref(!1),d={[t.USER_LEVELS.NEWBIE]:"萌新",[t.USER_LEVELS.FRIEND]:"缘友",[t.USER_LEVELS.EXPERT]:"情咖",[t.USER_LEVELS.MASTER]:"牵缘"},y={[t.USER_LEVELS.NEWBIE]:"star",[t.USER_LEVELS.FRIEND]:"heart",[t.USER_LEVELS.EXPERT]:"fire",[t.USER_LEVELS.MASTER]:"medal"};e.onPageScroll(s=>{c.value=s.scrollTop});const h=async()=>{try{_.value=!0;const s=await T.getUserLevel();s.code===200&&s.data&&(a.value=s.data,E.value=s.data.userLevel,u.value=s.data.userLevel,e.index.__f__("log","at pagesubs/my/upgrade/upgrade.vue:195","用户等级信息加载成功:",s.data))}catch(s){e.index.__f__("error","at pagesubs/my/upgrade/upgrade.vue:198","加载用户等级信息失败:",s),e.index.showToast({title:"加载用户等级失败",icon:"none"})}finally{_.value=!1}};e.onMounted(async()=>{await h(),i(u.value)}),e.watch(u,s=>{i(s)});const i=async s=>{try{n.value=!0;const r=await t.getTaskListByLevel(s);r.code===200&&r.data?(o.value=r.data,e.index.__f__("log","at pagesubs/my/upgrade/upgrade.vue:239","任务列表加载成功:",r.data),r.data.forEach((l,f)=>{e.index.__f__("log","at pagesubs/my/upgrade/upgrade.vue:243",`任务${f+1}:`,{id:l.id,title:l.title,isCompleted:l.isCompleted,path:l.path,taskType:l.taskType})})):(o.value=[],e.index.__f__("log","at pagesubs/my/upgrade/upgrade.vue:253","任务列表为空或加载失败:",r))}catch(r){e.index.__f__("error","at pagesubs/my/upgrade/upgrade.vue:256","加载任务列表失败:",r),o.value=[],e.index.showToast({title:"加载任务列表失败",icon:"none"})}finally{n.value=!1}},v=s=>{u.value=s,i(s),e.index.__f__("log","at pagesubs/my/upgrade/upgrade.vue:272","选中等级:",s,"等级名称:",d[s])},L=()=>d[a.value.userLevel]||"萌新",S=()=>y[a.value.userLevel]||"star",x=s=>{if(!s.isCompleted&&s.path&&s.path.trim()){const r=s.path.trim();e.index.navigateTo({url:r})}};return(s,r)=>e.e({a:e.t(L()),b:a.value.userLevelDesc},a.value.userLevelDesc?{}:{},{c:e.p({type:S(),size:"40",color:"#696CF3"}),d:e.t(L()),e:a.value.isVip},a.value.isVip?{}:{},{f:e.p({type:"star",size:"24",color:u.value===1?"#696CF3":"#fff"}),g:a.value.userLevel===1},a.value.userLevel===1?{}:{},{h:a.value.userLevel>=1?1:"",i:u.value===1?1:"",j:a.value.userLevel===1?1:"",k:e.o(l=>v(1)),l:a.value.userLevel>=2?1:"",m:e.p({type:"heart",size:"24",color:u.value===2?"#696CF3":"#fff"}),n:a.value.userLevel===2},a.value.userLevel===2?{}:{},{o:a.value.userLevel>=2?1:"",p:u.value===2?1:"",q:a.value.userLevel===2?1:"",r:e.o(l=>v(2)),s:a.value.userLevel>=3?1:"",t:e.p({type:"fire",size:"24",color:u.value===3?"#696CF3":"#fff"}),v:a.value.userLevel===3},a.value.userLevel===3?{}:{},{w:a.value.userLevel>=3?1:"",x:u.value===3?1:"",y:a.value.userLevel===3?1:"",z:e.o(l=>v(3)),A:a.value.userLevel>=4?1:"",B:e.p({type:"medal",size:"24",color:u.value===4?"#696CF3":"#fff"}),C:a.value.userLevel===4},a.value.userLevel===4?{}:{},{D:a.value.userLevel>=4?1:"",E:u.value===4?1:"",F:a.value.userLevel===4?1:"",G:e.o(l=>v(4)),H:n.value},n.value?{}:o.value.length>0?{J:e.f(o.value,(l,f,V)=>({a:e.t(l.taskName),b:e.t(l.coin),c:e.t(l.isCompleted?"已完成":"去完成"),d:e.n(l.isCompleted?"completed":"primary"),e:e.o(b=>x(l),l.taskId),f:l.taskId})),K:C._imports_0$1}:{L:e.p({type:"info",size:"48",color:"#ccc"})},{I:o.value.length>0,M:m.value+"px",N:e.p({title:"资料等级","show-back":!0})})}},U=e._export_sfc(g,[["__scopeId","data-v-3f4f3c41"]]);g.__runtimeHooks=1;wx.createPage(U);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/upgrade/upgrade.js.map
