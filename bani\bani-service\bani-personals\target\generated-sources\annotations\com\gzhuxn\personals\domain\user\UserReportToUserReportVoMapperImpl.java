package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserReportVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserReportToUserReportVoMapperImpl implements UserReportToUserReportVoMapper {

    @Override
    public UserReportVo convert(UserReport arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserReportVo userReportVo = new UserReportVo();

        userReportVo.setId( arg0.getId() );
        userReportVo.setTitle( arg0.getTitle() );
        userReportVo.setType( arg0.getType() );
        userReportVo.setBusinessId( arg0.getBusinessId() );
        userReportVo.setContent( arg0.getContent() );
        userReportVo.setImages( arg0.getImages() );
        userReportVo.setStatus( arg0.getStatus() );

        return userReportVo;
    }

    @Override
    public UserReportVo convert(UserReport arg0, UserReportVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
