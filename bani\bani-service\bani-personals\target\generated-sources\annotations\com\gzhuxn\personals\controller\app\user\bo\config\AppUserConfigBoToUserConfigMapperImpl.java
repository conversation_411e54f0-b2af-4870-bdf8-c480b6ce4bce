package com.gzhuxn.personals.controller.app.user.bo.config;

import com.gzhuxn.personals.domain.user.UserConfig;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserConfigBoToUserConfigMapperImpl implements AppUserConfigBoToUserConfigMapper {

    @Override
    public UserConfig convert(AppUserConfigBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserConfig userConfig = new UserConfig();

        userConfig.setConfigKey( arg0.getConfigKey() );
        userConfig.setVal( arg0.getVal() );

        return userConfig;
    }

    @Override
    public UserConfig convert(AppUserConfigBo arg0, UserConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setConfigKey( arg0.getConfigKey() );
        arg1.setVal( arg0.getVal() );

        return arg1;
    }
}
