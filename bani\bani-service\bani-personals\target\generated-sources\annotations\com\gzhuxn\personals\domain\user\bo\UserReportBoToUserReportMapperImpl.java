package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserReport;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserReportBoToUserReportMapperImpl implements UserReportBoToUserReportMapper {

    @Override
    public UserReport convert(UserReportBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserReport userReport = new UserReport();

        userReport.setSearchValue( arg0.getSearchValue() );
        userReport.setCreateBy( arg0.getCreateBy() );
        userReport.setCreateTime( arg0.getCreateTime() );
        userReport.setUpdateBy( arg0.getUpdateBy() );
        userReport.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userReport.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userReport.setCreateDept( arg0.getCreateDept() );
        userReport.setId( arg0.getId() );
        userReport.setTitle( arg0.getTitle() );
        userReport.setType( arg0.getType() );
        userReport.setBusinessId( arg0.getBusinessId() );
        userReport.setContent( arg0.getContent() );
        userReport.setImages( arg0.getImages() );
        userReport.setStatus( arg0.getStatus() );

        return userReport;
    }

    @Override
    public UserReport convert(UserReportBo arg0, UserReport arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
