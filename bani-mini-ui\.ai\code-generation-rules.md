# 代码生成规则文件

## 项目概述
本项目是一个基于 uni-app 的移动端应用，使用 Vue 3 + Composition API 开发。

## � 代码生成核心规则

### ⚠️ 重要：文档生成限制
**除非用户明确要求生成文档，否则只生成代码文件，严禁生成以下内容：**
- README.md 文件
- 使用示例文件（example.js、demo.js等）
- 说明文档
- API文档
- 任何形式的文档或示例代码

**只有在用户明确说明需要文档时，才可以生成相关文档文件。**

## �🔥 强制要求

### 1. Vue 3 语法规范
**所有代码必须使用 Vue 3 语法，禁止使用 Vue 2 语法！**

#### ✅ 必须使用的 Vue 3 语法
```vue
<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'

// 响应式数据
const count = ref(0)
const form = reactive({
  name: '',
  age: 0
})

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 监听器
watch(count, (newVal, oldVal) => {
  console.log('count changed:', newVal)
})

// 生命周期
onMounted(() => {
  console.log('component mounted')
})

onLoad(() => {
  console.log('page loaded')
})
</script>
```

#### ❌ 禁止使用的 Vue 2 语法
```vue
<!-- 禁止：Vue 2 Options API -->
<script>
export default {
  data() {
    return {
      count: 0
    }
  },
  computed: {
    doubleCount() {
      return this.count * 2
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}
</script>
```

### 2. 文件上传强制规范
**所有图片或文件上传必须使用 uni-file-picker 组件，禁止使用其他上传方式！**

#### ✅ 必须使用 uni-file-picker
```vue
<template>
  <uni-file-picker
    limit="9"
    fileMediatype="image"
    v-model="imageFiles"
    @select="handleImageSelect"
    @delete="handleImageDelete">
  </uni-file-picker>
</template>

<script setup>
import { reactive } from 'vue'
import { uploadImgAndSmallFile } from '@/api/oss/oss'
import { toast } from '@/utils/common'

const imageFiles = reactive([])

const handleImageSelect = async (event) => {
  const upFile = {
    ...event.tempFiles[0],
    status: 'uploading',
    message: '上传中',
  }

  try {
    const res = await uploadImgAndSmallFile(upFile.url, 'app_moment.images')
    const result = JSON.parse(res)

    imageFiles.push({
      ...upFile,
      status: 'success',
      message: '',
      url: result.data.url,
      ossId: result.data.ossId
    })
  } catch (error) {
    imageFiles.push({
      ...upFile,
      status: 'failed',
      message: '上传失败'
    })
    toast('上传失败，请重试')
  }
}

const handleImageDelete = (event) => {
  imageFiles.splice(event.index, 1)
}
</script>
```

#### ❌ 禁止使用的上传方式
```vue
<!-- 禁止：使用 uni.chooseImage -->
<script setup>
// ❌ 禁止这样做
const chooseImage = () => {
  uni.chooseImage({
    count: 9,
    success: (res) => {
      // 处理图片
    }
  })
}
</script>

<!-- 禁止：使用原生 input -->
<template>
  <!-- ❌ 禁止这样做 -->
  <input type="file" @change="handleFileChange">
</template>
```

## 组件使用规则

### 1. scroll-nav-page 组件使用规范

#### 🔥 强制要求：必须实现渐变效果

使用 `scroll-nav-page` 组件时，**必须**实现滚动渐变效果，不允许禁用渐变功能。

#### ✅ 正确使用示例

```vue
<template>
  <scroll-nav-page title="设置" :show-back="true" :enable-scroll-gradient="true" @heightChange="handleNavHeightChange">
    <template #content>
      <!-- 页面内容 -->
      <view class="main-container">
        <!-- 具体内容 -->
      </view>
    </template>
  </scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'

// 导航栏高度
const navBarHeight = ref(0)

// 导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}
</script>

<style lang="scss" scoped>
.main-container {
  padding: 0 30rpx 120rpx;
}
</style>
```

#### ❌ 禁止的使用方式

```vue
<!-- 禁止：关闭渐变效果 -->
<scroll-nav-page 
  title="页面标题"
  :enable-scroll-gradient="false"
/>

<!-- 禁止：不使用content插槽 -->
<scroll-nav-page title="页面标题">
  <!-- 直接在这里放内容是错误的 -->
  <view>内容</view>
</scroll-nav-page>
```

#### 📋 必需的实现清单

使用 `scroll-nav-page` 时必须包含以下内容：

1. **基本结构**：
   ```vue
   <scroll-nav-page title="页面标题">
     <template #content>
       <!-- 页面内容必须放在content插槽中 -->
     </template>
   </scroll-nav-page>
   ```

2. **导航栏高度处理**（可选）：
   ```js
   const handleNavHeightChange = (height) => {
     navBarHeight.value = height
   }
   ```

3. **渐变配置**（可选，有默认值）：
   - `:enable-scroll-gradient="true"` （默认为 true）
   - `:fade-transition-point="0.6"` （可自定义）
   - `:scroll-threshold="100"` （可自定义）
   - `:initial-bg-color` 和 `:scrolled-bg-color` （可自定义）

#### 🎨 推荐的渐变配置

```vue
<!-- 标准配置 -->
<scroll-nav-page 
  title="页面标题"
  :fade-transition-point="0.6"
  :scroll-threshold="100"
  :enable-scroll-gradient="true"
>
  <template #content>
    <!-- 页面内容 -->
  </template>
</scroll-nav-page>

<!-- 快速渐变 -->
<scroll-nav-page 
  title="页面标题"
  :fade-transition-point="0.4"
  :scroll-threshold="80"
>
  <template #content>
    <!-- 页面内容 -->
  </template>
</scroll-nav-page>

<!-- 慢速渐变 -->
<scroll-nav-page 
  title="页面标题"
  :fade-transition-point="0.8"
  :scroll-threshold="150"
>
  <template #content>
    <!-- 页面内容 -->
  </template>
</scroll-nav-page>
```

### 2. uni-file-picker 组件使用规范

#### 📸 图片上传标准实现

使用 `uni-file-picker` 组件进行图片上传时的标准模板：

```vue
<template>
    <uni-file-picker
      limit="1"
      fileMediatype="image"
      v-model="upFiles"
      @select="afterRead"
      @delete="deletePic">
    </uni-file-picker>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { uploadImgAndSmallFile } from '@/api/oss/oss'
import { toast } from '@/utils/common'

// 上传文件列表
const upFiles = reactive([])

// 删除图片
const deletePic = (event) => {
  upFiles.splice(event.index, 1)
}

// 新增图片 - 标准上传流程
const afterRead = async (event, name) => {
  const upFile = {
    ...event.tempFiles[0],
    status: 'uploading',
    message: '上传中',
  }

  try {
    const res = await uploadImgAndSmallFile(upFile.url, 'app_feedback.fileIds')
    const result = JSON.parse(res)

    upFiles.push({
      ...upFile,
      status: 'success',
      message: '',
      url: result.data.url,
      ossId: result.data.ossId
    })
  } catch (error) {
    upFiles.push({
      ...upFile,
      status: 'failed',
      message: '上传失败'
    })
    toast('上传失败，请重试')
  }
}
</script>
```

#### 🔧 必需的配置参数

使用 `uni-file-picker` 时必须包含以下配置：

1. **基础配置**：
   - `limit="1"` - 限制上传数量（根据需求调整）
   - `fileMediatype="image"` - 限制文件类型为图片
   - `v-model="upFiles"` - 双向绑定文件列表

2. **事件处理**：
   - `@select="afterRead"` - 文件选择后的处理
   - `@delete="deletePic"` - 文件删除的处理

3. **数据结构**：
   ```js
   // 文件列表结构
   const upFiles = reactive([])

   // 上传成功后的文件对象结构
   {
     status: 'success',     // 状态：uploading/success/failed
     message: '',           // 状态消息
     url: 'xxx',           // 文件访问URL
     ossId: 'xxx'          // OSS文件ID（用于后端保存）
   }
   ```

#### 📋 上传流程规范

1. **文件选择阶段**：
   ```js
   const afterRead = async (event, name) => {
     // 1. 创建上传文件对象
     const upFile = {
       ...event.tempFiles[0],
       status: 'uploading',
       message: '上传中',
     }
   }
   ```

2. **文件上传阶段**：
   ```js
   // 2. 调用OSS上传接口
   const res = await uploadImgAndSmallFile(upFile.url, 'app_feedback.fileIds')
   const result = JSON.parse(res)
   ```

3. **结果处理阶段**：
   ```js
   // 3. 成功时更新文件状态
   upFiles.push({
     ...upFile,
     status: 'success',
     url: result.data.url,
     ossId: result.data.ossId
   })

   // 4. 失败时显示错误
   upFiles.push({
     ...upFile,
     status: 'failed',
     message: '上传失败'
   })
   ```

#### 🎨 样式定制规范

使用深度选择器 `:deep()` 来定制 uni-file-picker 样式：

```scss
:deep(.uni-file-picker) {
  // 容器样式
  .file-picker__box {
    border-radius: 16rpx;
    width: 400rpx !important;
    height: 500rpx !important;
    box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
  }

  // 添加按钮样式
  .icon-add {
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 15%)) !important;
    border-radius: 50%;
    box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.2);
  }
}
```

### 3. z-paging 组件使用规范

#### 🔥 强制要求：遵循官方标准写法

使用 `z-paging` 组件时，**必须**遵循官方推荐的标准写法，参考：https://github.com/SmileZXLee/uni-z-paging/blob/main/demo/z-paging-demo/pages/swiper-demo/swiper-demo.vue

#### ✅ 标准使用示例

##### 1. 推荐写法：普通模式 + 导航栏间距处理（基于moment.vue实践）

```vue
<template>
  <scroll-nav-page title="页面标题" @heightChange="handleNavHeightChange">
    <template #content>
      <!-- z-paging主体内容 -->
      <z-paging ref="paging" v-model="dataList" @query="queryList">
        <template #top>

          <!-- 标签导航区域（如果需要） -->
          <z-tabs :list="tabList" :current="currentTab" @change="handleTabChange">
          </z-tabs>

          <!-- 其他固定在顶部的内容 -->
          <view class="header-content">
            <!-- 搜索框、筛选条件等 -->
          </view>
        </template>

        <!-- 列表内容 -->
        <view class="list-item" v-for="(item, index) in dataList" :key="index" @click="itemClick(item, index)">
          <!-- 列表项内容 -->
        </view>
      </z-paging>
    </template>
  </scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'

const paging = ref(null)
const dataList = ref([])
const navBarHeight = ref(0)
const currentTab = ref(0)
const tabList = ref(['tab1', 'tab2', 'tab3'])

// z-paging查询方法 - 必须使用async函数
const queryList = async (pageNo, pageSize) => {
  try {
    // API 调用
    const res = await getDataList({ pageNo, pageSize })
    paging.value.complete(res.data.list)  // 注意：传递list数组
  } catch (error) {
    paging.value.complete(false)
  }
}

// Tab切换处理
const handleTabChange = (index) => {
  currentTab.value = index
  // 当切换tab时请调用组件的reload方法，请勿直接调用：queryList方法！！
  if (paging.value) {
    paging.value.reload()
  }
}

// 导航栏高度变化
const handleNavHeightChange = (height) => {
  navBarHeight.value = height
}

// 列表项点击事件
const itemClick = (item, index) => {
  console.log('点击了', item.title)
}
</script>

<style lang="scss" scoped>
.header-content {
  padding: 20rpx;
  background: #fff;
}

.list-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
}
</style>
```

##### 2. 备选写法：使用z-paging-swiper（免计算高度）

```vue
<template>
  <!-- 使用z-paging-swiper为根节点可以免计算高度 -->
  <z-paging-swiper>
    <!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
    <template #top>
      <view class="header-content">
        <!-- 顶部固定内容，如搜索框、筛选条件等 -->
      </view>
    </template>

    <!-- z-paging主体内容 -->
    <z-paging
      ref="paging"
      v-model="dataList"
      @query="queryList">

      <!-- 列表内容 -->
      <view class="list-item" v-for="(item, index) in dataList" :key="index" @click="itemClick(item, index)">
        <!-- 列表项内容 -->
      </view>
    </z-paging>
  </z-paging-swiper>
</template>

<script setup>
import { ref } from 'vue'

const paging = ref(null)
const dataList = ref([])

// z-paging查询方法 - 必须使用async函数
const queryList = async (pageNo, pageSize) => {
  try {
    // API 调用
    const res = await getDataList({ pageNo, pageSize })
    paging.value.complete(res.data.list)  // 注意：传递list数组
  } catch (error) {
    paging.value.complete(false)
  }
}

// 列表项点击事件
const itemClick = (item, index) => {
  console.log('点击了', item.title)
}
</script>

<style lang="scss" scoped>
// 使用z-paging-swiper时无需设置额外高度
.header-content {
  padding: 20rpx;
  background: #fff;
}

.list-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
}
</style>
```

##### 3. Tab切换列表页面（官方标准写法）

```vue
<template>
  <!-- 使用z-paging-swiper为根节点可以免计算高度 -->
  <z-paging-swiper>
    <!-- 需要固定在顶部不滚动的view放在slot="top"的view中 -->
    <template #top>
      <z-tabs ref="tabs" :list="tabList" :current="current" @change="tabsChange" />
    </template>

    <!-- swiper必须设置height:100%，因为swiper有默认的高度，只有设置高度100%才可以铺满页面 -->
    <swiper class="swiper" :current="current" @transition="swiperTransition" @animationfinish="swiperAnimationfinish">
      <swiper-item class="swiper-item" v-for="(item, index) in tabList" :key="index">
        <!-- 每个tab对应一个独立的z-paging组件 -->
        <z-paging
          :ref="el => setPagingRef(el, index)"
          v-model="dataLists[index]"
          @query="(pageNo, pageSize) => queryList(pageNo, pageSize, index)">

          <!-- 列表内容 -->
          <view class="list-item" v-for="(listItem, listIndex) in dataLists[index]" :key="listIndex">
            <!-- 列表项内容 -->
          </view>

          <!-- 空状态 -->
          <template #empty>
            <view class="empty-state">
              <text class="empty-text">暂无{{ item }}数据</text>
            </view>
          </template>
        </z-paging>
      </swiper-item>
    </swiper>
  </z-paging-swiper>
</template>

<script setup>
import { ref, reactive } from 'vue'

const tabs = ref(null)
const pagingRefs = ref([])
const tabList = ref(['推荐', '广场', '附近'])
const current = ref(0)
const dataLists = reactive([[], [], []]) // 每个tab对应一个数据列表

// 设置z-paging组件引用
const setPagingRef = (el, index) => {
  if (el) {
    pagingRefs.value[index] = el
  }
}

// tabs通知swiper切换
const tabsChange = (index) => {
  current.value = index
}

// swiper滑动中
const swiperTransition = (e) => {
  tabs.value.setDx(e.detail.dx)
}

// swiper滑动结束
const swiperAnimationfinish = (e) => {
  current.value = e.detail.current
  tabs.value.unlockDx()
}

// z-paging查询方法
const queryList = async (pageNo, pageSize, tabIndex) => {
  try {
    // 根据不同tab调用不同API
    const res = await getTabDataList({ pageNo, pageSize, type: tabIndex })
    pagingRefs.value[tabIndex].complete(res.data)
  } catch (error) {
    pagingRefs.value[tabIndex].complete(false)
  }
}

// 刷新当前列表
const reloadCurrentList = () => {
  if (pagingRefs.value[current.value]) {
    pagingRefs.value[current.value].reload()
  }
}
</script>

<style lang="scss" scoped>
.swiper {
  height: 100%;
}

.swiper-item {
  height: 100%;
}

.list-item {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 12rpx;
}
</style>
```

#### 📋 必需的配置清单

使用 `z-paging` 时必须包含以下内容：

1. **推荐容器配置（普通模式 + 导航栏处理）**：
   ```vue
   <view class="content-wrapper">
     <z-paging ref="paging" v-model="dataList" @query="queryList">
       <template #top>
         <!-- 顶部间距，避免被导航栏覆盖 -->
         <view class="nav-spacer" :style="{ height: navBarHeight + 'px' }"></view>
         <!-- 固定在顶部的内容 -->
       </template>
       <!-- 列表内容 -->
     </z-paging>
   </view>
   ```

2. **容器样式配置**：
   ```scss
   .content-wrapper {
     height: 100vh;
     box-sizing: border-box;
   }

   .content-wrapper z-paging {
     height: 100%;
   }

   .nav-spacer {
     width: 100%;
     flex-shrink: 0;
   }
   ```

3. **基础组件配置**：
   ```vue
   <z-paging
     ref="paging"
     v-model="dataList"
     @query="queryList">
   ```

4. **查询方法（必须遵循）**：
   ```js
   // 必须使用async函数，参数名必须是pageNo和pageSize
   const queryList = async (pageNo, pageSize) => {
     try {
       const res = await getDataList({ pageNo, pageSize })
       paging.value.complete(res.data.list)  // 成功时传递数据数组
     } catch (error) {
       paging.value.complete(false)          // 失败时传递false
     }
   }
   ```

5. **响应式数据**：
   ```js
   const paging = ref(null)      // z-paging组件引用
   const dataList = ref([])      // 数据列表 - 重要：不要在分页请求结束中自己赋值！！！
   const navBarHeight = ref(0)   // 导航栏高度
   ```

6. **重要注意事项**：
   ```js
   // ✅ 正确：使用complete方法传递数据
   paging.value.complete(res.data.list)

   // ❌ 错误：不要手动赋值dataList
   // dataList.value = res.data.list  // 禁止这样做！

   // ✅ 正确：切换tab时使用reload方法
   const tabsChange = (index) => {
     tabIndex.value = index
     paging.value.reload()  // 使用reload，不要直接调用queryList
   }

   // ❌ 错误：不要直接调用查询方法
   // queryList(1, 10)  // 禁止这样做！
   ```

5. **Tab切换场景的特殊配置**：
   ```vue
   <!-- swiper必须设置height:100% -->
   <swiper class="swiper" :current="current">
     <swiper-item v-for="(item, index) in tabList" :key="index">
       <z-paging :ref="el => setPagingRef(el, index)" v-model="dataLists[index]">
         <!-- 内容 -->
       </z-paging>
     </swiper-item>
   </swiper>
   ```

#### ❌ 常见错误

```vue
<!-- 错误：容器没有设置高度 -->
<view class="content-wrapper">  <!-- ❌ 缺少 height: 100vh -->
  <z-paging ref="paging" v-model="dataList" @query="queryList">
  </z-paging>
</view>

<!-- 错误：没有处理导航栏覆盖问题 -->
<z-paging ref="paging" v-model="dataList" @query="queryList">
  <template #top>
    <!-- ❌ 缺少导航栏间距处理 -->
    <z-tabs :list="tabList" @change="tabsChange" />
  </template>
</z-paging>

<!-- 错误：查询方法参数名错误 -->
<script setup>
// ❌ 参数名应该是pageNo，不是pageNum
const queryList = async (pageNum, pageSize) => {
  // ...
}

// ❌ 不是async函数
const queryList = (pageNo, pageSize) => {
  setTimeout(() => {
    paging.value.complete(data)
  }, 500)
}

// ❌ 错误：手动赋值dataList
const queryList = async (pageNo, pageSize) => {
  const res = await getDataList({ pageNo, pageSize })
  dataList.value = res.data.list  // ❌ 禁止手动赋值！
}

// ❌ 错误：直接调用查询方法
const tabsChange = (index) => {
  tabIndex.value = index
  queryList(1, 10)  // ❌ 应该使用 paging.value.reload()
}

// ❌ 错误：不传递list数组
paging.value.complete(res.data)  // ❌ 应该传递 res.data.list

// ❌ 错误：缺少导航栏高度处理
// 没有监听导航栏高度变化
// 没有设置nav-spacer的高度
</script>

<!-- 错误：CSS样式配置不完整 -->
<style>
.content-wrapper {
  /* ❌ 缺少高度设置 */
}

/* ❌ 缺少z-paging高度设置 */
/* ❌ 缺少nav-spacer样式 */
</style>
```

#### 🎯 特殊场景处理

##### 1. 简单Tab切换（使用v-show）

```vue
<template>
  <z-paging-swiper>
    <template #top>
      <view class="tab-bar">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'invite' }"
          @click="switchTab('invite')">
          邀请信息
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'withdraw' }"
          @click="switchTab('withdraw')">
          提现记录
        </view>
      </view>
    </template>

    <!-- 邀请信息列表 -->
    <z-paging
      v-show="activeTab === 'invite'"
      ref="invitePaging"
      v-model="inviteList"
      @query="queryInviteList">
      <!-- 内容 -->
    </z-paging>

    <!-- 提现记录列表 -->
    <z-paging
      v-show="activeTab === 'withdraw'"
      ref="withdrawPaging"
      v-model="withdrawList"
      :auto="false"
      @query="queryWithdrawList">
      <!-- 内容 -->
    </z-paging>
  </z-paging-swiper>
</template>

<script setup>
import { ref, nextTick } from 'vue'

const activeTab = ref('invite')
const invitePaging = ref(null)
const withdrawPaging = ref(null)
const inviteList = ref([])
const withdrawList = ref([])

// 切换标签页
const switchTab = (tab) => {
  activeTab.value = tab
  if (tab === 'withdraw') {
    // 使用nextTick确保DOM更新完成后再触发reload
    nextTick(() => {
      setTimeout(() => {
        if (withdrawPaging.value) {
          withdrawPaging.value.reload()
        }
      }, 200)
    })
  }
}

const queryInviteList = async (pageNo, pageSize) => {
  // 邀请列表查询逻辑
}

const queryWithdrawList = async (pageNo, pageSize) => {
  // 提现记录查询逻辑
}
</script>
```

##### 2. 复杂Tab切换（使用swiper，官方推荐）

参考上面的"Tab切换列表页面（官方标准写法）"示例。

##### 3. 手动触发刷新

```js
// 刷新指定的z-paging
const refreshList = () => {
  if (paging.value) {
    paging.value.reload()  // 重新加载第一页
  }
}

// Tab切换场景下刷新当前列表
const refreshCurrentTab = () => {
  if (pagingRefs.value[current.value]) {
    pagingRefs.value[current.value].reload()
  }
}
```

## 样式规范

### 主题色使用
- 使用 `$primary-color` 作为主题色
- 背景渐变：`linear-gradient(180deg, $primary-color 0%, #f5f5f5 40%)`

### 间距规范
- 页面内边距：`padding: 0 30rpx`
- 卡片间距：`margin-bottom: 30rpx`
- 内容间距：`padding: 40rpx 30rpx`

### 圆角规范
- 大圆角：`border-radius: $radius-lg`
- 中圆角：`border-radius: $radius-md`
- 小圆角：`border-radius: $radius-sm`

## 路由配置规范

### 4. 创建新页面时必须添加路由配置

#### 🔥 强制要求：新页面必须配置路由

创建新页面时，**必须**在 `pages.json` 中添加对应的路由配置，否则页面无法正常访问。

#### ✅ 正确的路由配置流程

1. **创建页面文件**：
   ```
   pagesubs/my/example/example.vue
   ```

2. **在 pages.json 中添加路由**：
   ```json
   {
     "pages": [
       // 主页面路由
     ],
     "subPackages": [
       {
         "root": "pagesubs",
         "pages": [
           {
             "path": "my/example/example",
             "style": {
               "navigationBarTitleText": "示例页面",
               "navigationStyle": "custom"
             }
           }
         ]
       }
     ]
   }
   ```

#### 📋 路由配置规范

1. **页面路径规范**：
   - 主包页面：`pages/` 目录下
   - 分包页面：`pagesubs/` 目录下
   - 路径格式：`目录/子目录/页面名称`

2. **必需的配置项**：
   ```json
   {
     "path": "my/example/example",           // 页面路径（不含.vue后缀）
     "style": {
       "navigationBarTitleText": "页面标题", // 页面标题
       "navigationStyle": "custom"          // 使用自定义导航栏
     }
   }
   ```

3. **推荐的配置项**：
   ```json
   {
     "path": "my/example/example",
     "style": {
       "navigationBarTitleText": "页面标题",
       "navigationStyle": "custom",
       "backgroundColor": "#f5f5f5",        // 页面背景色
       "enablePullDownRefresh": false,      // 禁用下拉刷新
       "onReachBottomDistance": 50          // 上拉触底距离
     }
   }
   ```

#### 🗂️ 分包配置规范

根据功能模块组织分包：

```json
{
  "subPackages": [
    {
      "root": "pagesubs",
      "pages": [
        // 我的模块
        "my/setting/setting",
        "my/feedback/feedback",
        "my/upgrade/upgrade",

        // 认证模块
        "auth/identity/identity",
        "auth/education/education",

        // 推广中心模块
        "promotion-center/withdraw/withdraw"
      ]
    }
  ]
}
```

#### ❌ 常见错误

```json
// 错误：路径包含.vue后缀
{
  "path": "my/example/example.vue"  // ❌ 错误
}

// 错误：路径不匹配文件结构
{
  "path": "example"  // ❌ 文件在 pagesubs/my/example/example.vue
}

// 错误：缺少navigationStyle配置
{
  "path": "my/example/example",
  "style": {
    "navigationBarTitleText": "示例页面"
    // ❌ 缺少 "navigationStyle": "custom"
  }
}
```

#### 🔗 页面跳转规范

配置路由后，使用标准的跳转方法：

```js
// 正确的跳转方式
uni.navigateTo({
  url: '/pagesubs/my/example/example'
})

// 带参数跳转
uni.navigateTo({
  url: '/pagesubs/my/example/example?id=123&type=1'
})

// 替换当前页面
uni.redirectTo({
  url: '/pagesubs/my/example/example'
})
```

## 代码检查清单

在提交代码前，请确保：

- [ ] **使用 Vue 3 Composition API 语法（script setup）**
- [ ] **文件上传使用 uni-file-picker 组件**
- [ ] `scroll-nav-page` 组件已启用渐变效果
- [ ] 实现了页面滚动监听
- [ ] 正确处理了导航栏高度
- [ ] 主容器设置了正确的 `paddingTop`
- [ ] 使用了项目统一的主题色和样式规范
- [ ] **z-paging 组件遵循官方标准写法**
- [ ] **容器设置了正确的高度（height: 100vh）**
- [ ] **z-paging设置了正确的高度（height: 100%）**
- [ ] **正确处理了导航栏覆盖问题（nav-spacer间距）**
- [ ] **查询方法使用async函数且参数名为pageNo和pageSize**
- [ ] **使用 paging.value.complete() 传递数据，不手动赋值dataList**
- [ ] **Tab切换使用 paging.value.reload()，不直接调用查询方法**
- [ ] **正确传递 res.data.list 数组给 complete 方法**
- [ ] **导航栏高度监听和处理正确实现**
- [ ] `uni-file-picker` 组件正确配置了必需参数
- [ ] 图片上传实现了完整的错误处理流程
- [ ] 上传文件使用了标准的OSS接口
- [ ] 文件状态管理符合规范要求
- [ ] 所有组件都有适当的错误处理
- [ ] **新创建的页面已在 pages.json 中配置路由**
- [ ] **路由路径与文件路径完全匹配**
- [ ] **页面标题和导航栏样式已正确配置**

## 注意事项

1. **性能优化**：确保滚动监听不会造成性能问题
2. **兼容性**：测试在不同平台（H5、小程序、App）的表现
3. **用户体验**：确保渐变效果自然流畅
4. **代码复用**：相似页面可以提取公共逻辑

---

**重要提醒**：
请遵守如下规范：
1、**所有代码必须使用 Vue 3 语法，禁止使用 Vue 2 语法！**
2、**所有文件上传必须使用 uni-file-picker 组件！**
3、**除非用户明确要求生成文档，否则只生成代码文件，严禁生成README、示例文件、说明文档等任何文档！**
4、不需要生成优化总结文档；
5、不需要生成代码检查清单；
6、违反 scroll-nav-page 渐变效果规则的代码将不被接受，请严格按照规范实现！
7、违反 Vue 3 语法规范的代码将不被接受！
8、违反 uni-file-picker 上传规范的代码将不被接受！
9、**违反文档生成限制规则的代码将不被接受！**
