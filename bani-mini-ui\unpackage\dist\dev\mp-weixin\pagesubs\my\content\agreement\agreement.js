"use strict";const e=require("../../../../common/vendor.js"),u=require("../../../../utils/common.js"),m=require("../../../../api/content/agreement.js");Array||e.resolveComponent("uni-load-more")();const p=()=>"../../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";Math||(p+_)();const _=()=>"../../../../components/scroll-nav-page/scroll-nav-page.js",i={__name:"agreement",setup(l){e.ref(0);const r=e.ref(""),o=e.ref(""),a=e.ref(!0),s=e.ref({contentdown:"正在加载协议内容...",contentrefresh:"正在加载协议内容...",contentnomore:"加载完成"});e.onLoad(n=>{if(!n.key){u.toast("不存在的页面Key"),e.index.navigateBack({delta:1});return}r.value="",a.value=!0,m.getAgreementByKey(n.key).then(t=>{t&&t.data&&t.data.content?(o.value=t.data.content,r.value=t.data.name):o.value=c(n.key),a.value=!1}).catch(t=>{o.value=c(n.key),a.value=!1})});const c=n=>({userAgreement:`
			<h2>用户协议</h2>
			<p>欢迎使用伴你有约！</p>
			<p>本协议是您与伴你有约之间关于使用伴你有约服务的法律协议。</p>
			<h3>1. 服务条款</h3>
			<p>用户在使用本服务时，应当遵守相关法律法规...</p>
		`,privacyAgreement:`
			<h2>隐私政策</h2>
			<p>我们非常重视您的隐私保护。</p>
			<p>本隐私政策说明了我们如何收集、使用和保护您的个人信息。</p>
			<h3>1. 信息收集</h3>
			<p>我们可能收集以下类型的信息...</p>
		`,serviceAgreement:`
			<h2>服务协议</h2>
			<p>本服务协议规定了服务的具体条款和条件。</p>
		`,communityRules:`
			<h2>社区规范</h2>
			<p>为了维护良好的社区环境，请遵守以下规范。</p>
		`})[n]||"<p>内容加载中...</p>";return(n,t)=>e.e({a:a.value},a.value?{b:e.p({status:"loading","content-text":s.value})}:{c:o.value},{d:e.p({title:r.value,"show-back":!0})})}},d=e._export_sfc(i,[["__scopeId","data-v-5d6c4be3"]]);wx.createPage(d);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pagesubs/my/content/agreement/agreement.js.map
