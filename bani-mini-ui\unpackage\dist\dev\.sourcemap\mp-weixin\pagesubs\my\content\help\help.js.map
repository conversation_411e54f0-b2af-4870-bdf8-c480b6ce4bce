{"version": 3, "file": "help.js", "sources": ["pagesubs/my/content/help/help.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcY29udGVudFxoZWxwXGhlbHAudnVl"], "sourcesContent": ["<template>\n\t<scroll-nav-page :title=\"pageTitle\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-container\">\n\t\t\t\t<!-- 内容区域 -->\n\t\t\t\t<view class=\"content-wrapper\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<view class=\"help-content\">\n\t\t\t\t\t\t<rich-text :nodes=\"content\"></rich-text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\nimport { toast } from '@/utils/common'\nimport { getHelpByKey } from '@/api/content/help'\n\n// 页面滚动距离\nconst pageScrollTop = ref(0)\n// 导航栏高度\nconst navBarHeight = ref(0)\n// 页面标题\nconst pageTitle = ref('')\n// 页面内容\nconst content = ref('')\n\n// 页面滚动监听\nconst handlePageScroll = (e) => {\n\tpageScrollTop.value = e.scrollTop\n}\n\n// 导航栏高度变化\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 计算导航栏文字颜色\nconst getNavTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\n}\n\n\n// 页面加载\nonLoad((option) => {\n\tif (!option.key) {\n\t\ttoast('不存在的页面Key')\n\t\tuni.navigateBack({\n\t\t\tdelta: 1\n\t\t})\n\t\treturn\n\t}\n\tpageTitle.value = ''\n\t// 获取帮助内容\n\tgetHelpByKey(option.key).then(res => {\n\t\tcontent.value = res.data.content\n\t\tpageTitle.value = res.data.name\n\t}).catch(err => {\n\t\ttoast('获取内容失败')\n\t\t// 如果接口不存在，可以显示默认内容\n\t\tloadDefaultContent(option.key)\n\t})\n})\n\n// 加载默认内容（当接口不可用时）\nconst loadDefaultContent = (key) => {\n\tconst defaultContents = {\n\t\tuserGuide: `\n\t\t\t<h2>欢迎使用伴你有约</h2>\n\t\t\t<p>这里是用户指南的默认内容...</p>\n\t\t`,\n\t\tfaq: `\n\t\t\t<h2>常见问题</h2>\n\t\t\t<h3>Q: 如何注册账号？</h3>\n\t\t\t<p>A: 您可以通过手机号码快速注册...</p>\n\t\t`,\n\t\tcontactUs: `\n\t\t\t<h2>联系我们</h2>\n\t\t\t<p>客服电话：400-123-4567</p>\n\t\t\t<p>客服邮箱：<EMAIL></p>\n\t\t`,\n\t\tfeedback: `\n\t\t\t<h2>意见反馈</h2>\n\t\t\t<p>您的意见对我们很重要...</p>\n\t\t`,\n\t\tsafetyTips: `\n\t\t\t<h2>安全提示</h2>\n\t\t\t<p>为了您的账号安全，请注意以下事项...</p>\n\t\t`,\n\t\tfeatureIntro: `\n\t\t\t<h2>功能介绍</h2>\n\t\t\t<p>伴你有约为您提供以下功能...</p>\n\t\t`\n\t}\n\n\tcontent.value = defaultContents[key] || '<p>内容加载中...</p>'\n}\n\n// 页面滚动监听\nonPageScroll(handlePageScroll)\n\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.content-wrapper {\n\tmargin-top: 20rpx;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n}\n\n.help-content {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 20rpx;\n\tpadding: 40rpx 30rpx;\n\tfont-size: 30rpx;\n\tline-height: 1.8;\n\tcolor: #333;\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n\n\t// 富文本内容样式优化\n\t:deep(p) {\n\t\tmargin-bottom: 24rpx;\n\t\ttext-align: justify;\n\t}\n\n\t:deep(h1),\n\t:deep(h2),\n\t:deep(h3) {\n\t\tcolor: $primary-color;\n\t\tfont-weight: 600;\n\t\tmargin: 40rpx 0 20rpx 0;\n\t}\n\n\t:deep(h1) {\n\t\tfont-size: 36rpx;\n\t}\n\n\t:deep(h2) {\n\t\tfont-size: 34rpx;\n\t}\n\n\t:deep(h3) {\n\t\tfont-size: 32rpx;\n\t}\n\n\t:deep(ul),\n\t:deep(ol) {\n\t\tpadding-left: 40rpx;\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t:deep(li) {\n\t\tmargin-bottom: 12rpx;\n\t\tline-height: 1.6;\n\t}\n\n\t:deep(strong) {\n\t\tcolor: $primary-color;\n\t\tfont-weight: 600;\n\t}\n\n\t:deep(a) {\n\t\tcolor: $primary-color;\n\t\ttext-decoration: underline;\n\t}\n\n\t// 帮助页面特有样式\n\t:deep(.faq-item) {\n\t\tbackground: rgba(105, 108, 243, 0.05);\n\t\tpadding: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tborder-left: 4rpx solid $primary-color;\n\t}\n\n\t:deep(.contact-info) {\n\t\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(105, 108, 243, 0.05));\n\t\tpadding: 30rpx;\n\t\tborder-radius: 16rpx;\n\t\ttext-align: center;\n\t\tmargin: 30rpx 0;\n\t}\n\n\t:deep(.safety-warning) {\n\t\tbackground: rgba(255, 193, 7, 0.1);\n\t\tborder: 1px solid rgba(255, 193, 7, 0.3);\n\t\tpadding: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin: 20rpx 0;\n\t}\n}\n\n// 响应式设计\n@media screen and (max-width: 750rpx) {\n\t.help-content {\n\t\tpadding: 30rpx 24rpx;\n\t\tfont-size: 28rpx;\n\n\t\t:deep(h1) {\n\t\t\tfont-size: 34rpx;\n\t\t}\n\n\t\t:deep(h2) {\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t:deep(h3) {\n\t\t\tfont-size: 30rpx;\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/content/help/help.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "pageTitle", "content", "handlePageScroll", "e", "onLoad", "option", "toast", "uni", "getHelpByKey", "res", "err", "loadDefaultContent", "key", "defaultContents", "onPageScroll", "MiniProgramPage"], "mappings": "oTAsBA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAEAE,EAAAF,EAAA,IAAA,EAAA,EAEAG,EAAAH,EAAA,IAAA,EAAA,EAGAI,EAAAC,GAAA,CACAN,EAAA,MAAAM,EAAA,SACA,EAeAC,EAAA,OAAAC,GAAA,CACA,GAAA,CAAAA,EAAA,IAAA,CACAC,EAAAA,MAAA,WAAA,EACAC,EAAAA,MAAA,aAAA,CACA,MAAA,CACA,CAAA,EACA,MACA,CACAP,EAAA,MAAA,GAEAQ,EAAAA,aAAAH,EAAA,GAAA,EAAA,KAAAI,GAAA,CACAR,EAAA,MAAAQ,EAAA,KAAA,QACAT,EAAA,MAAAS,EAAA,KAAA,IACA,CAAA,EAAA,MAAAC,GAAA,CACAJ,EAAAA,MAAA,QAAA,EAEAK,EAAAN,EAAA,GAAA,CACA,CAAA,CACA,CAAA,EAGA,MAAAM,EAAAC,GAAA,CACA,MAAAC,EAAA,CACA,UAAA;AAAA;AAAA;AAAA,IAIA,IAAA;AAAA;AAAA;AAAA;AAAA,IAKA,UAAA;AAAA;AAAA;AAAA;AAAA,IAKA,SAAA;AAAA;AAAA;AAAA,IAIA,WAAA;AAAA;AAAA;AAAA,IAIA,aAAA;AAAA;AAAA;AAAA,GAIA,EAEAZ,EAAA,MAAAY,EAAAD,CAAA,GAAA,iBACA,EAGAE,OAAAA,EAAA,aAAAZ,CAAA,sJCtGA,GAAG,WAAWa,CAAe"}