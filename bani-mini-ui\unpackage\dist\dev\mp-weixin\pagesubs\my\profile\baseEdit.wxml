<scroll-nav-page wx:if="{{aj}}" u-s="{{['content']}}" u-i="4fd28516-0" bind:__l="__l" u-p="{{aj}}"><view slot="content"><view class="page-content" style="{{'padding-top:' + ai}}"><view class="form-item" bindtap="{{d}}"><text class="label">昵称<text class="required">*</text></text><text class="{{['value', b && 'placeholder']}}">{{a}}</text><uni-icons wx:if="{{c}}" u-i="4fd28516-1,4fd28516-0" bind:__l="__l" u-p="{{c}}"></uni-icons></view><picker mode="selector" range="{{h}}" range-key="name" value="{{i}}" bindchange="{{j}}"><view class="form-item"><text class="label">性别<text class="required">*</text></text><text class="{{['value', f && 'placeholder']}}">{{e}}</text><uni-icons wx:if="{{g}}" u-i="4fd28516-2,4fd28516-0" bind:__l="__l" u-p="{{g}}"></uni-icons></view></picker><picker mode="date" value="{{n}}" bindchange="{{o}}"><view class="form-item"><text class="label">出生日期<text class="required">*</text></text><text class="{{['value', l && 'placeholder']}}">{{k}}</text><uni-icons wx:if="{{m}}" u-i="4fd28516-3,4fd28516-0" bind:__l="__l" u-p="{{m}}"></uni-icons></view></picker><picker mode="selector" range="{{s}}" range-key="name" value="{{t}}" bindchange="{{v}}"><view class="form-item"><text class="label">身高<text class="required">*</text></text><text class="{{['value', q && 'placeholder']}}">{{p}}</text><uni-icons wx:if="{{r}}" u-i="4fd28516-4,4fd28516-0" bind:__l="__l" u-p="{{r}}"></uni-icons></view></picker><picker mode="selector" range="{{z}}" range-key="name" value="{{A}}" bindchange="{{B}}"><view class="form-item"><text class="label">体重<text class="required">*</text></text><text class="{{['value', x && 'placeholder']}}">{{w}}</text><uni-icons wx:if="{{y}}" u-i="4fd28516-5,4fd28516-0" bind:__l="__l" u-p="{{y}}"></uni-icons></view></picker><picker mode="selector" range="{{F}}" range-key="name" value="{{G}}" bindchange="{{H}}"><view class="form-item"><text class="label">学历<text class="required">*</text></text><text class="{{['value', D && 'placeholder']}}">{{C}}</text><uni-icons wx:if="{{E}}" u-i="4fd28516-6,4fd28516-0" bind:__l="__l" u-p="{{E}}"></uni-icons></view></picker><view class="form-item" bindtap="{{L}}"><text class="label">职业<text class="required">*</text></text><text class="{{['value', J && 'placeholder']}}">{{I}}</text><uni-icons wx:if="{{K}}" u-i="4fd28516-7,4fd28516-0" bind:__l="__l" u-p="{{K}}"></uni-icons></view><picker mode="selector" range="{{P}}" range-key="name" value="{{Q}}" bindchange="{{R}}"><view class="form-item"><text class="label">情感状况</text><text class="{{['value', N && 'placeholder']}}">{{M}}</text><uni-icons wx:if="{{O}}" u-i="4fd28516-8,4fd28516-0" bind:__l="__l" u-p="{{O}}"></uni-icons></view></picker><picker mode="selector" range="{{V}}" range-key="name" value="{{W}}" bindchange="{{X}}"><view class="form-item"><text class="label">收入</text><text class="{{['value', T && 'placeholder']}}">{{S}}</text><uni-icons wx:if="{{U}}" u-i="4fd28516-9,4fd28516-0" bind:__l="__l" u-p="{{U}}"></uni-icons></view></picker><view class="form-item" bindtap="{{ab}}"><text class="label">户籍地址<text class="required">*</text></text><text class="{{['value', Z && 'placeholder']}}">{{Y}}</text><uni-icons wx:if="{{aa}}" u-i="4fd28516-10,4fd28516-0" bind:__l="__l" u-p="{{aa}}"></uni-icons></view><view class="form-item" bindtap="{{af}}"><text class="label">现居地址<text class="required">*</text></text><text class="{{['value', ad && 'placeholder']}}">{{ac}}</text><uni-icons wx:if="{{ae}}" u-i="4fd28516-11,4fd28516-0" bind:__l="__l" u-p="{{ae}}"></uni-icons></view><view class="warning-tips"><text>为了保证真实、真诚的交友，请如实填写个人信息，虚假敷衍的内容不会审核通过，请知悉</text></view><view class="submit"><button bindtap="{{ah}}"><text wx:if="{{ag}}">下一步</text><text wx:else>保存</text></button></view></view></view></scroll-nav-page><uni-popup wx:if="{{aq}}" class="r" u-s="{{['d']}}" u-r="nicknamePopup" u-i="4fd28516-12" bind:__l="__l" u-p="{{aq}}"><uni-popup-dialog wx:if="{{ao}}" u-s="{{['d']}}" bindconfirm="{{am}}" bindclose="{{an}}" u-i="4fd28516-13,4fd28516-12" bind:__l="__l" u-p="{{ao}}"><input type="nickname" placeholder="请输入昵称" class="nickname-input" value="{{ak}}" bindinput="{{al}}"/></uni-popup-dialog></uni-popup><district-select class="r" u-r="districtSelectRef" bindconfirm="{{as}}" u-i="4fd28516-14" bind:__l="__l"/><tag-select wx:if="{{aw}}" class="r" u-r="jobSelectRef" bindconfirm="{{av}}" u-i="4fd28516-15" bind:__l="__l" u-p="{{aw}}"/>