<template>
	<view class="scroll-nav-page">
		<!-- 滚动渐变导航栏 -->
		<view class="scroll-nav-bar" :style="{ backgroundColor: currentBgColor, color: currentTextColor }">
			<!-- 状态栏占位 -->
			<view v-if="statusBar" class="status-bar"
				:style="{ height: statusBarHeight + 'px', backgroundColor: currentBgColor }"></view>

			<!-- 导航栏内容 -->
			<view class="nav-content" :style="{ height: navHeight + 'px', backgroundColor: currentBgColor }">
				<!-- 左侧区域 -->
				<view class="nav-left">
					<!-- 返回按钮 -->
					<view v-if="showBack" class="back-button" @click="handleBack">
						<uni-icons :type="currentBackIcon" size="20" :color="currentTextColor" />
					</view>

					<!-- 左侧插槽内容 -->
					<view class="left-slot" @click="handleClickLeft">
						<slot name="nav-left">
							<!-- 默认左侧内容 -->
							<view v-if="leftIcon || leftText" class="default-left">
								<uni-icons v-if="leftIcon" :type="leftIcon" size="20" :color="currentTextColor" />
								<text v-if="leftText" class="left-text" :style="{ color: currentTextColor }">{{ leftText
									}}</text>
							</view>
						</slot>
					</view>
				</view>

				<!-- 中间区域 -->
				<view class="nav-center">
					<slot name="nav-center">
						<!-- 默认中间内容 -->
						<text v-if="title" class="nav-title" :style="{ color: currentTextColor }">{{ title }}</text>
					</slot>
				</view>

				<!-- 右侧区域 -->
				<view class="nav-right" @click="handleClickRight">
					<slot name="nav-right">
						<!-- 默认右侧内容 -->
						<view v-if="rightIcon || rightText" class="default-right">
							<text v-if="rightText" class="right-text" :style="{ color: currentTextColor }">{{ rightText
								}}</text>
							<uni-icons v-if="rightIcon" :type="rightIcon" size="20" :color="currentTextColor" />
						</view>
					</slot>
				</view>
			</view>

			<!-- 边框 -->
			<view v-if="border" class="nav-border"></view>

			<!-- 阴影 -->
			<view v-if="shadow" class="nav-shadow"></view>
		</view>

		<!-- 内容区域插槽 -->
		<view class="content-container" :style="{ paddingTop: navBarHeight + 'px' }">
			<slot name="content">
				<!-- 默认内容区域 -->
				<view class="default-content">
					<text>请在content插槽中添加页面内容</text>
				</view>
			</slot>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { onPageScroll } from '@dcloudio/uni-app'
import globalConfig from '@/config'

// 定义 props
const props = defineProps({
	// 页面标题
	title: {
		type: String,
		default: ''
	},
	// 是否显示返回按钮
	showBack: {
		type: Boolean,
		default: false
	},
	// 左侧图标
	leftIcon: {
		type: String,
		default: ''
	},
	// 左侧文字
	leftText: {
		type: String,
		default: ''
	},
	// 右侧图标
	rightIcon: {
		type: String,
		default: ''
	},
	// 右侧文字
	rightText: {
		type: String,
		default: ''
	},
	// 是否显示边框
	border: {
		type: Boolean,
		default: false
	},
	// 是否固定
	fixed: {
		type: Boolean,
		default: true
	},
	// 是否包含状态栏
	statusBar: {
		type: Boolean,
		default: true
	},
	// 是否显示阴影
	shadow: {
		type: Boolean,
		default: false
	},
	// 导航栏高度
	height: {
		type: [String, Number],
		default: 44
	},
	// 初始背景色（主题色）
	initialBgColor: {
		type: String,
		default: globalConfig?.theme?.primaryColor || '#696CF3'
	},
	// 滚动后的背景色（白色）
	scrolledBgColor: {
		type: String,
		default: '#ffffff'
	},
	// 初始文字颜色
	initialTextColor: {
		type: String,
		default: '#ffffff'
	},
	// 滚动后的文字颜色
	scrolledTextColor: {
		type: String,
		default: '#333333'
	},
	// 滚动距离阈值
	scrollThreshold: {
		type: Number,
		default: 100
	},
	// 渐变分割点
	fadeTransitionPoint: {
		type: Number,
		default: 0.6,
		validator: (value) => value >= 0 && value <= 1
	},
	// 是否启用滚动渐变
	enableScrollGradient: {
		type: Boolean,
		default: true
	}
})

// 定义 emits
const emit = defineEmits(['clickLeft', 'clickRight', 'back', 'scroll', 'heightChange'])

// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)

// 当前背景色和文字颜色
const currentBgColor = ref(props.initialBgColor)
const currentTextColor = ref(props.initialTextColor)

// 状态栏高度
const statusBarHeight = ref(0)

// 当前返回按钮图标
const currentBackIcon = computed(() => {
	if (!props.showBack) {
		return
	}

	// 检查是否有上一页
	const pages = getCurrentPages()
	if (pages.length <= 1) {
		// 没有上一页，显示首页图标
		return 'home'
	} else {
		// 有上一页，显示返回图标
		return 'left'
	}
})

// 导航栏高度
const navHeight = computed(() => {
	return typeof props.height === 'number' ? props.height : parseInt(props.height)
})

// 总高度（状态栏 + 导航栏）
const totalHeight = computed(() => {
	return statusBarHeight.value + navHeight.value
})

// 更新导航栏颜色
const updateNavColors = (scrollTop) => {
	if (!props.enableScrollGradient) return

	// 根据滚动距离计算透明度，实现渐变效果
	const opacity = Math.min(scrollTop / props.scrollThreshold, 1)

	// 优化的渐变过程：先透明再变白
	if (opacity === 0) {
		// 完全透明状态
		currentBgColor.value = props.initialBgColor
		currentTextColor.value = props.initialTextColor
	} else if (opacity === 1) {
		// 完全不透明状态
		currentBgColor.value = props.scrolledBgColor
		currentTextColor.value = props.scrolledTextColor
	} else {
		// 渐变过程：分为两个阶段
		const transitionPoint = props.fadeTransitionPoint

		if (opacity <= transitionPoint) {
			// 第一阶段：从原色逐渐变透明
			const fadeProgress = opacity / transitionPoint // 0 到 1
			const fadeOpacity = 1 - fadeProgress // 从1变到0（完全透明）
			const initialColor = hexToRgb(props.initialBgColor)

			if (initialColor) {
				currentBgColor.value = `rgba(${initialColor.r}, ${initialColor.g}, ${initialColor.b}, ${fadeOpacity})`
				currentTextColor.value = props.initialTextColor
			}
		} else {
			// 第二阶段：从透明的白色变成不透明的白色
			const whiteProgress = (opacity - transitionPoint) / (1 - transitionPoint) // 0 到 1
			const scrolledColor = hexToRgb(props.scrolledBgColor)

			if (scrolledColor) {
				// 背景色：白色从透明变到不透明
				currentBgColor.value = `rgba(${scrolledColor.r}, ${scrolledColor.g}, ${scrolledColor.b}, ${whiteProgress})`

				// 文字颜色切换：在第二阶段的30%处切换
				const textSwitchPoint = transitionPoint + (1 - transitionPoint) * 0.3
				currentTextColor.value = opacity > textSwitchPoint ? props.scrolledTextColor : props.initialTextColor
			}
		}
	}
}

// 将十六进制颜色转换为 RGB
const hexToRgb = (hex) => {
	// 移除 # 符号
	hex = hex.replace('#', '')

	// 如果是3位十六进制，转换为6位
	if (hex.length === 3) {
		hex = hex.split('').map(char => char + char).join('')
	}

	const result = /^([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
	return result ? {
		r: parseInt(result[1], 16),
		g: parseInt(result[2], 16),
		b: parseInt(result[3], 16)
	} : null
}

// 页面滚动处理函数
const handlePageScroll = (e) => {
	// uni-app的onPageScroll事件对象结构是 { scrollTop: number }
	const scrollTop = e.scrollTop || 0
	pageScrollTop.value = scrollTop
	updateNavColors(scrollTop)
	emit('scroll', { scrollTop })
}

// 页面滚动监听
onPageScroll(handlePageScroll)

// 获取状态栏高度
const getStatusBarHeight = () => {
	try {
		// #ifdef MP-WEIXIN
		const systemInfo = uni.getWindowInfo()
		statusBarHeight.value = systemInfo.statusBarHeight || 0
		// #endif
		// #ifndef MP-WEIXIN
		const sysInfo = uni.getSystemInfoSync()
		statusBarHeight.value = sysInfo.statusBarHeight || 0
		// #endif
	} catch (e) {
		statusBarHeight.value = 0
	}
}

// 事件处理
const handleClickLeft = () => {
	emit('clickLeft')
}

const handleClickRight = () => {
	emit('clickRight')
}

const handleBack = () => {
	emit('back')

	// 检查是否有上一页
	const pages = getCurrentPages()
	if (pages.length <= 1) {
		// 没有上一页，跳转到首页
		uni.reLaunch({
			url: globalConfig.homePagePath
		})
	} else {
		// 有上一页，正常返回
		uni.navigateBack()
	}
}

// 监听总高度变化
watch(totalHeight, (newHeight) => {
	navBarHeight.value = newHeight
	emit('heightChange', newHeight)
}, { immediate: true })

// 初始化
onMounted(() => {
	getStatusBarHeight()
	updateNavColors(0)
})
</script>

<style lang="scss" scoped>
// 引入uni.scss变量
@import '@/uni.scss';

.scroll-nav-page {
	min-height: 100vh;
	background-color: #f8f9fa;
}

.scroll-nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	width: 100%;
	/* 移除过渡动画，让渐变效果更流畅 */
}

.status-bar {
	width: 100%;
}

.nav-content {
	display: flex;
	align-items: center;
	padding: 0 16rpx;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	min-width: 120rpx;
	height: 100%;

	.back-button {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 44rpx;
		height: 44rpx;
		margin-right: 8rpx;
		border-radius: 50%;
		transition: all 0.3s ease;
		cursor: pointer;

		&:hover {
			background: rgba(0, 0, 0, 0.05);
		}

		&:active {
			transform: scale(0.95);
			background: rgba(0, 0, 0, 0.1);
		}
	}

	.left-slot {
		display: flex;
		align-items: center;
		flex: 1;
	}
}

.nav-center {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	padding: 0 20rpx;
}

.nav-right {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	min-width: 120rpx;
	height: 100%;
}

.default-left,
.default-right {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.nav-title {
	font-size: $title-size-md;
	font-weight: 600;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
}

.left-text,
.right-text {
	font-size: $font-size-md;
}

.nav-border {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 1rpx;
	background-color: rgba(0, 0, 0, 0.1);
}

.nav-shadow {
	position: absolute;
	bottom: -10rpx;
	left: 0;
	right: 0;
	height: 10rpx;
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);
}

.content-container {
	min-height: calc(100vh - 88rpx);
}

.default-content {
	padding: 40rpx;
	text-align: center;
	color: #999;
	font-size: $font-size-md;
}
</style>
