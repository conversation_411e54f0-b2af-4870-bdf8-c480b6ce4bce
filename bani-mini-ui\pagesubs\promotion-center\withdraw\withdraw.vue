<template>
	<scroll-nav-page title="邀请朋友" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 主要内容 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<!-- 邀请统计卡片 -->
					<view class="invite-stats-card">
						<view class="stats-header">
							<text class="invite-count">已邀请：{{ inviteStats.inviteCount }}人</text>
							<view class="header-actions">
								<button class="invite-more-btn" @click="inviteMore">邀请更多朋友</button>
							</view>
						</view>

						<view class="stats-content">
							<view class="stat-row">
								<view class="stat-item">
									<text class="stat-label">总收益</text>
									<text class="stat-value">{{ inviteStats.totalEarnings }}元</text>
								</view>
								<view class="stat-item">
									<text class="stat-label">已提现收益</text>
									<text class="stat-value">{{ inviteStats.withdrawnEarnings }}元</text>
								</view>
							</view>
							<view class="stat-row">
								<view class="stat-item">
									<text class="stat-label">待提现收益</text>
									<text class="stat-value">{{ inviteStats.pendingEarnings }}元</text>
								</view>
								<view class="stat-actions">
									<button class="withdraw-btn" @click="showWithdrawModal">
										申请提现
									</button>
								</view>
							</view>
						</view>
					</view>

					<!-- 标签页切换 -->
					<view class="tab-section">
						<view class="tab-nav">
							<view class="tab-item" :class="{ active: activeTab === 'invite' }"
								@click="switchTab('invite')">
								<text>邀请信息</text>
							</view>
							<view class="tab-item" :class="{ active: activeTab === 'withdraw' }"
								@click="switchTab('withdraw')">
								<text>提现记录</text>
							</view>
						</view>
					</view>

					<!-- 邀请信息内容 -->
					<view v-if="activeTab === 'invite'" class="invite-content">
						<!-- 朋友信息Tab选项卡 -->
						<view class="friend-info-section">
							<view class="section-title">
								<text>朋友信息</text>
							</view>

							<!-- Tab选项卡导航 -->
							<view class="friend-tabs">
								<view class="friend-tab-item" :class="{ active: selectedCategory === 'all' }"
									@click="selectCategory('all')">
									<view class="tab-icon">
										<uni-icons type="person-filled" size="20" color="#696CF3"></uni-icons>
									</view>
									<view class="tab-content">
										<text class="tab-name">全部朋友</text>
										<text class="tab-count">{{ friendStats.total }}</text>
									</view>
								</view>

								<view class="friend-tab-item" :class="{ active: selectedCategory === 'premium' }"
									@click="selectCategory('premium')">
									<view class="tab-icon">
										<uni-icons type="vip-filled" size="20" color="#FFD700"></uni-icons>
									</view>
									<view class="tab-content">
										<text class="tab-name">高级朋友</text>
										<text class="tab-count">{{ friendStats.premium }}</text>
									</view>
								</view>

								<view class="friend-tab-item" :class="{ active: selectedCategory === 'active' }"
									@click="selectCategory('active')">
									<view class="tab-icon">
										<uni-icons type="heart-filled" size="20" color="#FF6B6B"></uni-icons>
									</view>
									<view class="tab-content">
										<text class="tab-name">活跃朋友</text>
										<text class="tab-count">{{ friendStats.active }}</text>
									</view>
								</view>

								<view class="friend-tab-item" :class="{ active: selectedCategory === 'new' }"
									@click="selectCategory('new')">
									<view class="tab-icon">
										<uni-icons type="star-filled" size="20" color="#4ECDC4"></uni-icons>
									</view>
									<view class="tab-content">
										<text class="tab-name">新朋友</text>
										<text class="tab-count">{{ friendStats.new }}</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 朋友列表 -->
						<z-paging ref="friendPaging" v-model="friendList" :fixed="false" :use-page-scroll="false"
							:data-key="selectedCategory" @query="queryFriendList">

							<view class="friend-list">
								<view class="friend-item" v-for="friend in friendList" :key="friend.id">
									<view class="friend-avatar">
										<image :src="friend.avatar" mode="aspectFill"></image>
									</view>
									<view class="friend-info">
										<text class="friend-name">{{ friend.name }}</text>
										<text class="friend-status">{{ friend.status }} 花瓣</text>
									</view>
									<view class="friend-level">
										<text class="level-name">{{ friend.level }}</text>
									</view>
								</view>
							</view>
						</z-paging>
					</view>

					<!-- 提现记录内容 -->
					<view v-if="activeTab === 'withdraw'" class="withdraw-content">
						<z-paging ref="withdrawPaging" v-model="withdrawList" :fixed="false" :use-page-scroll="false"
							@query="queryWithdrawList">

							<view class="withdraw-list">
								<view class="withdraw-item" v-for="record in withdrawList" :key="record.id">
									<view class="withdraw-info">
										<text class="withdraw-amount">{{ record.amount }}元</text>
										<text class="withdraw-time">{{ record.time }}</text>
									</view>
									<view class="withdraw-status" :class="record.status">
										<text>{{ getStatusText(record.status) }}</text>
									</view>
								</view>
							</view>
						</z-paging>
					</view>
				</view>

				<!-- 提现申请弹窗 -->
				<uni-popup ref="withdrawPopup" type="center" :mask-click="false">
					<view class="withdraw-modal">
						<view class="modal-header">
							<text class="modal-title">申请提现</text>
							<uni-icons type="close" size="20" color="#999" @click="closeWithdrawModal"></uni-icons>
						</view>
						<view class="modal-content">
							<view class="amount-info">
								<text class="available-amount">可提现金额：{{ inviteStats.pendingEarnings }}元</text>
							</view>
							<view class="withdraw-form">
								<view class="form-item">
									<text class="form-label">提现金额</text>
									<input class="form-input" type="number" v-model="withdrawForm.amount"
										placeholder="请输入提现金额" :max="inviteStats.pendingEarnings">
								</view>
							</view>
						</view>
						<view class="modal-footer">
							<button class="cancel-btn" @click="closeWithdrawModal">取消</button>
							<button class="confirm-btn" @click="submitWithdraw"
								:disabled="!canSubmitWithdraw">确认提现</button>
						</view>
					</view>
				</uni-popup>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import globalConfig from '@/config'
import { toast } from '@/utils/common'

// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const activeTab = ref('invite')
const selectedCategory = ref('all')

// 邀请统计数据
const inviteStats = reactive({
	inviteCount: 1,
	totalEarnings: 0,
	withdrawnEarnings: 0,
	pendingEarnings: 0
})

// 朋友统计数据
const friendStats = reactive({
	total: 100,
	premium: 25,
	active: 60,
	new: 15
})

// 朋友列表数据
const friends = ref([
	{
		id: 1,
		name: '悟净',
		avatar: 'https://picsum.photos/100/100?random=1',
		status: '+100',
		reward: 100,
		level: '黄金会员',
		category: 'new'
	},
	{
		id: 2,
		name: '小明',
		avatar: 'https://picsum.photos/100/100?random=2',
		status: '+200',
		reward: 200,
		level: '钻石会员',
		category: 'premium'
	},
	{
		id: 3,
		name: '小红',
		avatar: 'https://picsum.photos/100/100?random=3',
		status: '+50',
		reward: 50,
		level: '普通会员',
		category: 'active'
	},
	{
		id: 4,
		name: '张三',
		avatar: 'https://picsum.photos/100/100?random=4',
		status: '+150',
		reward: 150,
		level: '白金会员',
		category: 'premium'
	},
	{
		id: 5,
		name: '李四',
		avatar: 'https://picsum.photos/100/100?random=5',
		status: '+80',
		reward: 80,
		level: '银牌会员',
		category: 'active'
	},
	{
		id: 6,
		name: '王五',
		avatar: 'https://picsum.photos/100/100?random=6',
		status: '+120',
		reward: 120,
		level: '黄金会员',
		category: 'new'
	},
	{
		id: 7,
		name: '赵六',
		avatar: 'https://picsum.photos/100/100?random=7',
		status: '+300',
		reward: 300,
		level: '钻石会员',
		category: 'premium'
	},
	{
		id: 8,
		name: '孙七',
		avatar: 'https://picsum.photos/100/100?random=8',
		status: '+60',
		reward: 60,
		level: '普通会员',
		category: 'active'
	}
])

// z-paging相关数据
const friendList = ref([])
const withdrawList = ref([])
const friendPaging = ref(null)
const withdrawPaging = ref(null)

// 提现表单
const withdrawForm = reactive({
	amount: ''
})

// 弹窗引用
const withdrawPopup = ref(null)

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor
}



// 是否可以提交提现
const canSubmitWithdraw = computed(() => {
	const amount = parseFloat(withdrawForm.amount)
	return amount > 0 && amount <= inviteStats.pendingEarnings
})

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab
	// 切换到提现记录tab时，触发数据加载
	if (tab === 'withdraw') {
		setTimeout(() => {
			if (withdrawPaging.value) {
				withdrawPaging.value.reload()
			}
		}, 100)
	}
}

// 选择朋友分类
const selectCategory = (category) => {
	selectedCategory.value = category
	// 重新加载朋友列表数据
	if (friendPaging.value) {
		friendPaging.value.reload()
	}
}

// 邀请更多朋友
const inviteMore = () => {
	// TODO: 实现邀请功能
	toast('邀请功能开发中')
}

// 显示提现弹窗
const showWithdrawModal = () => {
	if (inviteStats.pendingEarnings <= 0) {
		toast('暂无可提现金额')
		//return
	}
	withdrawPopup.value.open()
}

// 关闭提现弹窗
const closeWithdrawModal = () => {
	withdrawPopup.value.close()
	withdrawForm.amount = ''
}

// 提交提现申请
const submitWithdraw = () => {
	if (!canSubmitWithdraw.value) {
		toast('请输入正确的提现金额')
		return
	}

	// TODO: 调用提现API
	toast('提现申请已提交')
	closeWithdrawModal()
}

// 查询朋友列表
const queryFriendList = async (pageNo, pageSize) => {
	try {
		console.log('=== 查询朋友列表开始 ===')
		console.log('pageNo:', pageNo, 'pageSize:', pageSize, '分类:', selectedCategory.value)

		// 根据分类过滤朋友数据
		let filteredData = friends.value
		if (selectedCategory.value !== 'all') {
			filteredData = friends.value.filter(friend => friend.category === selectedCategory.value)
		}

		console.log('原始朋友数据:', friends.value)
		console.log('过滤后的数据:', filteredData)

		// 模拟分页
		const startIndex = (pageNo - 1) * pageSize
		const endIndex = startIndex + pageSize
		const pageData = filteredData.slice(startIndex, endIndex)

		console.log('分页数据:', pageData)
		console.log('=== 查询朋友列表结束 ===')

		// 完成分页请求
		friendPaging.value.complete(pageData)
	} catch (error) {
		console.error('查询朋友列表出错:', error)
		// 请求失败
		friendPaging.value.complete(false)
	}
}

// 查询提现记录列表
const queryWithdrawList = async (pageNo, pageSize) => {
	try {
		console.log('=== 查询提现记录开始 ===')
		console.log('pageNo:', pageNo, 'pageSize:', pageSize)

		// 模拟提现记录数据
		const mockWithdrawData = [
			{
				id: 1,
				amount: 100,
				time: '2024-01-15 14:30',
				status: 'success'
			},
			{
				id: 2,
				amount: 50,
				time: '2024-01-14 10:20',
				status: 'pending'
			},
			{
				id: 3,
				amount: 200,
				time: '2024-01-13 16:45',
				status: 'failed'
			},
			{
				id: 4,
				amount: 150,
				time: '2024-01-12 09:15',
				status: 'success'
			},
			{
				id: 5,
				amount: 80,
				time: '2024-01-11 18:30',
				status: 'pending'
			}
		]

		console.log('提现记录总数据:', mockWithdrawData)

		// 模拟分页
		const startIndex = (pageNo - 1) * pageSize
		const endIndex = startIndex + pageSize
		const pageData = mockWithdrawData.slice(startIndex, endIndex)

		console.log('提现记录分页数据:', pageData)
		console.log('=== 查询提现记录结束 ===')

		// 完成分页请求
		withdrawPaging.value.complete(pageData)
	} catch (error) {
		console.error('查询提现记录出错:', error)
		// 请求失败
		withdrawPaging.value.complete(false)
	}
}

// 获取状态文字
const getStatusText = (status) => {
	const statusMap = {
		pending: '处理中',
		success: '已到账',
		failed: '失败'
	}
	return statusMap[status] || '未知'
}

onLoad(() => {
	// 初始化数据
	console.log('助力计划页面加载')
	console.log('朋友数据:', friends.value)
	console.log('当前选中分类:', selectedCategory.value)

	// 确保z-paging组件能正确初始化
	setTimeout(() => {
		console.log('friendPaging ref:', friendPaging.value)
		console.log('withdrawPaging ref:', withdrawPaging.value)
	}, 1000)
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.main-container {
	min-height: calc(100vh - var(--nav-height, 88px));
	height: calc(100vh - var(--nav-height, 88px));
	box-sizing: border-box;
	padding: 0 20rpx 120rpx 20rpx;
	display: flex;
	flex-direction: column;
}

// 邀请统计卡片
.invite-stats-card {
	background: linear-gradient(135deg, rgba($primary-color, 0.08), rgba($primary-color, 0.15));
	border-radius: 16rpx;
	padding: 20rpx 24rpx;
	margin: 16rpx 0 20rpx;
	border: 1rpx solid rgba($primary-color, 0.2);
	flex-shrink: 0; // 不允许压缩

	.stats-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;

		.invite-count {
			font-size: 30rpx;
			font-weight: 600;
			color: #333;
		}

		.header-actions {
			display: flex;
			justify-content: flex-end;

			.invite-more-btn {
				background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
				color: #fff;
				border: none;
				border-radius: 20rpx;
				padding: 10rpx 20rpx;
				font-size: 24rpx;
				font-weight: 600;
			}
		}
	}

	.stats-content {
		.stat-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: flex-start;

				.stat-label {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 4rpx;
				}

				.stat-value {
					font-size: 26rpx;
					font-weight: 600;
					color: #333;
				}
			}

			.stat-actions {
				display: flex;
				justify-content: flex-end;

				.withdraw-btn {
					background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
					color: #fff;
					border: none;
					border-radius: 18rpx;
					padding: 8rpx 18rpx;
					font-size: 24rpx;
					font-weight: 600;

					&:disabled {
						background: #ccc;
					}
				}
			}
		}
	}
}

// 标签页
.tab-section {
	margin-bottom: 24rpx;
	flex-shrink: 0; // 不允许压缩

	.tab-nav {
		display: flex;
		background: #fff;
		border-radius: 16rpx;
		padding: 8rpx;
		box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 16rpx 0;
			border-radius: 12rpx;
			transition: all 0.3s ease;

			text {
				font-size: 28rpx;
				color: #666;
				font-weight: 500;
			}

			&.active {
				background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));

				text {
					color: #fff;
					font-weight: 600;
				}
			}
		}
	}
}

// 朋友信息部分
.friend-info-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);

	.section-title {
		margin-bottom: 20rpx;

		text {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.friend-tabs {
		display: flex;
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 6rpx;
		gap: 4rpx;

		.friend-tab-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 16rpx 8rpx;
			border-radius: 8rpx;
			background: transparent;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			cursor: pointer;
			position: relative;

			&.active {
				background: #fff;
				transform: scale(1.05);
				box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.15);

				.tab-content {
					.tab-name {
						color: $primary-color;
						font-weight: 600;
					}

					.tab-count {
						color: $primary-color;
						font-weight: 600;
					}
				}
			}

			.tab-icon {
				margin-bottom: 8rpx;
				transition: transform 0.3s ease;
			}

			.tab-content {
				display: flex;
				flex-direction: column;
				align-items: center;

				.tab-name {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 4rpx;
					transition: all 0.3s ease;
					white-space: nowrap;
				}

				.tab-count {
					font-size: 20rpx;
					color: #999;
					transition: all 0.3s ease;
				}
			}

			&:active {
				transform: scale(0.98);
			}
		}
	}
}

// 朋友列表
.friend-list {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);

	.friend-item {
		display: flex;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid rgba($primary-color, 0.08);

		&:last-child {
			border-bottom: none;
		}

		.friend-avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 40rpx;
			overflow: hidden;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.friend-info {
			flex: 1;

			.friend-name {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				display: block;
				margin-bottom: 8rpx;
			}

			.friend-status {
				font-size: 24rpx;
				color: #666;
			}
		}

		.friend-level {
			.level-name {
				font-size: 26rpx;
				font-weight: 500;
				color: $primary-color;
				background: rgba($primary-color, 0.1);
				padding: 6rpx 12rpx;
				border-radius: 12rpx;
				border: 1rpx solid rgba($primary-color, 0.2);
			}
		}
	}
}

// 邀请信息内容
.invite-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	min-height: 0; // 允许flex子项收缩
}

// 提现记录内容
.withdraw-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	min-height: 0; // 允许flex子项收缩

	.withdraw-list {
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);

		.withdraw-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx;
			border-bottom: 1rpx solid rgba($primary-color, 0.08);

			&:last-child {
				border-bottom: none;
			}

			.withdraw-info {
				.withdraw-amount {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}

				.withdraw-time {
					font-size: 24rpx;
					color: #666;
				}
			}

			.withdraw-status {
				padding: 8rpx 16rpx;
				border-radius: 16rpx;
				font-size: 24rpx;
				font-weight: 500;

				&.pending {
					background: rgba(#E6A23C, 0.1);
					color: #E6A23C;
				}

				&.success {
					background: rgba(#67C23A, 0.1);
					color: #67C23A;
				}

				&.failed {
					background: rgba(#F56C6C, 0.1);
					color: #F56C6C;
				}
			}
		}
	}
}

// 提现弹窗
.withdraw-modal {
	width: 600rpx;
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.modal-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.modal-content {
		padding: 24rpx;

		.amount-info {
			margin-bottom: 24rpx;

			.available-amount {
				font-size: 28rpx;
				color: #666;
			}
		}

		.withdraw-form {
			.form-item {
				margin-bottom: 20rpx;

				.form-label {
					font-size: 28rpx;
					color: #333;
					display: block;
					margin-bottom: 12rpx;
				}

				.form-input {
					width: 100%;
					height: 80rpx;
					border: 2rpx solid #e0e0e0;
					border-radius: 12rpx;
					padding: 0 20rpx;
					font-size: 28rpx;
					box-sizing: border-box;

					&:focus {
						border-color: $primary-color;
					}
				}
			}
		}
	}

	.modal-footer {
		display: flex;
		gap: 16rpx;
		padding: 24rpx;
		border-top: 1rpx solid #f0f0f0;

		.cancel-btn,
		.confirm-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 12rpx;
			font-size: 28rpx;
			font-weight: 600;
			border: none;
		}

		.cancel-btn {
			background: #f5f5f5;
			color: #666;
		}

		.confirm-btn {
			background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
			color: #fff;

			&:disabled {
				background: #ccc;
			}
		}
	}
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 40rpx;

	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 24rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		text-align: center;
	}
}

// z-paging组件样式
:deep(.z-paging) {
	flex: 1;
	height: 100%;
	min-height: 400rpx;
}

:deep(.z-paging-content) {
	height: 100%;
	min-height: 400rpx;
}

// 确保z-paging容器有足够高度
.invite-content z-paging,
.withdraw-content z-paging {
	flex: 1;
	height: 100%;
	min-height: 400rpx;
}
</style>
