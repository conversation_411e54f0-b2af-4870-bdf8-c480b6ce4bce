{"version": 3, "file": "activity.js", "sources": ["pages/activity/activity.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWN0aXZpdHkvYWN0aXZpdHkudnVl"], "sourcesContent": ["<template>\n\t<!-- 自定义导航栏 -->\n\t<scroll-nav-page :enableScrollGradient=\"false\" @scroll=\"handlePageScroll\" @heightChange=\"handleNavHeightChange\">\n\t\t<template #nav-left>\n\t\t\t<nav-tabs v-model=\"currentTab\" :tabs=\"activityTabs\" :text-color=\"getTabTextColor()\"\n\t\t\t\t@change=\"handleTabChange\" />\n\t\t</template>\n\t\t<template #content>\n\t\t\t<!-- 官方活动列表 -->\n\t\t\t<OfficialList v-if=\"currentTab === 'official'\" :nav-bar-height=\"navBarHeight\" />\n\n\t\t\t<!-- 找搭子列表 -->\n\t\t\t<BuddyList v-else :nav-bar-height=\"navBarHeight\" />\n\n\t\t\t<!-- 发布按钮 -->\n\t\t\t<view class=\"publish-btn\" :class=\"{ 'loading': isPublishing }\" @click=\"goToPublish\">\n\t\t\t\t<uni-icons v-if=\"isPublishing\" type=\"spinner-cycle\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t\t<uni-icons v-else type=\"plus\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { onPageScroll, onShow } from '@dcloudio/uni-app'\nimport BuddyList from './components/buddy-list.vue'\nimport OfficialList from './components/official-list.vue'\nimport { isIdentityVerified } from '@/api/my/my'\n\n// 页面滚动距离\nconst pageScrollTop = ref(0)\n// 导航栏高度\nconst navBarHeight = ref(0)\n\n// 当前选中的标签页\nconst currentTab = ref('buddy')\n// 发布按钮加载状态\nconst isPublishing = ref(false)\n// 活动页面标签数据\nconst activityTabs = ref([\n\t{ label: '找搭子', value: 'buddy' },\n\t// { label: '官方活动', value: 'official' }\n])\n\n// 处理页面滚动\nconst handlePageScroll = (e) => {\n\tpageScrollTop.value = e.scrollTop\n\tconsole.log('页面滚动监听触发:', e.scrollTop)\n}\n\n// 处理导航栏高度变化\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 移除了 handleNavScroll 函数，使用默认配置\n\n// 计算标签文字颜色\nconst getTabTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\n}\n\n\n\n// 页面滚动监听\nonPageScroll(handlePageScroll)\n\n// 页面加载时获取位置\nonMounted(() => {\n\tconsole.log('Activity页面已挂载，滚动监听已注册')\n\tconsole.log('初始滚动距离:', pageScrollTop.value)\n})\n\n// 页面显示时重新加载数据（tabBar重复点击时会触发）\nonShow(() => {\n\tconsole.log('活动页面显示，重新加载数据')\n\t// 由于数据加载逻辑在子组件中，这里可以通过事件或其他方式通知子组件刷新\n\t// 子组件应该监听页面显示事件或提供刷新方法\n})\n\n// 切换标签页处理\nconst handleTabChange = (tab) => {\n\tcurrentTab.value = tab\n}\n\n\n\n// 跳转到发布页\nconst goToPublish = async () => {\n\tif (isPublishing.value) return\n\n\tisPublishing.value = true\n\ttry {\n\t\t// 检查用户是否已认证\n\t\tconst response = await isIdentityVerified()\n\n\t\tif (response.code === 200) {\n\t\t\tif (response.data) {\n\t\t\t\t// 已认证，正常跳转\n\t\t\t\tconst url = currentTab.value === 'official'\n\t\t\t\t\t? '/pages/activity/publish/publish'\n\t\t\t\t\t: '/pagesubs/activity/buddy/add'\n\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\t// 未认证，显示提示并跳转到认证页面\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '需要实名认证',\n\t\t\t\t\tcontent: '发布活动需要先完成实名认证，是否前往认证？',\n\t\t\t\t\tconfirmText: '去认证',\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: '/pagesubs/my/auth/auth'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t} else {\n\t\t\tconsole.error('查询认证状态失败:', response.msg)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: response.msg || '查询认证状态失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('查询认证状态异常:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '网络异常，请重试',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tisPublishing.value = false\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// nav-tabs 样式已移至独立组件中\n// 容器样式已移至各自组件中\n\n.publish-btn {\n\tposition: fixed;\n\tright: 30rpx;\n\tbottom: 30rpx;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: linear-gradient(to right, #696CF3, #9B9DF5);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);\n\ttransition: all 0.3s ease;\n\n\t&.loading {\n\t\topacity: 0.7;\n\t\ttransform: scale(0.95);\n\t}\n\n\t&:active {\n\t\ttransform: scale(0.9);\n\t}\n}\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pages/activity/activity.vue'\nwx.createPage(MiniProgramPage)"], "names": ["BuddyList", "OfficialList", "pageScrollTop", "ref", "navBarHeight", "currentTab", "isPublishing", "activityTabs", "handlePageScroll", "e", "uni", "handleNavHeightChange", "height", "getTabTextColor", "onPageScroll", "onMounted", "onShow", "handleTabChange", "tab", "goToPublish", "response", "isIdentityVerified", "url", "res", "error", "MiniProgramPage"], "mappings": "uaAyBA,MAAAA,EAAA,IAAA,6BACAC,EAAA,IAAA,8DAIA,MAAAC,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,OAAA,EAEAG,EAAAH,EAAA,IAAA,EAAA,EAEAI,EAAAJ,EAAAA,IAAA,CACA,CAAA,MAAA,MAAA,MAAA,OAAA,CAEA,CAAA,EAGAK,EAAAC,GAAA,CACAP,EAAA,MAAAO,EAAA,UACAC,EAAA,MAAA,MAAA,MAAA,oCAAA,YAAAD,EAAA,SAAA,CACA,EAGAE,EAAAC,GAAA,CACAR,EAAA,MAAAQ,CACA,EAKAC,EAAA,IACA,KAAA,IAAAX,EAAA,MAAA,IAAA,CAAA,EACA,GAAA,UAAA,UAMAY,EAAA,aAAAN,CAAA,EAGAO,EAAAA,UAAA,IAAA,CACAL,EAAAA,MAAA,MAAA,MAAA,oCAAA,uBAAA,EACAA,EAAA,MAAA,MAAA,MAAA,oCAAA,UAAAR,EAAA,KAAA,CACA,CAAA,EAGAc,EAAAA,OAAA,IAAA,CACAN,EAAAA,MAAA,MAAA,MAAA,oCAAA,eAAA,CAGA,CAAA,EAGA,MAAAO,EAAAC,GAAA,CACAb,EAAA,MAAAa,CACA,EAKAC,EAAA,SAAA,CACA,GAAA,CAAAb,EAAA,MAEA,CAAAA,EAAA,MAAA,GACA,GAAA,CAEA,MAAAc,EAAA,MAAAC,qBAAA,EAEA,GAAAD,EAAA,OAAA,IACA,GAAAA,EAAA,KAAA,CAEA,MAAAE,EAAAjB,EAAA,QAAA,WACA,kCACA,+BAEAK,EAAAA,MAAA,WAAA,CACA,IAAAY,CACA,CAAA,CACA,MAEAZ,EAAAA,MAAA,UAAA,CACA,MAAA,SACA,QAAA,wBACA,YAAA,MACA,WAAA,KACA,QAAAa,GAAA,CACAA,EAAA,SACAb,EAAAA,MAAA,WAAA,CACA,IAAA,wBACA,CAAA,CAEA,CACA,CAAA,OAGAA,EAAA,MAAA,MAAA,QAAA,qCAAA,YAAAU,EAAA,GAAA,EACAV,EAAAA,MAAA,UAAA,CACA,MAAAU,EAAA,KAAA,WACA,KAAA,MACA,CAAA,CAEA,OAAAI,EAAA,CACAd,EAAAA,MAAA,MAAA,QAAA,qCAAA,YAAAc,CAAA,EACAd,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,CACA,QAAA,CACAJ,EAAA,MAAA,EACA,EACA,ifC1IA,GAAG,WAAWmB,CAAe"}