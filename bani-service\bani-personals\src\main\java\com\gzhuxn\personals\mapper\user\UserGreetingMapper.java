package com.gzhuxn.personals.mapper.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gzhuxn.common.mybatis.core.mapper.BaseMapperPlus;
import com.gzhuxn.personals.domain.user.UserGreeting;
import com.gzhuxn.personals.domain.user.vo.UserGreetingVo;

/**
 * 用户-打招呼Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface UserGreetingMapper extends BaseMapperPlus<UserGreeting, UserGreetingVo> {

    /**
     * 查询用户是否已经向对方打过招呼
     *
     * @param userId         当前用户ID
     * @param oppositeUserId 对方用户ID
     * @return 打招呼记录
     */
    default UserGreeting getByUserIdAndOppositeUserId(Long userId, Long oppositeUserId) {
        return selectOne(Wrappers.<UserGreeting>lambdaQuery()
            .eq(UserGreeting::getUserId, userId)
            .eq(UserGreeting::getOppositeUserId, oppositeUserId)
            .last("LIMIT 1"));
    }

    /**
     * 查询收到的打招呼列表
     *
     * @param userId 当前用户ID
     * @return 打招呼列表
     */
    default LambdaQueryWrapper<UserGreeting> buildReceivedGreetingQuery(Long userId) {
        return Wrappers.<UserGreeting>lambdaQuery()
            .eq(UserGreeting::getOppositeUserId, userId)
            .orderByDesc(UserGreeting::getCreateTime);
    }

    /**
     * 查询发送的打招呼列表
     *
     * @param userId 当前用户ID
     * @return 打招呼列表
     */
    default LambdaQueryWrapper<UserGreeting> buildSentGreetingQuery(Long userId) {
        return Wrappers.<UserGreeting>lambdaQuery()
            .eq(UserGreeting::getUserId, userId)
            .orderByDesc(UserGreeting::getCreateTime);
    }
}
