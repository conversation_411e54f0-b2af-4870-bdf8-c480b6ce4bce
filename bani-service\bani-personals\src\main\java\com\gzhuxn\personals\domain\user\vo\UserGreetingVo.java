package com.gzhuxn.personals.domain.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.gzhuxn.personals.domain.user.UserGreeting;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户-打招呼视图对象 user_greeting
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserGreeting.class, convertGenerate = false)
public class UserGreetingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 对方用户ID
     */
    @ExcelProperty(value = "对方用户ID")
    private Long oppositeUserId;

    /**
     * 打招呼内容
     */
    @ExcelProperty(value = "打招呼内容")
    private String content;

    /**
     * 状态：0-待回复，1-已回复，2-已忽略
     */
    @ExcelProperty(value = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
