2025-08-12 00:00:39 [boundedElastic-792] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1zjp1w3Wrfs53D8D0w33QDWs1zjp1o","grantType":"mini","tenantId":"1"}]
2025-08-12 00:00:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1728]毫秒
2025-08-12 00:00:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 00:00:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:00:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[177]毫秒
2025-08-12 00:00:41 [boundedElastic-792] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:00:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:00:41 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:00:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:00:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:00:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[85]毫秒
2025-08-12 00:00:41 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[131]毫秒
2025-08-12 00:00:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[133]毫秒
2025-08-12 00:00:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[801]毫秒
2025-08-12 00:00:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[381]毫秒
2025-08-12 00:00:42 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[526]毫秒
2025-08-12 00:00:42 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:00:42 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:00:42 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[242]毫秒
2025-08-12 00:00:42 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[390]毫秒
2025-08-12 00:01:18 [boundedElastic-787] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:01:19 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[603]毫秒
2025-08-12 00:01:21 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:01:21 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[742]毫秒
2025-08-12 00:01:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:01:25 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-12 00:01:25 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[421]毫秒
2025-08-12 00:01:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[736]毫秒
2025-08-12 00:01:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 00:01:29 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 00:01:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[107]毫秒
2025-08-12 00:01:30 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[437]毫秒
2025-08-12 00:01:44 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 00:01:44 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[138]毫秒
2025-08-12 00:01:58 [boundedElastic-796] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:01:58 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:01:58 [boundedElastic-790] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:01:58 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:01:58 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:01:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[121]毫秒
2025-08-12 00:01:59 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[256]毫秒
2025-08-12 00:01:59 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[256]毫秒
2025-08-12 00:01:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[503]毫秒
2025-08-12 00:01:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:01:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[338]毫秒
2025-08-12 00:01:59 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[973]毫秒
2025-08-12 00:02:00 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:02:00 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[186]毫秒
2025-08-12 00:02:05 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:02:06 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[489]毫秒
2025-08-12 00:02:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:02:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[435]毫秒
2025-08-12 00:02:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:02:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[138]毫秒
2025-08-12 00:02:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:02:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:02:21 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[518]毫秒
2025-08-12 00:02:21 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[151]毫秒
2025-08-12 00:02:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:02:21 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[283]毫秒
2025-08-12 00:02:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:02:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[321]毫秒
2025-08-12 00:02:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-12 00:02:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[327]毫秒
2025-08-12 00:02:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:02:26 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[390]毫秒
2025-08-12 00:02:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:31 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[475]毫秒
2025-08-12 00:02:33 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:33 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[315]毫秒
2025-08-12 00:02:34 [boundedElastic-787] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[308]毫秒
2025-08-12 00:02:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:36 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[315]毫秒
2025-08-12 00:02:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:38 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[468]毫秒
2025-08-12 00:02:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:41 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[328]毫秒
2025-08-12 00:02:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:43 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[665]毫秒
2025-08-12 00:02:44 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[315]毫秒
2025-08-12 00:02:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:02:46 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[313]毫秒
2025-08-12 00:03:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 00:03:02 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[237]毫秒
2025-08-12 00:03:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 00:03:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 00:03:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[241]毫秒
2025-08-12 00:03:04 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[518]毫秒
2025-08-12 00:03:52 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:03:52 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[308]毫秒
2025-08-12 00:04:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:04:16 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[241]毫秒
2025-08-12 00:04:36 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:04:37 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[305]毫秒
2025-08-12 00:04:51 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:04:51 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[218]毫秒
2025-08-12 00:04:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:04:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[216]毫秒
2025-08-12 00:05:06 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:05:06 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[221]毫秒
2025-08-12 00:05:42 [boundedElastic-769] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:05:42 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[924]毫秒
2025-08-12 00:05:43 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:05:43 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:05:43 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[414]毫秒
2025-08-12 00:05:43 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[538]毫秒
2025-08-12 00:05:46 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 00:05:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 00:05:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[92]毫秒
2025-08-12 00:05:46 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[362]毫秒
2025-08-12 00:05:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:05:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:05:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:05:59 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:05:59 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:05:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[122]毫秒
2025-08-12 00:05:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[203]毫秒
2025-08-12 00:05:59 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[208]毫秒
2025-08-12 00:05:59 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[498]毫秒
2025-08-12 00:06:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:06:00 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[904]毫秒
2025-08-12 00:06:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[329]毫秒
2025-08-12 00:06:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:06:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[178]毫秒
2025-08-12 00:06:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:06:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[384]毫秒
2025-08-12 00:06:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:06:05 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[220]毫秒
2025-08-12 00:06:14 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:06:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[610]毫秒
2025-08-12 00:06:17 [boundedElastic-787] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:06:17 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[140]毫秒
2025-08-12 00:06:22 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:06:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:06:23 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[215]毫秒
2025-08-12 00:06:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[429]毫秒
2025-08-12 00:06:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:06:52 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:06:52 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:06:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:06:52 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:06:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[110]毫秒
2025-08-12 00:06:52 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[271]毫秒
2025-08-12 00:06:52 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[271]毫秒
2025-08-12 00:06:52 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[540]毫秒
2025-08-12 00:06:52 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:06:53 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[982]毫秒
2025-08-12 00:06:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[337]毫秒
2025-08-12 00:06:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:06:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[182]毫秒
2025-08-12 00:06:56 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:06:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[403]毫秒
2025-08-12 00:06:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:06:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[223]毫秒
2025-08-12 00:07:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:07:26 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:07:26 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:07:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:07:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:07:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[97]毫秒
2025-08-12 00:07:26 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[260]毫秒
2025-08-12 00:07:26 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[260]毫秒
2025-08-12 00:07:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:07:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[650]毫秒
2025-08-12 00:07:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[971]毫秒
2025-08-12 00:07:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[365]毫秒
2025-08-12 00:07:28 [boundedElastic-794] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:07:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[184]毫秒
2025-08-12 00:07:35 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:07:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[526]毫秒
2025-08-12 00:07:36 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:07:36 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[130]毫秒
2025-08-12 00:07:43 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:07:44 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[491]毫秒
2025-08-12 00:07:46 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:07:46 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[138]毫秒
2025-08-12 00:08:01 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:08:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[294]毫秒
2025-08-12 00:08:09 [boundedElastic-798] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-12 00:08:09 [boundedElastic-797] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 00:08:09 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 00:08:09 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 00:08:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[125]毫秒
2025-08-12 00:08:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[128]毫秒
2025-08-12 00:08:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[134]毫秒
2025-08-12 00:08:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[220]毫秒
2025-08-12 00:08:16 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:16 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[296]毫秒
2025-08-12 00:08:17 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:17 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[146]毫秒
2025-08-12 00:08:19 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:19 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[186]毫秒
2025-08-12 00:08:21 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[150]毫秒
2025-08-12 00:08:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/signIn/create],参数类型[json],参数:[null]
2025-08-12 00:08:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/signIn/create],耗时:[1290]毫秒
2025-08-12 00:08:27 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 00:08:27 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 00:08:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[53]毫秒
2025-08-12 00:08:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[142]毫秒
2025-08-12 00:08:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[155]毫秒
2025-08-12 00:08:31 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 00:08:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[190]毫秒
2025-08-12 00:08:36 [boundedElastic-792] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/help/key/coinIntroduction],参数类型[json],参数:[null]
2025-08-12 00:08:36 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/help/key/coinIntroduction],耗时:[195]毫秒
2025-08-12 00:08:47 [boundedElastic-792] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:08:48 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[300]毫秒
2025-08-12 00:08:54 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 00:08:55 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[307]毫秒
2025-08-12 00:08:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 00:08:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[172]毫秒
2025-08-12 00:08:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 00:08:56 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[218]毫秒
2025-08-12 00:09:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:09:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[232]毫秒
2025-08-12 00:09:19 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:09:19 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[909]毫秒
2025-08-12 00:09:20 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:09:20 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[550]毫秒
2025-08-12 00:09:22 [boundedElastic-794] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 00:09:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 00:09:23 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[100]毫秒
2025-08-12 00:09:23 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[357]毫秒
2025-08-12 00:11:33 [boundedElastic-799] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:11:33 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:11:33 [boundedElastic-796] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:11:33 [boundedElastic-787] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:11:33 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:11:33 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[154]毫秒
2025-08-12 00:11:33 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[269]毫秒
2025-08-12 00:11:33 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[269]毫秒
2025-08-12 00:12:25 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c11WR0w3HtDq53RxM0w3dNzt131WR0e","grantType":"mini","tenantId":"1"}]
2025-08-12 00:12:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1749]毫秒
2025-08-12 00:12:27 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 00:12:27 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:12:28 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[194]毫秒
2025-08-12 00:12:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[766]毫秒
2025-08-12 00:12:32 [boundedElastic-795] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:12:32 [boundedElastic-799] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:12:32 [boundedElastic-793] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:12:32 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:12:32 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:12:32 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[50]毫秒
2025-08-12 00:12:32 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[213]毫秒
2025-08-12 00:12:32 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[94]毫秒
2025-08-12 00:12:32 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[438]毫秒
2025-08-12 00:12:32 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:12:32 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:12:32 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[636]毫秒
2025-08-12 00:12:33 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[201]毫秒
2025-08-12 00:12:33 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[358]毫秒
2025-08-12 00:12:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:12:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[387]毫秒
2025-08-12 00:12:37 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:12:37 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[213]毫秒
2025-08-12 00:14:13 [boundedElastic-794] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0e1MZA0w3xmnq53ZY84w3CxvRI1MZA0t","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1589]毫秒
2025-08-12 00:14:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:15 [boundedElastic-794] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:14:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:14:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:14:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:14:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0d1SWj0w3Jvcr53A5t3w3M4Qpo1SWj0R","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:15 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[172]毫秒
2025-08-12 00:14:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[177]毫秒
2025-08-12 00:14:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[132]毫秒
2025-08-12 00:14:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:14:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[48]毫秒
2025-08-12 00:14:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[506]毫秒
2025-08-12 00:14:16 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:14:16 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0d1l07000Z9CLU1rSS200b68Yx2l070y","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[886]毫秒
2025-08-12 00:14:16 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[337]毫秒
2025-08-12 00:14:16 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1353]毫秒
2025-08-12 00:14:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1VT20w3ssVq536LV0w3T7j7p0VT20-","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[60]毫秒
2025-08-12 00:14:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0d1q070001aCLU1PDE300GZSg61q070q","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:17 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1316]毫秒
2025-08-12 00:14:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:17 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[57]毫秒
2025-08-12 00:14:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0f1ZT20w3vsVq53O381w3la6or0ZT20r","grantType":"mini","tenantId":"1"}]
2025-08-12 00:14:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1453]毫秒
2025-08-12 00:14:18 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[91]毫秒
2025-08-12 00:14:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:14:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1369]毫秒
2025-08-12 00:14:18 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[348]毫秒
2025-08-12 00:14:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[52]毫秒
2025-08-12 00:14:19 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1441]毫秒
2025-08-12 00:14:19 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 00:14:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[94]毫秒
2025-08-12 00:14:20 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:14:20 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[421]毫秒
2025-08-12 00:14:23 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:14:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[835]毫秒
2025-08-12 00:14:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 00:14:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62105658637152,"lat":26.602868923611112}]
2025-08-12 00:14:26 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[421]毫秒
2025-08-12 00:14:26 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[654]毫秒
2025-08-12 00:14:28 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 00:14:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 00:14:28 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[93]毫秒
2025-08-12 00:14:28 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[378]毫秒
2025-08-12 00:14:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 00:14:41 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 00:14:42 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[350]毫秒
2025-08-12 00:14:42 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[541]毫秒
2025-08-12 00:14:47 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 00:14:48 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[697]毫秒
2025-08-12 00:14:51 [boundedElastic-801] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 00:14:51 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[446]毫秒
2025-08-12 00:14:53 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 00:14:53 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[208]毫秒
2025-08-12 00:14:55 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:14:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[178]毫秒
2025-08-12 00:14:59 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:15:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[369]毫秒
2025-08-12 00:15:01 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:15:01 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[336]毫秒
2025-08-12 00:15:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-12 00:15:04 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[299]毫秒
2025-08-12 00:15:08 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 00:15:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[375]毫秒
2025-08-12 00:15:10 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:15:10 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[132]毫秒
2025-08-12 00:15:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 00:15:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[304]毫秒
2025-08-12 00:15:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 00:15:13 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[308]毫秒
2025-08-12 00:15:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 00:15:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[378]毫秒
2025-08-12 00:15:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 00:15:17 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[294]毫秒
2025-08-12 00:15:23 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 00:15:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[464]毫秒
2025-08-12 00:15:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:15:26 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[137]毫秒
2025-08-12 00:15:27 [boundedElastic-803] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:15:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[361]毫秒
2025-08-12 00:15:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:15:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[384]毫秒
2025-08-12 00:15:30 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:15:30 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[306]毫秒
2025-08-12 00:17:24 [boundedElastic-787] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:17:24 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[478]毫秒
2025-08-12 00:17:26 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:17:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[348]毫秒
2025-08-12 00:17:32 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:17:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[380]毫秒
2025-08-12 00:17:33 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:17:33 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[301]毫秒
2025-08-12 00:17:33 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 00:17:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[221]毫秒
2025-08-12 00:17:34 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:17:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[99]毫秒
2025-08-12 00:17:36 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:17:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[298]毫秒
2025-08-12 00:17:38 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:17:38 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[372]毫秒
2025-08-12 00:17:40 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:17:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[313]毫秒
2025-08-12 00:17:44 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 00:17:44 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[391]毫秒
2025-08-12 00:17:45 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 00:17:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[133]毫秒
2025-08-12 00:17:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:17:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[421]毫秒
2025-08-12 00:17:47 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:17:47 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:17:47 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:17:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:17:47 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:17:47 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-12 00:17:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[143]毫秒
2025-08-12 00:17:47 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[144]毫秒
2025-08-12 00:17:47 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[478]毫秒
2025-08-12 00:17:47 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:17:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[833]毫秒
2025-08-12 00:17:48 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[342]毫秒
2025-08-12 00:17:48 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:17:48 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:17:48 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[181]毫秒
2025-08-12 00:17:48 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[333]毫秒
2025-08-12 00:17:49 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:17:49 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[485]毫秒
2025-08-12 00:17:51 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:17:51 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[398]毫秒
2025-08-12 00:17:57 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:17:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 00:17:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 00:17:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 00:17:57 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 00:17:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 00:17:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[84]毫秒
2025-08-12 00:17:57 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[146]毫秒
2025-08-12 00:17:57 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[146]毫秒
2025-08-12 00:17:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[578]毫秒
2025-08-12 00:17:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 00:17:58 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[341]毫秒
2025-08-12 00:17:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 00:17:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[486]毫秒
2025-08-12 00:17:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[142]毫秒
2025-08-12 00:17:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[324]毫秒
2025-08-12 00:18:00 [boundedElastic-802] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/lit-list],参数类型[json],参数:[null]
2025-08-12 00:18:00 [boundedElastic-800] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/basic-info],参数类型[json],参数:[null]
2025-08-12 00:18:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/basic-info],耗时:[134]毫秒
2025-08-12 00:18:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/lit-list],耗时:[177]毫秒
2025-08-12 00:19:27 [boundedElastic-802] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/blacklist/create],参数类型[json],参数:[{"oppositeUserId":"1938886425951789058"}]
2025-08-12 00:19:28 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/blacklist/create],耗时:[485]毫秒
2025-08-12 00:19:39 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 00:19:39 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[127]毫秒
2025-08-12 00:19:47 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 00:19:47 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[617]毫秒
2025-08-12 06:50:55 [boundedElastic-983] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:50:56 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[838]毫秒
2025-08-12 06:50:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:50:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[519]毫秒
2025-08-12 06:50:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 06:50:59 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[160]毫秒
2025-08-12 06:51:01 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:01 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[524]毫秒
2025-08-12 06:51:02 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:03 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[454]毫秒
2025-08-12 06:51:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:05 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[446]毫秒
2025-08-12 06:51:08 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:09 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:09 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-12 06:51:09 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[401]毫秒
2025-08-12 06:51:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[1061]毫秒
2025-08-12 06:51:09 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[684]毫秒
2025-08-12 06:51:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:12 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[663]毫秒
2025-08-12 06:51:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:51:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:51:18 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[186]毫秒
2025-08-12 06:51:19 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[484]毫秒
2025-08-12 06:51:35 [boundedElastic-999] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:51:36 [boundedElastic-1000] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:51:36 [boundedElastic-1002] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:51:36 [boundedElastic-983] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:51:36 [boundedElastic-999] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:51:36 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[179]毫秒
2025-08-12 06:51:36 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[458]毫秒
2025-08-12 06:51:36 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[457]毫秒
2025-08-12 06:51:36 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[773]毫秒
2025-08-12 06:51:37 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[1110]毫秒
2025-08-12 06:51:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:51:37 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:51:37 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[361]毫秒
2025-08-12 06:51:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[571]毫秒
2025-08-12 06:51:40 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[463]毫秒
2025-08-12 06:51:43 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:44 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[771]毫秒
2025-08-12 06:51:44 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:51:45 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[558]毫秒
2025-08-12 06:51:48 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:51:48 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:51:48 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[157]毫秒
2025-08-12 06:51:48 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[457]毫秒
2025-08-12 06:53:57 [boundedElastic-1002] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:53:57 [boundedElastic-994] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:53:57 [boundedElastic-999] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:53:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:53:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:53:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[134]毫秒
2025-08-12 06:53:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[282]毫秒
2025-08-12 06:53:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[290]毫秒
2025-08-12 06:53:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[518]毫秒
2025-08-12 06:53:58 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:53:58 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[978]毫秒
2025-08-12 06:53:58 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[353]毫秒
2025-08-12 06:53:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:53:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[209]毫秒
2025-08-12 06:54:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[423]毫秒
2025-08-12 06:54:02 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[841]毫秒
2025-08-12 06:54:03 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[545]毫秒
2025-08-12 06:54:05 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:54:05 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:54:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[100]毫秒
2025-08-12 06:54:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[379]毫秒
2025-08-12 06:54:31 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:54:31 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:54:31 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:54:31 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:54:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:54:31 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[132]毫秒
2025-08-12 06:54:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[265]毫秒
2025-08-12 06:54:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[265]毫秒
2025-08-12 06:54:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[502]毫秒
2025-08-12 06:54:32 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:54:32 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[972]毫秒
2025-08-12 06:54:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[343]毫秒
2025-08-12 06:54:32 [boundedElastic-1000] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:54:33 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[192]毫秒
2025-08-12 06:54:37 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:38 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[505]毫秒
2025-08-12 06:54:39 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[720]毫秒
2025-08-12 06:54:40 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:54:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[544]毫秒
2025-08-12 06:54:42 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:54:42 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:54:42 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[102]毫秒
2025-08-12 06:54:42 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[377]毫秒
2025-08-12 06:55:02 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:55:02 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:55:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:55:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:55:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[646]毫秒
2025-08-12 06:55:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[102]毫秒
2025-08-12 06:55:03 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[777]毫秒
2025-08-12 06:55:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:55:03 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[332]毫秒
2025-08-12 06:55:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[539]毫秒
2025-08-12 06:55:09 [boundedElastic-983] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:55:09 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:55:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[95]毫秒
2025-08-12 06:55:10 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[440]毫秒
2025-08-12 06:55:28 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:55:28 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:55:28 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:55:28 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:55:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:55:28 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[129]毫秒
2025-08-12 06:55:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[215]毫秒
2025-08-12 06:55:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[216]毫秒
2025-08-12 06:55:29 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[506]毫秒
2025-08-12 06:55:29 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:55:29 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[905]毫秒
2025-08-12 06:55:29 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[339]毫秒
2025-08-12 06:55:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:55:29 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[199]毫秒
2025-08-12 06:55:32 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:55:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[406]毫秒
2025-08-12 06:56:00 [boundedElastic-1003] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:56:00 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:56:00 [boundedElastic-1002] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:56:00 [boundedElastic-984] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:56:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:56:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[97]毫秒
2025-08-12 06:56:00 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[283]毫秒
2025-08-12 06:56:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[283]毫秒
2025-08-12 06:56:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[520]毫秒
2025-08-12 06:56:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:56:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[969]毫秒
2025-08-12 06:56:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[337]毫秒
2025-08-12 06:56:01 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:56:01 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[185]毫秒
2025-08-12 06:56:09 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:56:10 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[485]毫秒
2025-08-12 06:56:37 [boundedElastic-1003] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0e1qAM000W27LU1qSa300Pfsna0qAM05","grantType":"mini","tenantId":"1"}]
2025-08-12 06:56:39 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[2124]毫秒
2025-08-12 06:56:39 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 06:56:39 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 06:56:39 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[185]毫秒
2025-08-12 06:56:39 [boundedElastic-983] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 06:56:39 [boundedElastic-1003] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 06:56:39 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 06:56:39 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 06:56:40 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 06:56:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[180]毫秒
2025-08-12 06:56:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[182]毫秒
2025-08-12 06:56:40 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[89]毫秒
2025-08-12 06:56:40 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[768]毫秒
2025-08-12 06:56:40 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[420]毫秒
2025-08-12 06:56:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[502]毫秒
2025-08-12 06:56:40 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 06:56:40 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 06:56:40 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[174]毫秒
2025-08-12 06:56:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[338]毫秒
2025-08-12 06:56:46 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 06:56:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[471]毫秒
2025-08-12 06:56:49 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:56:49 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[727]毫秒
2025-08-12 06:56:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 06:56:52 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-12 06:56:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[430]毫秒
2025-08-12 06:56:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[711]毫秒
2025-08-12 06:56:56 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:56:56 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:56:56 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[95]毫秒
2025-08-12 06:56:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[369]毫秒
2025-08-12 06:58:28 [boundedElastic-1002] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:58:28 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[141]毫秒
2025-08-12 06:59:53 [boundedElastic-1006] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 06:59:53 [boundedElastic-984] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 06:59:53 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[152]毫秒
2025-08-12 06:59:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[576]毫秒
2025-08-12 07:01:15 [boundedElastic-1005] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 07:01:15 [boundedElastic-1006] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 07:01:15 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[151]毫秒
2025-08-12 07:01:16 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[567]毫秒
2025-08-12 07:01:52 [boundedElastic-984] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:01:52 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:01:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[393]毫秒
2025-08-12 07:01:53 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[616]毫秒
2025-08-12 07:01:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:01:56 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[455]毫秒
2025-08-12 07:02:41 [boundedElastic-1005] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:02:41 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:02:41 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[255]毫秒
2025-08-12 07:02:42 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[496]毫秒
2025-08-12 07:04:08 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:04:09 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[615]毫秒
2025-08-12 07:04:11 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:04:11 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[409]毫秒
2025-08-12 07:05:01 [boundedElastic-1007] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0b1c6N000Iy5LU1try300BTP900c6N0B","grantType":"mini","tenantId":"1"}]
2025-08-12 07:05:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1695]毫秒
2025-08-12 07:05:03 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 07:05:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_revenue","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 07:05:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[196]毫秒
2025-08-12 07:05:03 [boundedElastic-1007] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:05:03 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:05:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:05:03 [boundedElastic-1007] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:05:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:05:03 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[179]毫秒
2025-08-12 07:05:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[178]毫秒
2025-08-12 07:05:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[50]毫秒
2025-08-12 07:05:04 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[783]毫秒
2025-08-12 07:05:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[420]毫秒
2025-08-12 07:05:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[508]毫秒
2025-08-12 07:05:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:05:04 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:05:04 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[189]毫秒
2025-08-12 07:05:04 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[331]毫秒
2025-08-12 07:05:06 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:05:06 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[449]毫秒
2025-08-12 07:05:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:05:09 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[177]毫秒
2025-08-12 07:05:12 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:05:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[778]毫秒
2025-08-12 07:05:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:05:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-12 07:05:15 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[422]毫秒
2025-08-12 07:05:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[693]毫秒
2025-08-12 07:05:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:05:18 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[680]毫秒
2025-08-12 07:05:20 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:05:20 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:05:20 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[193]毫秒
2025-08-12 07:05:20 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[491]毫秒
2025-08-12 07:05:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:05:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[511]毫秒
2025-08-12 07:06:44 [boundedElastic-1006] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:06:44 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[252]毫秒
2025-08-12 07:06:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:06:46 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[109]毫秒
2025-08-12 07:06:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:06:47 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[700]毫秒
2025-08-12 07:06:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:06:50 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[503]毫秒
2025-08-12 07:06:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:06:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[553]毫秒
2025-08-12 07:07:09 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:07:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[174]毫秒
2025-08-12 07:07:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:07:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[379]毫秒
2025-08-12 07:07:13 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:07:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[93]毫秒
2025-08-12 07:07:13 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:07:13 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[390]毫秒
2025-08-12 07:07:19 [boundedElastic-1008] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:07:19 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[566]毫秒
2025-08-12 07:10:55 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:10:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:10:55 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[278]毫秒
2025-08-12 07:10:56 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[440]毫秒
2025-08-12 07:11:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:11:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:11:05 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[170]毫秒
2025-08-12 07:11:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[381]毫秒
2025-08-12 07:11:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:11:11 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:11:11 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[174]毫秒
2025-08-12 07:11:12 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[422]毫秒
2025-08-12 07:11:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:11:16 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[92]毫秒
2025-08-12 07:11:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:11:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[396]毫秒
2025-08-12 07:11:37 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:11:37 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:11:37 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[263]毫秒
2025-08-12 07:11:37 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[464]毫秒
2025-08-12 07:11:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:11:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:11:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[92]毫秒
2025-08-12 07:11:40 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[130]毫秒
2025-08-12 07:11:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:11:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:11:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[89]毫秒
2025-08-12 07:11:41 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[125]毫秒
2025-08-12 07:11:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:11:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:11:43 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[128]毫秒
2025-08-12 07:11:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[171]毫秒
2025-08-12 07:11:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:11:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:11:46 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[85]毫秒
2025-08-12 07:11:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[129]毫秒
2025-08-12 07:14:14 [boundedElastic-1008] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:14:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:14:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[252]毫秒
2025-08-12 07:14:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[503]毫秒
2025-08-12 07:14:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:14:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:14:32 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[171]毫秒
2025-08-12 07:14:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[437]毫秒
2025-08-12 07:15:05 [boundedElastic-1012] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:15:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:15:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[254]毫秒
2025-08-12 07:15:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[492]毫秒
2025-08-12 07:15:09 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:15:10 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[438]毫秒
2025-08-12 07:15:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:15:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[92]毫秒
2025-08-12 07:15:12 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:15:12 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[417]毫秒
2025-08-12 07:15:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:15:16 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:15:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[313]毫秒
2025-08-12 07:15:17 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[462]毫秒
2025-08-12 07:15:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:15:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:15:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[129]毫秒
2025-08-12 07:15:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[443]毫秒
2025-08-12 07:15:53 [boundedElastic-1008] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/resource/oss/upload],无参数
2025-08-12 07:15:55 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/resource/oss/upload],耗时:[2128]毫秒
2025-08-12 07:15:56 [boundedElastic-1008] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/tag/list],参数类型[json],参数:[null]
2025-08-12 07:15:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/tag/list],耗时:[194]毫秒
2025-08-12 07:16:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/resource/map/geocoder/location],参数类型[json],参数:[null]
2025-08-12 07:16:01 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62254,"lat":26.6015}]
2025-08-12 07:16:01 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/resource/map/geocoder/location],耗时:[405]毫秒
2025-08-12 07:16:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[437]毫秒
2025-08-12 07:16:04 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/moment/create],参数类型[json],参数:[{"content":"收拾收拾","images":"1955045542165872641","visibilityStatus":1,"tagIds":["1930291797224824833","1930290567920459778"],"provinceName":"贵州省","cityName":"贵阳市","districtName":"观山湖区","location":"贵阳市·世纪城勇惠世纪金源商务中心店","lon":106.62254,"lat":26.6015}]
2025-08-12 07:16:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/moment/create],耗时:[1381]毫秒
2025-08-12 07:16:11 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:16:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[593]毫秒
2025-08-12 07:16:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:16:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[544]毫秒
2025-08-12 07:16:18 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:16:18 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[179]毫秒
2025-08-12 07:16:21 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:16:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[505]毫秒
2025-08-12 07:16:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:16:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[215]毫秒
2025-08-12 07:16:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:16:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[395]毫秒
2025-08-12 07:16:33 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:16:33 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[348]毫秒
2025-08-12 07:16:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:16:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[305]毫秒
2025-08-12 07:16:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:16:37 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[347]毫秒
2025-08-12 07:16:38 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:16:38 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[133]毫秒
2025-08-12 07:16:47 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 07:16:47 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[138]毫秒
2025-08-12 07:16:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:16:54 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:16:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:16:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[50]毫秒
2025-08-12 07:16:54 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[213]毫秒
2025-08-12 07:16:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[947]毫秒
2025-08-12 07:17:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 07:17:05 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[127]毫秒
2025-08-12 07:17:11 [boundedElastic-1013] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 07:17:12 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[130]毫秒
2025-08-12 07:17:17 [boundedElastic-1013] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/help/key/aboutUs],参数类型[json],参数:[null]
2025-08-12 07:17:17 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/help/key/aboutUs],耗时:[134]毫秒
2025-08-12 07:17:24 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/agreement/key/userAgreement],参数类型[json],参数:[null]
2025-08-12 07:17:24 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/agreement/key/userAgreement],耗时:[209]毫秒
2025-08-12 07:17:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/agreement/key/privacyAgreement],参数类型[json],参数:[null]
2025-08-12 07:17:35 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/agreement/key/privacyAgreement],耗时:[132]毫秒
2025-08-12 07:17:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:17:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[303]毫秒
2025-08-12 07:17:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/help/key/antiFraudReminder],参数类型[json],参数:[null]
2025-08-12 07:17:43 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/help/key/antiFraudReminder],耗时:[89]毫秒
2025-08-12 07:17:47 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:17:47 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[216]毫秒
2025-08-12 07:17:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 07:17:50 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[309]毫秒
2025-08-12 07:17:51 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:17:52 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[134]毫秒
2025-08-12 07:17:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:17:55 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[390]毫秒
2025-08-12 07:18:35 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:18:35 [boundedElastic-1014] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:18:35 [boundedElastic-1008] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:18:35 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:18:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:18:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[249]毫秒
2025-08-12 07:18:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[249]毫秒
2025-08-12 07:18:35 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[45]毫秒
2025-08-12 07:18:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[492]毫秒
2025-08-12 07:18:36 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:18:36 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[939]毫秒
2025-08-12 07:18:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[339]毫秒
2025-08-12 07:18:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:18:36 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[181]毫秒
2025-08-12 07:18:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-12 07:18:43 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/gift/list],参数类型[json],参数:[null]
2025-08-12 07:18:43 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[127]毫秒
2025-08-12 07:18:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/gift/list],耗时:[171]毫秒
2025-08-12 07:18:49 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:18:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[484]毫秒
2025-08-12 07:18:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:18:52 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[392]毫秒
2025-08-12 07:18:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:18:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:18:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:18:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:18:53 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:18:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[83]毫秒
2025-08-12 07:18:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[85]毫秒
2025-08-12 07:18:53 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[85]毫秒
2025-08-12 07:18:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:18:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[366]毫秒
2025-08-12 07:18:54 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[453]毫秒
2025-08-12 07:18:54 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:18:54 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[332]毫秒
2025-08-12 07:18:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[134]毫秒
2025-08-12 07:18:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:18:55 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:18:55 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:18:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:18:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:18:55 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[469]毫秒
2025-08-12 07:18:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[49]毫秒
2025-08-12 07:18:55 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[87]毫秒
2025-08-12 07:18:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[89]毫秒
2025-08-12 07:18:55 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:18:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[45]毫秒
2025-08-12 07:18:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:18:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[45]毫秒
2025-08-12 07:18:56 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[342]毫秒
2025-08-12 07:18:56 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:18:56 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[137]毫秒
2025-08-12 07:18:56 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[380]毫秒
2025-08-12 07:18:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:18:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[45]毫秒
2025-08-12 07:18:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[92]毫秒
2025-08-12 07:18:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[91]毫秒
2025-08-12 07:18:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[94]毫秒
2025-08-12 07:18:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:18:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[279]毫秒
2025-08-12 07:18:57 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[48]毫秒
2025-08-12 07:18:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:18:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[166]毫秒
2025-08-12 07:18:58 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:18:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[386]毫秒
2025-08-12 07:18:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:18:58 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:18:58 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:18:58 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:18:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:18:58 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[88]毫秒
2025-08-12 07:18:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[93]毫秒
2025-08-12 07:18:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[102]毫秒
2025-08-12 07:18:59 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:18:59 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[329]毫秒
2025-08-12 07:18:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[365]毫秒
2025-08-12 07:18:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:18:59 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[148]毫秒
2025-08-12 07:18:59 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[423]毫秒
2025-08-12 07:19:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:19:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[472]毫秒
2025-08-12 07:19:01 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/location],参数类型[json],参数:[{"lon":106.62105658637152,"lat":26.602868923611112}]
2025-08-12 07:19:01 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:19:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/location],耗时:[329]毫秒
2025-08-12 07:19:02 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[438]毫秒
2025-08-12 07:19:03 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:19:03 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:19:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[716]毫秒
2025-08-12 07:19:03 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[716]毫秒
2025-08-12 07:19:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 07:19:05 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 07:19:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[132]毫秒
2025-08-12 07:19:06 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[459]毫秒
2025-08-12 07:19:12 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/topic/list],参数类型[json],参数:[null]
2025-08-12 07:19:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:19:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/topic/list],耗时:[214]毫秒
2025-08-12 07:19:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[503]毫秒
2025-08-12 07:19:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:19:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:19:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[94]毫秒
2025-08-12 07:19:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[348]毫秒
2025-08-12 07:19:22 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/comment/create],参数类型[json],参数:[{"rootId":0,"parentId":0,"content":"弄了个","type":1,"businessId":"1955045584507498497","images":""}]
2025-08-12 07:19:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/comment/create],耗时:[735]毫秒
2025-08-12 07:19:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:19:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[54]毫秒
2025-08-12 07:19:25 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:19:25 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:19:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[229]毫秒
2025-08-12 07:19:26 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[326]毫秒
2025-08-12 07:19:35 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/comment/create],参数类型[json],参数:[{"rootId":"1955046412068843522","parentId":"1955046412068843522","content":"回复西门: 1111","type":1,"businessId":"1955045584507498497","images":""}]
2025-08-12 07:19:36 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/comment/create],耗时:[704]毫秒
2025-08-12 07:19:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:19:36 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[223]毫秒
2025-08-12 07:19:39 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:19:39 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:19:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[259]毫秒
2025-08-12 07:19:39 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[356]毫秒
2025-08-12 07:19:40 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/item/page],参数类型[json],参数:[null]
2025-08-12 07:19:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/item/page],耗时:[278]毫秒
2025-08-12 07:19:46 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/item/page],参数类型[json],参数:[null]
2025-08-12 07:19:46 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/item/page],耗时:[305]毫秒
2025-08-12 07:19:48 [boundedElastic-1014] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:19:48 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[455]毫秒
2025-08-12 07:19:52 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:19:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[528]毫秒
2025-08-12 07:19:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:19:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:19:53 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:19:53 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:19:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:19:53 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[72]毫秒
2025-08-12 07:19:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[101]毫秒
2025-08-12 07:19:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[136]毫秒
2025-08-12 07:19:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:19:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[583]毫秒
2025-08-12 07:19:54 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[778]毫秒
2025-08-12 07:19:54 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[337]毫秒
2025-08-12 07:19:54 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:19:54 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[178]毫秒
2025-08-12 07:20:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:20:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[487]毫秒
2025-08-12 07:20:13 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:20:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[211]毫秒
2025-08-12 07:20:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:20:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[213]毫秒
2025-08-12 07:20:17 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:20:18 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[174]毫秒
2025-08-12 07:20:21 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:20:21 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[208]毫秒
2025-08-12 07:20:23 [boundedElastic-1015] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:20:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[340]毫秒
2025-08-12 07:20:26 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/follow/cancel],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:20:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/follow/cancel],耗时:[372]毫秒
2025-08-12 07:20:28 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:20:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[339]毫秒
2025-08-12 07:20:30 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:20:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[164]毫秒
2025-08-12 07:20:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:34 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:34 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[379]毫秒
2025-08-12 07:20:34 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[379]毫秒
2025-08-12 07:20:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:35 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[249]毫秒
2025-08-12 07:20:40 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[244]毫秒
2025-08-12 07:20:42 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:42 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[252]毫秒
2025-08-12 07:20:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:44 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[194]毫秒
2025-08-12 07:20:50 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 07:20:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 07:20:50 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[128]毫秒
2025-08-12 07:20:50 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[545]毫秒
2025-08-12 07:20:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:20:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[141]毫秒
2025-08-12 07:21:14 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[130]毫秒
2025-08-12 07:21:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[226]毫秒
2025-08-12 07:21:17 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[175]毫秒
2025-08-12 07:21:22 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:22 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[343]毫秒
2025-08-12 07:21:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[190]毫秒
2025-08-12 07:21:29 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:21:29 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[268]毫秒
2025-08-12 07:21:31 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:21:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[128]毫秒
2025-08-12 07:21:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-12 07:21:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:21:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:21:32 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:21:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[83]毫秒
2025-08-12 07:21:32 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[91]毫秒
2025-08-12 07:21:32 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[127]毫秒
2025-08-12 07:21:32 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[174]毫秒
2025-08-12 07:21:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:34 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[135]毫秒
2025-08-12 07:21:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[193]毫秒
2025-08-12 07:21:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:37 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[144]毫秒
2025-08-12 07:21:38 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:38 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[141]毫秒
2025-08-12 07:21:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:40 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[139]毫秒
2025-08-12 07:21:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/history/page],参数类型[json],参数:[null]
2025-08-12 07:21:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/history/page],耗时:[182]毫秒
2025-08-12 07:21:44 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/app/help/key/coinIntroduction],参数类型[json],参数:[null]
2025-08-12 07:21:44 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/app/help/key/coinIntroduction],耗时:[137]毫秒
2025-08-12 07:21:49 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/recharge/list],参数类型[json],参数:[null]
2025-08-12 07:21:50 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/recharge/list],耗时:[133]毫秒
2025-08-12 07:22:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:22:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[305]毫秒
2025-08-12 07:22:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:22:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[212]毫秒
2025-08-12 07:22:38 [boundedElastic-1017] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:22:38 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[294]毫秒
2025-08-12 07:22:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:22:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[215]毫秒
2025-08-12 07:22:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:22:43 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/page],参数类型[json],参数:[null]
2025-08-12 07:22:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[398]毫秒
2025-08-12 07:22:43 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/page],耗时:[401]毫秒
2025-08-12 07:22:43 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:22:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[104]毫秒
2025-08-12 07:22:44 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:22:45 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[554]毫秒
2025-08-12 07:22:46 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:22:46 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[326]毫秒
2025-08-12 07:22:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:22:49 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[403]毫秒
2025-08-12 07:22:58 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:22:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[380]毫秒
2025-08-12 07:23:01 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[317]毫秒
2025-08-12 07:23:03 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:04 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[389]毫秒
2025-08-12 07:23:05 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:05 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[321]毫秒
2025-08-12 07:23:07 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[309]毫秒
2025-08-12 07:23:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:10 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[394]毫秒
2025-08-12 07:23:13 [boundedElastic-1001] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[395]毫秒
2025-08-12 07:23:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:19 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[385]毫秒
2025-08-12 07:23:20 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:20 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[315]毫秒
2025-08-12 07:23:24 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[388]毫秒
2025-08-12 07:23:27 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:27 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[313]毫秒
2025-08-12 07:23:29 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:30 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[394]毫秒
2025-08-12 07:23:31 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:31 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[311]毫秒
2025-08-12 07:23:34 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:34 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[345]毫秒
2025-08-12 07:23:35 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:23:36 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[344]毫秒
2025-08-12 07:23:39 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:23:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[132]毫秒
2025-08-12 07:23:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/data],参数类型[json],参数:[null]
2025-08-12 07:23:41 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-12 07:23:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/signIn/rewards],参数类型[json],参数:[null]
2025-08-12 07:23:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:23:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/rewards],耗时:[126]毫秒
2025-08-12 07:23:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[126]毫秒
2025-08-12 07:23:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/signIn/data],耗时:[165]毫秒
2025-08-12 07:23:41 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[207]毫秒
2025-08-12 07:23:42 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:23:42 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[180]毫秒
2025-08-12 07:23:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/level],参数类型[json],参数:[null]
2025-08-12 07:23:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/level],耗时:[89]毫秒
2025-08-12 07:23:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:23:45 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:23:45 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[124]毫秒
2025-08-12 07:23:45 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[163]毫秒
2025-08-12 07:23:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:23:49 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[294]毫秒
2025-08-12 07:23:50 [boundedElastic-1003] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 07:23:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[305]毫秒
2025-08-12 07:23:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/sent/page],参数类型[json],参数:[null]
2025-08-12 07:23:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/sent/page],耗时:[392]毫秒
2025-08-12 07:23:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 07:23:56 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[317]毫秒
2025-08-12 07:23:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 07:23:59 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[301]毫秒
2025-08-12 07:24:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:01 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[212]毫秒
2025-08-12 07:24:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 07:24:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[300]毫秒
2025-08-12 07:24:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[133]毫秒
2025-08-12 07:24:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[380]毫秒
2025-08-12 07:24:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:08 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[136]毫秒
2025-08-12 07:24:10 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 07:24:11 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[302]毫秒
2025-08-12 07:24:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 07:24:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[304]毫秒
2025-08-12 07:24:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 07:24:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[379]毫秒
2025-08-12 07:24:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],参数类型[json],参数:[null]
2025-08-12 07:24:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/browseHistory/userHome/page],耗时:[293]毫秒
2025-08-12 07:24:21 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:21 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[301]毫秒
2025-08-12 07:24:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/received/page],参数类型[json],参数:[null]
2025-08-12 07:24:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/received/page],耗时:[292]毫秒
2025-08-12 07:24:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[127]毫秒
2025-08-12 07:24:25 [boundedElastic-1010] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:26 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[310]毫秒
2025-08-12 07:24:33 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:33 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[389]毫秒
2025-08-12 07:24:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:35 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[143]毫秒
2025-08-12 07:24:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:37 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[318]毫秒
2025-08-12 07:24:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:39 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[209]毫秒
2025-08-12 07:24:40 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:24:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[313]毫秒
2025-08-12 07:24:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:24:47 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[214]毫秒
2025-08-12 07:24:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:49 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[303]毫秒
2025-08-12 07:24:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:51 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[299]毫秒
2025-08-12 07:24:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:53 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[477]毫秒
2025-08-12 07:24:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:24:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[306]毫秒
2025-08-12 07:24:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:25:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[216]毫秒
2025-08-12 07:25:10 [boundedElastic-1015] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:25:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[218]毫秒
2025-08-12 07:25:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:25:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[131]毫秒
2025-08-12 07:25:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[228]毫秒
2025-08-12 07:25:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:17 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[189]毫秒
2025-08-12 07:25:18 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:18 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[92]毫秒
2025-08-12 07:25:21 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:21 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[89]毫秒
2025-08-12 07:25:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[130]毫秒
2025-08-12 07:25:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[130]毫秒
2025-08-12 07:25:37 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:37 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[125]毫秒
2025-08-12 07:25:43 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:43 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[174]毫秒
2025-08-12 07:25:46 [boundedElastic-1014] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:46 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[151]毫秒
2025-08-12 07:25:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:48 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[87]毫秒
2025-08-12 07:25:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/detail],参数类型[json],参数:[null]
2025-08-12 07:25:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/detail],耗时:[125]毫秒
2025-08-12 07:25:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[138]毫秒
2025-08-12 07:25:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[189]毫秒
2025-08-12 07:25:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:25:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[142]毫秒
2025-08-12 07:26:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:26:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[184]毫秒
2025-08-12 07:26:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:26:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[161]毫秒
2025-08-12 07:26:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:26:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[92]毫秒
2025-08-12 07:26:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:26:07 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[104]毫秒
2025-08-12 07:26:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/order/page],参数类型[json],参数:[null]
2025-08-12 07:26:08 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/order/page],耗时:[183]毫秒
2025-08-12 07:26:09 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:26:09 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[260]毫秒
2025-08-12 07:26:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/data],参数类型[json],参数:[null]
2025-08-12 07:26:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/data],耗时:[173]毫秒
2025-08-12 07:26:22 [boundedElastic-1003] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:26:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[224]毫秒
2025-08-12 07:26:25 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[424]毫秒
2025-08-12 07:26:29 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[372]毫秒
2025-08-12 07:26:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/detail],参数类型[json],参数:[null]
2025-08-12 07:26:32 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/comment/root/page],参数类型[json],参数:[null]
2025-08-12 07:26:32 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/comment/root/page],耗时:[94]毫秒
2025-08-12 07:26:32 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/detail],耗时:[371]毫秒
2025-08-12 07:26:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[458]毫秒
2025-08-12 07:26:38 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:38 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[376]毫秒
2025-08-12 07:26:44 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:45 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[840]毫秒
2025-08-12 07:26:47 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/page],参数类型[json],参数:[null]
2025-08-12 07:26:47 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/page],耗时:[345]毫秒
2025-08-12 07:26:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:26:55 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[222]毫秒
2025-08-12 07:26:59 [boundedElastic-1019] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:26:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:26:59 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[83]毫秒
2025-08-12 07:26:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[89]毫秒
2025-08-12 07:27:02 [boundedElastic-1019] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/resource/oss/view],参数类型[json],参数:[null]
2025-08-12 07:27:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/resource/oss/view],耗时:[227]毫秒
2025-08-12 07:27:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:27:08 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[132]毫秒
2025-08-12 07:27:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:27:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[139]毫秒
2025-08-12 07:27:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:27:14 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[218]毫秒
2025-08-12 07:27:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:27:15 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[433]毫秒
2025-08-12 07:27:21 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:21 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[193]毫秒
2025-08-12 07:27:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:27:22 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[175]毫秒
2025-08-12 07:27:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[219]毫秒
2025-08-12 07:27:29 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:29 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[271]毫秒
2025-08-12 07:27:31 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:31 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[189]毫秒
2025-08-12 07:27:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:34 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[225]毫秒
2025-08-12 07:27:40 [boundedElastic-1015] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:40 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[276]毫秒
2025-08-12 07:27:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:42 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[183]毫秒
2025-08-12 07:27:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:27:49 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[212]毫秒
2025-08-12 07:27:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:51 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[319]毫秒
2025-08-12 07:27:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:27:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[133]毫秒
2025-08-12 07:27:52 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:52 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[136]毫秒
2025-08-12 07:27:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[142]毫秒
2025-08-12 07:27:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/page],参数类型[json],参数:[null]
2025-08-12 07:27:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/page],耗时:[177]毫秒
2025-08-12 07:28:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:28:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[179]毫秒
2025-08-12 07:28:01 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:28:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[553]毫秒
2025-08-12 07:28:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:28:04 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[176]毫秒
2025-08-12 07:28:06 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:28:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[500]毫秒
2025-08-12 07:28:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:28:09 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[406]毫秒
2025-08-12 07:28:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:28:12 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[398]毫秒
2025-08-12 07:28:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:28:13 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[491]毫秒
2025-08-12 07:28:14 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:28:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[404]毫秒
2025-08-12 07:28:16 [boundedElastic-1014] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:28:16 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[398]毫秒
2025-08-12 07:28:18 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:28:18 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[478]毫秒
2025-08-12 07:30:07 [boundedElastic-1021] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:30:07 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[472]毫秒
2025-08-12 07:30:09 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:30:09 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[303]毫秒
2025-08-12 07:30:10 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:30:10 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[306]毫秒
2025-08-12 07:30:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/page],参数类型[json],参数:[null]
2025-08-12 07:30:12 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/page],耗时:[346]毫秒
2025-08-12 07:30:14 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:30:14 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[172]毫秒
2025-08-12 07:31:52 [boundedElastic-1019] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:31:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[215]毫秒
2025-08-12 07:34:48 [boundedElastic-1021] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/message/group],参数类型[json],参数:[null]
2025-08-12 07:34:48 [boundedElastic-1019] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:48 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/message/group],耗时:[212]毫秒
2025-08-12 07:34:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[466]毫秒
2025-08-12 07:34:51 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:51 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[310]毫秒
2025-08-12 07:34:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:52 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[288]毫秒
2025-08-12 07:34:55 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:55 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[374]毫秒
2025-08-12 07:34:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[296]毫秒
2025-08-12 07:34:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:34:59 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[299]毫秒
2025-08-12 07:35:02 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 07:35:02 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[129]毫秒
2025-08-12 07:35:11 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:35:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[385]毫秒
2025-08-12 07:35:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:35:13 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[315]毫秒
2025-08-12 07:35:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:35:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[306]毫秒
2025-08-12 07:35:16 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:35:17 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[215]毫秒
2025-08-12 07:35:19 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:35:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[212]毫秒
2025-08-12 07:35:28 [boundedElastic-1026] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:35:29 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[502]毫秒
2025-08-12 07:36:21 [boundedElastic-1026] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:36:21 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[609]毫秒
2025-08-12 07:36:24 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:36:24 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:36:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:36:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:36:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:36:24 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[134]毫秒
2025-08-12 07:36:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[52]毫秒
2025-08-12 07:36:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[97]毫秒
2025-08-12 07:36:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:36:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[708]毫秒
2025-08-12 07:36:25 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[867]毫秒
2025-08-12 07:36:25 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:36:25 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[561]毫秒
2025-08-12 07:36:25 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[177]毫秒
2025-08-12 07:43:40 [boundedElastic-1031] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 07:43:41 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[159]毫秒
2025-08-12 07:43:44 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 07:43:44 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[95]毫秒
2025-08-12 07:43:48 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:43:49 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[626]毫秒
2025-08-12 07:43:51 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:43:51 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[410]毫秒
2025-08-12 07:43:53 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 07:43:53 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[87]毫秒
2025-08-12 07:43:56 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:43:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[484]毫秒
2025-08-12 07:43:59 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:44:00 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:44:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:44:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:44:00 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:44:00 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[94]毫秒
2025-08-12 07:44:00 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[143]毫秒
2025-08-12 07:44:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[147]毫秒
2025-08-12 07:44:00 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[472]毫秒
2025-08-12 07:44:00 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:44:00 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[333]毫秒
2025-08-12 07:44:00 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[860]毫秒
2025-08-12 07:44:01 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:44:01 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[191]毫秒
2025-08-12 07:44:10 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:44:10 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[489]毫秒
2025-08-12 07:44:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],参数类型[json],参数:[null]
2025-08-12 07:44:14 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/same-city],耗时:[809]毫秒
2025-08-12 07:44:20 [boundedElastic-1024] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],参数类型[json],参数:[null]
2025-08-12 07:44:20 [boundedElastic-1023] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],参数类型[json],参数:[null]
2025-08-12 07:44:20 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/activity-record/enrollUsers],耗时:[154]毫秒
2025-08-12 07:44:20 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/activity/buddy/detail],耗时:[520]毫秒
2025-08-12 07:44:29 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/moment/page],参数类型[json],参数:[null]
2025-08-12 07:44:29 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/moment/page],耗时:[495]毫秒
2025-08-12 07:44:32 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:44:32 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[134]毫秒
2025-08-12 07:44:37 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/data],参数类型[json],参数:[null]
2025-08-12 07:44:37 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/data],耗时:[251]毫秒
2025-08-12 07:44:40 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:44:40 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[132]毫秒
2025-08-12 07:44:44 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:44:44 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[519]毫秒
2025-08-12 07:44:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:44:46 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[134]毫秒
2025-08-12 07:44:49 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:44:50 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[217]毫秒
2025-08-12 07:45:41 [boundedElastic-1030] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/same-city],参数类型[json],参数:[null]
2025-08-12 07:45:42 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/same-city],耗时:[581]毫秒
2025-08-12 07:45:52 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:45:52 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[217]毫秒
2025-08-12 07:51:08 [boundedElastic-1034] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:51:08 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[487]毫秒
2025-08-12 07:51:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/follow/user/page],参数类型[json],参数:[null]
2025-08-12 07:51:09 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/follow/user/page],耗时:[315]毫秒
2025-08-12 07:51:11 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 07:51:11 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[87]毫秒
2025-08-12 07:51:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:51:15 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[220]毫秒
2025-08-12 07:51:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/level],参数类型[json],参数:[null]
2025-08-12 07:51:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/level],耗时:[86]毫秒
2025-08-12 07:51:19 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:51:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[126]毫秒
2025-08-12 07:51:19 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],参数类型[json],参数:[null]
2025-08-12 07:51:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/coin/task/listByLevel],耗时:[128]毫秒
2025-08-12 07:51:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/my],参数类型[json],参数:[null]
2025-08-12 07:51:23 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/my],耗时:[217]毫秒
2025-08-12 07:52:24 [boundedElastic-1031] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:52:24 [boundedElastic-1033] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:52:24 [boundedElastic-1017] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:52:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:52:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:52:24 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[102]毫秒
2025-08-12 07:52:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[277]毫秒
2025-08-12 07:52:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[153]毫秒
2025-08-12 07:52:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[522]毫秒
2025-08-12 07:52:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:52:25 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[1035]毫秒
2025-08-12 07:52:25 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[377]毫秒
2025-08-12 07:52:25 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:52:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[191]毫秒
2025-08-12 07:52:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:52:41 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:52:41 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:52:41 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:52:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:52:41 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[144]毫秒
2025-08-12 07:52:41 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[250]毫秒
2025-08-12 07:52:41 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[250]毫秒
2025-08-12 07:52:41 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:52:41 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[617]毫秒
2025-08-12 07:52:42 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:52:42 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[937]毫秒
2025-08-12 07:52:42 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[206]毫秒
2025-08-12 07:52:42 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[832]毫秒
2025-08-12 07:52:57 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:52:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:52:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:52:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:52:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:52:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[131]毫秒
2025-08-12 07:52:57 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[262]毫秒
2025-08-12 07:52:57 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[262]毫秒
2025-08-12 07:52:57 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[492]毫秒
2025-08-12 07:52:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:52:57 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[953]毫秒
2025-08-12 07:52:58 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[381]毫秒
2025-08-12 07:52:58 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:52:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[187]毫秒
2025-08-12 07:53:00 [boundedElastic-1037] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/account/info],参数类型[json],参数:[null]
2025-08-12 07:53:00 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/manage/gift/list],参数类型[json],参数:[null]
2025-08-12 07:53:00 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/account/info],耗时:[87]毫秒
2025-08-12 07:53:00 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/manage/gift/list],耗时:[103]毫秒
2025-08-12 07:53:07 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/greeting/check],参数类型[json],参数:[null]
2025-08-12 07:53:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/greeting/check],耗时:[147]毫秒
2025-08-12 07:53:16 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:53:16 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[557]毫秒
2025-08-12 07:53:21 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:53:21 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[514]毫秒
2025-08-12 07:53:22 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:53:22 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:53:22 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:53:22 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:53:22 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:53:23 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[102]毫秒
2025-08-12 07:53:23 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[93]毫秒
2025-08-12 07:53:23 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[134]毫秒
2025-08-12 07:53:23 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:53:23 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[400]毫秒
2025-08-12 07:53:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[468]毫秒
2025-08-12 07:53:23 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:53:23 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[344]毫秒
2025-08-12 07:53:23 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[146]毫秒
2025-08-12 07:53:25 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:53:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[410]毫秒
2025-08-12 07:53:34 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:53:34 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[150]毫秒
2025-08-12 07:55:58 [boundedElastic-1017] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:55:59 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[128]毫秒
2025-08-12 07:56:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/moment/latest],参数类型[json],参数:[null]
2025-08-12 07:56:11 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":1,"businessId":"1938886425951789058"}]
2025-08-12 07:56:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],参数类型[json],参数:[null]
2025-08-12 07:56:11 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/detail],参数类型[json],参数:[null]
2025-08-12 07:56:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/album/list],参数类型[json],参数:[null]
2025-08-12 07:56:11 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/authApply/authenticatedTypes],耗时:[136]毫秒
2025-08-12 07:56:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/moment/latest],耗时:[272]毫秒
2025-08-12 07:56:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/album/list],耗时:[272]毫秒
2025-08-12 07:56:11 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[526]毫秒
2025-08-12 07:56:11 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/browseHistory/create],参数类型[json],参数:[{"type":4,"businessId":"1945500476072919041"}]
2025-08-12 07:56:12 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/browseHistory/create],耗时:[355]毫秒
2025-08-12 07:56:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/detail],耗时:[1026]毫秒
2025-08-12 07:56:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/gift/wall],参数类型[json],参数:[null]
2025-08-12 07:56:12 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/gift/wall],耗时:[211]毫秒
2025-08-12 07:56:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:56:19 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[497]毫秒
2025-08-12 07:56:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:56:24 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[136]毫秒
2025-08-12 07:56:32 [boundedElastic-1033] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 07:56:32 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[142]毫秒
2025-08-12 07:56:32 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 07:56:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[455]毫秒
2025-08-12 07:56:38 [boundedElastic-1023] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:56:38 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[131]毫秒
2025-08-12 07:57:44 [boundedElastic-1038] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:57:44 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[137]毫秒
2025-08-12 07:58:25 [boundedElastic-1031] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:58:25 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[160]毫秒
2025-08-12 07:58:40 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:58:41 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[131]毫秒
2025-08-12 07:58:53 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:58:54 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[136]毫秒
2025-08-12 07:59:39 [boundedElastic-1037] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 07:59:39 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[135]毫秒
2025-08-12 08:00:52 [boundedElastic-1031] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:00:52 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[136]毫秒
2025-08-12 08:01:01 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:01:01 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[130]毫秒
2025-08-12 08:06:15 [boundedElastic-1039] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:06:15 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[131]毫秒
2025-08-12 08:06:28 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:06:28 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[128]毫秒
2025-08-12 08:06:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:06:49 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[130]毫秒
2025-08-12 08:07:08 [boundedElastic-1039] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:07:08 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[130]毫秒
2025-08-12 08:07:37 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:07:37 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[131]毫秒
2025-08-12 08:07:57 [boundedElastic-1042] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:07:57 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[131]毫秒
2025-08-12 08:08:11 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:08:11 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[129]毫秒
2025-08-12 08:08:33 [boundedElastic-1038] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:08:34 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[129]毫秒
2025-08-12 08:08:36 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:08:36 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[100]毫秒
2025-08-12 08:08:47 [boundedElastic-1041] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:08:47 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[262]毫秒
2025-08-12 08:08:48 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:08:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[179]毫秒
2025-08-12 08:08:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:08:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[174]毫秒
2025-08-12 08:08:51 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:08:51 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[180]毫秒
2025-08-12 08:08:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:08:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[224]毫秒
2025-08-12 08:11:18 [boundedElastic-1039] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:11:18 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[224]毫秒
2025-08-12 08:11:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:11:19 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[178]毫秒
2025-08-12 08:11:20 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:11:20 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[171]毫秒
2025-08-12 08:12:09 [boundedElastic-1043] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:12:09 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[132]毫秒
2025-08-12 08:13:35 [boundedElastic-1039] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:13:35 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[137]毫秒
2025-08-12 08:13:49 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:13:49 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[130]毫秒
2025-08-12 08:14:02 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:14:02 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[126]毫秒
2025-08-12 08:26:25 [boundedElastic-1052] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:26:25 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[230]毫秒
2025-08-12 08:26:26 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:26:26 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[184]毫秒
2025-08-12 08:26:27 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 08:26:27 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[177]毫秒
2025-08-12 08:27:02 [boundedElastic-1057] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:27:02 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[148]毫秒
2025-08-12 08:30:25 [boundedElastic-1051] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:30:25 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[135]毫秒
2025-08-12 08:32:09 [boundedElastic-1058] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 08:32:09 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[130]毫秒
2025-08-12 22:21:37 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-12 22:21:37 [main] INFO  c.g.gateway.BaniGatewayApplication - Starting BaniGatewayApplication using Java 21.0.3 with PID 29440 (E:\bani\code\bani\bani-gateway\target\classes started by likavn in E:\bani\code\bani)
2025-08-12 22:21:37 [main] INFO  c.g.gateway.BaniGatewayApplication - The following 1 profile is active: "dev"
2025-08-12 22:21:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-gateway.yml, group=DEFAULT_GROUP] success
2025-08-12 22:21:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-12 22:21:40 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-12 22:21:40 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-12 22:21:40 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-12 22:21:41 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 101.200.242.239/101.200.242.239:8762
2025-08-12 22:21:41 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 101.200.242.239/101.200.242.239:8762
2025-08-12 22:21:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-gateway 172.31.0.1:8080 register finished
2025-08-12 22:21:45 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-08-12 22:21:46 [main] INFO  c.g.gateway.BaniGatewayApplication - Started BaniGatewayApplication in 10.392 seconds (process running for 12.565)
2025-08-12 22:21:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-12 22:21:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-gateway.yml, group=DEFAULT_GROUP
2025-08-12 22:21:46 [main] INFO  c.g.gateway.BaniGatewayApplication - 网关启动成功...
2025-08-12 22:24:02 [boundedElastic-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0d1DksGa1NfE7K0qllHa1yxlHU2DksGd","grantType":"mini","tenantId":"1"}]
2025-08-12 22:24:06 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[3987]毫秒
2025-08-12 22:24:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 22:24:07 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 22:24:07 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[131]毫秒
2025-08-12 22:24:08 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[844]毫秒
2025-08-12 22:24:19 [boundedElastic-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 22:24:24 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[5559]毫秒
2025-08-12 22:24:28 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:24:28 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[154]毫秒
2025-08-12 22:25:27 [boundedElastic-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:25:27 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[144]毫秒
2025-08-12 22:26:33 [boundedElastic-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:26:33 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[147]毫秒
2025-08-12 22:26:44 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:26:45 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[143]毫秒
2025-08-12 22:27:50 [boundedElastic-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:27:51 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[235]毫秒
2025-08-12 22:31:04 [boundedElastic-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:31:05 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[153]毫秒
2025-08-12 22:31:33 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:31:33 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[132]毫秒
2025-08-12 22:31:46 [boundedElastic-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:31:47 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[138]毫秒
2025-08-12 22:34:59 [boundedElastic-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:34:59 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[153]毫秒
2025-08-12 22:35:07 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:35:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[141]毫秒
2025-08-12 22:35:23 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:35:24 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[146]毫秒
2025-08-12 22:35:43 [boundedElastic-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:35:44 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[139]毫秒
2025-08-12 22:35:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:35:58 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[214]毫秒
2025-08-12 22:36:20 [boundedElastic-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:36:20 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[141]毫秒
2025-08-12 22:36:59 [boundedElastic-14] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:36:59 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[264]毫秒
2025-08-12 22:37:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:37:00 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[178]毫秒
2025-08-12 22:37:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:37:00 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[136]毫秒
2025-08-12 22:39:23 [boundedElastic-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:39:23 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[223]毫秒
2025-08-12 22:39:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:39:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[169]毫秒
2025-08-12 22:39:24 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:39:24 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[131]毫秒
2025-08-12 22:40:23 [boundedElastic-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:40:23 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[152]毫秒
2025-08-12 22:40:44 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:40:44 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[139]毫秒
2025-08-12 22:40:45 [boundedElastic-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:40:45 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[234]毫秒
2025-08-12 22:44:26 [boundedElastic-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:44:26 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[142]毫秒
2025-08-12 22:44:29 [boundedElastic-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:44:29 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[219]毫秒
2025-08-12 22:44:30 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:44:30 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[184]毫秒
2025-08-12 22:44:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:44:31 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[132]毫秒
2025-08-12 22:44:31 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:44:31 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[131]毫秒
2025-08-12 22:46:01 [boundedElastic-16] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:46:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[153]毫秒
2025-08-12 22:46:57 [boundedElastic-15] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:46:57 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[223]毫秒
2025-08-12 22:46:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:46:58 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[177]毫秒
2025-08-12 22:46:58 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:46:59 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[130]毫秒
2025-08-12 22:47:01 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:01 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[170]毫秒
2025-08-12 22:47:02 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[169]毫秒
2025-08-12 22:47:02 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:02 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[182]毫秒
2025-08-12 22:47:03 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:03 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[135]毫秒
2025-08-12 22:47:04 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:04 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[174]毫秒
2025-08-12 22:47:11 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:11 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[218]毫秒
2025-08-12 22:47:13 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:13 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[171]毫秒
2025-08-12 22:47:14 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[171]毫秒
2025-08-12 22:47:15 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 22:47:15 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[170]毫秒
2025-08-12 22:53:20 [boundedElastic-19] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:53:20 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[149]毫秒
2025-08-12 22:53:42 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:53:42 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[139]毫秒
2025-08-12 22:57:11 [boundedElastic-21] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:57:12 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[151]毫秒
2025-08-12 22:58:08 [boundedElastic-22] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:58:08 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[136]毫秒
2025-08-12 22:58:37 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 22:58:37 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[147]毫秒
2025-08-12 23:04:32 [boundedElastic-21] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:04:32 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[165]毫秒
2025-08-12 23:05:16 [boundedElastic-22] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/auth/login],参数类型[json],参数:[{"code":"0c1rHFFa1BTa6K0pquIa1KcDGU2rHFF5","grantType":"mini","tenantId":"1"}]
2025-08-12 23:05:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/auth/login],耗时:[1774]毫秒
2025-08-12 23:05:18 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/user/getInfo],参数类型[json],参数:[null]
2025-08-12 23:05:18 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/system/dict/data],参数类型[json],参数:[["user_sex","user_revenue","user_affective_status","user_edu","user_job","user_tag_ones","user_tag_car","user_tag_house","user_tag_marriage","user_tag_want_marry","user_require_tag_edu","user_require_tag_revenue","user_require_tag_house","user_require_tag_marriage","user_require_tag_accept_child","user_require_tag_trait","recommend_set_education","recommend_set_location"]]
2025-08-12 23:05:18 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/user/getInfo],耗时:[93]毫秒
2025-08-12 23:05:19 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/system/dict/data],耗时:[760]毫秒
2025-08-12 23:05:51 [boundedElastic-22] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:05:52 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[776]毫秒
2025-08-12 23:05:54 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:05:54 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[535]毫秒
2025-08-12 23:05:57 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:05:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[599]毫秒
2025-08-12 23:05:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:05:59 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[487]毫秒
2025-08-12 23:06:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:06:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[573]毫秒
2025-08-12 23:09:52 [boundedElastic-21] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:09:53 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[863]毫秒
2025-08-12 23:09:55 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:09:55 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[456]毫秒
2025-08-12 23:10:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:10:11 [reactor-http-nio-3] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[142]毫秒
2025-08-12 23:13:09 [boundedElastic-31] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:13:09 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[142]毫秒
2025-08-12 23:14:02 [boundedElastic-30] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:14:03 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[138]毫秒
2025-08-12 23:16:00 [boundedElastic-30] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:16:00 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[148]毫秒
2025-08-12 23:18:46 [boundedElastic-37] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:18:46 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[155]毫秒
2025-08-12 23:20:35 [boundedElastic-30] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:20:36 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[147]毫秒
2025-08-12 23:22:01 [boundedElastic-36] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:22:01 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[721]毫秒
2025-08-12 23:22:03 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 23:22:03 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[186]毫秒
2025-08-12 23:22:38 [boundedElastic-39] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 23:22:38 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[133]毫秒
2025-08-12 23:23:27 [boundedElastic-34] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 23:23:27 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[134]毫秒
2025-08-12 23:24:17 [boundedElastic-34] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/config/map],参数类型[json],参数:[null]
2025-08-12 23:24:17 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/config/map],耗时:[139]毫秒
2025-08-12 23:24:20 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/recommend/user/nearby],参数类型[json],参数:[null]
2025-08-12 23:24:21 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/recommend/user/nearby],耗时:[639]毫秒
2025-08-12 23:24:28 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:24:29 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[142]毫秒
2025-08-12 23:29:23 [boundedElastic-44] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:29:23 [reactor-http-nio-9] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[148]毫秒
2025-08-12 23:33:44 [boundedElastic-45] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:44 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[252]毫秒
2025-08-12 23:33:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[175]毫秒
2025-08-12 23:33:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:45 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[129]毫秒
2025-08-12 23:33:46 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:46 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[130]毫秒
2025-08-12 23:33:48 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:48 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[181]毫秒
2025-08-12 23:33:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:49 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[209]毫秒
2025-08-12 23:33:49 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:50 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[133]毫秒
2025-08-12 23:33:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:50 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[132]毫秒
2025-08-12 23:33:50 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:51 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[134]毫秒
2025-08-12 23:33:56 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:56 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[214]毫秒
2025-08-12 23:33:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:33:59 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[193]毫秒
2025-08-12 23:34:03 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:03 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[213]毫秒
2025-08-12 23:34:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:04 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[172]毫秒
2025-08-12 23:34:07 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:07 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[181]毫秒
2025-08-12 23:34:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:08 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[167]毫秒
2025-08-12 23:34:08 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:08 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[129]毫秒
2025-08-12 23:34:09 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:09 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[210]毫秒
2025-08-12 23:34:11 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:11 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[196]毫秒
2025-08-12 23:34:12 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:12 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[172]毫秒
2025-08-12 23:34:13 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:13 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[175]毫秒
2025-08-12 23:34:15 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:34:15 [reactor-http-nio-10] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[221]毫秒
2025-08-12 23:34:52 [boundedElastic-45] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/base],参数类型[json],参数:[{"nickName":"大伟","birthday":"2007-08-11","height":168,"weight":50,"edu":"2","job":"20","affectiveStatus":"2","revenue":"3","addrProvinceCode":"130000","addrCityCode":"130300","addrDistrictCode":"130303","addrStreetCode":"130303003","addr":"河北省秦皇岛市山海关区西关街道","addrNewProvinceCode":"140000","addrNewCityCode":"140400","addrNewDistrictCode":"140405","addrNewStreetCode":"140405103","addrNew":"山西省长治市屯留区余吾镇","sex":"0"}]
2025-08-12 23:34:57 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/base],耗时:[4958]毫秒
2025-08-12 23:35:54 [boundedElastic-46] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/base],参数类型[json],参数:[{"nickName":"大伟","birthday":"2007-08-11","height":168,"weight":50,"edu":"2","job":"20","affectiveStatus":"2","revenue":"3","addrProvinceCode":"130000","addrCityCode":"130300","addrDistrictCode":"130303","addrStreetCode":"130303003","addr":"河北省秦皇岛市山海关区西关街道","addrNewProvinceCode":"140000","addrNewCityCode":"140400","addrNewDistrictCode":"140405","addrNewStreetCode":"140405103","addrNew":"山西省长治市屯留区余吾镇","sex":"0"}]
2025-08-12 23:35:55 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/base],耗时:[1456]毫秒
2025-08-12 23:36:18 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:36:19 [reactor-http-nio-11] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[323]毫秒
2025-08-12 23:40:17 [boundedElastic-48] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:40:17 [reactor-http-nio-12] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[328]毫秒
2025-08-12 23:42:44 [boundedElastic-48] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:42:45 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[621]毫秒
2025-08-12 23:42:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:42:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[303]毫秒
2025-08-12 23:43:21 [boundedElastic-54] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:43:21 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[307]毫秒
2025-08-12 23:43:35 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:43:36 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[451]毫秒
2025-08-12 23:44:57 [boundedElastic-34] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:44:57 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[235]毫秒
2025-08-12 23:44:58 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:44:58 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[174]毫秒
2025-08-12 23:44:59 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:44:59 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[131]毫秒
2025-08-12 23:45:00 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:45:00 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[173]毫秒
2025-08-12 23:45:02 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:45:02 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[235]毫秒
2025-08-12 23:45:03 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:45:03 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[181]毫秒
2025-08-12 23:45:04 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:45:04 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[174]毫秒
2025-08-12 23:45:05 [reactor-http-nio-1] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/system/district/list],参数类型[json],参数:[null]
2025-08-12 23:45:05 [reactor-http-nio-2] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/system/district/list],耗时:[174]毫秒
2025-08-12 23:45:08 [boundedElastic-34] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/base],参数类型[json],参数:[{"nickName":"大伟","birthday":"2007-08-11","height":168,"weight":50,"edu":"2","job":"20","affectiveStatus":"2","revenue":"2","addrProvinceCode":"130000","addrCityCode":"130300","addrDistrictCode":"130303","addrStreetCode":"130303003","addr":"河北省秦皇岛市山海关区西关街道","addrNewProvinceCode":"150000","addrNewCityCode":"150500","addrNewDistrictCode":"150523","addrNewStreetCode":"150523104","addrNew":"内蒙古自治区通辽市开鲁县义和塔拉镇","sex":"0"}]
2025-08-12 23:51:20 [boundedElastic-52] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/base],参数类型[json],参数:[{"nickName":"大伟","birthday":"2007-08-11","height":168,"weight":50,"edu":"2","job":"20","affectiveStatus":"2","revenue":"2","addrProvinceCode":"130000","addrCityCode":"130300","addrDistrictCode":"130303","addrStreetCode":"130303003","addr":"河北省秦皇岛市山海关区西关街道","addrNewProvinceCode":"150000","addrNewCityCode":"150500","addrNewDistrictCode":"150523","addrNewStreetCode":"150523104","addrNew":"内蒙古自治区通辽市开鲁县义和塔拉镇","sex":"0"}]
2025-08-12 23:51:29 [reactor-http-nio-5] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/base],耗时:[9083]毫秒
2025-08-12 23:53:08 [boundedElastic-34] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:53:08 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[377]毫秒
2025-08-12 23:53:13 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:53:14 [reactor-http-nio-6] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[328]毫秒
2025-08-12 23:56:07 [boundedElastic-58] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:56:08 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[334]毫秒
2025-08-12 23:56:44 [boundedElastic-55] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:56:45 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[341]毫秒
2025-08-12 23:57:23 [boundedElastic-57] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:57:23 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[340]毫秒
2025-08-12 23:57:31 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:57:31 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[326]毫秒
2025-08-12 23:57:39 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:57:39 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[337]毫秒
2025-08-12 23:57:48 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:57:48 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[315]毫秒
2025-08-12 23:57:53 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:57:53 [reactor-http-nio-7] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[245]毫秒
2025-08-12 23:59:26 [boundedElastic-61] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /app-api/personals/user/base],参数类型[json],参数:[null]
2025-08-12 23:59:26 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /app-api/personals/user/base],耗时:[349]毫秒
2025-08-12 23:59:33 [reactor-http-nio-4] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /app-api/personals/user/update/base],参数类型[json],参数:[{"nickName":"大伟","birthday":"2007-08-11","height":168,"weight":53,"edu":"2","job":"20","affectiveStatus":"2","revenue":"2","addrProvinceCode":"130000","addrCityCode":"130300","addrDistrictCode":"130303","addrStreetCode":"130303003","addr":"河北省秦皇岛市山海关区西关街道","addrNewProvinceCode":"150000","addrNewCityCode":"150500","addrNewDistrictCode":"150523","addrNewStreetCode":"150523104","addrNew":"内蒙古自治区通辽市开鲁县义和塔拉镇","sex":"0"}]
2025-08-12 23:59:35 [reactor-http-nio-8] INFO  c.g.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /app-api/personals/user/update/base],耗时:[1770]毫秒
