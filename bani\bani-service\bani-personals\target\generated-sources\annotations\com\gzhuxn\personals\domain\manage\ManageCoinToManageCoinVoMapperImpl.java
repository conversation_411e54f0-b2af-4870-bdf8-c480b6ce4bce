package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.vo.ManageCoinVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageCoinToManageCoinVoMapperImpl implements ManageCoinToManageCoinVoMapper {

    @Override
    public ManageCoinVo convert(ManageCoin arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageCoinVo manageCoinVo = new ManageCoinVo();

        manageCoinVo.setId( arg0.getId() );
        manageCoinVo.setType( arg0.getType() );
        manageCoinVo.setSubType( arg0.getSubType() );
        manageCoinVo.setName( arg0.getName() );
        manageCoinVo.setCoin( arg0.getCoin() );

        return manageCoinVo;
    }

    @Override
    public ManageCoinVo convert(ManageCoin arg0, ManageCoinVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );
        arg1.setName( arg0.getName() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
