package com.gzhuxn.personals.controller.app.activity.bo;

import com.gzhuxn.personals.domain.activity.Activity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppActivityBoToActivityMapperImpl implements AppActivityBoToActivityMapper {

    @Override
    public Activity convert(AppActivityBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Activity activity = new Activity();

        activity.setId( arg0.getId() );
        activity.setGroupId( arg0.getGroupId() );
        activity.setName( arg0.getName() );
        activity.setEnrollStartTime( arg0.getEnrollStartTime() );
        activity.setEnrollEndTime( arg0.getEnrollEndTime() );
        activity.setStartTime( arg0.getStartTime() );
        activity.setEndTime( arg0.getEndTime() );
        activity.setTimeLength( arg0.getTimeLength() );
        activity.setRefundTime( arg0.getRefundTime() );
        activity.setOfficialFlag( arg0.getOfficialFlag() );
        activity.setIntroduce( arg0.getIntroduce() );
        activity.setOriginalAmount( arg0.getOriginalAmount() );
        activity.setAmount( arg0.getAmount() );
        activity.setStatus( arg0.getStatus() );
        activity.setType( arg0.getType() );
        activity.setLon( arg0.getLon() );
        activity.setLat( arg0.getLat() );

        return activity;
    }

    @Override
    public Activity convert(AppActivityBo arg0, Activity arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
