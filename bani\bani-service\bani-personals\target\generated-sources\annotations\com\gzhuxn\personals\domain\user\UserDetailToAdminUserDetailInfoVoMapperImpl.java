package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.admin.user.vo.AdminUserDetailInfoVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAdminUserDetailInfoVoMapperImpl implements UserDetailToAdminUserDetailInfoVoMapper {

    @Override
    public AdminUserDetailInfoVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminUserDetailInfoVo adminUserDetailInfoVo = new AdminUserDetailInfoVo();

        adminUserDetailInfoVo.setUserId( arg0.getUserId() );
        adminUserDetailInfoVo.setNickName( arg0.getNickName() );
        adminUserDetailInfoVo.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            adminUserDetailInfoVo.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        adminUserDetailInfoVo.setPhoneNumber( arg0.getPhoneNumber() );
        adminUserDetailInfoVo.setPid( arg0.getPid() );
        adminUserDetailInfoVo.setBirthday( arg0.getBirthday() );
        adminUserDetailInfoVo.setStar( arg0.getStar() );
        adminUserDetailInfoVo.setAnimal( arg0.getAnimal() );
        adminUserDetailInfoVo.setHeight( arg0.getHeight() );
        adminUserDetailInfoVo.setWeight( arg0.getWeight() );
        adminUserDetailInfoVo.setEdu( arg0.getEdu() );
        adminUserDetailInfoVo.setJob( arg0.getJob() );
        adminUserDetailInfoVo.setRevenue( arg0.getRevenue() );
        adminUserDetailInfoVo.setWechat( arg0.getWechat() );
        adminUserDetailInfoVo.setAddr( arg0.getAddr() );
        adminUserDetailInfoVo.setAddrNew( arg0.getAddrNew() );
        adminUserDetailInfoVo.setProgress( arg0.getProgress() );
        adminUserDetailInfoVo.setAuditStatus( arg0.getAuditStatus() );
        adminUserDetailInfoVo.setIsIdentity( arg0.getIsIdentity() );
        adminUserDetailInfoVo.setUserLevel( arg0.getUserLevel() );
        adminUserDetailInfoVo.setIsMatched( arg0.getIsMatched() );
        if ( arg0.getStatus() != null ) {
            adminUserDetailInfoVo.setStatus( String.valueOf( arg0.getStatus() ) );
        }

        return adminUserDetailInfoVo;
    }

    @Override
    public AdminUserDetailInfoVo convert(UserDetail arg0, AdminUserDetailInfoVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            arg1.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        else {
            arg1.setAvatar( null );
        }
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setIsIdentity( arg0.getIsIdentity() );
        arg1.setUserLevel( arg0.getUserLevel() );
        arg1.setIsMatched( arg0.getIsMatched() );
        if ( arg0.getStatus() != null ) {
            arg1.setStatus( String.valueOf( arg0.getStatus() ) );
        }
        else {
            arg1.setStatus( null );
        }

        return arg1;
    }
}
