package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.user.AppRecommendUserPageVo;
import com.gzhuxn.personals.controller.app.recommend.vo.user.AppRecommendUserPageVoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserDetailBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserFullBaseBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserLocationBoToUserDetailMapper;
import com.gzhuxn.personals.controller.app.user.vo.AppUserBaseVoToUserDetailMapper;
import com.gzhuxn.personals.domain.user.bo.UserDetailBoToUserDetailMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserDetailBoToUserDetailMapper.class,AppUserFullBaseBoToUserDetailMapper.class,AppUserLocationBoToUserDetailMapper.class,AppRecommendUserPageVoToUserDetailMapper.class,UserDetailBoToUserDetailMapper.class,AppUserBaseVoToUserDetailMapper.class,AppUserBaseBoToUserDetailMapper.class,UserDetailToAppUserFullBaseVoMapper.class,UserDetailToUserDetailAuditVoMapper.class,UserDetailToAppUserMyProfileVoMapper.class,UserDetailToUserDetailVoMapper.class,UserDetailToAppUserEditDetailVoMapper.class,UserDetailToAdminUserDetailInfoVoMapper.class,UserDetailToAdminContentAuditUserDetailVoMapper.class,UserDetailToAppUserBaseVoMapper.class,UserDetailToAdminUserDetailPageVoMapper.class},
    imports = {}
)
public interface UserDetailToAppRecommendUserPageVoMapper extends BaseMapper<UserDetail, AppRecommendUserPageVo> {
  @Mapping(
      target = "addrNewProvince",
      source = "addrNewProvinceCode"
  )
  @Mapping(
      target = "addrProvince",
      source = "addrProvinceCode"
  )
  AppRecommendUserPageVo convert(UserDetail source);

  @Mapping(
      target = "addrNewProvince",
      source = "addrNewProvinceCode"
  )
  @Mapping(
      target = "addrProvince",
      source = "addrProvinceCode"
  )
  AppRecommendUserPageVo convert(UserDetail source, @MappingTarget AppRecommendUserPageVo target);
}
