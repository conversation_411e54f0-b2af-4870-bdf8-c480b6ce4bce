package com.gzhuxn.personals.controller.app.activity.bo;

import com.gzhuxn.personals.domain.activity.ActivityFee;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppActivityFeeBoToActivityFeeMapperImpl implements AppActivityFeeBoToActivityFeeMapper {

    @Override
    public ActivityFee convert(AppActivityFeeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActivityFee activityFee = new ActivityFee();

        activityFee.setId( arg0.getId() );
        activityFee.setActivityId( arg0.getActivityId() );
        activityFee.setType( arg0.getType() );
        activityFee.setBusinessId( arg0.getBusinessId() );
        activityFee.setAmount( arg0.getAmount() );

        return activityFee;
    }

    @Override
    public ActivityFee convert(AppActivityFeeBo arg0, ActivityFee arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setAmount( arg0.getAmount() );

        return arg1;
    }
}
