package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.vo.ManageVipVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageVipToManageVipVoMapperImpl implements ManageVipToManageVipVoMapper {

    @Override
    public ManageVipVo convert(ManageVip arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageVipVo manageVipVo = new ManageVipVo();

        manageVipVo.setId( arg0.getId() );
        manageVipVo.setMonths( arg0.getMonths() );
        manageVipVo.setOriginalAmount( arg0.getOriginalAmount() );
        manageVipVo.setAmount( arg0.getAmount() );
        manageVipVo.setCoin( arg0.getCoin() );
        manageVipVo.setSellNum( arg0.getSellNum() );
        manageVipVo.setStatus( arg0.getStatus() );

        return manageVipVo;
    }

    @Override
    public ManageVipVo convert(ManageVip arg0, ManageVipVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMonths( arg0.getMonths() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setSellNum( arg0.getSellNum() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
