package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserQuestionApply;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface UserQuestionApplyBoToUserQuestionApplyMapper extends BaseMapper<UserQuestionApplyBo, UserQuestionApply> {
}
