"use strict";const t=require("../../common/vendor.js"),s=require("../../utils/request.js");function d(e){return s.request({url:"/personals/recommend/user/same-city",method:"get",params:e}).catch(r=>(t.index.__f__("error","at api/recommend/recommend.js:25","同城用户列表API调用失败:",r),{code:0,msg:"网络错误，请稍后重试",rows:[],total:0}))}function c(e){return s.request({url:"/personals/recommend/user/nearby",method:"get",params:e}).catch(r=>(t.index.__f__("error","at api/recommend/recommend.js:61","附近用户列表API调用失败:",r),{code:0,msg:"网络错误，请稍后重试",rows:[],total:0}))}function i(e){return{id:e.oppUserId,avatar:e.oppAvatar,nickname:e.opp<PERSON><PERSON><PERSON><PERSON>||"用户",gender:e.oppSex==="0"?"male":"female",isVerified:e.oppIsIdentity||!1,currentCity:e.oppCity||"未知",hometown:e.addrProvince||"未知",age:parseInt(e.oppAge)||18,height:parseInt(e.oppHeight)||170,education:e.edu||"本科",occupation:e.job||"职员",distance:e.distance>1?e.distance.toFixed(2)+"km":"<1km",isFollowed:e.oppIsFollowed||!1,isMe:e.oppIsMe||!1,imageHeight:250+Math.floor(Math.random()*100)}}function u(e=1,r=10,o={}){return{pageNum:e,pageSize:r,isMatched:null,...o}}function l(e=1,r=10,o,n,m={}){return{pageNum:e,pageSize:r,isMatched:null,lon:o,lat:n,distance:100,...m}}exports.buildNearbyParams=l;exports.buildSameCityParams=u;exports.getNearbyUsers=c;exports.getSameCityUsers=d;exports.transformUserData=i;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/recommend/recommend.js.map
