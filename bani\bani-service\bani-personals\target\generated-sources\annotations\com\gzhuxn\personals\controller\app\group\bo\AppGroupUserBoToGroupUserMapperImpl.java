package com.gzhuxn.personals.controller.app.group.bo;

import com.gzhuxn.personals.domain.group.GroupUser;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppGroupUserBoToGroupUserMapperImpl implements AppGroupUserBoToGroupUserMapper {

    @Override
    public GroupUser convert(AppGroupUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupUser groupUser = new GroupUser();

        groupUser.setId( arg0.getId() );
        groupUser.setGroupId( arg0.getGroupId() );
        groupUser.setUserId( arg0.getUserId() );
        groupUser.setAdminFlag( arg0.getAdminFlag() );

        return groupUser;
    }

    @Override
    public GroupUser convert(AppGroupUserBo arg0, GroupUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAdminFlag( arg0.getAdminFlag() );

        return arg1;
    }
}
