package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgContentUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentUserToAppMsgContentUserVoMapperImpl implements MsgContentUserToAppMsgContentUserVoMapper {

    @Override
    public AppMsgContentUserVo convert(MsgContentUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppMsgContentUserVo appMsgContentUserVo = new AppMsgContentUserVo();

        appMsgContentUserVo.setId( arg0.getId() );
        appMsgContentUserVo.setContentId( arg0.getContentId() );

        return appMsgContentUserVo;
    }

    @Override
    public AppMsgContentUserVo convert(MsgContentUser arg0, AppMsgContentUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContentId( arg0.getContentId() );

        return arg1;
    }
}
