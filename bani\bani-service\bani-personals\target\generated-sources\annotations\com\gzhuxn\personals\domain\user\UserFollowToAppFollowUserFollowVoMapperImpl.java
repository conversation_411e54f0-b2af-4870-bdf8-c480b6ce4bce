package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.follow.AppFollowUserFollowVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserFollowToAppFollowUserFollowVoMapperImpl implements UserFollowToAppFollowUserFollowVoMapper {

    @Override
    public AppFollowUserFollowVo convert(UserFollow source) {
        if ( source == null ) {
            return null;
        }

        AppFollowUserFollowVo appFollowUserFollowVo = new AppFollowUserFollowVo();

        appFollowUserFollowVo.setUid( source.getBusinessId() );
        appFollowUserFollowVo.setOppUserId( source.getBusinessId() );
        appFollowUserFollowVo.setId( source.getId() );
        appFollowUserFollowVo.setCreateTime( source.getCreateTime() );

        return appFollowUserFollowVo;
    }

    @Override
    public AppFollowUserFollowVo convert(UserFollow source, AppFollowUserFollowVo target) {
        if ( source == null ) {
            return target;
        }

        target.setUid( source.getBusinessId() );
        target.setOppUserId( source.getBusinessId() );
        target.setId( source.getId() );
        target.setCreateTime( source.getCreateTime() );

        return target;
    }
}
