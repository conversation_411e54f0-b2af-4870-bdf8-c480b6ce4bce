<template>
	<scroll-nav-page title="写动态" :show-back="true">
		<template #content>
			<view class="add-moment-container">
				<!-- 主要内容 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<!-- 文本输入区域 -->
					<view class="text-input-section">
						<textarea class="moment-textarea" v-model="momentForm.content" placeholder="分享新鲜事..."
							:maxlength="500" :auto-height="true" :show-confirm-bar="false"></textarea>
						<view class="char-count">
							<text class="count-text">{{ momentForm.content.length }}/255</text>
						</view>
					</view>

					<!-- 图片选择区域 -->
					<view class="image-section">
						<images-select :limit="6" showTip="true" type="user_comment.images" v-model="imageFiles" />
					</view>

					<!-- 功能选项区域 -->
					<view class="options-section">
						<view class="option-item" @click="showTopicModal">
							<view class="option-icon">
								<uni-icons type="chat" size="24" color="#696CF3"></uni-icons>
							</view>
							<view class="option-content">
								<text class="option-label">选择话题</text>
								<view class="selected-topics" v-if="selectedTopics.length > 0">
									<text class="topic-tag" v-for="topic in selectedTopics" :key="topic.id">
										#{{ topic.name }}
									</text>
								</view>
								<text class="option-placeholder" v-else>添加话题，让更多人看到</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>

						<view class="option-item" @click="showLocationSelect">
							<view class="option-icon">
								<uni-icons type="location" size="24" color="#696CF3"></uni-icons>
							</view>
							<view class="option-content">
								<text class="option-label">所在位置</text>
								<text class="option-value" v-if="momentForm.location">{{ momentForm.location }}</text>
								<text class="option-placeholder" v-else>你在哪里？</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>

						<view class="option-item" @click="showVisibilityPicker">
							<view class="option-icon">
								<uni-icons type="eye" size="24" color="#696CF3"></uni-icons>
							</view>
							<view class="option-content">
								<text class="option-label">谁可以看</text>
								<text class="option-value">{{ getVisibilityLabel(momentForm.visibility) }}</text>
							</view>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
					<!-- 发布按钮区域 -->
					<view class="publish-area">
						<button class="publish-btn" @click="publishMoment" :disabled="!canPublish">
							<text class="publish-text">发布</text>
						</button>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>


	<!-- 选择话题弹窗 -->
	<uni-popup ref="topicPopup" type="bottom" :mask-click="true">
		<view class="topic-modal">
			<view class="modal-header">
				<text class="modal-title">选择话题</text>
				<uni-icons type="close" size="20" color="#999" @click="closeTopicModal"></uni-icons>
			</view>
			<view class="topic-list">
				<!-- 加载状态 -->
				<view class="loading-container" v-if="isLoadingTopics">
					<view class="loading-content">
						<uni-icons type="spinner-cycle" size="24" color="#696CF3"></uni-icons>
						<text class="loading-text">加载话题中...</text>
					</view>
				</view>

				<!-- 话题列表 -->
				<view v-else-if="topicList.length > 0">
					<view class="topic-item" :class="{ selected: isTopicSelected(topic) }" v-for="topic in topicList"
						:key="topic.id" @click="toggleTopic(topic)">
						<view class="topic-info">
							<text class="topic-name"># {{ topic.name }}</text>
						</view>
						<view class="topic-check" v-if="isTopicSelected(topic)">
							<uni-icons type="checkmarkempty" size="20" color="#696CF3"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-container" v-else>
					<view class="empty-content">
						<uni-icons type="chat" size="48" color="#ccc"></uni-icons>
						<text class="empty-text">暂无话题</text>
						<button class="retry-btn" @click="loadTopicList">重新加载</button>
					</view>
				</view>
			</view>

			<!-- 确认按钮 -->
			<view class="topic-confirm" v-if="selectedTopics.length > 0">
				<button class="confirm-btn" @click="closeTopicModal">
					<text class="confirm-text">确定 ({{ selectedTopics.length }})</text>
				</button>
			</view>
		</view>
	</uni-popup>

	<!-- 地址选择组件 -->
	<location-select ref="LocationSelectRef" @select="handleLocationSelect" />

	<!-- 可见性选择弹窗 -->
	<uni-popup ref="visibilityPopupRef" type="bottom" background-color="#fff">
		<view class="visibility-popup">
			<view class="popup-header">
				<text class="popup-title">谁可以看</text>
				<view class="popup-close" @click="hideVisibilityPicker">
					<uni-icons type="close" size="20" color="#999"></uni-icons>
				</view>
			</view>

			<view class="visibility-options">
				<view v-for="option in visibilityOptions" :key="option.value" class="visibility-option"
					:class="{ 'selected': momentForm.visibility === option.value }"
					@click="selectVisibility(option.value)">
					<view class="option-info">
						<view class="option-icon">
							<uni-icons :type="option.icon" size="24"
								:color="momentForm.visibility === option.value ? '#696CF3' : '#999'"></uni-icons>
						</view>
						<view class="option-text">
							<text class="option-title">{{ option.label }}</text>
							<text class="option-desc">{{ option.description }}</text>
						</view>
					</view>
					<view class="option-check" v-if="momentForm.visibility === option.value">
						<uni-icons type="checkmarkempty" size="20" color="#696CF3"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onPageScroll } from '@dcloudio/uni-app'
import { getMomentTagList } from '@/api/manage/tag'
import { createMoment, VISIBILITY_STATUS, VISIBILITY_STATUS_NAMES } from '@/api/moment/moment'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)

// 动态表单数据
const momentForm = reactive({
	content: '',
	location: '',
	visibility: VISIBILITY_STATUS.ALL
})

// 图片文件列表
const imageFiles = ref([])

// 选中的话题（支持多选）
const selectedTopics = ref([])

// 话题列表
const topicList = ref([])
const isLoadingTopics = ref(false)

// 弹窗引用
const topicPopup = ref(null)
const LocationSelectRef = ref(null)
const visibilityPopupRef = ref(null)

// 位置相关数据
const selectedLocationData = ref(null) // 存储完整的位置信息（包含经纬度）

// 可见性选项
const visibilityOptions = ref([
	{
		value: VISIBILITY_STATUS.ALL,
		label: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.ALL],
		description: '所有人都可以看到这条动态',
		icon: 'eye'
	},
	{
		value: VISIBILITY_STATUS.FOLLOWERS,
		label: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.FOLLOWERS],
		description: '只有关注我的人可以看到',
		icon: 'heart'
	},
	{
		value: VISIBILITY_STATUS.MUTUAL,
		label: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.MUTUAL],
		description: '只有互相关注的人可以看到',
		icon: 'heart-filled'
	},
	{
		value: VISIBILITY_STATUS.PRIVATE,
		label: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.PRIVATE],
		description: '只有自己可以看到',
		icon: 'locked'
	}
])

// 是否可以发布
const canPublish = computed(() => {
	return momentForm.content.trim().length > 0 || imageFiles.value.length > 0
})

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})
// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333' : '#fff'
}

// 加载话题列表
const loadTopicList = () => {
	isLoadingTopics.value = true
	getMomentTagList().then(response => {
		// 转换API数据格式
		topicList.value = response.data.map(item => ({
			id: item.id,
			name: item.name,
			icon: item.icon
		}))
	}).finally(() => {
		isLoadingTopics.value = false
	})
}

// 显示话题选择弹窗
const showTopicModal = async () => {
	// 如果话题列表为空，先加载话题列表
	if (topicList.value.length === 0) {
		loadTopicList()
	}
	topicPopup.value.open()
}

// 关闭话题选择弹窗
const closeTopicModal = () => {
	topicPopup.value.close()
}

// 切换话题选择状态
const toggleTopic = (topic) => {
	const index = selectedTopics.value.findIndex(t => t.id === topic.id)
	if (index > -1) {
		// 如果已选中，则取消选择
		selectedTopics.value.splice(index, 1)
	} else {
		// 如果未选中，则添加到选中列表
		selectedTopics.value.push(topic)
	}
}

// 检查话题是否已选中
const isTopicSelected = (topic) => {
	return selectedTopics.value.some(t => t.id === topic.id)
}

// 显示位置选择器
const showLocationSelect = () => {
	if (LocationSelectRef.value) {
		LocationSelectRef.value.open()
	} else {
		uni.showToast({
			title: '组件加载失败，请重试',
			icon: 'none'
		})
	}
}

// 处理位置选择
const handleLocationSelect = (locationData) => {
	momentForm.location = locationData.name
	selectedLocationData.value = locationData
}

// 显示可见性选择器
const showVisibilityPicker = () => {
	if (visibilityPopupRef.value) {
		visibilityPopupRef.value.open()
	}
}

// 隐藏可见性选择器
const hideVisibilityPicker = () => {
	if (visibilityPopupRef.value) {
		visibilityPopupRef.value.close()
	}
}

// 选择可见性
const selectVisibility = (value) => {
	momentForm.visibility = value
	hideVisibilityPicker()
}

// 获取可见性标签
const getVisibilityLabel = (value) => {
	const option = visibilityOptions.value.find(item => item.value === value)
	return option ? option.label : '所有人'
}


// 发布动态
const publishMoment = async () => {
	if (!canPublish.value) {
		uni.showToast({
			title: '请输入内容或选择图片',
			icon: 'none'
		})
		return
	}
	try {
		// 准备提交数据
		const submitData = {
			content: momentForm.content,
			images: imageFiles.value.map(file => file.ossId).join(','), // 图片ID，用逗号分隔
			visibilityStatus: momentForm.visibility,
			tagIds: selectedTopics.value.map(topic => topic.id) // 话题ID数组
		}

		// 如果有位置信息，添加位置相关字段
		if (selectedLocationData.value) {
			const location = selectedLocationData.value
			submitData.provinceName = location.province || ''
			submitData.cityName = location.city || ''
			submitData.districtName = location.district || ''
			submitData.location = location.city + "·" + momentForm.location
			submitData.lon = location.longitude || 0
			submitData.lat = location.latitude || 0
		}

		console.log('发布动态数据:', submitData)

		// 调用API发布动态
		const response = await createMoment(submitData)

		if (response.code === 200) {
			uni.showToast({
				title: '发布成功',
				icon: 'success'
			})

			// 返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		} else {
			uni.showToast({
				title: response.msg || '发布失败，请重试',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('发布动态失败:', error)
		uni.showToast({
			title: '发布失败，请检查网络连接',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.add-moment-container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.main-container {
	margin-top: 20rpx;
	min-height: calc(100vh - var(--nav-height, 88px));
	box-sizing: border-box;
	padding: 20rpx;
	padding-bottom: 40rpx;
}

/* 发布按钮区域 */
.publish-area {
	padding: 24rpx;
	border-top: 1rpx solid #f5f5f5;
	background: #fff;
}

/* 发布按钮样式 */
.publish-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
	border: none;
	border-radius: 44rpx;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba($primary-color, 0.3);
}

.publish-btn:disabled {
	background: #ccc;
	box-shadow: none;
}

.publish-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 10rpx rgba($primary-color, 0.4);
}

.publish-btn::after {
	border: none;
}

.publish-text {
	font-size: 32rpx;
	color: #fff;
	font-weight: 600;
}

/* 文本输入区域 */
.text-input-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.moment-textarea {
	width: 100%;
	min-height: 200rpx;
	font-size: 32rpx;
	line-height: 1.6;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}

.char-count {
	display: flex;
	justify-content: flex-end;
	margin-top: 16rpx;
}

.count-text {
	font-size: 24rpx;
	color: #999;
}

/* 图片选择区域 */
.image-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* uni-file-picker 样式定制 */
:deep(.uni-file-picker) {
	.file-picker__progress {
		border-radius: 8rpx;
	}
}

/* 功能选项区域 */
.options-section {
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	margin-bottom: 20rpx;
}

.option-item {
	display: flex;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
	transition: all 0.3s ease;
}

.option-item:last-child {
	border-bottom: none;
}

.option-item:active {
	background: #f8f9fa;
}

.option-icon {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.option-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.option-label {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.option-value {
	font-size: 26rpx;
	color: $primary-color;
}

.option-placeholder {
	font-size: 26rpx;
	color: #999;
}

/* 选中话题标签 */
.selected-topics {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
	margin-top: 8rpx;
}

.topic-tag {
	background: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(155, 157, 245, 0.15));
	color: #696CF3;
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(105, 108, 243, 0.2);
}

/* 话题选择弹窗 */
.topic-modal {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.topic-list {
	max-height: 60vh;
	overflow-y: auto;
	padding: 16rpx 0;

	/* 自定义滚动条 */
	&::-webkit-scrollbar {
		width: 6rpx;
	}

	&::-webkit-scrollbar-track {
		background: #f5f5f5;
		border-radius: 3rpx;
	}

	&::-webkit-scrollbar-thumb {
		background: #d0d0d0;
		border-radius: 3rpx;

		&:hover {
			background: #b0b0b0;
		}
	}
}

.topic-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
	transition: all 0.3s ease;
}

.topic-item:last-child {
	border-bottom: none;
}

.topic-item:active {
	background: #f8f9fa;
}

.topic-item.selected {
	background: rgba(105, 108, 243, 0.05);
	border-left: 4rpx solid #696CF3;
}

.topic-check {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 44rpx;
	height: 44rpx;
	background: linear-gradient(135deg, #696CF3 0%, lighten(#696CF3, 10%) 100%);
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.3);
	position: relative;
	z-index: 1;
	animation: checkIn 0.3s ease;
}

@keyframes checkIn {
	0% {
		transform: scale(0);
		opacity: 0;
	}

	50% {
		transform: scale(1.2);
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.topic-info {
	display: flex;
	flex-direction: column;
}

.topic-name {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.4;
	position: relative;
	z-index: 1;
}



/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 80rpx 40rpx;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 空状态 */
.empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 80rpx 40rpx;
}

.empty-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 16rpx;
}

.retry-btn {
	background: #696CF3;
	color: white;
	border: none;
	padding: 16rpx 32rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
}

/* 话题确认按钮 */
.topic-confirm {
	padding: 20rpx 24rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #fff;
}

.confirm-btn {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
	border: none;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.confirm-btn::after {
	border: none;
}

.confirm-btn:active {
	transform: scale(0.98);
}

.confirm-text {
	font-size: 30rpx;
	color: #fff;
	font-weight: 600;
}

/* 可见性选择弹窗样式 */
.visibility-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 0;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.popup-close {
	padding: 8rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.popup-close:active {
	background: #f0f0f0;
}

.visibility-options {
	padding: 24rpx 0;
}

.visibility-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 32rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.visibility-option:active {
	background: #f8f8f8;
}

.visibility-option.selected {
	background: rgba(105, 108, 243, 0.05);
}

.option-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.option-icon {
	margin-right: 24rpx;
}

.option-text {
	flex: 1;
}

.option-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.option-desc {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.option-check {
	margin-left: 16rpx;
}
</style>
