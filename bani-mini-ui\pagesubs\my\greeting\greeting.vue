<template>
	<scroll-nav-page title="打招呼记录" :show-back="true" @heightChange="handleNavHeightChange">
		<template #content>
			<z-paging ref="paging" v-model="datas" :auto="true" :refresher-enabled="true" :loading-more-enabled="true"
				@query="queryList">
				<template #top>
					<!-- 顶部菜单 -->
					<view class="top-menu margin-split" :style="{ paddingTop: navBarHeight + 'px' }">
						<uni-segmented-control :current="segmentedIndex" :values="['想认识我的', '我想认识的']"
							@clickItem="switchTab" styleType="text" activeColor="#696CF3"></uni-segmented-control>
					</view>
				</template>
				<!-- 自定义刷新组件 -->
				<template #refresher="{ refresherStatus }">
					<custom-refresher :refresher-status="refresherStatus" />
				</template>
				<!-- 打招呼记录列表 -->
				<view class="greeting-list margin-split">
					<view class="greeting-card" v-for="(item, index) in datas" :key="item.id">
						<!-- 卡片内容 -->
						<view class="card-content">
							<view class="user-section">
								<image class="avatar" :src="item.oppAvatar"></image>
								<view class="user-info">
									<!-- 昵称与时间同一行 -->
									<view class="name-time-row">
										<view class="name-icons">
											<text class="nick-name">{{ item.oppNickName }}</text>
											<!-- 性别图标 -->
											<text class="iconfont gender-icon"
												:class="item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'"
												:style="{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }"></text>
											<text v-if="item.oppIsIdentity" class="verified-tag">已实名</text>
										</view>
										<text class="time">{{ item.time }}</text>
									</view>
									<text class="user-detail">{{ item.oppAge }} · {{ item.oppHeight }} · {{ item.oppCity
									}}</text>
								</view>
							</view>

							<!-- 打招呼内容 -->
							<view class="greeting-section">
								<view class="greeting-content">
									<text class="greeting-text">{{ item.content || '向你打招呼' }}</text>
								</view>
								<!-- 状态标识 -->
								<view class="greeting-status" v-if="item.status">
									<text class="status-text" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</text>
								</view>
							</view>

							<!-- 回复内容 (仅在已回复状态下显示) -->
							<view class="reply-content" v-if="item.status === 1 && item.replyContent">
								<text class="reply-label">回复：</text>
								<text class="reply-text">{{ item.replyContent }}</text>
							</view>

							<!-- 操作按钮区域 (仅收到的打招呼显示) -->
							<view class="action-section" v-if="segmentedIndex === 0 && item.status === 0">
								<button class="action-btn ignore-btn" @click="handleIgnore(item.id)">忽略</button>
								<button class="action-btn reply-btn" @click="handleReply(item.id)">回复</button>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<template #empty>
					<view class="empty-state">
						<image class="empty-icon" src="/static/image/empty.png" mode="aspectFit"></image>
						<text class="empty-text">暂无打招呼记录</text>
					</view>
				</template>
			</z-paging>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onPageScroll } from "@dcloudio/uni-app"
import { getReceivedGreetingPage, getSentGreetingPage, replyGreeting, ignoreGreeting } from '@/api/user/greeting'

// 导航栏相关
const pageScrollTop = ref(0)
const navBarHeight = ref(0) // 初始为0，等待组件传递真实高度

// 页面数据
const segmentedIndex = ref(0)
const queryType = ref('received') // 'received'-收到的打招呼、'sent'-发送的打招呼

// z-paging组件
const paging = ref(null)
const datas = ref([])

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

onLoad((param) => {
	// 默认显示收到的打招呼
	if (!param.type) {
		param.type = 'received'
	}
	const type = param.type
	segmentedIndex.value = type === 'received' ? 0 : 1 // 'received'-想认识我的(index=0), 'sent'-我想认识的(index=1)
	queryType.value = type
})

function queryList(pageNum, pageSize) {
	const apiFunction = queryType.value === 'received' ? getReceivedGreetingPage : getSentGreetingPage

	apiFunction({
		pageNum: pageNum,
		pageSize: pageSize,
	}).then(res => {
		paging.value.complete(res.rows);
	})
}

const switchTab = (e) => {
	if (segmentedIndex.value !== e.currentIndex) {
		segmentedIndex.value = e.currentIndex
		// 0-想认识我的(queryType='received'), 1-我想认识的(queryType='sent')
		queryType.value = segmentedIndex.value === 0 ? 'received' : 'sent'
		paging.value.reload()
	}
}

// 获取状态文本
const getStatusText = (status) => {
	switch (status) {
		case 0:
			return '待回复'
		case 1:
			return '已回复'
		case 2:
			return '已忽略'
		default:
			return ''
	}
}

// 获取状态样式类
const getStatusClass = (status) => {
	switch (status) {
		case 0:
			return 'status-pending'
		case 1:
			return 'status-replied'
		case 2:
			return 'status-ignored'
		default:
			return ''
	}
}

// 处理回复
const handleReply = (id) => {
	uni.showModal({
		title: '回复打招呼',
		editable: true,
		placeholderText: '请输入回复内容...',
		success: (res) => {
			if (res.confirm && res.content) {
				replyGreeting(id, res.content).then(() => {
					uni.showToast({
						title: '回复成功',
						icon: 'success'
					})
					paging.value.reload()
				}).catch(err => {
					uni.showToast({
						title: err.message || '回复失败',
						icon: 'none'
					})
				})
			}
		}
	})
}

// 处理忽略
const handleIgnore = (id) => {
	uni.showModal({
		title: '确认忽略',
		content: '确定要忽略这条打招呼吗？',
		success: (res) => {
			if (res.confirm) {
				ignoreGreeting(id).then(() => {
					uni.showToast({
						title: '已忽略',
						icon: 'success'
					})
					paging.value.reload()
				}).catch(err => {
					uni.showToast({
						title: err.message || '操作失败',
						icon: 'none'
					})
				})
			}
		}
	})
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

// z-paging组件样式
:deep(.z-paging-content) {
	min-height: calc(100vh - 200px);
}

.greeting-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.greeting-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	overflow: hidden;
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.12);
	}

	.card-content {
		padding: 24rpx;

		.user-section {
			display: flex;
			align-items: flex-start;
			gap: 16rpx;
			margin-bottom: 16rpx;

			.avatar {
				height: 80rpx;
				width: 80rpx;
				border-radius: 40rpx;
				box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.15);
				border: 2rpx solid rgba(255, 255, 255, 0.8);
				flex-shrink: 0;
			}

			.user-info {
				flex: 1;
				min-width: 0;

				.name-time-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8rpx;

					.name-icons {
						display: flex;
						align-items: center;
						gap: 6rpx;

						.nick-name {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
							letter-spacing: 0.5rpx;
						}

						.gender-icon {
							margin-left: 4rpx;
						}

						.verified-tag {
							background: #696CF3;
							color: white;
							font-size: 20rpx;
							padding: 2rpx 6rpx;
							border-radius: 8rpx;
							font-weight: 500;
							margin-left: 4rpx;
						}
					}

					.time {
						font-size: 22rpx;
						color: #999;
						font-weight: 400;
						flex-shrink: 0;
					}
				}

				.user-detail {
					font-size: 24rpx;
					color: #666;
					line-height: 1.4;
				}
			}
		}

		.greeting-section {
			background: rgba($primary-color, 0.05);
			border-radius: 12rpx;
			padding: 16rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;

			.greeting-content {
				flex: 1;

				.greeting-text {
					font-size: 26rpx;
					color: #333;
					line-height: 1.4;
				}
			}

			.greeting-status {
				flex-shrink: 0;
				margin-left: 16rpx;

				.status-text {
					font-size: 22rpx;
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
					font-weight: 500;

					&.status-pending {
						color: #FF8C00;
						background: rgba(255, 140, 0, 0.1);
					}

					&.status-replied {
						color: #4CAF50;
						background: rgba(76, 175, 80, 0.1);
					}

					&.status-ignored {
						color: #999;
						background: rgba(153, 153, 153, 0.1);
					}
				}
			}
		}

		.reply-content {
			margin: 12rpx 0;
			padding: 16rpx;
			background: rgba($primary-color, 0.05);
			border-radius: 12rpx;
			border-left: 4rpx solid $primary-color;

			.reply-label {
				font-size: 24rpx;
				color: $primary-color;
				font-weight: 600;
				margin-right: 8rpx;
			}

			.reply-text {
				font-size: 26rpx;
				color: #555;
				line-height: 1.4;
				word-break: break-all;
			}
		}

		.action-section {
			display: flex;
			gap: 16rpx;
			justify-content: flex-end;

			.action-btn {
				padding: 12rpx 24rpx;
				border-radius: 24rpx;
				font-size: 24rpx;
				font-weight: 500;
				border: none;
				cursor: pointer;
				transition: all 0.3s ease;

				&.ignore-btn {
					background: rgba(153, 153, 153, 0.1);
					color: #999;

					&:hover {
						background: rgba(153, 153, 153, 0.2);
					}
				}

				&.reply-btn {
					background: $primary-color;
					color: white;

					&:hover {
						background: darken($primary-color, 10%);
					}
				}
			}
		}
	}
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 32rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		font-weight: 500;
	}
}
</style>

<style>
@import '@/static/fonts/iconfont.css';
</style>