package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserMoment;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentBoToUserMomentMapperImpl implements UserMomentBoToUserMomentMapper {

    @Override
    public UserMoment convert(UserMomentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMoment userMoment = new UserMoment();

        userMoment.setSearchValue( arg0.getSearchValue() );
        userMoment.setCreateBy( arg0.getCreateBy() );
        userMoment.setCreateTime( arg0.getCreateTime() );
        userMoment.setUpdateBy( arg0.getUpdateBy() );
        userMoment.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userMoment.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userMoment.setCreateDept( arg0.getCreateDept() );
        userMoment.setId( arg0.getId() );
        userMoment.setUserId( arg0.getUserId() );
        userMoment.setContent( arg0.getContent() );
        userMoment.setImages( arg0.getImages() );
        userMoment.setStatus( arg0.getStatus() );

        return userMoment;
    }

    @Override
    public UserMoment convert(UserMomentBo arg0, UserMoment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
