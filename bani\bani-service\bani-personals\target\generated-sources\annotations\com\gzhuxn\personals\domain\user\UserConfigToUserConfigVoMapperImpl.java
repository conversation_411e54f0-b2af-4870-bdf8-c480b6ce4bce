package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserConfigVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserConfigToUserConfigVoMapperImpl implements UserConfigToUserConfigVoMapper {

    @Override
    public UserConfigVo convert(UserConfig arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserConfigVo userConfigVo = new UserConfigVo();

        userConfigVo.setId( arg0.getId() );
        userConfigVo.setUserId( arg0.getUserId() );
        userConfigVo.setConfigKey( arg0.getConfigKey() );
        userConfigVo.setVal( arg0.getVal() );

        return userConfigVo;
    }

    @Override
    public UserConfigVo convert(UserConfig arg0, UserConfigVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setConfigKey( arg0.getConfigKey() );
        arg1.setVal( arg0.getVal() );

        return arg1;
    }
}
