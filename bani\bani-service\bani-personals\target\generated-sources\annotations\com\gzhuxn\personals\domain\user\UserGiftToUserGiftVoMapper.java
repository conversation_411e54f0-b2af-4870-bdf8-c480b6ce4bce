package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.gift.AppUserGiftCreateBoToUserGiftMapper;
import com.gzhuxn.personals.controller.app.user.vo.gift.AppUserGiftVoToUserGiftMapper;
import com.gzhuxn.personals.domain.user.bo.UserGiftBoToUserGiftMapper;
import com.gzhuxn.personals.domain.user.vo.UserGiftVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserGiftCreateBoToUserGiftMapper.class,AppUserGiftVoToUserGiftMapper.class,UserGiftBoToUserGiftMapper.class,UserGiftToAppUserGiftVoMapper.class},
    imports = {}
)
public interface UserGiftToUserGiftVoMapper extends BaseMapper<UserGift, UserGiftVo> {
}
