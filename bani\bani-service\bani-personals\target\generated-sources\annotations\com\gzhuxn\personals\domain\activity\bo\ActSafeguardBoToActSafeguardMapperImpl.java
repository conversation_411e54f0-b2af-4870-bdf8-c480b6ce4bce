package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.personals.domain.activity.ActSafeguard;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardBoToActSafeguardMapperImpl implements ActSafeguardBoToActSafeguardMapper {

    @Override
    public ActSafeguard convert(ActSafeguardBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguard actSafeguard = new ActSafeguard();

        actSafeguard.setSearchValue( arg0.getSearchValue() );
        actSafeguard.setCreateBy( arg0.getCreateBy() );
        actSafeguard.setCreateTime( arg0.getCreateTime() );
        actSafeguard.setUpdateBy( arg0.getUpdateBy() );
        actSafeguard.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            actSafeguard.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        actSafeguard.setCreateDept( arg0.getCreateDept() );
        actSafeguard.setId( arg0.getId() );
        actSafeguard.setName( arg0.getName() );
        actSafeguard.setAmount( arg0.getAmount() );
        actSafeguard.setSafeKey( arg0.getSafeKey() );
        actSafeguard.setStatus( arg0.getStatus() );

        return actSafeguard;
    }

    @Override
    public ActSafeguard convert(ActSafeguardBo arg0, ActSafeguard arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setSafeKey( arg0.getSafeKey() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
