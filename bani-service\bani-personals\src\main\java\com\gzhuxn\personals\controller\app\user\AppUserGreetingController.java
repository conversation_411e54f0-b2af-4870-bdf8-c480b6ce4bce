package com.gzhuxn.personals.controller.app.user;

import com.gzhuxn.common.core.domain.R;
import com.gzhuxn.common.idempotent.annotation.RepeatSubmit;
import com.gzhuxn.common.log.annotation.Log;
import com.gzhuxn.common.log.enums.BusinessType;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.web.core.BaseController;
import com.gzhuxn.personals.controller.app.user.bo.AppUserGreetingCreateBo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserGreetingVo;
import com.gzhuxn.personals.service.user.IUserGreetingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * APP端-用户打招呼
 * 前端访问路由地址为:/personals/user/greeting
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/greeting")
public class AppUserGreetingController extends BaseController {
    
    private final IUserGreetingService userGreetingService;

    /**
     * 发送打招呼
     */
    @Log(title = "打招呼-发送", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping("/send")
    public R<Void> sendGreeting(@Valid @RequestBody AppUserGreetingCreateBo bo) {
        return toAjax(userGreetingService.insertByBo(bo));
    }

    /**
     * 查询收到的打招呼列表
     */
    @GetMapping("/received/page")
    public TableDataInfo<AppUserGreetingVo> receivedPage(PageQuery pageQuery) {
        return userGreetingService.queryReceivedPageList(pageQuery);
    }

    /**
     * 查询发送的打招呼列表
     */
    @GetMapping("/sent/page")
    public TableDataInfo<AppUserGreetingVo> sentPage(PageQuery pageQuery) {
        return userGreetingService.querySentPageList(pageQuery);
    }

    /**
     * 检查是否已经向对方打过招呼
     */
    @GetMapping("/check")
    public R<Boolean> checkGreeted(@RequestParam("oppositeUserId") Long oppositeUserId) {
        return R.ok(userGreetingService.hasGreeted(null, oppositeUserId));
    }

    /**
     * 回复打招呼
     */
    @Log(title = "打招呼-回复", businessType = BusinessType.UPDATE)
    @PostMapping("/reply/{id}")
    public R<Void> reply(@PathVariable Long id, @RequestParam("content") String content) {
        return toAjax(userGreetingService.reply(id, content));
    }

    /**
     * 忽略打招呼
     */
    @Log(title = "打招呼-忽略", businessType = BusinessType.UPDATE)
    @PostMapping("/ignore/{id}")
    public R<Void> ignore(@PathVariable Long id) {
        return toAjax(userGreetingService.ignore(id));
    }
}
