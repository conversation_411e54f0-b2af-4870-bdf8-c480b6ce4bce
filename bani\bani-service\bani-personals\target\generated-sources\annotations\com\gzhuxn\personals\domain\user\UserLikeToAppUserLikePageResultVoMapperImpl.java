package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.AppUserLikePageResultVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLikeToAppUserLikePageResultVoMapperImpl implements UserLikeToAppUserLikePageResultVoMapper {

    @Override
    public AppUserLikePageResultVo convert(UserLike arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserLikePageResultVo appUserLikePageResultVo = new AppUserLikePageResultVo();

        appUserLikePageResultVo.setId( arg0.getId() );
        appUserLikePageResultVo.setUserId( arg0.getUserId() );
        appUserLikePageResultVo.setType( arg0.getType() );
        appUserLikePageResultVo.setBusinessId( arg0.getBusinessId() );
        appUserLikePageResultVo.setCreateTime( arg0.getCreateTime() );

        return appUserLikePageResultVo;
    }

    @Override
    public AppUserLikePageResultVo convert(UserLike arg0, AppUserLikePageResultVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
