package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBoToActSafeguardMapper;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardBoToActSafeguardMapper.class,ActSafeguardToActSafeguardVoMapper.class},
    imports = {}
)
public interface ActSafeguardToActSafeguardStorageVoMapper extends BaseMapper<ActSafeguard, ActSafeguardStorageVo> {
}
