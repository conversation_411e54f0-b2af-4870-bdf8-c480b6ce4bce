package com.gzhuxn.personals.controller.app.user.bo.greeting;

import com.gzhuxn.personals.domain.user.UserGreeting;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppUserGreetingCreateBoToUserGreetingMapper extends BaseMapper<AppUserGreetingCreateBo, UserGreeting> {
}
