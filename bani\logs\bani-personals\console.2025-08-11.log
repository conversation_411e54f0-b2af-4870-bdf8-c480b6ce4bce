2025-08-11 01:22:58 [Thread-27] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.2.14, current host: **********, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:502)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/java.io.InputStream.read(InputStream.java:220)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
2025-08-11 04:41:45 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] start to publish all metadata., dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteMessageService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=publishAll,publishMessage, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369152}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteLocationMapService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteLocationMapService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=geocoderLocation, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369746}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteContentAuditService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteContentAuditService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=checkIdentity,checkImage,checkText, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839370141}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteFileService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByIds,selectMapByIds,selectSmallUrlByIds,selectUrlByIds,upload, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368659}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDictService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDictService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectDictDataByType,selectLabelByType,selectLabelByTypes, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369344}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDeptService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDeptService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectDeptNameByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839367379}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteClientService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteClientService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=queryByClientId, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369946}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteLogService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteLogService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839370345}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteUserService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteUserService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,isUserLoggedOff,logoffUser,recordLoginInfo,registerUserInfo,selectById,selectEmailById,selectListByIds,selectMpOpenIdsByIds,selectNicknameById,selectNicknameByIds,selectPhoneNumberById,selectUserIdsByRoleIds,selectUserNameById,softDeleteUser,updateMpSubscribe,updateUserAvatar,updateUserInfo,updateUserStatus, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368740}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteFileService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByIds,selectMapByIds,selectSmallUrlByIds,selectUrlByIds,upload, logger=slf4j, check=false, qos.enable=false, timeout=3000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, sticky=false, mock=true, validation=jvalidationNew, timestamp=1754839421893}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDataScopeService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDataScopeService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=getDeptAndChild,getRoleCustom, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368940}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDistrictService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDistrictService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByCode,selectByNameAndParent,selectCodeByNameAndParent,selectNameByCode,selectNameMapByCodes, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369548}, dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-11 07:57:09 [Thread-6] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-11 07:57:09 [Thread-6] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-11 07:57:09 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-11 07:57:09 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](bani-personals), dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [bani-system], dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [GRPC-UNSUBSCRIBE] service:bani-system, group:DUBBO_GROUP, cluster: 
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x61dabb14, L:/**********:64869 - R:/**********:20880], dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /**********:64869 -> /**********:20880 is disconnected., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](bani-personals) to null, dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](bani-personals) is stopping., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-11 07:57:22 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 26860 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-11 07:57:22 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 07:57:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 07:57:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 07:57:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@77bd9821
2025-08-11 07:57:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 07:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 07:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 07:57:39 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-11 07:57:39 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-11 07:57:40 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-11 07:57:41 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-11 07:57:41 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-11 07:57:47 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 07:57:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-11 07:57:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 07:57:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 07:57:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 07:57:51 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-11 07:57:55 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 36.274 seconds (process running for 41.636)
2025-08-11 07:57:57 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-11 07:58:10 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 07:58:24 [XNIO-1 task-2] ERROR c.g.c.w.h.GlobalExceptionHandler - 请求地址'/app-api/user/greeting/received/page',连接中断
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to write: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:343)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:401)
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:261)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2210)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1204)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1063)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:483)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:114)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:297)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:192)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.jrHandle(ServletInitialHandler.java:40001)
	at org.zeroturnaround.javarebel.integration.servlet.undertow.cbp.ServletInitialHandlerCBP.handleRequest(ServletInitialHandlerCBP.java:131)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:227)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:158)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:574)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.conduits.BytesSentStreamSinkConduit.write(BytesSentStreamSinkConduit.java:76)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:667)
	at io.undertow.conduits.ChunkedStreamSinkConduit.doWrite(ChunkedStreamSinkConduit.java:166)
	at io.undertow.conduits.ChunkedStreamSinkConduit.write(ChunkedStreamSinkConduit.java:128)
	at io.undertow.conduits.ChunkedStreamSinkConduit.write(ChunkedStreamSinkConduit.java:220)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2172)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:201)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:398)
	... 79 common frames omitted
2025-08-11 08:23:18 [Thread-14] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-11 08:23:18 [Thread-16] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-08-11 08:23:18 [Thread-16] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-08-11 08:23:18 [Thread-14] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-08-11 08:23:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-11 08:23:24 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 31284 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-11 08:23:24 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 08:23:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 08:23:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 08:23:33 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@c0d114b
2025-08-11 08:23:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 08:23:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 08:23:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 08:23:38 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-11 08:23:38 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-11 08:23:39 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-11 08:23:40 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-11 08:23:40 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-11 08:23:47 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 08:23:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-11 08:23:52 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 08:23:52 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 08:23:52 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 08:23:52 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-11 08:23:56 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 34.272 seconds (process running for 35.302)
2025-08-11 08:23:57 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-11 08:23:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-11 08:23:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-11 08:23:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-11 08:23:58 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-11 08:23:58 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 10:24:08 [Thread-27] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.2.14, current host: **********, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:502)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/java.io.InputStream.read(InputStream.java:220)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
2025-08-11 10:46:37 [XNIO-1 task-4] ERROR c.g.c.w.h.GlobalExceptionHandler - 请求地址'/app-api/recommend/moment/page',连接中断
org.springframework.web.context.request.async.AsyncRequestNotUsableException: ServletOutputStream failed to write: 你的主机中的软件中止了一个已建立的连接。
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.handleIOException(StandardServletAsyncWebRequest.java:343)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:401)
	at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:261)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2210)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1204)
	at com.fasterxml.jackson.databind.ObjectMapper.writeValue(ObjectMapper.java:3366)
	at com.fasterxml.jackson.core.base.GeneratorBase.writeObject(GeneratorBase.java:422)
	at com.gzhuxn.common.translation.core.handler.TranslationHandler.serialize(TranslationHandler.java:50)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:183)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serializeContents(IndexedListSerializer.java:119)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:79)
	at com.fasterxml.jackson.databind.ser.impl.IndexedListSerializer.serialize(IndexedListSerializer.java:18)
	at com.fasterxml.jackson.databind.ser.BeanPropertyWriter.serializeAsField(BeanPropertyWriter.java:732)
	at com.fasterxml.jackson.databind.ser.std.BeanSerializerBase.serializeFields(BeanSerializerBase.java:770)
	at com.fasterxml.jackson.databind.ser.BeanSerializer.serialize(BeanSerializer.java:183)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider._serialize(DefaultSerializerProvider.java:502)
	at com.fasterxml.jackson.databind.ser.DefaultSerializerProvider.serializeValue(DefaultSerializerProvider.java:341)
	at com.fasterxml.jackson.databind.ObjectWriter$Prefetch.serialize(ObjectWriter.java:1574)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1061)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:483)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:114)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:297)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:192)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
	Suppressed: org.springframework.web.context.request.async.AsyncRequestNotUsableException: Response not usable after response errors.
		at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleHttpServletResponse.obtainLockOrRaiseException(StandardServletAsyncWebRequest.java:335)
		at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:396)
		at org.springframework.util.StreamUtils$NonClosingOutputStream.write(StreamUtils.java:261)
		at com.fasterxml.jackson.core.json.UTF8JsonGenerator._flushBuffer(UTF8JsonGenerator.java:2210)
		at com.fasterxml.jackson.core.json.UTF8JsonGenerator.close(UTF8JsonGenerator.java:1234)
		at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:452)
		... 72 common frames omitted
Caused by: java.io.IOException: 你的主机中的软件中止了一个已建立的连接。
	at java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:227)
	at java.base/sun.nio.ch.IOUtil.write(IOUtil.java:158)
	at java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:574)
	at org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)
	at io.undertow.conduits.BytesSentStreamSinkConduit.write(BytesSentStreamSinkConduit.java:76)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:667)
	at io.undertow.conduits.ChunkedStreamSinkConduit.doWrite(ChunkedStreamSinkConduit.java:166)
	at io.undertow.conduits.ChunkedStreamSinkConduit.write(ChunkedStreamSinkConduit.java:128)
	at io.undertow.conduits.ChunkedStreamSinkConduit.write(ChunkedStreamSinkConduit.java:220)
	at org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)
	at io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2172)
	at org.xnio.channels.Channels.writeBlocking(Channels.java:202)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:201)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)
	at org.springframework.web.context.request.async.StandardServletAsyncWebRequest$LifecycleServletOutputStream.write(StandardServletAsyncWebRequest.java:398)
	... 92 common frames omitted
2025-08-11 11:11:59 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 11:11:59 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 11:11:59 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 11:25:37 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 11:25:37 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 11:25:38 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 12:05:57 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 12:05:58 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 12:05:58 [eventbus-msg-pool-1] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 23:07:13 [XNIO-1 task-2] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:07:13 [XNIO-1 task-3] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:07:15 [XNIO-1 task-2] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:07:15 [XNIO-1 task-6] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:09:24 [XNIO-1 task-5] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:10:53 [eventbus-msg-pool-2] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 23:10:53 [eventbus-msg-pool-2] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 23:10:53 [eventbus-msg-pool-2] WARN  c.g.c.m.h.InjectionMetaObjectHandler - 自动注入警告 => 用户未登录
2025-08-11 23:16:18 [XNIO-1 task-6] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
2025-08-11 23:16:19 [XNIO-1 task-3] ERROR c.g.c.w.h.GlobalExceptionHandler - 不允许重复提交，请稍候再试 - ServiceException堆栈信息(前5行): 
  1. com.gzhuxn.common.idempotent.aspectj.RepeatSubmitAspect.doBefore(RepeatSubmitAspect.java:68)
  2. jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
  3. java.lang.reflect.Method.invoke(Method.java:580)
  4. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
  5. org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:620)
