package com.gzhuxn.personals.controller.app.activity.bo;

import com.gzhuxn.personals.domain.activity.Activity;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppActivityFeeBoToActivityFeeMapper.class},
    imports = {}
)
public interface AppActivityBuddyCreateBoToActivityMapper extends BaseMapper<AppActivityBuddyCreateBo, Activity> {
}
