package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.vo.ManageGiftVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageGiftToManageGiftVoMapperImpl implements ManageGiftToManageGiftVoMapper {

    @Override
    public ManageGiftVo convert(ManageGift arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageGiftVo manageGiftVo = new ManageGiftVo();

        manageGiftVo.setId( arg0.getId() );
        manageGiftVo.setName( arg0.getName() );
        manageGiftVo.setIcon( arg0.getIcon() );
        manageGiftVo.setPrice( arg0.getPrice() );
        manageGiftVo.setFreeFlag( arg0.getFreeFlag() );
        manageGiftVo.setSort( arg0.getSort() );
        manageGiftVo.setStatus( arg0.getStatus() );
        manageGiftVo.setVersion( arg0.getVersion() );

        return manageGiftVo;
    }

    @Override
    public ManageGiftVo convert(ManageGift arg0, ManageGiftVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setFreeFlag( arg0.getFreeFlag() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVersion( arg0.getVersion() );

        return arg1;
    }
}
