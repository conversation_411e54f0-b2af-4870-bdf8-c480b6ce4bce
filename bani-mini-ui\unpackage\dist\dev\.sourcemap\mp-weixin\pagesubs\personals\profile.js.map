{"version": 3, "file": "profile.js", "sources": ["pagesubs/personals/profile.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccGVyc29uYWxzXHByb2ZpbGUudnVl"], "sourcesContent": ["<template>\r\n\t<!-- 自定义导航栏 -->\r\n\t<scroll-nav-page :title=\"userInfo.nickName\" :show-back=\"true\">\r\n\t\t<template #content>\r\n\t\t\t<view class=\"profile-content\">\r\n\t\t\t\t<!-- 用户头像和基本信息 -->\r\n\t\t\t\t<view class=\"user-header\">\r\n\t\t\t\t\t<!-- 用户图片展示区域 -->\r\n\t\t\t\t\t<view class=\"user-gallery\" v-if=\"userAlbums && userAlbums.length > 0\">\r\n\t\t\t\t\t\t<view class=\"gallery-main\">\r\n\t\t\t\t\t\t\t<!-- 主图片容器 -->\r\n\t\t\t\t\t\t\t<view class=\"main-photo-container\">\r\n\t\t\t\t\t\t\t\t<image v-for=\"(album, index) in userAlbums\" :key=\"index\" class=\"main-photo\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'active': index === currentPhotoIndex }\" :src=\"album.imageUrl\"\r\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\" @click=\"previewPhoto\" @load=\"handlePhotoLoad(index)\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 图片缩略图导航 -->\r\n\t\t\t\t\t\t\t<view class=\"photo-thumbnails\" v-if=\"userAlbums.length > 1\">\r\n\t\t\t\t\t\t\t\t<view class=\"thumbnail-item\" v-for=\"(album, index) in userAlbums\" :key=\"index\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'active': index === currentPhotoIndex }\" @click=\"switchPhoto(index)\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"thumbnail-image\" :src=\"album.imageUrl\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 图片计数器 -->\r\n\t\t\t\t\t\t\t<view class=\"photo-counter\" v-if=\"userAlbums.length > 1\">\r\n\t\t\t\t\t\t\t\t<text>{{ currentPhotoIndex + 1 }}/{{ userAlbums.length }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t<view class=\"basic-info\">\r\n\t\t\t\t\t\t\t<view class=\"name-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"name-gender\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{ userInfo.nickName }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\r\n\t\t\t\t\t\t\t\t\t\t:class=\"userInfo.gender === 'male' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ color: userInfo.gender === 'male' ? '#4A90E2' : '#E91E63' }\"></text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"user-id-container\" @click=\"copyUserId\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-id-icon\">ID</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-id-value\">{{ userInfo.pid }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- 关注按钮 -->\r\n\t\t\t\t\t\t\t\t<view class=\"follow-btn-inline\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'followed': userInfo.isFollowed, 'loading': isFollowLoading }\"\r\n\t\t\t\t\t\t\t\t\t@click=\"toggleFollow\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons v-if=\"!isFollowLoading\"\r\n\t\t\t\t\t\t\t\t\t\t:type=\"userInfo.isFollowed ? 'star-filled' : 'star'\" size=\"20\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t\t\t<uni-icons v-else type=\"spinner-cycle\" size=\"20\" color=\"#fff\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"follow-text\">\r\n\t\t\t\t\t\t\t\t\t\t{{ isFollowLoading ? '处理中...' : (userInfo.isFollowed ? '已关注' : '关注') }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"user-stats\">\r\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.age }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.height }}cm</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.weight }}kg</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.edu }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.revenue }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"location-info\">\r\n\t\t\t\t\t\t\t<!-- 出生年月·星座 -->\r\n\t\t\t\t\t\t\t<view class=\"location-item\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t\t\t<text class=\"location-text\">{{ userInfo.birthdayYear }} · {{ userInfo.star }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 职业 -->\r\n\t\t\t\t\t\t\t<view class=\"location-item\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"wallet\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t\t\t<text class=\"location-text\">{{ userInfo.job }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- 现居地址·籍贯地址 -->\r\n\t\t\t\t\t\t\t<view class=\"location-item\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t\t\t<text class=\"location-text\">现居{{ userInfo.addrNew }} · {{ userInfo.addr }}人</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 认证卡片 -->\r\n\t\t\t\t<AuthCard v-if=\"!authStatus.isAuthenticated\" :user-id=\"userId\" :auth-types=\"authStatus.authTypes\"\r\n\t\t\t\t\t@auth-complete=\"handleAuthComplete\" />\r\n\r\n\t\t\t\t<!-- 关于我 -->\r\n\t\t\t\t<view class=\"about-me\">\r\n\t\t\t\t\t<text class=\"section-title\">关于我</text>\r\n\t\t\t\t\t<view class=\"tags-container\" v-if=\"userInfo.aboutMeTags && userInfo.aboutMeTags.length > 0\">\r\n\t\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t\t<text v-for=\"(tag, index) in userInfo.aboutMeTags\" :key=\"index\" class=\"tag-label\">\r\n\t\t\t\t\t\t\t\t{{ tag.label }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tags-container\" v-else>\r\n\t\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t\t<text class=\"tag-label no-data\">暂无标签信息</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t<text class=\"intro-text\" v-if=\"userInfo.introduction\">{{ userInfo.introduction }}</text>\r\n\t\t\t\t\t<text class=\"intro-text no-data\" v-else>暂无个人介绍</text>\r\n\r\n\t\t\t\t\t<!-- 个人优势 -->\r\n\t\t\t\t\t<view class=\"advantages-section\" v-if=\"userInfo.advantages\">\r\n\t\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t\t<text class=\"advantages-title\">个人优势</text>\r\n\t\t\t\t\t\t<text class=\"advantages-text\">{{ userInfo.advantages }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t<!-- 最新动态 -->\r\n\t\t\t\t<view class=\"latest-moments\">\r\n\t\t\t\t\t<view class=\"moment-header\" @click=\"goToMoments\">\r\n\t\t\t\t\t\t<view class=\"moment-title-row\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">最新动态</text>\r\n\t\t\t\t\t\t\t<text class=\"moment-count\">（{{ latestMoment.recentCount || 0 }}）</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"more-arrow\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"moment-content\" @click=\"goToMoments\">\r\n\t\t\t\t\t\t<view class=\"moment-main\">\r\n\t\t\t\t\t\t\t<view class=\"moment-text-area\">\r\n\t\t\t\t\t\t\t\t<text class=\"moment-text\">{{ latestMoment.text }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"moment-time\">{{ latestMoment.time }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<image v-if=\"latestMoment.image\" :src=\"latestMoment.image\" class=\"moment-image\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- Ta希望你 -->\r\n\t\t\t\t<view class=\"mate-requirements\" v-if=\"userInfo.isMatched\">\r\n\t\t\t\t\t<text class=\"section-title\">Ta希望你</text>\r\n\t\t\t\t\t<view class=\"tags-container\" v-if=\"userInfo.requirementTags && userInfo.requirementTags.length > 0\">\r\n\t\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t\t<text v-for=\"(tag, index) in userInfo.requirementTags\" :key=\"index\" class=\"tag-label\">\r\n\t\t\t\t\t\t\t\t{{ tag.label }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tags-container\" v-else>\r\n\t\t\t\t\t\t<view class=\"tag-group\">\r\n\t\t\t\t\t\t\t<text class=\"tag-label no-data\">暂无要求标签</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"divider-line\"></view>\r\n\t\t\t\t\t<text class=\"req-description\" v-if=\"userInfo.dreamPartnerDescription\">\r\n\t\t\t\t\t\t{{ userInfo.dreamPartnerDescription }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"req-description no-data\" v-else>\r\n\t\t\t\t\t\t暂无理想型描述\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 礼物墙列表 -->\r\n\t\t\t\t<view class=\"gift-section\">\r\n\t\t\t\t\t<view class=\"gift-header\" @click=\"goToGiftPage\">\r\n\t\t\t\t\t\t<view class=\"gift-title-row\">\r\n\t\t\t\t\t\t\t<text class=\"section-title\">礼物墙</text>\r\n\t\t\t\t\t\t\t<text class=\"gift-count-text\">（已收到{{ giftWallData.totalGiftCount }}份礼物）</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"gift-arrow\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gifts-container\" v-if=\"giftWallData.giftList && giftWallData.giftList.length > 0\">\r\n\t\t\t\t\t\t<view class=\"gift-item\" v-for=\"(gift, index) in giftWallData.giftList\" :key=\"gift.id || index\">\r\n\t\t\t\t\t\t\t<image :src=\"gift.giftIcon\" class=\"gift-icon\" />\r\n\t\t\t\t\t\t\t<text class=\"gift-name\">{{ gift.giftName }}</text>\r\n\t\t\t\t\t\t\t<text class=\"gift-count\">×{{ gift.giftNum }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gifts-container\" v-else>\r\n\t\t\t\t\t\t<view class=\"empty-gifts\">\r\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无收到的礼物</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n\r\n\t<!-- 礼物弹窗 -->\r\n\t<GiftModal :visible=\"giftModalVisible\" :target-user=\"userInfo\" :sending=\"isSendingGift\" @close=\"closeGiftModal\"\r\n\t\t@send=\"handleSendGift\" />\r\n\r\n\t<!-- 浮动底部操作按钮 -->\r\n\t<view class=\"floating-bottom-actions\">\r\n\t\t<view class=\"action-btn gift-btn\" @click=\"sendGift\">\r\n\t\t\t<uni-icons type=\"gift\" size=\"18\" color=\"#696CF3\" />\r\n\t\t\t<text class=\"btn-text\">送礼物</text>\r\n\t\t</view>\r\n\t\t<view class=\"action-btn main-btn\" @click=\"requestKnow\">\r\n\t\t\t<text class=\"hi-text\">Hi</text>\r\n\t\t\t<text class=\"btn-text\">想认识Ta</text>\r\n\t\t</view>\r\n\t\t<view class=\"action-btn chat-btn\" @click=\"startChat\">\r\n\t\t\t<uni-icons type=\"chat\" size=\"18\" color=\"#fff\" />\r\n\t\t\t<text class=\"btn-text\">搭讪</text>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<!-- 右侧浮动按钮组 -->\r\n\t<view class=\"floating-buttons-group\">\r\n\t\t<!-- 分享按钮 -->\r\n\t\t<view class=\"floating-btn share-btn\" @click=\"shareProfile\">\r\n\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"#fff\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 更多按钮 -->\r\n\t\t<view class=\"floating-btn more-btn\" @click=\"toggleMoreMenu\">\r\n\t\t\t<uni-icons type=\"more-filled\" size=\"20\" color=\"#fff\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- 横向弹出的操作按钮 -->\r\n\t\t<view class=\"horizontal-menu\" :class=\"{ 'show': showMoreMenu }\">\r\n\t\t\t<view class=\"menu-btn report-btn\" @click=\"reportUser\">\r\n\t\t\t\t<uni-icons type=\"info\" size=\"18\" color=\"#ff6b6b\" />\r\n\t\t\t\t<text class=\"menu-text\">举报</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"menu-btn block-btn\" @click=\"blockUser\">\r\n\t\t\t\t<uni-icons type=\"minus-filled\" size=\"18\" color=\"#666\" />\r\n\t\t\t\t<text class=\"menu-text\">拉黑</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport GiftModal from './gift/gift-modal.vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\r\nimport AuthCard from '@/components/auth-card/auth-card.vue'\r\nimport { getUserDetail } from '@/api/my/my'\r\nimport { getUserLatestMoment } from '@/api/moment/moment'\r\nimport { getAlbums } from '@/api/my/album'\r\nimport { createUserHomeBrowseHistory, createUserAlbumBrowseHistory } from '@/api/my/browse'\r\nimport { toggleUserFollow } from '@/api/my/follow'\r\nimport { sendGiftToUser, getMyReceivedGifts, getUserGiftWall } from '@/api/my/gift'\r\nimport { addBlacklist } from '@/api/my/blacklist'\r\n\r\n// 获取页面参数\r\nlet userId = ref('1')\r\n\r\n// 用户详细信息\r\nconst userDetail = ref({})\r\n\r\n// 用户相册\r\nconst userAlbums = ref([])\r\n\r\n// 认证状态\r\nconst authStatus = ref({\r\n\tisAuthenticated: false,\r\n\tauthTypes: []\r\n})\r\n\r\n// 页面加载时获取参数\r\nonLoad((options) => {\r\n\tconsole.log('页面参数:', options)\r\n\tif (options && options.userId) {\r\n\t\tuserId.value = options.userId\r\n\t\t// 记录用户主页浏览历史\r\n\t\trecordUserHomeBrowse(options.userId)\r\n\t\tloadUserData()\r\n\t}\r\n})\r\n\r\n// 用户信息\r\nconst userInfo = ref({})\r\n// 状态\r\nconst isLiked = ref(false)\r\nconst isFollowed = ref(false)\r\nconst currentPhotoIndex = ref(0)\r\nconst loadedPhotos = ref(new Set())\r\nconst showMoreMenu = ref(false)\r\nconst receivedGiftsCount = ref(23) // 已收到的礼物数量\r\nconst isFollowLoading = ref(false) // 关注操作加载状态\r\nconst isSendingGift = ref(false) // 送礼物加载状态\r\n\r\nconst giftModalVisible = ref(false) // 礼物弹窗显示状态\r\n\r\n// 礼物墙数据\r\nconst giftWallData = ref({\r\n\ttotalGiftCount: 0,\r\n\tgiftList: []\r\n})\r\n\r\n// 最新动态数据\r\nconst latestMoment = reactive({\r\n\trecentCount: 0,\r\n\ttext: '暂无动态内容',\r\n\ttime: '',\r\n\timage: null\r\n})\r\n\r\n// 切换更多菜单显示状态\r\nconst toggleMoreMenu = () => {\r\n\tshowMoreMenu.value = !showMoreMenu.value\r\n}\r\n\r\n// 图片相关方法\r\nconst switchPhoto = (index) => {\r\n\tif (index === currentPhotoIndex.value) return\r\n\r\n\t// 直接切换，不显示加载状态\r\n\tcurrentPhotoIndex.value = index\r\n\r\n\t// 记录相册图片浏览历史\r\n\tif (userAlbums.value[index] && userAlbums.value[index].id) {\r\n\t\trecordUserAlbumBrowse(userAlbums.value[index].id)\r\n\t}\r\n}\r\n\r\nconst handlePhotoLoad = (index) => {\r\n\tloadedPhotos.value.add(index)\r\n}\r\n\r\nconst toggleLike = () => {\r\n\tisLiked.value = !isLiked.value\r\n\tuni.showToast({\r\n\t\ttitle: isLiked.value ? '已喜欢' : '取消喜欢',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\nconst toggleFollow = () => {\r\n\t// 防止重复点击\r\n\tif (isFollowLoading.value) {\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!userInfo.value.userId) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '用户信息无效',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 设置加载状态\r\n\tisFollowLoading.value = true\r\n\ttoggleUserFollow(userInfo.value.userId, userInfo.value.isFollowed).then(response => {\r\n\t\t// 更新关注状态\r\n\t\tuserInfo.value.isFollowed = !userInfo.value.isFollowed\r\n\t\tisFollowed.value = userInfo.value.isFollowed\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: userInfo.value.isFollowed ? '关注成功' : '取消关注成功',\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 1000\r\n\t\t})\r\n\r\n\t\tconsole.log('关注状态切换成功:', userInfo.value.isFollowed)\r\n\t}).finally(() => {\r\n\t\t// 清除加载状态\r\n\t\tisFollowLoading.value = false\r\n\t})\r\n}\r\n\r\nconst copyUserId = () => {\r\n\tuni.setClipboardData({\r\n\t\tdata: userInfo.value.userId,\r\n\t\tsuccess: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '用户ID已复制',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 1500\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '复制失败',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 1500\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 送礼物\r\nconst sendGift = () => {\r\n\tgiftModalVisible.value = true\r\n}\r\n\r\n// 关闭礼物弹窗\r\nconst closeGiftModal = () => {\r\n\tgiftModalVisible.value = false\r\n\t// 重置发送状态\r\n\tisSendingGift.value = false\r\n}\r\n\r\n// 处理送礼物\r\nconst handleSendGift = async (giftData) => {\r\n\t// 防止重复点击\r\n\tif (isSendingGift.value) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// 设置加载状态\r\n\tisSendingGift.value = true\r\n\r\n\tsendGiftToUser(\r\n\t\tuserId.value, // 目标用户ID\r\n\t\t{\r\n\t\t\tid: giftData.gift.id,\r\n\t\t\tname: giftData.gift.name,\r\n\t\t\tprice: giftData.gift.price\r\n\t\t},\r\n\t\tgiftData.quantity || 1 // 礼物数量\r\n\t).then(response => {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: `成功赠送${giftData.gift.name}`,\r\n\t\t\ticon: 'success'\r\n\t\t})\r\n\r\n\t\t// 关闭礼物弹窗\r\n\t\tcloseGiftModal()\r\n\t\tfetchReceivedGiftsCount()\r\n\t}).catch(error => {\r\n\t\tconsole.error('送礼物失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '送礼物失败，请重试',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t}).finally(() => {\r\n\t\t// 清除加载状态\r\n\t\tisSendingGift.value = false\r\n\t})\r\n}\r\n\r\n// 申请认识\r\nconst requestKnow = () => {\r\n\t// 跳转到打招呼页面\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/personals/greeting/greeting?userId=${userId.value}&nickName=${encodeURIComponent(userInfo.value.nickName)}&avatar=${userInfo.value.avatar}`,\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('跳转打招呼页面失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 开始聊天\r\nconst startChat = () => {\r\n\tuni.showToast({\r\n\t\ttitle: '开始聊天',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\n// 分享用户资料\r\nconst shareProfile = () => {\r\n\tuni.showToast({\r\n\t\ttitle: '分享用户资料',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\n// 举报用户\r\nconst reportUser = () => {\r\n\tshowMoreMenu.value = false // 关闭更多菜单\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/my/report/report?type=1&targetId=${userId.value}&targetName=${encodeURIComponent(userInfo.value.nickName)}`\r\n\t})\r\n}\r\n\r\n// 拉黑用户\r\nconst blockUser = () => {\r\n\tshowMoreMenu.value = false // 关闭更多菜单\r\n\tuni.showModal({\r\n\t\ttitle: '拉黑用户',\r\n\t\tcontent: '拉黑后将不再看到该用户的信息，确定要拉黑吗？',\r\n\t\tconfirmText: '确定拉黑',\r\n\t\tcancelText: '取消',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\taddBlacklist({\r\n\t\t\t\t\toppositeUserId: userId.value\r\n\t\t\t\t}).then(response => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '已拉黑该用户',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 跳转到动态页面\r\nconst goToMoments = () => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/personals/moments?userId=${userId.value}`,\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('跳转动态页面失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 跳转到礼物页面\r\nconst goToGiftPage = () => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pagesubs/personals/gift/gift?userId=${userId.value}`,\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('跳转礼物页面失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 获取用户详细信息\r\nconst fetchUserDetail = () => {\r\n\tgetUserDetail(userId.value).then(response => {\r\n\t\tuserDetail.value = response.data\r\n\t\t// 更新用户信息显示\r\n\t\tupdateUserInfo(response.data)\r\n\t\t// 获取用户礼物墙数据\r\n\t\tfetchGiftWall()\r\n\t})\r\n}\r\n\r\n// 获取用户收到的礼物数量\r\nconst fetchReceivedGiftsCount = () => {\r\n\t// 如果是查看其他用户，暂时不获取礼物数量（需要后端支持）\r\n\tif (userId.value && userId.value !== 'default') {\r\n\t\t// 这里可以根据实际需求调用相应的API\r\n\t\t// 目前保持默认值\r\n\t\treturn\r\n\t}\r\n\r\n\t// 获取当前用户收到的礼物数量\r\n\tgetMyReceivedGifts({ pageSize: 1, pageNum: 1 }).then(response => {\r\n\t\tif (response.code === 200 || response.code === 1) {\r\n\t\t\treceivedGiftsCount.value = response.total || 0\r\n\t\t\tconsole.log('收到的礼物数量:', receivedGiftsCount.value)\r\n\t\t}\r\n\t}).catch(error => {\r\n\t\tconsole.error('获取礼物数量失败:', error)\r\n\t})\r\n}\r\n\r\n// 获取用户礼物墙数据\r\nconst fetchGiftWall = () => {\r\n\tgetUserGiftWall({\r\n\t\tuserId: userId.value,\r\n\t\tpageSize: 10,\r\n\t\tpageNum: 1\r\n\t}).then(response => {\r\n\t\tgiftWallData.value = response.data || { totalGiftCount: 0, giftList: [] }\r\n\t\t// 更新礼物数量显示\r\n\t\treceivedGiftsCount.value = giftWallData.value.totalGiftCount || 0\r\n\t\tconsole.log('礼物墙数据:', giftWallData.value)\r\n\t})\r\n}\r\n\r\n// 获取用户最新动态\r\nconst fetchUserLatestMoment = () => {\r\n\tgetUserLatestMoment(userId.value).then(response => {\r\n\t\tif (response.data) {\r\n\t\t\t// 更新最新动态数据\r\n\t\t\tlatestMoment.text = response.data.content\r\n\t\t\tlatestMoment.time = response.data.time\r\n\t\t\tlatestMoment.image = response.data.firstImage\r\n\t\t\tlatestMoment.recentCount = response.data.recentMomentCount\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 获取用户相册\r\nconst fetchUserAlbums = () => {\r\n\tgetAlbums(userId.value).then(response => {\r\n\t\tuserAlbums.value = response.data\r\n\t\t// 更新用户图片展示\r\n\t\t// 如果有相册图片，记录第一张图片的浏览历史\r\n\t\tif (response.data.length > 0 && response.data[0].id) {\r\n\t\t\trecordUserAlbumBrowse(response.data[0].id)\r\n\t\t}\r\n\t});\r\n}\r\n\r\n// 从 tags 中提取关于我的介绍\r\nconst extractIntroductionFromTags = (tags) => {\r\n\tif (!Array.isArray(tags)) return ''\r\n\r\n\tconst aboutMeTag = tags.find(tag => tag.tagKey === 'about_me')\r\n\treturn aboutMeTag ? aboutMeTag.tagVal : ''\r\n}\r\n\r\n// 从 tags 中提取个人优势\r\nconst extractAdvantagesFromTags = (tags) => {\r\n\tif (!Array.isArray(tags)) return ''\r\n\r\n\tconst advantagesTag = tags.find(tag => tag.tagKey === 'advantages')\r\n\treturn advantagesTag ? advantagesTag.tagVal : ''\r\n}\r\n\r\n// 从 tags 中提取关于我的标签（namespace=matched）\r\nconst extractAboutMeTagsFromTags = (tags) => {\r\n\tif (!Array.isArray(tags)) return []\r\n\r\n\treturn tags\r\n\t\t.filter(tag => tag.namespace === 'matched')\r\n\t\t.map(tag => ({\r\n\t\t\tkey: tag.tagKey,\r\n\t\t\tvalue: tag.tagVal,\r\n\t\t\tlabel: tag.tagVal\r\n\t\t}))\r\n}\r\n\r\n// 从 requireTags 中提取要求标签（namespace=matched）\r\nconst extractRequirementTagsFromRequireTags = (requireTags) => {\r\n\tif (!Array.isArray(requireTags)) return []\r\n\r\n\treturn requireTags\r\n\t\t.filter(tag => tag.namespace === 'matched')\r\n\t\t.map(tag => ({\r\n\t\t\tkey: tag.tagKey,\r\n\t\t\tvalue: tag.tagVal,\r\n\t\t\tlabel: tag.tagVal\r\n\t\t}))\r\n}\r\n\r\n// 从 requireTags 中提取理想型描述（namespace=profile, tagKey=dream_partner）\r\nconst extractDreamPartnerFromRequireTags = (requireTags) => {\r\n\tif (!Array.isArray(requireTags)) return ''\r\n\r\n\tconst dreamPartnerTag = requireTags.find(tag =>\r\n\t\ttag.namespace === 'profile' && tag.tagKey === 'dream_partner'\r\n\t)\r\n\treturn dreamPartnerTag ? dreamPartnerTag.tagVal : ''\r\n}\r\n\r\n// 更新用户信息显示\r\nconst updateUserInfo = (userData) => {\r\n\tuserInfo.value = userData\r\n\tuserInfo.value.gender = userData.sex === '0' ? 'male' : 'female'\r\n\r\n\t// 从 tags 字段中提取关于我的介绍\r\n\tuserInfo.value.introduction = extractIntroductionFromTags(userData.tags) || ''\r\n\r\n\t// 从 tags 字段中提取个人优势\r\n\tuserInfo.value.advantages = extractAdvantagesFromTags(userData.tags) || ''\r\n\r\n\t// 从 tags 字段中提取关于我的标签\r\n\tuserInfo.value.aboutMeTags = extractAboutMeTagsFromTags(userData.tags) || []\r\n\r\n\t// 从 requireTags 字段中提取要求标签（namespace=matched）\r\n\tuserInfo.value.requirementTags = extractRequirementTagsFromRequireTags(userData.requireTags) || []\r\n\r\n\t// 从 requireTags 字段中提取理想型描述（namespace=profile, tagKey=dream_partner）\r\n\tuserInfo.value.dreamPartnerDescription = extractDreamPartnerFromRequireTags(userData.requireTags) || ''\r\n\r\n\t// 同步关注状态到本地变量\r\n\tisFollowed.value = userData.isFollowed || false\r\n\r\n\tconsole.log('用户介绍:', userInfo.value.introduction)\r\n\tconsole.log('个人优势:', userInfo.value.advantages)\r\n\tconsole.log('关于我标签:', userInfo.value.aboutMeTags)\r\n\tconsole.log('要求标签:', userInfo.value.requirementTags)\r\n\tconsole.log('理想型描述:', userInfo.value.dreamPartnerDescription)\r\n\tconsole.log('关注状态:', isFollowed.value)\r\n\r\n\t// 更新认证状态\r\n\tauthStatus.value.isAuthenticated = userData.isAuthenticated || false\r\n\tauthStatus.value.authTypes = userData.authTypes || []\r\n}\r\n\r\n// 计算年龄\r\nconst calculateAge = (birthday) => {\r\n\tif (!birthday) return null\r\n\tconst birthDate = new Date(birthday)\r\n\tconst today = new Date()\r\n\tlet age = today.getFullYear() - birthDate.getFullYear()\r\n\tconst monthDiff = today.getMonth() - birthDate.getMonth()\r\n\tif (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\r\n\t\tage--\r\n\t}\r\n\treturn age\r\n}\r\n\r\n// 创建用户主页浏览记录\r\nconst recordUserHomeBrowse = (userId) => {\r\n\tcreateUserHomeBrowseHistory(userId)\r\n}\r\n\r\n// 创建用户相册图片浏览记录\r\nconst recordUserAlbumBrowse = (albumId) => {\r\n\tcreateUserAlbumBrowseHistory(albumId)\r\n}\r\n\r\n// 认证完成处理\r\nconst handleAuthComplete = (authType) => {\r\n\tconsole.log('认证完成:', authType)\r\n\t// 重新获取用户详细信息以更新认证状态\r\n\tfetchUserDetail()\r\n\tuni.showToast({\r\n\t\ttitle: '认证完成',\r\n\t\ticon: 'success'\r\n\t})\r\n}\r\n\r\n// 根据用户ID加载用户数据\r\nconst loadUserData = async () => {\r\n\t// 并行获取用户数据\r\n\tawait Promise.all([\r\n\t\tfetchUserDetail(),\r\n\t\tfetchUserLatestMoment(),\r\n\t\tfetchUserAlbums()\r\n\t])\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 引入uni.scss变量\r\n@import '@/uni.scss';\r\n\r\n.nav-right-icons {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.profile-content {\r\n\tpadding: 0 16rpx 120rpx;\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.user-header {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 0rpx 16rpx;\r\n\tmargin-bottom: 16rpx;\r\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.avatar-section {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 24rpx;\r\n\tposition: relative;\r\n}\r\n\r\n/* 用户图片展示区域 */\r\n.user-gallery {\r\n\tmargin: 0 -16rpx 20rpx -16rpx;\r\n}\r\n\r\n.gallery-main {\r\n\tposition: relative;\r\n\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\r\n\toverflow: hidden;\r\n\tbackground: #f5f5f5;\r\n}\r\n\r\n.main-photo-container {\r\n\tposition: relative;\r\n\twidth: 100%;\r\n\theight: 480rpx;\r\n}\r\n\r\n.main-photo {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tdisplay: block;\r\n\topacity: 0;\r\n\ttransition: opacity 0.25s ease;\r\n\twill-change: opacity;\r\n}\r\n\r\n.main-photo.active {\r\n\topacity: 1;\r\n\tz-index: 2;\r\n}\r\n\r\n\r\n\r\n/* 缩略图导航 */\r\n.photo-thumbnails {\r\n\tposition: absolute;\r\n\tbottom: 12rpx;\r\n\tright: 12rpx;\r\n\tdisplay: flex;\r\n\tgap: 6rpx;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tpadding: 6rpx;\r\n\tborder-radius: 10rpx;\r\n\tbackdrop-filter: blur(10rpx);\r\n\tz-index: 10;\r\n}\r\n\r\n.thumbnail-item {\r\n\twidth: 36rpx;\r\n\theight: 36rpx;\r\n\tborder-radius: 6rpx;\r\n\toverflow: hidden;\r\n\tborder: 1.5rpx solid transparent;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n}\r\n\r\n.thumbnail-item.active {\r\n\tborder-color: #fff;\r\n\ttransform: scale(1.1);\r\n}\r\n\r\n.thumbnail-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 图片计数器 */\r\n.photo-counter {\r\n\tposition: absolute;\r\n\ttop: 12rpx;\r\n\tright: 12rpx;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tcolor: #fff;\r\n\tpadding: 4rpx 10rpx;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 20rpx;\r\n\tbackdrop-filter: blur(10rpx);\r\n\tz-index: 10;\r\n}\r\n\r\n.user-avatar {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 50%;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.vip-badge {\r\n\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n\r\n\r\n.vip-text {\r\n\tcolor: white;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.user-info {\r\n\tflex: 1;\r\n\tpadding: 24rpx;\r\n}\r\n\r\n.basic-info {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.name-row {\r\n\tdisplay: flex;\r\n\talign-items: flex-start;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.name-gender {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.follow-btn-inline {\r\n\tmin-width: 120rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground: #FF6681;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 6rpx;\r\n\tpadding: 0 16rpx;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.follow-btn-inline.followed {\r\n\tbackground: #FF6681;\r\n\topacity: 0.8;\r\n}\r\n\r\n.follow-btn-inline.loading {\r\n\topacity: 0.6;\r\n\tpointer-events: none;\r\n}\r\n\r\n.follow-btn-inline:active {\r\n\ttransform: scale(0.9);\r\n}\r\n\r\n\r\n.nickname {\r\n\tfont-size: $title-size-lg;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.gender-icon {\r\n\tfont-size: 28rpx;\r\n\tline-height: 1;\r\n\tmargin-right: 12rpx;\r\n}\r\n\r\n.user-id-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 6rpx;\r\n}\r\n\r\n.user-id-value {\r\n\tfont-size: 24rpx;\r\n\tcolor: $uni-text-color-disable;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 2rpx 6rpx;\r\n\tborder-radius: 4rpx;\r\n}\r\n\r\n.user-id-value:active {\r\n\tbackground: rgba(105, 108, 243, 0.1);\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.follow-section {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 4rpx;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.follow-section:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.follow-text {\r\n\tline-height: 1;\r\n\tfont-size: $font-size-xs;\r\n\tcolor: #fff;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.user-id-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-left: 12rpx;\r\n\tcursor: pointer;\r\n}\r\n\r\n.user-id-icon {\r\n\tfont-size: 24rpx;\r\n\tcolor: #fff;\r\n\tmargin-right: 6rpx;\r\n\tline-height: 1;\r\n\tborder: none;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 2rpx 4rpx;\r\n\tbackground: #999;\r\n\tdisplay: inline-block;\r\n\ttext-align: center;\r\n\tmin-width: 36rpx;\r\n\tfont-style: italic;\r\n}\r\n\r\n.user-id {\r\n\tfont-size: $font-size-xs;\r\n\tcolor: #999;\r\n\tbackground: #f5f5f5;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 12rpx;\r\n}\r\n\r\n.user-stats {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.stat-item {\r\n\tfont-size: $font-size-sm;\r\n\tcolor: #666;\r\n\tbackground: #f8f9fa;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 16rpx;\r\n}\r\n\r\n.location-info {\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.location-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tflex: 1;\r\n\tmin-width: calc(50% - 8rpx);\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.location-text {\r\n\tfont-size: $font-size-sm;\r\n\tcolor: #666;\r\n}\r\n\r\n.verification-status {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.verify-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.verify-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #4CD964;\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tgap: 20rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.action-btn {\r\n\tbackground: white;\r\n\tcolor: $primary-color;\r\n\tborder: none;\r\n\tborder-radius: 50rpx;\r\n\tpadding: 12rpx 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tfont-size: 26rpx;\r\n\tmin-width: 120rpx;\r\n\tjustify-content: center;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.action-btn::after {\r\n\tborder: none;\r\n}\r\n\r\n.action-btn:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.action-btn.like-btn {\r\n\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);\r\n}\r\n\r\n.about-me,\r\n.mate-requirements,\r\n.latest-moments,\r\n.identity-verification {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 16rpx;\r\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.gift-section {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 120rpx;\r\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.section-title {\r\n\tfont-size: $title-size-md;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.gift-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.gift-arrow {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.gift-header .section-title {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.gift-title-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.gift-title-row .section-title {\r\n\tmargin-bottom: 0;\r\n\tdisplay: inline;\r\n}\r\n\r\n.gift-count-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-weight: 400;\r\n}\r\n\r\n.verification-header {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.verification-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.verification-text {\r\n\tfont-size: $title-size-md;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.verification-status {\r\n\tfont-size: $font-size-xs;\r\n\tcolor: #999;\r\n}\r\n\r\n.verification-items {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tgap: 16rpx;\r\n\tbackground: rgba(105, 108, 243, 0.05);\r\n\tborder-radius: 12rpx;\r\n\tpadding: 16rpx;\r\n}\r\n\r\n.verification-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tflex: 1;\r\n\tpadding: 16rpx 8rpx;\r\n\tborder-radius: 12rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.verification-item.verified {\r\n\tbackground: rgba(105, 108, 243, 0.1);\r\n}\r\n\r\n.verification-item.unverified {\r\n\tbackground: rgba(204, 204, 204, 0.1);\r\n}\r\n\r\n.verification-item .item-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.verification-item.unverified .item-label {\r\n\tcolor: #999;\r\n}\r\n\r\n.tags-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.tag-group {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 12rpx;\r\n}\r\n\r\n.tag-label {\r\n\tbackground: #f8f9fa;\r\n\tcolor: #666;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: $font-size-sm;\r\n}\r\n\r\n.intro-text {\r\n\tfont-size: $font-size-md;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.intro-text.no-data,\r\n.tag-label.no-data {\r\n\tcolor: #999;\r\n\tfont-style: italic;\r\n}\r\n\r\n.advantages-section {\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.advantages-title {\r\n\tfont-size: $font-size-md;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 12rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.advantages-text {\r\n\tfont-size: $font-size-md;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.divider-line {\r\n\twidth: 100%;\r\n\theight: 1rpx;\r\n\tbackground: #f0f0f0;\r\n\tmargin: 20rpx 0;\r\n}\r\n\r\n.moment-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 16rpx;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.moment-header:active {\r\n\ttransform: scale(0.98);\r\n}\r\n\r\n.moment-title-row {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.moment-title-row .section-title {\r\n\tmargin-bottom: 0;\r\n\tdisplay: inline;\r\n}\r\n\r\n.moment-count {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-weight: 400;\r\n}\r\n\r\n.more-arrow {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.moment-content {\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n\tpadding: 12rpx 0;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.moment-content:active {\r\n\tbackground: rgba(105, 108, 243, 0.05);\r\n\ttransform: scale(0.98);\r\n}\r\n\r\n.moment-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tline-height: 1.5;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.moment-time {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.moment-main {\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\talign-items: flex-start;\r\n}\r\n\r\n.moment-text-area {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.moment-image {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 12rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.req-description {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n}\r\n\r\n.gifts-container {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\toverflow-x: auto;\r\n}\r\n\r\n.gift-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tmin-width: 100rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.gift-icon {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n}\r\n\r\n.gift-name {\r\n\tfont-size: 22rpx;\r\n\tcolor: #666;\r\n\ttext-align: center;\r\n}\r\n\r\n.gift-count {\r\n\tfont-size: 20rpx;\r\n\tcolor: #999;\r\n\ttext-align: center;\r\n\tmargin-top: 4rpx;\r\n}\r\n\r\n.empty-gifts {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 40rpx 0;\r\n\twidth: 100%;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tfont-style: italic;\r\n}\r\n\r\n\r\n\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: white;\r\n\tpadding: 16rpx 24rpx;\r\n\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\tbox-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.bottom-btn {\r\n\tflex: 1;\r\n\tborder: none;\r\n\tborder-radius: 40rpx;\r\n\tpadding: 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 6rpx;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 500;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.bottom-btn::after {\r\n\tborder: none;\r\n}\r\n\r\n.bottom-btn:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.send-msg-btn {\r\n\tbackground: $primary-color;\r\n\tcolor: white;\r\n}\r\n\r\n.like-btn {\r\n\tbackground: #ff4757;\r\n\tcolor: white;\r\n}\r\n\r\n.contact-btn {\r\n\tbackground: #2ed573;\r\n\tcolor: white;\r\n}\r\n\r\n// 浮动底部操作按钮\r\n.floating-bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: rgba(255, 255, 255, 0.6);\r\n\tpadding: 20rpx 32rpx;\r\n\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 16rpx;\r\n\tz-index: 1000;\r\n}\r\n\r\n.action-btn {\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmin-height: 64rpx;\r\n\tborder-radius: 50rpx;\r\n\ttransition: all 0.3s ease;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tgap: 8rpx;\r\n\tbackground: white;\r\n\tborder: none;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.action-btn:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n.gift-btn {\r\n\tflex: 1;\r\n\tcolor: $primary-color;\r\n}\r\n\r\n.chat-btn {\r\n\tflex: 1;\r\n\tbackground: #FF6681 !important;\r\n\tcolor: white !important;\r\n\tborder: none !important;\r\n}\r\n\r\n.chat-btn .btn-text {\r\n\tcolor: white !important;\r\n}\r\n\r\n.main-btn {\r\n\tflex: 2;\r\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n\tcolor: white;\r\n\tfont-weight: 600;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);\r\n}\r\n\r\n.btn-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #696CF3;\r\n}\r\n\r\n.main-btn .btn-text {\r\n\tcolor: white;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.hi-text {\r\n\tbackground: white;\r\n\tcolor: $primary-color;\r\n\tborder: 1rpx solid $primary-color;\r\n\tborder-radius: 50rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 600;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n// 右侧浮动按钮组\r\n.floating-buttons-group {\r\n\tposition: fixed;\r\n\tbottom: 210rpx;\r\n\tright: 32rpx;\r\n\tz-index: 1001;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.floating-btn {\r\n\twidth: 80rpx;\r\n\theight: 80rpx;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbox-shadow: 0 6rpx 20rpx rgba(105, 108, 243, 0.3);\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.floating-btn:active {\r\n\ttransform: scale(0.9);\r\n}\r\n\r\n.share-btn {\r\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n}\r\n\r\n.follow-btn {\r\n\tbackground: #FF6681;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.follow-btn.followed {\r\n\tbackground: #FF6681;\r\n\topacity: 0.8;\r\n}\r\n\r\n.more-btn {\r\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n}\r\n\r\n// 横向弹出菜单\r\n.horizontal-menu {\r\n\tposition: absolute;\r\n\tright: 96rpx;\r\n\tbottom: 0;\r\n\tdisplay: flex;\r\n\tgap: 12rpx;\r\n\topacity: 0;\r\n\ttransform: translateX(20rpx);\r\n\ttransition: all 0.3s ease;\r\n\tpointer-events: none;\r\n}\r\n\r\n.horizontal-menu.show {\r\n\topacity: 1;\r\n\ttransform: translateX(0);\r\n\tpointer-events: auto;\r\n}\r\n\r\n.menu-btn {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n\tpadding: 12rpx 20rpx;\r\n\tborder-radius: 50rpx;\r\n\tbackground: white;\r\n\tborder: 1rpx solid rgba(105, 108, 243, 0.2);\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n\ttransition: all 0.3s ease;\r\n\tmin-width: 120rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.menu-btn:active {\r\n\ttransform: scale(0.95);\r\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);\r\n}\r\n\r\n.report-btn {\r\n\tborder-color: rgba(255, 107, 107, 0.3);\r\n}\r\n\r\n.report-btn .menu-text {\r\n\tcolor: #ff6b6b;\r\n}\r\n\r\n.block-btn {\r\n\tborder-color: rgba(102, 102, 102, 0.3);\r\n}\r\n\r\n.block-btn .menu-text {\r\n\tcolor: #666;\r\n}\r\n\r\n.menu-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: $primary-color;\r\n\tfont-weight: 500;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/personals/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["GiftModal", "ScrollNavPage", "AuthCard", "userId", "ref", "userDetail", "userAlbums", "authStatus", "onLoad", "options", "uni", "recordUserHomeBrowse", "loadUserData", "userInfo", "isFollowed", "currentPhotoIndex", "loadedPhotos", "showMoreMenu", "receivedGiftsCount", "isFollowLoading", "isSendingGift", "giftModalVisible", "giftWallData", "latestMoment", "reactive", "toggleMoreMenu", "switchPhoto", "index", "recordUserAlbumBrowse", "handlePhotoLoad", "to<PERSON><PERSON><PERSON><PERSON>", "toggle<PERSON>ser<PERSON><PERSON>ow", "response", "copyUserId", "sendGift", "closeGiftModal", "handleSendGift", "giftData", "sendGiftToUser", "fetchReceivedGiftsCount", "error", "requestKnow", "err", "startChat", "shareProfile", "reportUser", "blockUser", "res", "addBlacklist", "goToMoments", "goToGiftPage", "fetchUserDetail", "getUserDetail", "updateUserInfo", "fetchGiftWall", "getMyReceivedGifts", "getUserGiftWall", "fetchUserLatestMoment", "getUserLatestMoment", "fetchUserAlbums", "getAlbums", "extractIntroductionFromTags", "tags", "aboutMeTag", "tag", "extractAdvantagesFromTags", "advantagesTag", "extractAboutMeTagsFromTags", "extractRequirementTagsFromRequireTags", "requireTags", "extractDreamPartnerFromRequireTags", "dreamPartnerTag", "userData", "createUserHomeBrowseHistory", "albumId", "createUserAlbumBrowseHistory", "handleAuthComplete", "authType", "MiniProgramPage"], "mappings": "ocAiPA,MAAMA,GAAY,IAAW,uBAEvBC,GAAgB,IAAW,sDAC3BC,GAAW,IAAW,wEAU5B,IAAIC,EAASC,EAAG,IAAC,GAAG,EAGpB,MAAMC,EAAaD,EAAG,IAAC,EAAE,EAGnBE,EAAaF,EAAG,IAAC,EAAE,EAGnBG,EAAaH,EAAAA,IAAI,CACtB,gBAAiB,GACjB,UAAW,CAAE,CACd,CAAC,EAGDI,EAAM,OAAEC,GAAY,CACnBC,EAAAA,0DAAY,QAASD,CAAO,EACxBA,GAAWA,EAAQ,SACtBN,EAAO,MAAQM,EAAQ,OAEvBE,EAAqBF,EAAQ,MAAM,EACnCG,GAAc,EAEhB,CAAC,EAGD,MAAMC,EAAWT,EAAG,IAAC,EAAE,EAEPA,EAAG,IAAC,EAAK,EACzB,MAAMU,EAAaV,EAAG,IAAC,EAAK,EACtBW,EAAoBX,EAAG,IAAC,CAAC,EACzBY,EAAeZ,EAAAA,IAAI,IAAI,GAAK,EAC5Ba,EAAeb,EAAG,IAAC,EAAK,EACxBc,EAAqBd,EAAG,IAAC,EAAE,EAC3Be,EAAkBf,EAAG,IAAC,EAAK,EAC3BgB,EAAgBhB,EAAG,IAAC,EAAK,EAEzBiB,EAAmBjB,EAAG,IAAC,EAAK,EAG5BkB,EAAelB,EAAAA,IAAI,CACxB,eAAgB,EAChB,SAAU,CAAE,CACb,CAAC,EAGKmB,EAAeC,EAAAA,SAAS,CAC7B,YAAa,EACb,KAAM,SACN,KAAM,GACN,MAAO,IACR,CAAC,EAGKC,EAAiB,IAAM,CAC5BR,EAAa,MAAQ,CAACA,EAAa,KACpC,EAGMS,EAAeC,GAAU,CAC1BA,IAAUZ,EAAkB,QAGhCA,EAAkB,MAAQY,EAGtBrB,EAAW,MAAMqB,CAAK,GAAKrB,EAAW,MAAMqB,CAAK,EAAE,IACtDC,EAAsBtB,EAAW,MAAMqB,CAAK,EAAE,EAAE,EAElD,EAEME,EAAmBF,GAAU,CAClCX,EAAa,MAAM,IAAIW,CAAK,CAC7B,EAUMG,EAAe,IAAM,CAE1B,GAAI,CAAAX,EAAgB,MAIpB,IAAI,CAACN,EAAS,MAAM,OAAQ,CAC3BH,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,MACT,CAAG,EACD,MACA,CAGDS,EAAgB,MAAQ,GACxBY,oBAAiBlB,EAAS,MAAM,OAAQA,EAAS,MAAM,UAAU,EAAE,KAAKmB,GAAY,CAEnFnB,EAAS,MAAM,WAAa,CAACA,EAAS,MAAM,WAC5CC,EAAW,MAAQD,EAAS,MAAM,WAElCH,EAAAA,MAAI,UAAU,CACb,MAAOG,EAAS,MAAM,WAAa,OAAS,SAC5C,KAAM,UACN,SAAU,GACb,CAAG,EAEDH,EAAY,MAAA,MAAA,MAAA,wCAAA,YAAaG,EAAS,MAAM,UAAU,CACpD,CAAE,EAAE,QAAQ,IAAM,CAEhBM,EAAgB,MAAQ,EAC1B,CAAE,EACF,EAEMc,EAAa,IAAM,CACxBvB,EAAAA,MAAI,iBAAiB,CACpB,KAAMG,EAAS,MAAM,OACrB,QAAS,IAAM,CACdH,EAAAA,MAAI,UAAU,CACb,MAAO,UACP,KAAM,UACN,SAAU,IACd,CAAI,CACD,EACD,KAAM,IAAM,CACXA,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACN,SAAU,IACd,CAAI,CACD,CACH,CAAE,CACF,EAGMwB,EAAW,IAAM,CACtBb,EAAiB,MAAQ,EAC1B,EAGMc,EAAiB,IAAM,CAC5Bd,EAAiB,MAAQ,GAEzBD,EAAc,MAAQ,EACvB,EAGMgB,EAAiB,MAAOC,GAAa,CAEtCjB,EAAc,QAKlBA,EAAc,MAAQ,GAEtBkB,EAAc,eACbnC,EAAO,MACP,CACC,GAAIkC,EAAS,KAAK,GAClB,KAAMA,EAAS,KAAK,KACpB,MAAOA,EAAS,KAAK,KACrB,EACDA,EAAS,UAAY,CACvB,EAAG,KAAKL,GAAY,CAClBtB,EAAAA,MAAI,UAAU,CACb,MAAO,OAAO2B,EAAS,KAAK,IAAI,GAChC,KAAM,SACT,CAAG,EAGDF,EAAgB,EAChBI,EAAyB,CAC3B,CAAE,EAAE,MAAMC,GAAS,CACjB9B,EAAAA,MAAc,MAAA,QAAA,wCAAA,SAAU8B,CAAK,EAC7B9B,EAAAA,MAAI,UAAU,CACb,MAAO,YACP,KAAM,OACN,SAAU,GACb,CAAG,CACH,CAAE,EAAE,QAAQ,IAAM,CAEhBU,EAAc,MAAQ,EACxB,CAAE,EACF,EAGMqB,EAAc,IAAM,CAEzB/B,EAAAA,MAAI,WAAW,CACd,IAAK,gDAAgDP,EAAO,KAAK,aAAa,mBAAmBU,EAAS,MAAM,QAAQ,CAAC,WAAWA,EAAS,MAAM,MAAM,GACzJ,KAAO6B,GAAQ,CACdhC,EAAAA,MAAc,MAAA,QAAA,wCAAA,aAAcgC,CAAG,EAC/BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMiC,EAAY,IAAM,CACvBjC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,SACR,CAAE,CACF,EAGMkC,EAAe,IAAM,CAC1BlC,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,SACR,CAAE,CACF,EAGMmC,EAAa,IAAM,CACxB5B,EAAa,MAAQ,GACrBP,EAAAA,MAAI,WAAW,CACd,IAAK,8CAA8CP,EAAO,KAAK,eAAe,mBAAmBU,EAAS,MAAM,QAAQ,CAAC,EAC3H,CAAE,CACF,EAGMiC,EAAY,IAAM,CACvB7B,EAAa,MAAQ,GACrBP,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,QAAS,yBACT,YAAa,OACb,WAAY,KACZ,QAAUqC,GAAQ,CACbA,EAAI,SACPC,gBAAa,CACZ,eAAgB7C,EAAO,KAC5B,CAAK,EAAE,KAAK6B,GAAY,CACnBtB,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,SACZ,CAAM,CACN,CAAK,CAEF,CACH,CAAE,CACF,EAGMuC,EAAc,IAAM,CACzBvC,EAAAA,MAAI,WAAW,CACd,IAAK,sCAAsCP,EAAO,KAAK,GACvD,KAAOuC,GAAQ,CACdhC,EAAAA,MAAc,MAAA,QAAA,wCAAA,YAAagC,CAAG,EAC9BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMwC,EAAe,IAAM,CAC1BxC,EAAAA,MAAI,WAAW,CACd,IAAK,wCAAwCP,EAAO,KAAK,GACzD,KAAOuC,GAAQ,CACdhC,EAAAA,MAAc,MAAA,QAAA,wCAAA,YAAagC,CAAG,EAC9BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMyC,EAAkB,IAAM,CAC7BC,GAAAA,cAAcjD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CAC5C3B,EAAW,MAAQ2B,EAAS,KAE5BqB,EAAerB,EAAS,IAAI,EAE5BsB,EAAe,CACjB,CAAE,CACF,EAGMf,EAA0B,IAAM,CAEjCpC,EAAO,OAASA,EAAO,QAAU,WAOrCoD,qBAAmB,CAAE,SAAU,EAAG,QAAS,EAAG,EAAE,KAAKvB,GAAY,EAC5DA,EAAS,OAAS,KAAOA,EAAS,OAAS,KAC9Cd,EAAmB,MAAQc,EAAS,OAAS,EAC7CtB,EAAA,MAAA,MAAA,MAAA,wCAAY,WAAYQ,EAAmB,KAAK,EAEnD,CAAE,EAAE,MAAMsB,GAAS,CACjB9B,EAAAA,4DAAc,YAAa8B,CAAK,CAClC,CAAE,CACF,EAGMc,EAAgB,IAAM,CAC3BE,kBAAgB,CACf,OAAQrD,EAAO,MACf,SAAU,GACV,QAAS,CACX,CAAE,EAAE,KAAK6B,GAAY,CACnBV,EAAa,MAAQU,EAAS,MAAQ,CAAE,eAAgB,EAAG,SAAU,EAAI,EAEzEd,EAAmB,MAAQI,EAAa,MAAM,gBAAkB,EAChEZ,EAAA,MAAA,MAAA,MAAA,wCAAY,SAAUY,EAAa,KAAK,CAC1C,CAAE,CACF,EAGMmC,EAAwB,IAAM,CACnCC,GAAAA,oBAAoBvD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CAC9CA,EAAS,OAEZT,EAAa,KAAOS,EAAS,KAAK,QAClCT,EAAa,KAAOS,EAAS,KAAK,KAClCT,EAAa,MAAQS,EAAS,KAAK,WACnCT,EAAa,YAAcS,EAAS,KAAK,kBAE5C,CAAE,CACF,EAGM2B,EAAkB,IAAM,CAC7BC,GAAAA,UAAUzD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CACxC1B,EAAW,MAAQ0B,EAAS,KAGxBA,EAAS,KAAK,OAAS,GAAKA,EAAS,KAAK,CAAC,EAAE,IAChDJ,EAAsBI,EAAS,KAAK,CAAC,EAAE,EAAE,CAE5C,CAAE,CACF,EAGM6B,EAA+BC,GAAS,CAC7C,GAAI,CAAC,MAAM,QAAQA,CAAI,EAAG,MAAO,GAEjC,MAAMC,EAAaD,EAAK,KAAKE,GAAOA,EAAI,SAAW,UAAU,EAC7D,OAAOD,EAAaA,EAAW,OAAS,EACzC,EAGME,EAA6BH,GAAS,CAC3C,GAAI,CAAC,MAAM,QAAQA,CAAI,EAAG,MAAO,GAEjC,MAAMI,EAAgBJ,EAAK,KAAKE,GAAOA,EAAI,SAAW,YAAY,EAClE,OAAOE,EAAgBA,EAAc,OAAS,EAC/C,EAGMC,EAA8BL,GAC9B,MAAM,QAAQA,CAAI,EAEhBA,EACL,OAAOE,GAAOA,EAAI,YAAc,SAAS,EACzC,IAAIA,IAAQ,CACZ,IAAKA,EAAI,OACT,MAAOA,EAAI,OACX,MAAOA,EAAI,MACd,EAAI,EAR8B,CAAE,EAY9BI,EAAyCC,GACzC,MAAM,QAAQA,CAAW,EAEvBA,EACL,OAAOL,GAAOA,EAAI,YAAc,SAAS,EACzC,IAAIA,IAAQ,CACZ,IAAKA,EAAI,OACT,MAAOA,EAAI,OACX,MAAOA,EAAI,MACd,EAAI,EARqC,CAAE,EAYrCM,EAAsCD,GAAgB,CAC3D,GAAI,CAAC,MAAM,QAAQA,CAAW,EAAG,MAAO,GAExC,MAAME,EAAkBF,EAAY,KAAKL,GACxCA,EAAI,YAAc,WAAaA,EAAI,SAAW,eAC9C,EACD,OAAOO,EAAkBA,EAAgB,OAAS,EACnD,EAGMlB,EAAkBmB,GAAa,CACpC3D,EAAS,MAAQ2D,EACjB3D,EAAS,MAAM,OAAS2D,EAAS,MAAQ,IAAM,OAAS,SAGxD3D,EAAS,MAAM,aAAegD,EAA4BW,EAAS,IAAI,GAAK,GAG5E3D,EAAS,MAAM,WAAaoD,EAA0BO,EAAS,IAAI,GAAK,GAGxE3D,EAAS,MAAM,YAAcsD,EAA2BK,EAAS,IAAI,GAAK,CAAE,EAG5E3D,EAAS,MAAM,gBAAkBuD,EAAsCI,EAAS,WAAW,GAAK,CAAE,EAGlG3D,EAAS,MAAM,wBAA0ByD,EAAmCE,EAAS,WAAW,GAAK,GAGrG1D,EAAW,MAAQ0D,EAAS,YAAc,GAE1C9D,4DAAY,QAASG,EAAS,MAAM,YAAY,EAChDH,4DAAY,QAASG,EAAS,MAAM,UAAU,EAC9CH,4DAAY,SAAUG,EAAS,MAAM,WAAW,EAChDH,4DAAY,QAASG,EAAS,MAAM,eAAe,EACnDH,EAAA,MAAA,MAAA,MAAA,wCAAY,SAAUG,EAAS,MAAM,uBAAuB,EAC5DH,EAAA,MAAA,MAAA,MAAA,wCAAY,QAASI,EAAW,KAAK,EAGrCP,EAAW,MAAM,gBAAkBiE,EAAS,iBAAmB,GAC/DjE,EAAW,MAAM,UAAYiE,EAAS,WAAa,CAAE,CACtD,EAgBM7D,EAAwBR,GAAW,CACxCsE,EAAAA,4BAA4BtE,CAAM,CACnC,EAGMyB,EAAyB8C,GAAY,CAC1CC,EAAAA,6BAA6BD,CAAO,CACrC,EAGME,EAAsBC,GAAa,CACxCnE,EAAAA,0DAAY,QAASmE,CAAQ,EAE7B1B,EAAiB,EACjBzC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,SACR,CAAE,CACF,EAGME,GAAe,SAAY,CAEhC,MAAM,QAAQ,IAAI,CACjBuC,EAAiB,EACjBM,EAAuB,EACvBE,EAAiB,CACnB,CAAE,CACF,67FC3tBA,GAAG,WAAWmB,EAAe"}