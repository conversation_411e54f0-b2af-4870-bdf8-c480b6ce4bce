{"version": 3, "file": "profile.js", "sources": ["pagesubs/personals/profile.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccGVyc29uYWxzXHByb2ZpbGUudnVl"], "sourcesContent": ["<template>\n\t<!-- 自定义导航栏 -->\n\t<scroll-nav-page :title=\"userInfo.nickName\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"profile-content\">\n\t\t\t\t<!-- 用户头像和基本信息 -->\n\t\t\t\t<view class=\"user-header\">\n\t\t\t\t\t<!-- 用户图片展示区域 -->\n\t\t\t\t\t<view class=\"user-gallery\" v-if=\"userAlbums && userAlbums.length > 0\">\n\t\t\t\t\t\t<view class=\"gallery-main\">\n\t\t\t\t\t\t\t<!-- 主图片容器 -->\n\t\t\t\t\t\t\t<view class=\"main-photo-container\">\n\t\t\t\t\t\t\t\t<image v-for=\"(album, index) in userAlbums\" :key=\"index\" class=\"main-photo\"\n\t\t\t\t\t\t\t\t\t:class=\"{ 'active': index === currentPhotoIndex }\" :src=\"album.imageUrl\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\" @click=\"previewPhoto\" @load=\"handlePhotoLoad(index)\" />\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 图片缩略图导航 -->\n\t\t\t\t\t\t\t<view class=\"photo-thumbnails\" v-if=\"userAlbums.length > 1\">\n\t\t\t\t\t\t\t\t<view class=\"thumbnail-item\" v-for=\"(album, index) in userAlbums\" :key=\"index\"\n\t\t\t\t\t\t\t\t\t:class=\"{ 'active': index === currentPhotoIndex }\" @click=\"switchPhoto(index)\">\n\t\t\t\t\t\t\t\t\t<image class=\"thumbnail-image\" :src=\"album.imageUrl\" mode=\"aspectFill\" />\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 图片计数器 -->\n\t\t\t\t\t\t\t<view class=\"photo-counter\" v-if=\"userAlbums.length > 1\">\n\t\t\t\t\t\t\t\t<text>{{ currentPhotoIndex + 1 }}/{{ userAlbums.length }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t<view class=\"basic-info\">\n\t\t\t\t\t\t\t<view class=\"name-row\">\n\t\t\t\t\t\t\t\t<view class=\"name-gender\">\n\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{ userInfo.nickName }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\n\t\t\t\t\t\t\t\t\t\t:class=\"userInfo.gender === 'male' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\n\t\t\t\t\t\t\t\t\t\t:style=\"{ color: userInfo.gender === 'male' ? '#4A90E2' : '#E91E63' }\"></text>\n\t\t\t\t\t\t\t\t\t<view class=\"user-id-container\" @click=\"copyUserId\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-id-icon\">ID</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"user-id-value\">{{ userInfo.pid }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<!-- 关注按钮 -->\n\t\t\t\t\t\t\t\t<view class=\"follow-btn-inline\"\n\t\t\t\t\t\t\t\t\t:class=\"{ 'followed': userInfo.isFollowed, 'loading': isFollowLoading }\"\n\t\t\t\t\t\t\t\t\t@click=\"toggleFollow\">\n\t\t\t\t\t\t\t\t\t<uni-icons v-if=\"!isFollowLoading\"\n\t\t\t\t\t\t\t\t\t\t:type=\"userInfo.isFollowed ? 'star-filled' : 'star'\" size=\"20\" color=\"#fff\" />\n\t\t\t\t\t\t\t\t\t<uni-icons v-else type=\"spinner-cycle\" size=\"20\" color=\"#fff\" />\n\t\t\t\t\t\t\t\t\t<text class=\"follow-text\">\n\t\t\t\t\t\t\t\t\t\t{{ isFollowLoading ? '处理中...' : (userInfo.isFollowed ? '已关注' : '关注') }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"user-stats\">\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.age }}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.height }}cm</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.weight }}kg</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.edu }}</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-item\">{{ userInfo.revenue }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"location-info\">\n\t\t\t\t\t\t\t<!-- 出生年月·星座 -->\n\t\t\t\t\t\t\t<view class=\"location-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"#999\" />\n\t\t\t\t\t\t\t\t<text class=\"location-text\">{{ userInfo.birthdayYear }} · {{ userInfo.star }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 职业 -->\n\t\t\t\t\t\t\t<view class=\"location-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"wallet\" size=\"16\" color=\"#999\" />\n\t\t\t\t\t\t\t\t<text class=\"location-text\">{{ userInfo.job }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 现居地址·籍贯地址 -->\n\t\t\t\t\t\t\t<view class=\"location-item\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"16\" color=\"#999\" />\n\t\t\t\t\t\t\t\t<text class=\"location-text\">现居{{ userInfo.addrNew }} · {{ userInfo.addr }}人</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 认证卡片 -->\n\t\t\t\t<AuthCard v-if=\"!authStatus.isAuthenticated\" :user-id=\"userId\" :auth-types=\"authStatus.authTypes\"\n\t\t\t\t\t@auth-complete=\"handleAuthComplete\" />\n\n\t\t\t\t<!-- 关于我 -->\n\t\t\t\t<view class=\"about-me\">\n\t\t\t\t\t<text class=\"section-title\">关于我</text>\n\t\t\t\t\t<view class=\"tags-container\" v-if=\"userInfo.aboutMeTags && userInfo.aboutMeTags.length > 0\">\n\t\t\t\t\t\t<view class=\"tag-group\">\n\t\t\t\t\t\t\t<text v-for=\"(tag, index) in userInfo.aboutMeTags\" :key=\"index\" class=\"tag-label\">\n\t\t\t\t\t\t\t\t{{ tag.label }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tags-container\" v-else>\n\t\t\t\t\t\t<view class=\"tag-group\">\n\t\t\t\t\t\t\t<text class=\"tag-label no-data\">暂无标签信息</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"divider-line\"></view>\n\t\t\t\t\t<text class=\"intro-text\" v-if=\"userInfo.introduction\">{{ userInfo.introduction }}</text>\n\t\t\t\t\t<text class=\"intro-text no-data\" v-else>暂无个人介绍</text>\n\n\t\t\t\t\t<!-- 个人优势 -->\n\t\t\t\t\t<view class=\"advantages-section\" v-if=\"userInfo.advantages\">\n\t\t\t\t\t\t<view class=\"divider-line\"></view>\n\t\t\t\t\t\t<text class=\"advantages-title\">个人优势</text>\n\t\t\t\t\t\t<text class=\"advantages-text\">{{ userInfo.advantages }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\t\t\t\t<!-- 最新动态 -->\n\t\t\t\t<view class=\"latest-moments\">\n\t\t\t\t\t<view class=\"moment-header\" @click=\"goToMoments\">\n\t\t\t\t\t\t<view class=\"moment-title-row\">\n\t\t\t\t\t\t\t<text class=\"section-title\">最新动态</text>\n\t\t\t\t\t\t\t<text class=\"moment-count\">（{{ latestMoment.recentCount || 0 }}）</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"more-arrow\">\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"moment-content\" @click=\"goToMoments\">\n\t\t\t\t\t\t<view class=\"moment-main\">\n\t\t\t\t\t\t\t<view class=\"moment-text-area\">\n\t\t\t\t\t\t\t\t<text class=\"moment-text\">{{ latestMoment.text }}</text>\n\t\t\t\t\t\t\t\t<text class=\"moment-time\">{{ latestMoment.time }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<image v-if=\"latestMoment.image\" :src=\"latestMoment.image\" class=\"moment-image\"\n\t\t\t\t\t\t\t\tmode=\"aspectFill\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- Ta希望你 -->\n\t\t\t\t<view class=\"mate-requirements\" v-if=\"userInfo.isMatched\">\n\t\t\t\t\t<text class=\"section-title\">Ta希望你</text>\n\t\t\t\t\t<view class=\"tags-container\" v-if=\"userInfo.requirementTags && userInfo.requirementTags.length > 0\">\n\t\t\t\t\t\t<view class=\"tag-group\">\n\t\t\t\t\t\t\t<text v-for=\"(tag, index) in userInfo.requirementTags\" :key=\"index\" class=\"tag-label\">\n\t\t\t\t\t\t\t\t{{ tag.label }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"tags-container\" v-else>\n\t\t\t\t\t\t<view class=\"tag-group\">\n\t\t\t\t\t\t\t<text class=\"tag-label no-data\">暂无要求标签</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"divider-line\"></view>\n\t\t\t\t\t<text class=\"req-description\" v-if=\"userInfo.dreamPartnerDescription\">\n\t\t\t\t\t\t{{ userInfo.dreamPartnerDescription }}\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"req-description no-data\" v-else>\n\t\t\t\t\t\t暂无理想型描述\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 礼物墙列表 -->\n\t\t\t\t<view class=\"gift-section\">\n\t\t\t\t\t<view class=\"gift-header\" @click=\"goToGiftPage\">\n\t\t\t\t\t\t<view class=\"gift-title-row\">\n\t\t\t\t\t\t\t<text class=\"section-title\">礼物墙</text>\n\t\t\t\t\t\t\t<text class=\"gift-count-text\">（已收到{{ giftWallData.totalGiftCount }}份礼物）</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"gift-arrow\">\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"gifts-container\" v-if=\"giftWallData.giftList && giftWallData.giftList.length > 0\">\n\t\t\t\t\t\t<view class=\"gift-item\" v-for=\"(gift, index) in giftWallData.giftList\" :key=\"gift.id || index\">\n\t\t\t\t\t\t\t<image :src=\"gift.giftIcon\" class=\"gift-icon\" />\n\t\t\t\t\t\t\t<text class=\"gift-name\">{{ gift.giftName }}</text>\n\t\t\t\t\t\t\t<text class=\"gift-count\">×{{ gift.giftNum }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"gifts-container\" v-else>\n\t\t\t\t\t\t<view class=\"empty-gifts\">\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无收到的礼物</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n\n\t<!-- 礼物弹窗 -->\n\t<GiftModal :visible=\"giftModalVisible\" :target-user=\"userInfo\" :sending=\"isSendingGift\" @close=\"closeGiftModal\"\n\t\t@send=\"handleSendGift\" />\n\n\t<!-- 浮动底部操作按钮 -->\n\t<view class=\"floating-bottom-actions\">\n\t\t<view class=\"action-btn gift-btn\" @click=\"sendGift\">\n\t\t\t<uni-icons type=\"gift\" size=\"18\" color=\"#696CF3\" />\n\t\t\t<text class=\"btn-text\">送礼物</text>\n\t\t</view>\n\t\t<view class=\"action-btn main-btn\" @click=\"requestKnow\">\n\t\t\t<text class=\"hi-text\">Hi</text>\n\t\t\t<text class=\"btn-text\">想认识Ta</text>\n\t\t</view>\n\t\t<view class=\"action-btn chat-btn\" @click=\"startChat\">\n\t\t\t<uni-icons type=\"chat\" size=\"18\" color=\"#fff\" />\n\t\t\t<text class=\"btn-text\">搭讪</text>\n\t\t</view>\n\t</view>\n\n\t<!-- 右侧浮动按钮组 -->\n\t<view class=\"floating-buttons-group\">\n\t\t<!-- 分享按钮 -->\n\t\t<view class=\"floating-btn share-btn\" @click=\"shareProfile\">\n\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"#fff\" />\n\t\t</view>\n\n\t\t<!-- 更多按钮 -->\n\t\t<view class=\"floating-btn more-btn\" @click=\"toggleMoreMenu\">\n\t\t\t<uni-icons type=\"more-filled\" size=\"20\" color=\"#fff\" />\n\t\t</view>\n\n\t\t<!-- 横向弹出的操作按钮 -->\n\t\t<view class=\"horizontal-menu\" :class=\"{ 'show': showMoreMenu }\">\n\t\t\t<view class=\"menu-btn report-btn\" @click=\"reportUser\">\n\t\t\t\t<uni-icons type=\"info\" size=\"18\" color=\"#ff6b6b\" />\n\t\t\t\t<text class=\"menu-text\">举报</text>\n\t\t\t</view>\n\t\t\t<view class=\"menu-btn block-btn\" @click=\"blockUser\">\n\t\t\t\t<uni-icons type=\"minus-filled\" size=\"18\" color=\"#666\" />\n\t\t\t\t<text class=\"menu-text\">拉黑</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport GiftModal from './gift/gift-modal.vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport AuthCard from '@/components/auth-card/auth-card.vue'\nimport { getUserDetail } from '@/api/my/my'\nimport { getUserLatestMoment } from '@/api/moment/moment'\nimport { getAlbums } from '@/api/my/album'\nimport { createUserHomeBrowseHistory, createUserAlbumBrowseHistory } from '@/api/my/browse'\nimport { toggleUserFollow } from '@/api/my/follow'\nimport { sendGiftToUser, getMyReceivedGifts, getUserGiftWall } from '@/api/my/gift'\nimport { addBlacklist } from '@/api/my/blacklist'\n\n// 获取页面参数\nlet userId = ref('1')\n\n// 用户详细信息\nconst userDetail = ref({})\n\n// 用户相册\nconst userAlbums = ref([])\n\n// 认证状态\nconst authStatus = ref({\n\tisAuthenticated: false,\n\tauthTypes: []\n})\n\n// 页面加载时获取参数\nonLoad((options) => {\n\tconsole.log('页面参数:', options)\n\tif (options && options.userId) {\n\t\tuserId.value = options.userId\n\t\t// 记录用户主页浏览历史\n\t\trecordUserHomeBrowse(options.userId)\n\t\tloadUserData()\n\t}\n})\n\n// 用户信息\nconst userInfo = ref({})\n// 状态\nconst isLiked = ref(false)\nconst isFollowed = ref(false)\nconst currentPhotoIndex = ref(0)\nconst loadedPhotos = ref(new Set())\nconst showMoreMenu = ref(false)\nconst receivedGiftsCount = ref(23) // 已收到的礼物数量\nconst isFollowLoading = ref(false) // 关注操作加载状态\nconst isSendingGift = ref(false) // 送礼物加载状态\n\nconst giftModalVisible = ref(false) // 礼物弹窗显示状态\n\n// 礼物墙数据\nconst giftWallData = ref({\n\ttotalGiftCount: 0,\n\tgiftList: []\n})\n\n// 最新动态数据\nconst latestMoment = reactive({\n\trecentCount: 0,\n\ttext: '暂无动态内容',\n\ttime: '',\n\timage: null\n})\n\n// 切换更多菜单显示状态\nconst toggleMoreMenu = () => {\n\tshowMoreMenu.value = !showMoreMenu.value\n}\n\n// 图片相关方法\nconst switchPhoto = (index) => {\n\tif (index === currentPhotoIndex.value) return\n\n\t// 直接切换，不显示加载状态\n\tcurrentPhotoIndex.value = index\n\n\t// 记录相册图片浏览历史\n\tif (userAlbums.value[index] && userAlbums.value[index].id) {\n\t\trecordUserAlbumBrowse(userAlbums.value[index].id)\n\t}\n}\n\nconst handlePhotoLoad = (index) => {\n\tloadedPhotos.value.add(index)\n}\n\nconst toggleLike = () => {\n\tisLiked.value = !isLiked.value\n\tuni.showToast({\n\t\ttitle: isLiked.value ? '已喜欢' : '取消喜欢',\n\t\ticon: 'success'\n\t})\n}\n\nconst toggleFollow = () => {\n\t// 防止重复点击\n\tif (isFollowLoading.value) {\n\t\treturn\n\t}\n\n\tif (!userInfo.value.userId) {\n\t\tuni.showToast({\n\t\t\ttitle: '用户信息无效',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\t// 设置加载状态\n\tisFollowLoading.value = true\n\ttoggleUserFollow(userInfo.value.userId, userInfo.value.isFollowed).then(response => {\n\t\t// 更新关注状态\n\t\tuserInfo.value.isFollowed = !userInfo.value.isFollowed\n\t\tisFollowed.value = userInfo.value.isFollowed\n\n\t\tuni.showToast({\n\t\t\ttitle: userInfo.value.isFollowed ? '关注成功' : '取消关注成功',\n\t\t\ticon: 'success',\n\t\t\tduration: 1000\n\t\t})\n\n\t\tconsole.log('关注状态切换成功:', userInfo.value.isFollowed)\n\t}).finally(() => {\n\t\t// 清除加载状态\n\t\tisFollowLoading.value = false\n\t})\n}\n\nconst copyUserId = () => {\n\tuni.setClipboardData({\n\t\tdata: userInfo.value.userId,\n\t\tsuccess: () => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '用户ID已复制',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1500\n\t\t\t})\n\t\t},\n\t\tfail: () => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '复制失败',\n\t\t\t\ticon: 'none',\n\t\t\t\tduration: 1500\n\t\t\t})\n\t\t}\n\t})\n}\n\n// 送礼物\nconst sendGift = () => {\n\tgiftModalVisible.value = true\n}\n\n// 关闭礼物弹窗\nconst closeGiftModal = () => {\n\tgiftModalVisible.value = false\n\t// 重置发送状态\n\tisSendingGift.value = false\n}\n\n// 处理送礼物\nconst handleSendGift = async (giftData) => {\n\t// 防止重复点击\n\tif (isSendingGift.value) {\n\t\treturn\n\t}\n\n\t// 设置加载状态\n\tisSendingGift.value = true\n\n\tsendGiftToUser(\n\t\tuserId.value, // 目标用户ID\n\t\t{\n\t\t\tid: giftData.gift.id,\n\t\t\tname: giftData.gift.name,\n\t\t\tprice: giftData.gift.price\n\t\t},\n\t\tgiftData.quantity || 1 // 礼物数量\n\t).then(response => {\n\t\tuni.showToast({\n\t\t\ttitle: `成功赠送${giftData.gift.name}`,\n\t\t\ticon: 'success'\n\t\t})\n\n\t\t// 关闭礼物弹窗\n\t\tcloseGiftModal()\n\t\tfetchReceivedGiftsCount()\n\t}).catch(error => {\n\t\tconsole.error('送礼物失败:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '送礼物失败，请重试',\n\t\t\ticon: 'none',\n\t\t\tduration: 2000\n\t\t})\n\t}).finally(() => {\n\t\t// 清除加载状态\n\t\tisSendingGift.value = false\n\t})\n}\n\n// 申请认识\nconst requestKnow = () => {\n\t// 跳转到打招呼页面\n\tuni.navigateTo({\n\t\turl: `/pagesubs/personals/greeting/greeting?userId=${userId.value}&nickName=${encodeURIComponent(userInfo.value.nickName)}&avatar=${userInfo.value.avatar}`,\n\t\tfail: (err) => {\n\t\t\tconsole.error('跳转打招呼页面失败:', err)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转失败',\n\t\t\t\ticon: 'error'\n\t\t\t})\n\t\t}\n\t})\n}\n\n// 开始聊天\nconst startChat = () => {\n\tuni.showToast({\n\t\ttitle: '开始聊天',\n\t\ticon: 'success'\n\t})\n}\n\n// 分享用户资料\nconst shareProfile = () => {\n\tuni.showToast({\n\t\ttitle: '分享用户资料',\n\t\ticon: 'success'\n\t})\n}\n\n// 举报用户\nconst reportUser = () => {\n\tshowMoreMenu.value = false // 关闭更多菜单\n\tuni.navigateTo({\n\t\turl: `/pagesubs/my/report/report?type=1&targetId=${userId.value}&targetName=${encodeURIComponent(userInfo.value.nickName)}`\n\t})\n}\n\n// 拉黑用户\nconst blockUser = () => {\n\tshowMoreMenu.value = false // 关闭更多菜单\n\tuni.showModal({\n\t\ttitle: '拉黑用户',\n\t\tcontent: '拉黑后将不再看到该用户的信息，确定要拉黑吗？',\n\t\tconfirmText: '确定拉黑',\n\t\tcancelText: '取消',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\taddBlacklist({\n\t\t\t\t\toppositeUserId: userId.value\n\t\t\t\t}).then(response => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已拉黑该用户',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n\n// 跳转到动态页面\nconst goToMoments = () => {\n\tuni.navigateTo({\n\t\turl: `/pagesubs/personals/moments?userId=${userId.value}`,\n\t\tfail: (err) => {\n\t\t\tconsole.error('跳转动态页面失败:', err)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转失败',\n\t\t\t\ticon: 'error'\n\t\t\t})\n\t\t}\n\t})\n}\n\n// 跳转到礼物页面\nconst goToGiftPage = () => {\n\tuni.navigateTo({\n\t\turl: `/pagesubs/personals/gift/gift?userId=${userId.value}`,\n\t\tfail: (err) => {\n\t\t\tconsole.error('跳转礼物页面失败:', err)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '跳转失败',\n\t\t\t\ticon: 'error'\n\t\t\t})\n\t\t}\n\t})\n}\n\n// 获取用户详细信息\nconst fetchUserDetail = () => {\n\tgetUserDetail(userId.value).then(response => {\n\t\tuserDetail.value = response.data\n\t\t// 更新用户信息显示\n\t\tupdateUserInfo(response.data)\n\t\t// 获取用户礼物墙数据\n\t\tfetchGiftWall()\n\t})\n}\n\n// 获取用户收到的礼物数量\nconst fetchReceivedGiftsCount = () => {\n\t// 如果是查看其他用户，暂时不获取礼物数量（需要后端支持）\n\tif (userId.value && userId.value !== 'default') {\n\t\t// 这里可以根据实际需求调用相应的API\n\t\t// 目前保持默认值\n\t\treturn\n\t}\n\n\t// 获取当前用户收到的礼物数量\n\tgetMyReceivedGifts({ pageSize: 1, pageNum: 1 }).then(response => {\n\t\tif (response.code === 200 || response.code === 1) {\n\t\t\treceivedGiftsCount.value = response.total || 0\n\t\t\tconsole.log('收到的礼物数量:', receivedGiftsCount.value)\n\t\t}\n\t}).catch(error => {\n\t\tconsole.error('获取礼物数量失败:', error)\n\t})\n}\n\n// 获取用户礼物墙数据\nconst fetchGiftWall = () => {\n\tgetUserGiftWall({\n\t\tuserId: userId.value,\n\t\tpageSize: 10,\n\t\tpageNum: 1\n\t}).then(response => {\n\t\tgiftWallData.value = response.data || { totalGiftCount: 0, giftList: [] }\n\t\t// 更新礼物数量显示\n\t\treceivedGiftsCount.value = giftWallData.value.totalGiftCount || 0\n\t\tconsole.log('礼物墙数据:', giftWallData.value)\n\t})\n}\n\n// 获取用户最新动态\nconst fetchUserLatestMoment = () => {\n\tgetUserLatestMoment(userId.value).then(response => {\n\t\tif (response.data) {\n\t\t\t// 更新最新动态数据\n\t\t\tlatestMoment.text = response.data.content\n\t\t\tlatestMoment.time = response.data.time\n\t\t\tlatestMoment.image = response.data.firstImage\n\t\t\tlatestMoment.recentCount = response.data.recentMomentCount\n\t\t}\n\t})\n}\n\n// 获取用户相册\nconst fetchUserAlbums = () => {\n\tgetAlbums(userId.value).then(response => {\n\t\tuserAlbums.value = response.data\n\t\t// 更新用户图片展示\n\t\t// 如果有相册图片，记录第一张图片的浏览历史\n\t\tif (response.data.length > 0 && response.data[0].id) {\n\t\t\trecordUserAlbumBrowse(response.data[0].id)\n\t\t}\n\t});\n}\n\n// 从 tags 中提取关于我的介绍\nconst extractIntroductionFromTags = (tags) => {\n\tif (!Array.isArray(tags)) return ''\n\n\tconst aboutMeTag = tags.find(tag => tag.tagKey === 'about_me')\n\treturn aboutMeTag ? aboutMeTag.tagVal : ''\n}\n\n// 从 tags 中提取个人优势\nconst extractAdvantagesFromTags = (tags) => {\n\tif (!Array.isArray(tags)) return ''\n\n\tconst advantagesTag = tags.find(tag => tag.tagKey === 'advantages')\n\treturn advantagesTag ? advantagesTag.tagVal : ''\n}\n\n// 从 tags 中提取关于我的标签（namespace=matched）\nconst extractAboutMeTagsFromTags = (tags) => {\n\tif (!Array.isArray(tags)) return []\n\n\treturn tags\n\t\t.filter(tag => tag.namespace === 'matched')\n\t\t.map(tag => ({\n\t\t\tkey: tag.tagKey,\n\t\t\tvalue: tag.tagVal,\n\t\t\tlabel: tag.tagVal\n\t\t}))\n}\n\n// 从 requireTags 中提取要求标签（namespace=matched）\nconst extractRequirementTagsFromRequireTags = (requireTags) => {\n\tif (!Array.isArray(requireTags)) return []\n\n\treturn requireTags\n\t\t.filter(tag => tag.namespace === 'matched')\n\t\t.map(tag => ({\n\t\t\tkey: tag.tagKey,\n\t\t\tvalue: tag.tagVal,\n\t\t\tlabel: tag.tagVal\n\t\t}))\n}\n\n// 从 requireTags 中提取理想型描述（namespace=profile, tagKey=dream_partner）\nconst extractDreamPartnerFromRequireTags = (requireTags) => {\n\tif (!Array.isArray(requireTags)) return ''\n\n\tconst dreamPartnerTag = requireTags.find(tag =>\n\t\ttag.namespace === 'profile' && tag.tagKey === 'dream_partner'\n\t)\n\treturn dreamPartnerTag ? dreamPartnerTag.tagVal : ''\n}\n\n// 更新用户信息显示\nconst updateUserInfo = (userData) => {\n\tuserInfo.value = userData\n\tuserInfo.value.gender = userData.sex === '0' ? 'male' : 'female'\n\n\t// 从 tags 字段中提取关于我的介绍\n\tuserInfo.value.introduction = extractIntroductionFromTags(userData.tags) || ''\n\n\t// 从 tags 字段中提取个人优势\n\tuserInfo.value.advantages = extractAdvantagesFromTags(userData.tags) || ''\n\n\t// 从 tags 字段中提取关于我的标签\n\tuserInfo.value.aboutMeTags = extractAboutMeTagsFromTags(userData.tags) || []\n\n\t// 从 requireTags 字段中提取要求标签（namespace=matched）\n\tuserInfo.value.requirementTags = extractRequirementTagsFromRequireTags(userData.requireTags) || []\n\n\t// 从 requireTags 字段中提取理想型描述（namespace=profile, tagKey=dream_partner）\n\tuserInfo.value.dreamPartnerDescription = extractDreamPartnerFromRequireTags(userData.requireTags) || ''\n\n\t// 同步关注状态到本地变量\n\tisFollowed.value = userData.isFollowed || false\n\n\tconsole.log('用户介绍:', userInfo.value.introduction)\n\tconsole.log('个人优势:', userInfo.value.advantages)\n\tconsole.log('关于我标签:', userInfo.value.aboutMeTags)\n\tconsole.log('要求标签:', userInfo.value.requirementTags)\n\tconsole.log('理想型描述:', userInfo.value.dreamPartnerDescription)\n\tconsole.log('关注状态:', isFollowed.value)\n\n\t// 更新认证状态\n\tauthStatus.value.isAuthenticated = userData.isAuthenticated || false\n\tauthStatus.value.authTypes = userData.authTypes || []\n}\n\n// 计算年龄\nconst calculateAge = (birthday) => {\n\tif (!birthday) return null\n\tconst birthDate = new Date(birthday)\n\tconst today = new Date()\n\tlet age = today.getFullYear() - birthDate.getFullYear()\n\tconst monthDiff = today.getMonth() - birthDate.getMonth()\n\tif (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n\t\tage--\n\t}\n\treturn age\n}\n\n// 创建用户主页浏览记录\nconst recordUserHomeBrowse = (userId) => {\n\tcreateUserHomeBrowseHistory(userId)\n}\n\n// 创建用户相册图片浏览记录\nconst recordUserAlbumBrowse = (albumId) => {\n\tcreateUserAlbumBrowseHistory(albumId)\n}\n\n// 认证完成处理\nconst handleAuthComplete = (authType) => {\n\tconsole.log('认证完成:', authType)\n\t// 重新获取用户详细信息以更新认证状态\n\tfetchUserDetail()\n\tuni.showToast({\n\t\ttitle: '认证完成',\n\t\ticon: 'success'\n\t})\n}\n\n// 根据用户ID加载用户数据\nconst loadUserData = async () => {\n\t// 并行获取用户数据\n\tawait Promise.all([\n\t\tfetchUserDetail(),\n\t\tfetchUserLatestMoment(),\n\t\tfetchUserAlbums()\n\t])\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 引入uni.scss变量\n@import '@/uni.scss';\n\n.nav-right-icons {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.profile-content {\n\tpadding: 0 16rpx 120rpx;\n\tmargin-top: 20rpx;\n}\n\n.user-header {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\tpadding: 0rpx 16rpx;\n\tmargin-bottom: 16rpx;\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\n}\n\n.avatar-section {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 24rpx;\n\tposition: relative;\n}\n\n/* 用户图片展示区域 */\n.user-gallery {\n\tmargin: 0 -16rpx 20rpx -16rpx;\n}\n\n.gallery-main {\n\tposition: relative;\n\tborder-radius: 16rpx 16rpx 16rpx 16rpx;\n\toverflow: hidden;\n\tbackground: #f5f5f5;\n}\n\n.main-photo-container {\n\tposition: relative;\n\twidth: 100%;\n\theight: 480rpx;\n}\n\n.main-photo {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: block;\n\topacity: 0;\n\ttransition: opacity 0.25s ease;\n\twill-change: opacity;\n}\n\n.main-photo.active {\n\topacity: 1;\n\tz-index: 2;\n}\n\n\n\n/* 缩略图导航 */\n.photo-thumbnails {\n\tposition: absolute;\n\tbottom: 12rpx;\n\tright: 12rpx;\n\tdisplay: flex;\n\tgap: 6rpx;\n\tbackground: rgba(0, 0, 0, 0.6);\n\tpadding: 6rpx;\n\tborder-radius: 10rpx;\n\tbackdrop-filter: blur(10rpx);\n\tz-index: 10;\n}\n\n.thumbnail-item {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder-radius: 6rpx;\n\toverflow: hidden;\n\tborder: 1.5rpx solid transparent;\n\ttransition: all 0.3s ease;\n\tcursor: pointer;\n}\n\n.thumbnail-item.active {\n\tborder-color: #fff;\n\ttransform: scale(1.1);\n}\n\n.thumbnail-image {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: block;\n}\n\n/* 图片计数器 */\n.photo-counter {\n\tposition: absolute;\n\ttop: 12rpx;\n\tright: 12rpx;\n\tbackground: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tpadding: 4rpx 10rpx;\n\tborder-radius: 16rpx;\n\tfont-size: 20rpx;\n\tbackdrop-filter: blur(10rpx);\n\tz-index: 10;\n}\n\n.user-avatar {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tmargin-right: 20rpx;\n}\n\n.vip-badge {\n\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\n\tborder-radius: 20rpx;\n\tpadding: 8rpx 16rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n\n\n.vip-text {\n\tcolor: white;\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n}\n\n.user-info {\n\tflex: 1;\n\tpadding: 24rpx;\n}\n\n.basic-info {\n\tmargin-bottom: 20rpx;\n}\n\n.name-row {\n\tdisplay: flex;\n\talign-items: flex-start;\n\tjustify-content: space-between;\n\tmargin-bottom: 8rpx;\n}\n\n.name-gender {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n}\n\n.follow-btn-inline {\n\tmin-width: 120rpx;\n\theight: 60rpx;\n\tborder-radius: 30rpx;\n\tbackground: #FF6681;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 6rpx;\n\tpadding: 0 16rpx;\n\ttransition: all 0.3s ease;\n\tcursor: pointer;\n\tflex-shrink: 0;\n}\n\n.follow-btn-inline.followed {\n\tbackground: #FF6681;\n\topacity: 0.8;\n}\n\n.follow-btn-inline.loading {\n\topacity: 0.6;\n\tpointer-events: none;\n}\n\n.follow-btn-inline:active {\n\ttransform: scale(0.9);\n}\n\n\n.nickname {\n\tfont-size: $title-size-lg;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-right: 12rpx;\n}\n\n.gender-icon {\n\tfont-size: 28rpx;\n\tline-height: 1;\n\tmargin-right: 12rpx;\n}\n\n.user-id-label {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-right: 6rpx;\n}\n\n.user-id-value {\n\tfont-size: 24rpx;\n\tcolor: $uni-text-color-disable;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\tpadding: 2rpx 6rpx;\n\tborder-radius: 4rpx;\n}\n\n.user-id-value:active {\n\tbackground: rgba(105, 108, 243, 0.1);\n\ttransform: scale(0.95);\n}\n\n.follow-section {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4rpx;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.follow-section:active {\n\ttransform: scale(0.95);\n}\n\n.follow-text {\n\tline-height: 1;\n\tfont-size: $font-size-xs;\n\tcolor: #fff;\n\tfont-weight: 500;\n}\n\n.user-id-container {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-left: 12rpx;\n\tcursor: pointer;\n}\n\n.user-id-icon {\n\tfont-size: 24rpx;\n\tcolor: #fff;\n\tmargin-right: 6rpx;\n\tline-height: 1;\n\tborder: none;\n\tborder-radius: 12rpx;\n\tpadding: 2rpx 4rpx;\n\tbackground: #999;\n\tdisplay: inline-block;\n\ttext-align: center;\n\tmin-width: 36rpx;\n\tfont-style: italic;\n}\n\n.user-id {\n\tfont-size: $font-size-xs;\n\tcolor: #999;\n\tbackground: #f5f5f5;\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 12rpx;\n}\n\n.user-stats {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tmargin-top: 16rpx;\n}\n\n.stat-item {\n\tfont-size: $font-size-sm;\n\tcolor: #666;\n\tbackground: #f8f9fa;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 16rpx;\n}\n\n.location-info {\n\tmargin-bottom: 20rpx;\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 16rpx;\n}\n\n.location-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tflex: 1;\n\tmin-width: calc(50% - 8rpx);\n\tmargin-bottom: 8rpx;\n}\n\n.location-text {\n\tfont-size: $font-size-sm;\n\tcolor: #666;\n}\n\n.verification-status {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.verify-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.verify-text {\n\tfont-size: 24rpx;\n\tcolor: #4CD964;\n}\n\n.action-buttons {\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 20rpx;\n\tpadding: 24rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.action-btn {\n\tbackground: white;\n\tcolor: $primary-color;\n\tborder: none;\n\tborder-radius: 50rpx;\n\tpadding: 12rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tfont-size: 26rpx;\n\tmin-width: 120rpx;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n}\n\n.action-btn::after {\n\tborder: none;\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n.action-btn.like-btn {\n\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\n\tcolor: white;\n\tborder: none;\n\tbox-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);\n}\n\n.about-me,\n.mate-requirements,\n.latest-moments,\n.identity-verification {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tmargin-bottom: 16rpx;\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\n}\n\n.gift-section {\n\tbackground: white;\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tmargin-bottom: 120rpx;\n\tbox-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);\n}\n\n.section-title {\n\tfont-size: $title-size-md;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.gift-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.gift-arrow {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.gift-header .section-title {\n\tmargin-bottom: 0;\n}\n\n.gift-title-row {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.gift-title-row .section-title {\n\tmargin-bottom: 0;\n\tdisplay: inline;\n}\n\n.gift-count-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tfont-weight: 400;\n}\n\n.verification-header {\n\tmargin-bottom: 20rpx;\n}\n\n.verification-title {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.verification-text {\n\tfont-size: $title-size-md;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.verification-status {\n\tfont-size: $font-size-xs;\n\tcolor: #999;\n}\n\n.verification-items {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tgap: 16rpx;\n\tbackground: rgba(105, 108, 243, 0.05);\n\tborder-radius: 12rpx;\n\tpadding: 16rpx;\n}\n\n.verification-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 8rpx;\n\tflex: 1;\n\tpadding: 16rpx 8rpx;\n\tborder-radius: 12rpx;\n\ttransition: all 0.3s ease;\n}\n\n.verification-item.verified {\n\tbackground: rgba(105, 108, 243, 0.1);\n}\n\n.verification-item.unverified {\n\tbackground: rgba(204, 204, 204, 0.1);\n}\n\n.verification-item .item-label {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n\n.verification-item.unverified .item-label {\n\tcolor: #999;\n}\n\n.tags-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 16rpx;\n}\n\n.tag-group {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 12rpx;\n}\n\n.tag-label {\n\tbackground: #f8f9fa;\n\tcolor: #666;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 16rpx;\n\tfont-size: $font-size-sm;\n}\n\n.intro-text {\n\tfont-size: $font-size-md;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.intro-text.no-data,\n.tag-label.no-data {\n\tcolor: #999;\n\tfont-style: italic;\n}\n\n.advantages-section {\n\tmargin-top: 16rpx;\n}\n\n.advantages-title {\n\tfont-size: $font-size-md;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 12rpx;\n\tdisplay: block;\n}\n\n.advantages-text {\n\tfont-size: $font-size-md;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.divider-line {\n\twidth: 100%;\n\theight: 1rpx;\n\tbackground: #f0f0f0;\n\tmargin: 20rpx 0;\n}\n\n.moment-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n}\n\n.moment-header:active {\n\ttransform: scale(0.98);\n}\n\n.moment-title-row {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.moment-title-row .section-title {\n\tmargin-bottom: 0;\n\tdisplay: inline;\n}\n\n.moment-count {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tfont-weight: 400;\n}\n\n.more-arrow {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.moment-content {\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\tpadding: 12rpx 0;\n\tborder-radius: 8rpx;\n}\n\n.moment-content:active {\n\tbackground: rgba(105, 108, 243, 0.05);\n\ttransform: scale(0.98);\n}\n\n.moment-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tline-height: 1.5;\n\tmargin-bottom: 8rpx;\n\tdisplay: block;\n}\n\n.moment-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.moment-main {\n\tdisplay: flex;\n\tgap: 16rpx;\n\talign-items: flex-start;\n}\n\n.moment-text-area {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8rpx;\n}\n\n.moment-image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 12rpx;\n\tflex-shrink: 0;\n}\n\n.req-description {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.gifts-container {\n\tdisplay: flex;\n\tgap: 20rpx;\n\toverflow-x: auto;\n}\n\n.gift-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 8rpx;\n\tmin-width: 100rpx;\n\tposition: relative;\n}\n\n.gift-icon {\n\twidth: 60rpx;\n\theight: 60rpx;\n}\n\n.gift-name {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\ttext-align: center;\n}\n\n.gift-count {\n\tfont-size: 20rpx;\n\tcolor: #999;\n\ttext-align: center;\n\tmargin-top: 4rpx;\n}\n\n.empty-gifts {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 40rpx 0;\n\twidth: 100%;\n}\n\n.empty-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tfont-style: italic;\n}\n\n\n\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: white;\n\tpadding: 16rpx 24rpx;\n\tpadding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n\tdisplay: flex;\n\tgap: 16rpx;\n\tbox-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);\n}\n\n.bottom-btn {\n\tflex: 1;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tpadding: 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 6rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n}\n\n.bottom-btn::after {\n\tborder: none;\n}\n\n.bottom-btn:active {\n\ttransform: scale(0.95);\n}\n\n.send-msg-btn {\n\tbackground: $primary-color;\n\tcolor: white;\n}\n\n.like-btn {\n\tbackground: #ff4757;\n\tcolor: white;\n}\n\n.contact-btn {\n\tbackground: #2ed573;\n\tcolor: white;\n}\n\n// 浮动底部操作按钮\n.floating-bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: rgba(255, 255, 255, 0.6);\n\tpadding: 20rpx 32rpx;\n\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tz-index: 1000;\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tjustify-content: center;\n\tmin-height: 64rpx;\n\tborder-radius: 50rpx;\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\toverflow: hidden;\n\tgap: 8rpx;\n\tbackground: white;\n\tborder: none;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n.gift-btn {\n\tflex: 1;\n\tcolor: $primary-color;\n}\n\n.chat-btn {\n\tflex: 1;\n\tbackground: #FF6681 !important;\n\tcolor: white !important;\n\tborder: none !important;\n}\n\n.chat-btn .btn-text {\n\tcolor: white !important;\n}\n\n.main-btn {\n\tflex: 2;\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n\tcolor: white;\n\tfont-weight: 600;\n\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);\n}\n\n.btn-text {\n\tfont-size: 26rpx;\n\tcolor: #696CF3;\n}\n\n.main-btn .btn-text {\n\tcolor: white;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n}\n\n.hi-text {\n\tbackground: white;\n\tcolor: $primary-color;\n\tborder: 1rpx solid $primary-color;\n\tborder-radius: 50rpx;\n\tpadding: 8rpx 16rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n// 右侧浮动按钮组\n.floating-buttons-group {\n\tposition: fixed;\n\tbottom: 210rpx;\n\tright: 32rpx;\n\tz-index: 1001;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 16rpx;\n}\n\n.floating-btn {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 6rpx 20rpx rgba(105, 108, 243, 0.3);\n\ttransition: all 0.3s ease;\n}\n\n.floating-btn:active {\n\ttransform: scale(0.9);\n}\n\n.share-btn {\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n}\n\n.follow-btn {\n\tbackground: #FF6681;\n\ttransition: all 0.3s ease;\n}\n\n.follow-btn.followed {\n\tbackground: #FF6681;\n\topacity: 0.8;\n}\n\n.more-btn {\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n}\n\n// 横向弹出菜单\n.horizontal-menu {\n\tposition: absolute;\n\tright: 96rpx;\n\tbottom: 0;\n\tdisplay: flex;\n\tgap: 12rpx;\n\topacity: 0;\n\ttransform: translateX(20rpx);\n\ttransition: all 0.3s ease;\n\tpointer-events: none;\n}\n\n.horizontal-menu.show {\n\topacity: 1;\n\ttransform: translateX(0);\n\tpointer-events: auto;\n}\n\n.menu-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 50rpx;\n\tbackground: white;\n\tborder: 1rpx solid rgba(105, 108, 243, 0.2);\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\ttransition: all 0.3s ease;\n\tmin-width: 120rpx;\n\tjustify-content: center;\n}\n\n.menu-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);\n}\n\n.report-btn {\n\tborder-color: rgba(255, 107, 107, 0.3);\n}\n\n.report-btn .menu-text {\n\tcolor: #ff6b6b;\n}\n\n.block-btn {\n\tborder-color: rgba(102, 102, 102, 0.3);\n}\n\n.block-btn .menu-text {\n\tcolor: #666;\n}\n\n.menu-text {\n\tfont-size: 24rpx;\n\tcolor: $primary-color;\n\tfont-weight: 500;\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/personals/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["GiftModal", "ScrollNavPage", "AuthCard", "userId", "ref", "userDetail", "userAlbums", "authStatus", "onLoad", "options", "uni", "recordUserHomeBrowse", "loadUserData", "userInfo", "isFollowed", "currentPhotoIndex", "loadedPhotos", "showMoreMenu", "receivedGiftsCount", "isFollowLoading", "isSendingGift", "giftModalVisible", "giftWallData", "latestMoment", "reactive", "toggleMoreMenu", "switchPhoto", "index", "recordUserAlbumBrowse", "handlePhotoLoad", "to<PERSON><PERSON><PERSON><PERSON>", "toggle<PERSON>ser<PERSON><PERSON>ow", "response", "copyUserId", "sendGift", "closeGiftModal", "handleSendGift", "giftData", "sendGiftToUser", "fetchReceivedGiftsCount", "error", "requestKnow", "err", "startChat", "shareProfile", "reportUser", "blockUser", "res", "addBlacklist", "goToMoments", "goToGiftPage", "fetchUserDetail", "getUserDetail", "updateUserInfo", "fetchGiftWall", "getMyReceivedGifts", "getUserGiftWall", "fetchUserLatestMoment", "getUserLatestMoment", "fetchUserAlbums", "getAlbums", "extractIntroductionFromTags", "tags", "aboutMeTag", "tag", "extractAdvantagesFromTags", "advantagesTag", "extractAboutMeTagsFromTags", "extractRequirementTagsFromRequireTags", "requireTags", "extractDreamPartnerFromRequireTags", "dreamPartnerTag", "userData", "createUserHomeBrowseHistory", "albumId", "createUserAlbumBrowseHistory", "handleAuthComplete", "authType", "MiniProgramPage"], "mappings": "ocAiPA,MAAMA,GAAY,IAAW,uBAEvBC,GAAgB,IAAW,sDAC3BC,GAAW,IAAW,wEAU5B,IAAIC,EAASC,EAAG,IAAC,GAAG,EAGpB,MAAMC,EAAaD,EAAG,IAAC,EAAE,EAGnBE,EAAaF,EAAG,IAAC,EAAE,EAGnBG,EAAaH,EAAAA,IAAI,CACtB,gBAAiB,GACjB,UAAW,CAAE,CACd,CAAC,EAGDI,EAAM,OAAEC,GAAY,CACnBC,EAAAA,0DAAY,QAASD,CAAO,EACxBA,GAAWA,EAAQ,SACtBN,EAAO,MAAQM,EAAQ,OAEvBE,EAAqBF,EAAQ,MAAM,EACnCG,GAAc,EAEhB,CAAC,EAGD,MAAMC,EAAWT,EAAG,IAAC,EAAE,EAEPA,EAAG,IAAC,EAAK,EACzB,MAAMU,EAAaV,EAAG,IAAC,EAAK,EACtBW,EAAoBX,EAAG,IAAC,CAAC,EACzBY,EAAeZ,EAAAA,IAAI,IAAI,GAAK,EAC5Ba,EAAeb,EAAG,IAAC,EAAK,EACxBc,EAAqBd,EAAG,IAAC,EAAE,EAC3Be,EAAkBf,EAAG,IAAC,EAAK,EAC3BgB,EAAgBhB,EAAG,IAAC,EAAK,EAEzBiB,EAAmBjB,EAAG,IAAC,EAAK,EAG5BkB,EAAelB,EAAAA,IAAI,CACxB,eAAgB,EAChB,SAAU,CAAE,CACb,CAAC,EAGKmB,EAAeC,EAAAA,SAAS,CAC7B,YAAa,EACb,KAAM,SACN,KAAM,GACN,MAAO,IACR,CAAC,EAGKC,EAAiB,IAAM,CAC5BR,EAAa,MAAQ,CAACA,EAAa,KACpC,EAGMS,EAAeC,GAAU,CAC1BA,IAAUZ,EAAkB,QAGhCA,EAAkB,MAAQY,EAGtBrB,EAAW,MAAMqB,CAAK,GAAKrB,EAAW,MAAMqB,CAAK,EAAE,IACtDC,EAAsBtB,EAAW,MAAMqB,CAAK,EAAE,EAAE,EAElD,EAEME,EAAmBF,GAAU,CAClCX,EAAa,MAAM,IAAIW,CAAK,CAC7B,EAUMG,EAAe,IAAM,CAE1B,GAAI,CAAAX,EAAgB,MAIpB,IAAI,CAACN,EAAS,MAAM,OAAQ,CAC3BH,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,MACT,CAAG,EACD,MACA,CAGDS,EAAgB,MAAQ,GACxBY,oBAAiBlB,EAAS,MAAM,OAAQA,EAAS,MAAM,UAAU,EAAE,KAAKmB,GAAY,CAEnFnB,EAAS,MAAM,WAAa,CAACA,EAAS,MAAM,WAC5CC,EAAW,MAAQD,EAAS,MAAM,WAElCH,EAAAA,MAAI,UAAU,CACb,MAAOG,EAAS,MAAM,WAAa,OAAS,SAC5C,KAAM,UACN,SAAU,GACb,CAAG,EAEDH,EAAY,MAAA,MAAA,MAAA,wCAAA,YAAaG,EAAS,MAAM,UAAU,CACpD,CAAE,EAAE,QAAQ,IAAM,CAEhBM,EAAgB,MAAQ,EAC1B,CAAE,EACF,EAEMc,EAAa,IAAM,CACxBvB,EAAAA,MAAI,iBAAiB,CACpB,KAAMG,EAAS,MAAM,OACrB,QAAS,IAAM,CACdH,EAAAA,MAAI,UAAU,CACb,MAAO,UACP,KAAM,UACN,SAAU,IACd,CAAI,CACD,EACD,KAAM,IAAM,CACXA,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACN,SAAU,IACd,CAAI,CACD,CACH,CAAE,CACF,EAGMwB,EAAW,IAAM,CACtBb,EAAiB,MAAQ,EAC1B,EAGMc,EAAiB,IAAM,CAC5Bd,EAAiB,MAAQ,GAEzBD,EAAc,MAAQ,EACvB,EAGMgB,EAAiB,MAAOC,GAAa,CAEtCjB,EAAc,QAKlBA,EAAc,MAAQ,GAEtBkB,EAAc,eACbnC,EAAO,MACP,CACC,GAAIkC,EAAS,KAAK,GAClB,KAAMA,EAAS,KAAK,KACpB,MAAOA,EAAS,KAAK,KACrB,EACDA,EAAS,UAAY,CACvB,EAAG,KAAKL,GAAY,CAClBtB,EAAAA,MAAI,UAAU,CACb,MAAO,OAAO2B,EAAS,KAAK,IAAI,GAChC,KAAM,SACT,CAAG,EAGDF,EAAgB,EAChBI,EAAyB,CAC3B,CAAE,EAAE,MAAMC,GAAS,CACjB9B,EAAAA,MAAc,MAAA,QAAA,wCAAA,SAAU8B,CAAK,EAC7B9B,EAAAA,MAAI,UAAU,CACb,MAAO,YACP,KAAM,OACN,SAAU,GACb,CAAG,CACH,CAAE,EAAE,QAAQ,IAAM,CAEhBU,EAAc,MAAQ,EACxB,CAAE,EACF,EAGMqB,EAAc,IAAM,CAEzB/B,EAAAA,MAAI,WAAW,CACd,IAAK,gDAAgDP,EAAO,KAAK,aAAa,mBAAmBU,EAAS,MAAM,QAAQ,CAAC,WAAWA,EAAS,MAAM,MAAM,GACzJ,KAAO6B,GAAQ,CACdhC,EAAAA,MAAc,MAAA,QAAA,wCAAA,aAAcgC,CAAG,EAC/BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMiC,EAAY,IAAM,CACvBjC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,SACR,CAAE,CACF,EAGMkC,EAAe,IAAM,CAC1BlC,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,SACR,CAAE,CACF,EAGMmC,EAAa,IAAM,CACxB5B,EAAa,MAAQ,GACrBP,EAAAA,MAAI,WAAW,CACd,IAAK,8CAA8CP,EAAO,KAAK,eAAe,mBAAmBU,EAAS,MAAM,QAAQ,CAAC,EAC3H,CAAE,CACF,EAGMiC,EAAY,IAAM,CACvB7B,EAAa,MAAQ,GACrBP,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,QAAS,yBACT,YAAa,OACb,WAAY,KACZ,QAAUqC,GAAQ,CACbA,EAAI,SACPC,gBAAa,CACZ,eAAgB7C,EAAO,KAC5B,CAAK,EAAE,KAAK6B,GAAY,CACnBtB,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,SACZ,CAAM,CACN,CAAK,CAEF,CACH,CAAE,CACF,EAGMuC,EAAc,IAAM,CACzBvC,EAAAA,MAAI,WAAW,CACd,IAAK,sCAAsCP,EAAO,KAAK,GACvD,KAAOuC,GAAQ,CACdhC,EAAAA,MAAA,MAAA,QAAA,wCAAc,YAAagC,CAAG,EAC9BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMwC,EAAe,IAAM,CAC1BxC,EAAAA,MAAI,WAAW,CACd,IAAK,wCAAwCP,EAAO,KAAK,GACzD,KAAOuC,GAAQ,CACdhC,EAAAA,MAAA,MAAA,QAAA,wCAAc,YAAagC,CAAG,EAC9BhC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,OACV,CAAI,CACD,CACH,CAAE,CACF,EAGMyC,EAAkB,IAAM,CAC7BC,GAAAA,cAAcjD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CAC5C3B,EAAW,MAAQ2B,EAAS,KAE5BqB,EAAerB,EAAS,IAAI,EAE5BsB,EAAe,CACjB,CAAE,CACF,EAGMf,EAA0B,IAAM,CAEjCpC,EAAO,OAASA,EAAO,QAAU,WAOrCoD,qBAAmB,CAAE,SAAU,EAAG,QAAS,EAAG,EAAE,KAAKvB,GAAY,EAC5DA,EAAS,OAAS,KAAOA,EAAS,OAAS,KAC9Cd,EAAmB,MAAQc,EAAS,OAAS,EAC7CtB,EAAA,MAAA,MAAA,MAAA,wCAAY,WAAYQ,EAAmB,KAAK,EAEnD,CAAE,EAAE,MAAMsB,GAAS,CACjB9B,EAAAA,4DAAc,YAAa8B,CAAK,CAClC,CAAE,CACF,EAGMc,EAAgB,IAAM,CAC3BE,kBAAgB,CACf,OAAQrD,EAAO,MACf,SAAU,GACV,QAAS,CACX,CAAE,EAAE,KAAK6B,GAAY,CACnBV,EAAa,MAAQU,EAAS,MAAQ,CAAE,eAAgB,EAAG,SAAU,EAAI,EAEzEd,EAAmB,MAAQI,EAAa,MAAM,gBAAkB,EAChEZ,EAAA,MAAA,MAAA,MAAA,wCAAY,SAAUY,EAAa,KAAK,CAC1C,CAAE,CACF,EAGMmC,EAAwB,IAAM,CACnCC,GAAAA,oBAAoBvD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CAC9CA,EAAS,OAEZT,EAAa,KAAOS,EAAS,KAAK,QAClCT,EAAa,KAAOS,EAAS,KAAK,KAClCT,EAAa,MAAQS,EAAS,KAAK,WACnCT,EAAa,YAAcS,EAAS,KAAK,kBAE5C,CAAE,CACF,EAGM2B,EAAkB,IAAM,CAC7BC,GAAAA,UAAUzD,EAAO,KAAK,EAAE,KAAK6B,GAAY,CACxC1B,EAAW,MAAQ0B,EAAS,KAGxBA,EAAS,KAAK,OAAS,GAAKA,EAAS,KAAK,CAAC,EAAE,IAChDJ,EAAsBI,EAAS,KAAK,CAAC,EAAE,EAAE,CAE5C,CAAE,CACF,EAGM6B,EAA+BC,GAAS,CAC7C,GAAI,CAAC,MAAM,QAAQA,CAAI,EAAG,MAAO,GAEjC,MAAMC,EAAaD,EAAK,KAAKE,GAAOA,EAAI,SAAW,UAAU,EAC7D,OAAOD,EAAaA,EAAW,OAAS,EACzC,EAGME,EAA6BH,GAAS,CAC3C,GAAI,CAAC,MAAM,QAAQA,CAAI,EAAG,MAAO,GAEjC,MAAMI,EAAgBJ,EAAK,KAAKE,GAAOA,EAAI,SAAW,YAAY,EAClE,OAAOE,EAAgBA,EAAc,OAAS,EAC/C,EAGMC,EAA8BL,GAC9B,MAAM,QAAQA,CAAI,EAEhBA,EACL,OAAOE,GAAOA,EAAI,YAAc,SAAS,EACzC,IAAIA,IAAQ,CACZ,IAAKA,EAAI,OACT,MAAOA,EAAI,OACX,MAAOA,EAAI,MACd,EAAI,EAR8B,CAAE,EAY9BI,EAAyCC,GACzC,MAAM,QAAQA,CAAW,EAEvBA,EACL,OAAOL,GAAOA,EAAI,YAAc,SAAS,EACzC,IAAIA,IAAQ,CACZ,IAAKA,EAAI,OACT,MAAOA,EAAI,OACX,MAAOA,EAAI,MACd,EAAI,EARqC,CAAE,EAYrCM,EAAsCD,GAAgB,CAC3D,GAAI,CAAC,MAAM,QAAQA,CAAW,EAAG,MAAO,GAExC,MAAME,EAAkBF,EAAY,KAAKL,GACxCA,EAAI,YAAc,WAAaA,EAAI,SAAW,eAC9C,EACD,OAAOO,EAAkBA,EAAgB,OAAS,EACnD,EAGMlB,EAAkBmB,GAAa,CACpC3D,EAAS,MAAQ2D,EACjB3D,EAAS,MAAM,OAAS2D,EAAS,MAAQ,IAAM,OAAS,SAGxD3D,EAAS,MAAM,aAAegD,EAA4BW,EAAS,IAAI,GAAK,GAG5E3D,EAAS,MAAM,WAAaoD,EAA0BO,EAAS,IAAI,GAAK,GAGxE3D,EAAS,MAAM,YAAcsD,EAA2BK,EAAS,IAAI,GAAK,CAAE,EAG5E3D,EAAS,MAAM,gBAAkBuD,EAAsCI,EAAS,WAAW,GAAK,CAAE,EAGlG3D,EAAS,MAAM,wBAA0ByD,EAAmCE,EAAS,WAAW,GAAK,GAGrG1D,EAAW,MAAQ0D,EAAS,YAAc,GAE1C9D,4DAAY,QAASG,EAAS,MAAM,YAAY,EAChDH,EAAY,MAAA,MAAA,MAAA,wCAAA,QAASG,EAAS,MAAM,UAAU,EAC9CH,4DAAY,SAAUG,EAAS,MAAM,WAAW,EAChDH,4DAAY,QAASG,EAAS,MAAM,eAAe,EACnDH,EAAA,MAAA,MAAA,MAAA,wCAAY,SAAUG,EAAS,MAAM,uBAAuB,EAC5DH,EAAA,MAAA,MAAA,MAAA,wCAAY,QAASI,EAAW,KAAK,EAGrCP,EAAW,MAAM,gBAAkBiE,EAAS,iBAAmB,GAC/DjE,EAAW,MAAM,UAAYiE,EAAS,WAAa,CAAE,CACtD,EAgBM7D,EAAwBR,GAAW,CACxCsE,EAAAA,4BAA4BtE,CAAM,CACnC,EAGMyB,EAAyB8C,GAAY,CAC1CC,EAAAA,6BAA6BD,CAAO,CACrC,EAGME,EAAsBC,GAAa,CACxCnE,EAAAA,0DAAY,QAASmE,CAAQ,EAE7B1B,EAAiB,EACjBzC,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,SACR,CAAE,CACF,EAGME,GAAe,SAAY,CAEhC,MAAM,QAAQ,IAAI,CACjBuC,EAAiB,EACjBM,EAAuB,EACvBE,EAAiB,CACnB,CAAE,CACF,67FC3tBA,GAAG,WAAWmB,EAAe"}