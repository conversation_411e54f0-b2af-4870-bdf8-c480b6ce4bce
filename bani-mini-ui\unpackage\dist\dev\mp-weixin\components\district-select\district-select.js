"use strict";const l=require("../../common/vendor.js"),q=require("../../api/district/district.js");if(!Array){const m=l.resolveComponent("uni-load-more"),f=l.resolveComponent("uni-popup");(m+f)()}const B=()=>"../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js",M=()=>"../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";Math||(B+M)();const A={__name:"district-select",props:{show:{type:Boolean,default:!1},defaultValue:{type:Object,default:()=>({})}},emits:["confirm","cancel","update:show"],setup(m,{expose:f,emit:w}){const h=w,i=l.ref(null),c=l.ref(!1),v=l.ref([]),a=l.ref([]),s=l.ref(null),t=l.ref([]),g=l.ref(0),$=e=>{e.show?L():j()},L=()=>{l.index.__f__("log","at components/district-select/district-select.vue:96","初始化行政区域选择器"),a.value=[{name:"全国",code:"0"}],t.value=[],s.value=null,_("")},j=()=>{v.value=[],a.value=[],t.value=[],s.value=null,c.value=!1},_=(e="0")=>{c.value=!0,q.getDistrictList(e).then(o=>{v.value=o.data}).finally(()=>{c.value=!1})},k=e=>{if(s.value=e,e.hasChild){const o=a.value.length;t.value.length>o&&(t.value=t.value.slice(0,o)),t.value.push(e),a.value.length>o&&(a.value=a.value.slice(0,o)),a.value.push(e),_(e.code)}else{const o=a.value.length;t.value.length>o&&(t.value=t.value.slice(0,o)),t.value.push(e),C()}},D=e=>{if(e===a.value.length-1)return;const o=a.value[e];a.value=a.value.slice(0,e+1),t.value=t.value.slice(0,e+1),e===0&&(t.value=[]),s.value=null,_(o.code)},C=()=>{var n,u,d,r;if(t.value.length===0){l.index.showToast({title:"请选择地区",icon:"none"});return}const e=I(t.value[0]),o={province:t.value[0]||null,city:t.value[e?0:1]||null,district:t.value[e?1:2]||null,street:t.value[e?2:3]||null,fullPath:t.value,fullName:t.value.map(p=>p.name).join(""),codes:{provinceCode:((n=t.value[0])==null?void 0:n.code)||"",cityCode:((u=t.value[e?0:1])==null?void 0:u.code)||"",districtCode:((d=t.value[e?1:2])==null?void 0:d.code)||"",streetCode:((r=t.value[e?2:3])==null?void 0:r.code)||""}};l.index.__f__("log","at components/district-select/district-select.vue:201","确认选择地区:",o),h("confirm",o),y()},I=e=>{switch(e.name){case"北京市":case"天津市":case"上海市":case"重庆市":case"香港":case"澳门":return!0;default:return!1}},P=()=>{l.index.__f__("log","at components/district-select/district-select.vue:225","取消选择地区"),h("cancel"),y()},y=()=>{var e;(e=i.value)==null||e.close(),h("update:show",!1)};return f({open:()=>{var e;(e=i.value)==null||e.open()},close:()=>{var e;(e=i.value)==null||e.close()}}),(e,o)=>l.e({a:l.o(P),b:l.o(C),c:a.value.length>0},a.value.length>0?{d:l.f(a.value,(n,u,d)=>l.e({a:l.t(n.name),b:u<a.value.length-1},u<a.value.length-1?{}:{},{c:u,d:l.o(r=>D(u),u)}))}:{},{e:l.f(v.value,(n,u,d)=>{var r,p,b;return l.e({a:l.t(n.name),b:n.code===((r=s.value)==null?void 0:r.code)},n.code===((p=s.value)==null?void 0:p.code)?{}:n.hasChild?{}:{},{c:n.hasChild,d:`${g.value}-${n.code}-${u}`,e:n.code===((b=s.value)==null?void 0:b.code)?1:"",f:l.o(T=>k(n),`${g.value}-${n.code}-${u}`)})}),f:c.value},c.value?{g:l.p({status:"loading","content-text":{contentdown:"加载中...",contentrefresh:"加载中...",contentnomore:"加载中..."}})}:{},{h:!c.value&&v.value.length===0},!c.value&&v.value.length===0?{}:{},{i:l.sr(i,"ef5086dd-0",{k:"popup"}),j:l.o($),k:l.p({type:"bottom","safe-area":!1})})}},K=l._export_sfc(A,[["__scopeId","data-v-ef5086dd"]]);wx.createComponent(K);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/district-select/district-select.js.map
