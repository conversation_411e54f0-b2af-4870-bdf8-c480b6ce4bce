package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.greeting.AppUserGreetingCreateBoToUserGreetingMapper;
import com.gzhuxn.personals.controller.app.user.vo.greeting.AppUserGreetingVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserGreetingCreateBoToUserGreetingMapper.class,UserGreetingToUserGreetingVoMapper.class},
    imports = {}
)
public interface UserGreetingToAppUserGreetingVoMapper extends BaseMapper<UserGreeting, AppUserGreetingVo> {
  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserGreetingVo convert(UserGreeting source);

  @Mapping(
      target = "time",
      source = "createTime"
  )
  AppUserGreetingVo convert(UserGreeting source, @MappingTarget AppUserGreetingVo target);
}
