"use strict";const e=require("../../common/vendor.js");if(!Array){const l=e.resolveComponent("uni-icons"),o=e.resolveComponent("scroll-nav-page");(l+o)()}const f=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",x=()=>"../../components/scroll-nav-page/scroll-nav-page.js";Math||(f+x)();const r={__name:"detail",setup(l){const o=e.ref(0);e.ref(0);const n=e.ref("official"),i=e.ref(null),t=e.ref({id:1,title:"2024春季相亲交友会",image:"https://p3.toutiaoimg.com/large/tos-cn-i-qvj2lq49k0/b66de365c7614b24af852e21b4271976.jpg",name:"小明",avatar:"/static/image/avatar/1.png",type:1,location:"北京市朝阳区某咖啡厅",startTime:"2024-04-20 14:00",duration:"3小时",price:99,status:"enrolling",description:"本次活动旨在为单身人士提供一个轻松愉快的交友平台。通过精心设计的互动环节，帮助参与者更好地了解彼此，找到心仪的对象。",notices:["请准时到达活动地点","活动期间请保持手机静音","请着装整洁得体","活动费用包含场地费和饮品"]}),c=e.ref(!0),u=a=>({notStarted:"未开始",enrolling:"报名中",enrollEnd:"报名已结束",inProgress:"活动进行中",ended:"活动已结束"})[a]||"",p=a=>({1:"相亲会",2:"聊天",3:"干饭",4:"户外",5:"看展",6:"运动",7:"学习",8:"喝酒",9:"打游戏",10:"其他"})[a]||"",d=()=>{const a=t.value.status;return{notStarted:"预约报名",enrolling:"立即报名",enrollEnd:"报名已结束",inProgress:"活动进行中",ended:"活动已结束"}[a]||"立即报名"},v=()=>{c.value&&e.index.navigateTo({url:`/pages/activity/enroll/enroll?id=${t.value.id}`})},g=()=>{e.index.navigateTo({url:`/pages/message/chat/chat?id=${t.value.id}&name=${t.value.name}`})},_=a=>{o.value=a.scrollTop};return e.onPageScroll(_),e.onMounted(a=>{i.value=a.id,n.value=a.type}),(a,s)=>e.e({a:t.value.image,b:e.t(t.value.title),c:e.t(u(t.value.status)),d:e.n(t.value.status),e:n.value==="buddy"},n.value==="buddy"?{f:t.value.avatar,g:e.t(t.value.name),h:e.t(p(t.value.type))}:{},{i:e.p({type:"location",size:"16",color:"$text-secondary"}),j:e.t(t.value.location),k:e.p({type:"calendar",size:"16",color:"$text-secondary"}),l:e.t(t.value.startTime),m:e.p({type:"clock",size:"16",color:"$text-secondary"}),n:e.t(t.value.duration),o:e.p({type:"wallet",size:"16",color:"$text-secondary"}),p:e.t(t.value.price),q:e.t(t.value.description),r:e.f(t.value.notices,(y,m,b)=>({a:e.t(y),b:m})),s:e.p({type:"redo",size:"20",color:"$text-secondary"}),t:e.p({type:"chat",size:"20",color:"$text-secondary"}),v:e.o(g),w:e.t(d()),x:c.value?"":1,y:e.o(v),z:e.p({title:"活动详情","show-back":!0})})}},h=e._export_sfc(r,[["__scopeId","data-v-1e0aca97"]]);r.__runtimeHooks=3;wx.createPage(h);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesubs/activity/detail.js.map
