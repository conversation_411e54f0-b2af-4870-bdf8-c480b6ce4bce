package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.follow.AppFollowUserFollowVo;
import com.gzhuxn.personals.domain.user.bo.UserFollowBoToUserFollowMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserFollowBoToUserFollowMapper.class,UserFollowToUserFollowVoMapper.class},
    imports = {}
)
public interface UserFollowToAppFollowUserFollowVoMapper extends BaseMapper<UserFollow, AppFollowUserFollowVo> {
  @Mapping(
      target = "uid",
      source = "businessId"
  )
  @Mapping(
      target = "oppUserId",
      source = "businessId"
  )
  AppFollowUserFollowVo convert(UserFollow source);

  @Mapping(
      target = "uid",
      source = "businessId"
  )
  @Mapping(
      target = "oppUserId",
      source = "businessId"
  )
  AppFollowUserFollowVo convert(UserFollow source, @MappingTarget AppFollowUserFollowVo target);
}
