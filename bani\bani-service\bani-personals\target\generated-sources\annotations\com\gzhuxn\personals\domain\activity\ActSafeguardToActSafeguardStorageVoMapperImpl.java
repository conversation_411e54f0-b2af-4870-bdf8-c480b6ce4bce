package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActSafeguardStorageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardToActSafeguardStorageVoMapperImpl implements ActSafeguardToActSafeguardStorageVoMapper {

    @Override
    public ActSafeguardStorageVo convert(ActSafeguard arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguardStorageVo actSafeguardStorageVo = new ActSafeguardStorageVo();

        actSafeguardStorageVo.setId( arg0.getId() );
        actSafeguardStorageVo.setName( arg0.getName() );
        actSafeguardStorageVo.setAmount( arg0.getAmount() );

        return actSafeguardStorageVo;
    }

    @Override
    public ActSafeguardStorageVo convert(ActSafeguard arg0, ActSafeguardStorageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAmount( arg0.getAmount() );

        return arg1;
    }
}
