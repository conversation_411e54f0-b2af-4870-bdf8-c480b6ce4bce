package com.gzhuxn.personals.domain.audit;

import com.gzhuxn.personals.domain.audit.vo.ContentAuditVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ContentAuditRecordToContentAuditDetailVoMapper.class},
    imports = {}
)
public interface ContentAuditRecordToContentAuditVoMapper extends BaseMapper<ContentAuditRecord, ContentAuditVo> {
}
