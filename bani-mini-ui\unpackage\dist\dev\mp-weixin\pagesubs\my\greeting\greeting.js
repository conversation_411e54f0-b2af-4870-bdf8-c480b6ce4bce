"use strict";const e=require("../../../common/vendor.js"),b=require("../../../common/assets.js"),u=require("../../../api/user/greeting.js");if(!Array){const d=e.resolveComponent("uni-segmented-control"),l=e.resolveComponent("custom-refresher"),r=e.resolveComponent("z-paging"),s=e.resolveComponent("scroll-nav-page");(d+l+r+s)()}const T=()=>"../../../uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.js",I=()=>"../../../components/custom-refresher/custom-refresher.js",S=()=>"../../../uni_modules/z-paging/components/z-paging/z-paging.js",z=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(T+I+S+z)();const _={__name:"greeting",setup(d){const l=e.ref(0),r=e.ref(0),s=e.ref(0),i=e.ref("received"),a=e.ref(null),p=e.ref([]);e.onPageScroll(t=>{l.value=t.scrollTop});const v=t=>{r.value=t};e.onLoad(t=>{t.type||(t.type="received");const o=t.type;s.value=o==="received"?0:1,i.value=o});function h(t,o){(i.value==="received"?u.getReceivedGreetingPage:u.getSentGreetingPage)({pageNum:t,pageSize:o}).then(c=>{a.value.complete(c.rows)})}const f=t=>{s.value!==t.currentIndex&&(s.value=t.currentIndex,i.value=s.value===0?"received":"sent",a.value.reload())},y=t=>{switch(t){case 0:return"待回复";case 1:return"已回复";case 2:return"已忽略";default:return""}},m=t=>{switch(t){case 0:return"status-pending";case 1:return"status-replied";case 2:return"status-ignored";default:return""}},x=t=>{e.index.showModal({title:"回复打招呼",editable:!0,placeholderText:"请输入回复内容...",success:o=>{o.confirm&&o.content&&u.replyGreeting(t,o.content).then(()=>{e.index.showToast({title:"回复成功",icon:"success"}),a.value.reload()}).catch(n=>{e.index.showToast({title:n.message||"回复失败",icon:"none"})})}})},w=t=>{e.index.showModal({title:"确认忽略",content:"确定要忽略这条打招呼吗？",success:o=>{o.confirm&&u.ignoreGreeting(t).then(()=>{e.index.showToast({title:"已忽略",icon:"success"}),a.value.reload()}).catch(n=>{e.index.showToast({title:n.message||"操作失败",icon:"none"})})}})};return(t,o)=>({a:e.o(f),b:e.p({current:s.value,values:["想认识我的","我想认识的"],styleType:"text",activeColor:"#696CF3"}),c:r.value+"px",d:e.w(({refresherStatus:n},c,g)=>({a:"9184a8c5-3-"+g+",9184a8c5-1",b:e.p({"refresher-status":n}),c:g,d:c}),{name:"refresher",path:"d",vueId:"9184a8c5-1,9184a8c5-0"}),e:e.f(p.value,(n,c,g)=>e.e({a:n.oppAvatar,b:e.t(n.oppNickName),c:e.n(n.oppSex==="0"?"bani-xingbie-nan":"bani-xingbie-nv"),d:n.oppSex==="0"?"#4A90E2":"#E91E63",e:n.oppIsIdentity},n.oppIsIdentity?{}:{},{f:e.t(n.time),g:e.t(n.oppAge),h:e.t(n.oppHeight),i:e.t(n.oppCity),j:e.t(n.content||"向你打招呼"),k:n.status},n.status?{l:e.t(y(n.status)),m:e.n(m(n.status))}:{},{n:n.status===1&&n.replyContent},n.status===1&&n.replyContent?{o:e.t(n.replyContent)}:{},{p:s.value===0&&n.status===0},s.value===0&&n.status===0?{q:e.o(C=>w(n.id),n.id),r:e.o(C=>x(n.id),n.id)}:{},{s:n.id})),f:b._imports_0$2,g:e.sr(a,"9184a8c5-1,9184a8c5-0",{k:"paging"}),h:e.o(h),i:e.o(n=>p.value=n),j:e.p({auto:!0,"refresher-enabled":!0,"loading-more-enabled":!0,modelValue:p.value}),k:e.o(v),l:e.p({title:"打招呼记录","show-back":!0})})}},j=e._export_sfc(_,[["__scopeId","data-v-9184a8c5"]]);_.__runtimeHooks=1;wx.createPage(j);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/greeting/greeting.js.map
