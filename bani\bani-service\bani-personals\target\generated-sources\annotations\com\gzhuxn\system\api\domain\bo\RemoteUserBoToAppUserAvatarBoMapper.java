package com.gzhuxn.system.api.domain.bo;

import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBoToRemoteUserBoMapper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBoToRemoteUserBoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserAvatarBoToRemoteUserBoMapper.class,AppUserBaseBoToRemoteUserBoMapper.class},
    imports = {}
)
public interface RemoteUserBoToAppUserAvatarBoMapper extends BaseMapper<RemoteUserBo, AppUserAvatarBo> {
}
