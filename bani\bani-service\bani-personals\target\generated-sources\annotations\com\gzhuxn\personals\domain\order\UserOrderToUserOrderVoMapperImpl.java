package com.gzhuxn.personals.domain.order;

import com.gzhuxn.personals.domain.order.vo.UserOrderVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserOrderToUserOrderVoMapperImpl implements UserOrderToUserOrderVoMapper {

    @Override
    public UserOrderVo convert(UserOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserOrderVo userOrderVo = new UserOrderVo();

        userOrderVo.setId( arg0.getId() );
        userOrderVo.setUserId( arg0.getUserId() );
        if ( arg0.getAmount() != null ) {
            userOrderVo.setAmount( arg0.getAmount().intValue() );
        }
        userOrderVo.setWithdrawCoin( arg0.getWithdrawCoin() );
        userOrderVo.setCoin( arg0.getCoin() );
        userOrderVo.setStatus( arg0.getStatus() );
        userOrderVo.setPayTime( arg0.getPayTime() );
        userOrderVo.setFailMsg( arg0.getFailMsg() );
        userOrderVo.setFailDesc( arg0.getFailDesc() );

        return userOrderVo;
    }

    @Override
    public UserOrderVo convert(UserOrder arg0, UserOrderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        if ( arg0.getAmount() != null ) {
            arg1.setAmount( arg0.getAmount().intValue() );
        }
        else {
            arg1.setAmount( null );
        }
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setFailMsg( arg0.getFailMsg() );
        arg1.setFailDesc( arg0.getFailDesc() );

        return arg1;
    }
}
