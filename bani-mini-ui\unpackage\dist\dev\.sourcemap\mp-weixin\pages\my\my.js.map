{"version": 3, "file": "my.js", "sources": ["pages/my/my.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvbXkudnVl"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"个人中心\"  @heightChange=\"handleNavHeightChange\">\r\n\t\t<template #content>\r\n\t\t\t<!-- 未注册 -->\r\n\t\t\t<unregistered-user v-if=\"$store.isUserShort()\"></unregistered-user>\r\n\t\t\t<!-- 已注册 -->\r\n\t\t\t<view v-else class=\"login content-with-nav page-content\">\r\n\t\t\t\t<view class=\"main-container\">\r\n\t\t\t\t\t<!-- 用户信息卡片 -->\r\n\t\t\t\t\t<view class=\"user-card\">\r\n\t\t\t\t\t\t<!-- 用户基本信息 -->\r\n\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-section\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"userDetail.avatar.smallUrl\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t<view v-show=\"userDetail.isIdentity\" class=\"identity-badge\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"14\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"info-section\">\r\n\t\t\t\t\t\t\t\t<view class=\"name-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"name\">{{ userDetail.nickName }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\r\n\t\t\t\t\t\t\t\t\t\t:class=\"userDetail.sex === '0' ? 'icon-gender-male' : 'icon-gender-female'\"\r\n\t\t\t\t\t\t\t\t\t\t:style=\"{ color: userDetail.sex === '0' ? '#4A90E2' : '#E91E63' }\"></text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"id-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"pid\">伴你ID:{{ userDetail.pid }}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"copy-btn\" @click=\"handleCopyId\">\r\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"wallet\" size=\"14\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<navigator url=\"/pagesubs/my/profile/profileEdit\" class=\"edit-btn-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class=\"edit-btn\">编辑资料</view>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 数据统计 -->\r\n\t\t\t\t\t\t<view class=\"stats-section\">\r\n\t\t\t\t\t\t\t<navigator url=\"/pagesubs/my/greeting/greeting\" class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.wantKowMe }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"label\">想认识</text>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t<navigator url=\"/pagesubs/my/follow/follow?type=1\" class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.myFollow }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"label\">关注</text>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t<navigator url=\"/pagesubs/my/follow/follow?type=2\" class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.followMy }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"label\">粉丝</text>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t<navigator url=\"/pagesubs/my/browse/browse?type=1\" class=\"stat-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.browseMy }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"label\">足迹</text>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 功能卡片区域 -->\r\n\t\t\t\t\t<view class=\"feature-cards\">\r\n\t\t\t\t\t\t<navigator url=\"/pagesubs/my/coin/coin\" class=\"feature-card\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon-column\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"feature-icon\" src=\"/static/image/icons/coin.png\" mode=\"aspectFit\">\r\n\t\t\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-column\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">我的花瓣</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.coin || 0 }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\r\n\t\t\t\t\t\t<navigator url=\"/pagesubs/my/gift/gift\" class=\"feature-card\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon-column\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"gift\" :size=\"32\" color=\"#FF6B35\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-column\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">礼物</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.gift || 0 }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t<navigator url=\"/pagesubs/my/activity/activity\" class=\"feature-card\">\r\n\t\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t\t<view class=\"icon-column\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"calendar\" :size=\"32\" color=\"#4ECDC4\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"text-column\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">活动</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"count\">{{ userDetail.count.activity || 0 }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 资料等级卡片 -->\r\n\t\t\t\t\t<view class=\"level-card\">\r\n\t\t\t\t\t\t<view class=\"level-content\">\r\n\t\t\t\t\t\t\t<view class=\"level-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"star-filled\" size=\"42\" :color=\"$primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"level-info\">\r\n\t\t\t\t\t\t\t\t<text class=\"level-title\">资料等级</text>\r\n\t\t\t\t\t\t\t\t<text class=\"level-desc\">升级至高级，可查看更多条件</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"upgrade-btn\" @click=\"navigateTo('/pagesubs/my/upgrade/upgrade')\">\r\n\t\t\t\t\t\t\t\t<text>去升级</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 功能菜单 -->\r\n\t\t\t\t\t<view class=\"menu-grid\">\r\n\t\t\t\t\t\t<!-- 第一行 -->\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/auth/auth')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"32\" color=\"#67C23A\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">我的认证</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/moment/moment')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"pyq\" size=\"32\" color=\"#F56C6C\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">我的动态</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/question/question')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"help-filled\" size=\"32\" color=\"#409EFF\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">我的问答</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/album/album')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"image-filled\" size=\"32\" color=\"#E6A23C\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">我的相册</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 第二行 -->\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/order/order-list')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"list\" size=\"32\" :color=\"globalConfig.theme.primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">我的订单</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"menu-item\" @click=\"navigateTo('/pagesubs/promotion-center/withdraw/withdraw')\">\r\n\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"wallet-filled\" size=\"32\" color=\"#FF6B6B\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">助力计划</text>\r\n\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t\t<!-- <view class=\"menu-item\" @click=\"handleShare\">\r\n\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"redo-filled\" size=\"32\" :color=\"globalConfig.theme.primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"menu-title\">邀请朋友</text>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 更多菜单区域 -->\r\n\t\t\t\t\t<view class=\"more-menu-section\">\r\n\t\t\t\t\t\t<view class=\"more-indicator\">\r\n\t\t\t\t\t\t\t<text class=\"more-text\">更多</text>\r\n\t\t\t\t\t\t\t<uni-icons type=\"down\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 底部菜单 -->\r\n\t\t\t\t\t<view class=\"bottom-menu\">\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo('/pagesubs/my/setting/setting')\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"gear-filled\" size=\"32\"\r\n\t\t\t\t\t\t\t\t\t:color=\"globalConfig.theme.primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">设置及隐私</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"navigateTo(globalConfig.help.antiFraudReminder)\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"info-filled\" size=\"32\"\r\n\t\t\t\t\t\t\t\t\t:color=\"globalConfig.theme.primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">防骗提醒</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"menu-item\" @click=\"clickDeveloping()\">\r\n\t\t\t\t\t\t\t<view class=\"menu-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"headphones\" size=\"32\"\r\n\t\t\t\t\t\t\t\t\t:color=\"globalConfig.theme.primaryColor\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<text class=\"menu-title\">联系客服</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 空占位 -->\r\n\t\t\t\t\t\t<view class=\"menu-placeholder\"></view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t\t<view class=\"companion-card\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"heart-filled\" size=\"22\" color=\"#FF69B4\"></uni-icons>\r\n\t\t\t\t\t\t\t<text class=\"days-text\">伴你有约与您相伴</text>\r\n\t\t\t\t\t\t\t<text class=\"days-count\">{{ userDetail.count.registerDays }}</text>\r\n\t\t\t\t\t\t\t<text class=\"days-unit\">天</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 关注订阅号组件 -->\r\n\t\t\t<mp-subscribe :navBarHeight=\"navBarHeight\" />\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n\r\n<script setup>\r\nimport {\r\n\tonLoad,\r\n\tonShow,\r\n\tonPageScroll\r\n} from \"@dcloudio/uni-app\"\r\nimport {\r\n\tref,\r\n\tcomputed\r\n} from 'vue'\r\nimport {\r\n\tclickCopy, clickDeveloping\r\n} from \"@/utils/common\"\r\nimport {\r\n\tgetMy\r\n} from \"@/api/my/my\"\r\nimport $store from '@/store'\r\nimport globalConfig from '@/config'\r\nimport UnregisteredUser from '@/components/unregistered-user/unregistered-user.vue'\r\nimport MpSubscribe from '@/components/mp-subscribe/mp-subscribe.vue'\r\n\r\n// 主题色\r\nconst $primaryColor = '#696CF3'\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n\r\n// 导航栏高度\r\nconst navBarHeight = ref(0)\r\n\r\n// 导航栏文字颜色（用于插槽中的图标）\r\nconst navTextColor = computed(() => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\r\n})\r\n\r\n// 用户详情\r\nconst userDetail = ref({\r\n\tavatar: '',\r\n\tnickName: '',\r\n\tsex: '',\r\n\tpid: '',\r\n\t// 统计信息\r\n\tcount: {\r\n\t\twantKowMe: 0,\r\n\t\tmyFollow: 0,\r\n\t\tfollowMy: 0,\r\n\t\tbrowseMy: 0,\r\n\t\tcoin: 0,\r\n\t\tmoment: 0,\r\n\t\tgift: 0,\r\n\t\tactivity: 0,\r\n\t\tregisterDays: 0\r\n\t}\r\n})\r\n\r\n\r\n\r\n// 处理页面滚动\r\nconst handlePageScroll = (e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n}\r\n\r\n// 处理导航栏高度变化\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n\r\n// 扫码功能\r\nconst handleScan = () => {\r\n\tuni.showToast({ title: '扫码功能', icon: 'none' })\r\n}\r\n\r\n// 设置功能\r\nconst handleSettings = () => {\r\n\tuni.navigateTo({ url: '/pages/my/setting/setting' })\r\n}\r\n\r\n// 处理分享功能\r\nconst handleShare = () => {\r\n\tuni.showActionSheet({\r\n\t\titemList: ['分享给微信好友', '分享到朋友圈', '复制链接'],\r\n\t\tsuccess: (res) => {\r\n\t\t\tswitch (res.tapIndex) {\r\n\t\t\t\tcase 0:\r\n\t\t\t\t\tshareToWechat()\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tshareToMoments()\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tcopyShareLink()\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 分享到微信好友\r\nconst shareToWechat = () => {\r\n\tuni.share({\r\n\t\tprovider: 'weixin',\r\n\t\tscene: 'WXSceneSession',\r\n\t\ttype: 0,\r\n\t\thref: 'https://www.banyouyue.com',\r\n\t\ttitle: '伴你有约 - 真实的社交平台',\r\n\t\tsummary: '发现身边有趣的人，开启美好的社交体验',\r\n\t\timageUrl: '/static/logo.png',\r\n\t\tsuccess: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('分享失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 分享到朋友圈\r\nconst shareToMoments = () => {\r\n\tuni.share({\r\n\t\tprovider: 'weixin',\r\n\t\tscene: 'WXSceneTimeline',\r\n\t\ttype: 0,\r\n\t\thref: 'https://www.banyouyue.com',\r\n\t\ttitle: '伴你有约 - 真实的社交平台',\r\n\t\tsummary: '发现身边有趣的人，开启美好的社交体验',\r\n\t\timageUrl: '/static/logo.png',\r\n\t\tsuccess: () => {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\t\t},\r\n\t\tfail: (err) => {\r\n\t\t\tconsole.error('分享失败:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n// 复制用户ID\r\nconst handleCopyId = () => {\r\n\tconst pid = userDetail.value.pid\r\n\tclickCopy(pid)\r\n}\r\n\r\n// 复制分享链接\r\nconst copyShareLink = () => {\r\n\tconst shareLink = 'https://www.banyouyue.com'\r\n\tclickCopy(shareLink)\r\n}\r\n\r\n// 通用导航方法\r\nconst navigateTo = (url) => {\r\n\tuni.navigateTo({\r\n\t\turl: url\r\n\t})\r\n}\r\n\r\n// 处理关注订阅号组件关闭事件\r\nconst handleMpSubscribeClose = () => {\r\n\tconsole.log('关注订阅号弹框已关闭')\r\n}\r\n\r\n\r\n\r\n// 获取用户数据的方法\r\nconst loadUserData = () => {\r\n\tif ($store.isUserShort()) {\r\n\t\treturn;\r\n\t}\r\n\t// get\r\n\tgetMy().then(res => {\r\n\t\tuserDetail.value = res.data;\r\n\t})\r\n}\r\n\r\n// init\r\nonLoad(() => {\r\n\t// loadUserData()\r\n})\r\n\r\n// 页面显示时重新加载数据（tabBar重复点击时会触发）\r\nonShow(() => {\r\n\tconsole.log('个人中心页面显示，重新加载用户数据')\r\n\tloadUserData()\r\n})\r\n\r\n// 页面滚动监听\r\nonPageScroll(handlePageScroll)\r\n\r\nfunction onShareAppMessage(res) {\r\n\tif (res.from === 'button') { // 来自页面内分享按钮\r\n\t\tconsole.log(res.target)\r\n\t}\r\n\treturn {\r\n\t\ttitle: '伴你有约',\r\n\t\tpath: '/pages/index/index',\r\n\t\timageUrl: '/static/logo.png',\r\n\t\tsuccess: function (e) {\r\n\t\t\tconsole.log('分享成功')\r\n\t\t},\r\n\t\tfail: function (e) {\r\n\t\t\tconsole.log('分享失败')\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/uni.scss';\r\n@import '@/static/fonts/iconfont.css';\r\n\r\n\r\n\r\n.nav-right-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 24rpx;\r\n}\r\n\r\n.card {\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(105, 108, 243, 0.1);\r\n}\r\n\r\n.login {\r\n\tflex: 1;\r\n\tflex-direction: column;\r\n\r\n\t.main-container {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t// 用户信息卡片\r\n\t.user-card {\r\n\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 24rpx 20rpx;\r\n\t\tbox-shadow: 0 8rpx 32rpx rgba(105, 108, 243, 0.08);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\r\n\t\t.user-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t.avatar-section {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\r\n\t\t\t\t.avatar {\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tborder: 4rpx solid rgba(105, 108, 243, 0.1);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.identity-badge {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tborder: 3rpx solid #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.info-section {\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.name-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tgap: 8rpx;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.gender-icon {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.id-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.pid {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.copy-btn {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tbackground: rgba(105, 108, 243, 0.1);\r\n\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\t\t\t\tbackground: rgba(105, 108, 243, 0.2);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.edit-btn-wrapper {\r\n\t\t\t\t.edit-btn {\r\n\t\t\t\t\tbackground: linear-gradient(135deg, $primary-color, #9B9DF5);\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.stats-section {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tpadding-top: 24rpx;\r\n\t\t\tborder-top: 1rpx solid rgba(105, 108, 243, 0.1);\r\n\r\n\t\t\t.stat-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t\t.count {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.label {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 功能卡片区域\r\n\t.feature-cards {\r\n\t\tdisplay: flex;\r\n\t\tgap: 12rpx;\r\n\r\n\t\t.feature-card {\r\n\t\t\tbackground: rgba(255, 255, 255, 0.95);\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 16rpx 12rpx;\r\n\t\t\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\r\n\t\t\tbackdrop-filter: blur(10rpx);\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmin-height: 120rpx;\r\n\r\n\t\t\t// 我的花瓣占4/8\r\n\t\t\t&:nth-child(1) {\r\n\t\t\t\tflex: 4;\r\n\t\t\t}\r\n\r\n\t\t\t// 礼物和活动各占2/8\r\n\t\t\t&:nth-child(2),\r\n\t\t\t&:nth-child(3) {\r\n\t\t\t\tflex: 2;\r\n\t\t\t}\r\n\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: translateY(-4rpx);\r\n\t\t\t\tbox-shadow: 0 12rpx 32rpx rgba(105, 108, 243, 0.15);\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t.card-content {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center; // 保持整体内容居中\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tgap: 16rpx; // 设置图标和文字之间的间距，约2px\r\n\r\n\t\t\t\t.icon-column {\r\n\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\r\n\t\t\t\t\t.feature-icon {\r\n\t\t\t\t\t\twidth: 32px;\r\n\t\t\t\t\t\theight: 32px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text-column {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start; // 文字左对齐，与图标紧挨着\r\n\t\t\t\t\tflex-shrink: 0; // 防止文字区域被压缩\r\n\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t\t\tline-height: 1.1;\r\n\t\t\t\t\t\twhite-space: nowrap; // 防止文字换行\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.count {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\twhite-space: nowrap; // 防止文字换行\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 礼物和活动卡片采用两列布局，图标和文字紧挨着\r\n\t\t\t&:nth-child(2),\r\n\t\t\t&:nth-child(3) {\r\n\t\t\t\t.card-content {\r\n\t\t\t\t\tflex-direction: row;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center; // 保持整体内容居中\r\n\t\t\t\t\tgap: 16rpx; // 设置图标和文字之间的间距，约2px\r\n\r\n\t\t\t\t\t.icon-column {\r\n\t\t\t\t\t\twidth: 36rpx;\r\n\t\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.text-column {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\talign-items: flex-start; // 文字左对齐，与图标紧挨着\r\n\t\t\t\t\t\tflex-shrink: 0; // 防止文字区域被压缩\r\n\r\n\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tmargin-bottom: 2rpx;\r\n\t\t\t\t\t\t\tline-height: 1.1;\r\n\t\t\t\t\t\t\twhite-space: nowrap; // 防止文字换行\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.count {\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\t\twhite-space: nowrap; // 防止文字换行\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 资料等级卡片\r\n.level-card {\r\n\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(155, 157, 245, 0.15));\r\n\tborder: 2rpx solid rgba(105, 108, 243, 0.2);\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.15);\r\n\tbackdrop-filter: blur(10rpx);\r\n\r\n\t.level-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.level-icon {\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.2);\r\n\t\t}\r\n\r\n\t\t.level-info {\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.level-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\r\n\t\t\t.level-desc {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.upgrade-btn {\r\n\t\t\tbackground: linear-gradient(135deg, $primary-color, #9B9DF5);\r\n\t\t\tcolor: white;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: translateY(2rpx);\r\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.3);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 菜单网格\r\n.menu-grid {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx 20rpx;\r\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(4, 1fr);\r\n\tgap: 30rpx 16rpx;\r\n\r\n\t.menu-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t}\r\n\r\n\t\t&.more-item {\r\n\t\t\tgrid-column: span 2;\r\n\t\t}\r\n\r\n\t\t.menu-icon {\r\n\t\t\twidth: 64rpx;\r\n\t\t\theight: 64rpx;\r\n\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t}\r\n\r\n\t\t.menu-title {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 底部菜单\r\n.bottom-menu {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 16rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(4, 1fr);\r\n\tgap: 20rpx;\r\n\r\n\t.menu-placeholder {\r\n\t\t// 空占位元素，不显示任何内容\r\n\t}\r\n\r\n\t.menu-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t}\r\n\r\n\t\t.menu-icon {\r\n\t\t\twidth: 52rpx;\r\n\t\t\theight: 52rpx;\r\n\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-bottom: 12rpx;\r\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t\t}\r\n\r\n\t\t.menu-title {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.bottom {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 24rpx 0 20rpx;\r\n\r\n\t.companion-card {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(105, 108, 243, 0.1));\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 16rpx 24rpx;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tborder: 1rpx solid rgba(255, 105, 180, 0.2);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.15);\r\n\r\n\t\t.days-text {\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tmargin-left: 8rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t}\r\n\r\n\t\t.days-count {\r\n\t\t\tcolor: #FF69B4;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmargin-left: 6rpx;\r\n\t\t}\r\n\r\n\t\t.days-unit {\r\n\t\t\tcolor: #666;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tmargin-left: 2rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t}\r\n\t}\r\n\r\n\t// 更多菜单区域\r\n\t.more-menu-section {\r\n\t\tmargin: 20rpx 0;\r\n\t\tpadding: 0 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.more-indicator {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tbackground: rgba(255, 255, 255, 0.6);\r\n\t\t\tborder-radius: 24rpx;\r\n\t\t\tbackdrop-filter: blur(8rpx);\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:active {\r\n\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\t\t\ttransform: scale(0.98);\r\n\t\t\t}\r\n\r\n\t\t\t.more-text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pages/my/my.vue'\nwx.createPage(MiniProgramPage)"], "names": ["UnregisteredUser", "MpSubscribe", "$primaryColor", "pageScrollTop", "ref", "navBarHeight", "computed", "userDetail", "handlePageScroll", "e", "handleNavHeightChange", "height", "handleCopyId", "pid", "clickCopy", "navigateTo", "url", "uni", "loadUserData", "$store", "getMy", "res", "onLoad", "onShow", "onPageScroll", "MiniProgramPage"], "mappings": "0dA6OA,MAAAA,EAAA,IAAA,0DACAC,EAAA,IAAA,gDAGAC,EAAA,kCAGA,MAAAC,EAAAC,EAAA,IAAA,CAAA,EAGAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAA,SAAA,IACA,KAAA,IAAAH,EAAA,MAAA,IAAA,CAAA,EACA,GAAA,UAAA,SACA,EAGA,MAAAI,EAAAH,EAAAA,IAAA,CACA,OAAA,GACA,SAAA,GACA,IAAA,GACA,IAAA,GAEA,MAAA,CACA,UAAA,EACA,SAAA,EACA,SAAA,EACA,SAAA,EACA,KAAA,EACA,OAAA,EACA,KAAA,EACA,SAAA,EACA,aAAA,CACA,CACA,CAAA,EAKAI,EAAAC,GAAA,CACAN,EAAA,MAAAM,EAAA,SACA,EAGAC,EAAAC,GAAA,CACAN,EAAA,MAAAM,CACA,EAsFAC,EAAA,IAAA,CACA,MAAAC,EAAAN,EAAA,MAAA,IACAO,EAAAA,UAAAD,CAAA,CACA,EASAE,EAAAC,GAAA,CACAC,EAAAA,MAAA,WAAA,CACA,IAAAD,CACA,CAAA,CACA,EAUAE,EAAA,IAAA,CACAC,EAAAA,OAAA,eAIAC,QAAA,EAAA,KAAAC,GAAA,CACAd,EAAA,MAAAc,EAAA,IACA,CAAA,CACA,EAGAC,OAAAA,EAAAA,OAAA,IAAA,CAEA,CAAA,EAGAC,EAAAA,OAAA,IAAA,CACAN,EAAAA,MAAA,MAAA,MAAA,yBAAA,mBAAA,EACAC,EAAA,CACA,CAAA,EAGAM,EAAA,aAAAhB,CAAA,+7DClaA,GAAG,WAAWiB,CAAe"}