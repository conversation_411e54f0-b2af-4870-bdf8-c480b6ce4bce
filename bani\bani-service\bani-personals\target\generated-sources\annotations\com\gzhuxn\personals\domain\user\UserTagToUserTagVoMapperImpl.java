package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserTagToUserTagVoMapperImpl implements UserTagToUserTagVoMapper {

    @Override
    public UserTagVo convert(UserTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserTagVo userTagVo = new UserTagVo();

        userTagVo.setId( arg0.getId() );
        userTagVo.setUserId( arg0.getUserId() );
        userTagVo.setNamespace( arg0.getNamespace() );
        userTagVo.setTagKey( arg0.getTagKey() );
        userTagVo.setTagVal( arg0.getTagVal() );
        userTagVo.setTagValName( arg0.getTagValName() );

        return userTagVo;
    }

    @Override
    public UserTagVo convert(UserTag arg0, UserTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setNamespace( arg0.getNamespace() );
        arg1.setTagKey( arg0.getTagKey() );
        arg1.setTagVal( arg0.getTagVal() );
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
