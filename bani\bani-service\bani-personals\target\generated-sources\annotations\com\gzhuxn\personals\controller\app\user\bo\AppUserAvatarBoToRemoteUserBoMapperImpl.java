package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserAvatarBoToRemoteUserBoMapperImpl implements AppUserAvatarBoToRemoteUserBoMapper {

    @Override
    public RemoteUserBo convert(AppUserAvatarBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteUserBo remoteUserBo = new RemoteUserBo();

        remoteUserBo.setUserId( arg0.getUserId() );
        remoteUserBo.setAvatar( arg0.getAvatar() );

        return remoteUserBo;
    }

    @Override
    public RemoteUserBo convert(AppUserAvatarBo arg0, RemoteUserBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setAvatar( arg0.getAvatar() );

        return arg1;
    }
}
