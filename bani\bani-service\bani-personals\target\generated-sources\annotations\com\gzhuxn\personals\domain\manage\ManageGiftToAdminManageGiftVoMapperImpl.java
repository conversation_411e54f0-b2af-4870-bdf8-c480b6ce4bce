package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.admin.manage.vo.AdminManageGiftVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageGiftToAdminManageGiftVoMapperImpl implements ManageGiftToAdminManageGiftVoMapper {

    @Override
    public AdminManageGiftVo convert(ManageGift arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminManageGiftVo adminManageGiftVo = new AdminManageGiftVo();

        adminManageGiftVo.setId( arg0.getId() );
        adminManageGiftVo.setName( arg0.getName() );
        adminManageGiftVo.setIcon( arg0.getIcon() );
        adminManageGiftVo.setPrice( arg0.getPrice() );
        adminManageGiftVo.setFreeFlag( arg0.getFreeFlag() );
        adminManageGiftVo.setSort( arg0.getSort() );
        adminManageGiftVo.setStatus( arg0.getStatus() );
        adminManageGiftVo.setVersion( arg0.getVersion() );

        return adminManageGiftVo;
    }

    @Override
    public AdminManageGiftVo convert(ManageGift arg0, AdminManageGiftVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setFreeFlag( arg0.getFreeFlag() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVersion( arg0.getVersion() );

        return arg1;
    }
}
