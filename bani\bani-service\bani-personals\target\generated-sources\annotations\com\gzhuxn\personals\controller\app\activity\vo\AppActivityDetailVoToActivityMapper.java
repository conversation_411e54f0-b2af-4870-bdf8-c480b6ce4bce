package com.gzhuxn.personals.controller.app.activity.vo;

import com.gzhuxn.personals.domain.activity.ActSafeguardToActSafeguardStorageVoMapper;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityFeeToAppActivityDetailFeeVoMapper;
import com.gzhuxn.personals.domain.activity.ActivityToAppActivityDetailVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardToActSafeguardStorageVoMapper.class,AppActivityDetailFeeVoToActivityFeeMapper.class,ActivityFeeToAppActivityDetailFeeVoMapper.class,ActivityToAppActivityDetailVoMapper.class},
    imports = {}
)
public interface AppActivityDetailVoToActivityMapper extends BaseMapper<AppActivityDetailVo, Activity> {
}
