package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserRequireTagBoToUserRequireTagMapper;
import com.gzhuxn.personals.domain.user.vo.UserRequireTagVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserRequireTagBoToUserRequireTagMapper.class},
    imports = {}
)
public interface UserRequireTagToUserRequireTagVoMapper extends BaseMapper<UserRequireTag, UserRequireTagVo> {
}
