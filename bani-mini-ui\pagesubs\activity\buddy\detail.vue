<template>
	<scroll-nav-page title="搭子详情" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 活动封面图 -->
				<image class="cover-image" :src="activityDetail.backgroundImage" mode="aspectFill"></image>
				<!-- 活动内容 -->
				<view class="content">
					<!-- 用户信息 -->
					<view class="user-info">
						<view class="user-left">
							<image class="avatar" :src="activityDetail.oppAvatar" mode="aspectFill"></image>
							<view class="user-detail">
								<text class="name">{{ activityDetail.oppNickName }}</text>
								<view class="tags">
									<uni-tag :text="activityTypes.find(t => t.value === activityDetail.classify)?.label"
										type="primary" :inverted="false" size="small" :color="'#696CF3'" />
									<uni-tag v-if="activityDetail.oppIsIdentity" text="已实名" type="success"
										:inverted="false" size="small" :color="'#52c41a'" />
								</view>
							</view>
						</view>
						<button class="follow-btn" :class="{
							followed: activityDetail.oppIsFollowed,
							loading: isFollowLoading
						}" :disabled="isFollowLoading" @click="handleFollow">
							<view v-if="isFollowLoading" class="loading-icon">
								<uni-icons type="spinner-cycle" size="16" color="#999" />
							</view>
							<text v-else>{{ activityDetail.oppIsFollowed ? '已关注' : '关注' }}</text>
						</button>
					</view>

					<!-- 活动标题 -->
					<view class="title-section">
						<view class="title-left">
							<text class="title">{{ activityDetail.name }}</text>
							<view class="status-tag" :class="getStatusClass(activityDetail.status)">
								{{ getStatusText(activityDetail.status) }}
							</view>
						</view>
						<view class="title-right">
							<text class="publish-time">{{ activityDetail.createTime }}</text>
						</view>
					</view>

					<!-- 活动信息 -->
					<view class="info-section">
						<view class="info-item">
							<text class="iconfont bani-position info-icon"></text>
							<text class="info-text">地址：{{ activityDetail.address }}</text>
						</view>
						<view class="info-item">
							<text class="iconfont bani-rili info-icon"></text>
							<text class="info-text">活动开始时间：{{ activityDetail.startTime }}</text>
						</view>
						<view class="info-item" v-if="activityDetail.timeLength">
							<text class="iconfont bani-time info-icon"></text>
							<text class="info-text">活动时长：{{ activityDetail.timeLength }}</text>
						</view>
						<view class="info-item">
							<text class="iconfont bani-jine info-icon"></text>
							<text class="info-text">费用：{{ activityDetail.amount > 0 ? `¥${activityDetail.amount}` : '免费'
								}}</text>
						</view>
						<view class="info-item">
							<text class="iconfont bani-yonghu info-icon"></text>
							<text class="info-text">已报名：{{ activityDetail.enrollNum }}{{
								activityDetail.limitNum ? '/' + activityDetail.limitNum : '' }}</text>
						</view>
					</view>

					<!-- 活动详情 -->
					<view class="detail-section">
						<text class="section-title">活动详情</text>
						<text class="detail-text">{{ activityDetail.introduce }}</text>
						<!-- 活动图片 -->
						<view v-if="activityDetail.introduceImages" class="detail-images">
							<image v-for="(image, index) in getIntroduceImages()" :key="index" :src="getImageUrl(image)"
								mode="widthFix" class="detail-image" @error="onImageError" @load="onImageLoad"></image>
						</view>
					</view>

					<!-- 报名列表 -->
					<view class="enroll-section">
						<view class="section-header">
							<text class="section-title">报名列表</text>
							<text class="enroll-count">({{ enrollUserList.length }}人)</text>
							<view class="refresh-btn" @click="loadEnrollUsers" :class="{ loading: enrollLoading }">
								<uni-icons type="refreshempty" size="16"
									:color="enrollLoading ? '$text-tertiary' : '$primary-color'" />
							</view>
						</view>

						<!-- 加载状态 -->
						<view v-if="enrollLoading" class="loading-container">
							<uni-icons type="spinner-cycle" size="20" color="$primary-color" />
							<text class="loading-text">加载中...</text>
						</view>

						<!-- 报名用户列表 -->
						<view v-else-if="enrollUserList.length > 0" class="enroll-list">
							<view class="enroll-item" v-for="(user, index) in enrollUserList" :key="user.uid || index">
								<image class="enroll-avatar" :src="user.oppAvatar" mode="aspectFill"></image>
								<view class="enroll-info">
									<text class="enroll-name">{{ user.oppNickName }}</text>
									<text class="enroll-time">{{ user.enrollTime }}</text>
								</view>
								<!-- 可以根据需要添加实名认证标识 -->
								<!-- <uni-tag v-if="user.isVerified" text="已实名" type="success" :inverted="false" size="small"
							:color="'#52c41a'" /> -->
							</view>
						</view>

						<!-- 暂无报名用户 -->
						<view v-else class="empty-state">
							<text class="empty-text">暂无报名用户</text>
						</view>
					</view>
				</view>

				<!-- 底部操作栏 -->
				<view class="bottom-bar">
					<view class="action-btns">
						<button class="share-btn" @click="shareActivity">
							<text class="iconfont bani-fenxiang action-icon"></text>
							<text>分享</text>
						</button>
						<button class="more-btn" @click="showMoreActions">
							<text class="iconfont bani-gengduo action-icon"></text>
							<text>更多</text>
						</button>
					</view>
					<button class="enroll-btn" :class="{
						disabled: activityDetail.status !== 3 || activityDetail.hasEnrolled,
						enrolled: activityDetail.hasEnrolled
					}" @click="enrollActivity">
						{{ getEnrollBtnText() }}
					</button>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { onPageScroll, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { getBuddyActivityDetail, getActivityTypeName, getActivityStatusName, BUDDY_ACTIVITY_TYPES } from '@/api/activity/buddy'
import {
	enrollFreeActivity,
	enrollPaidActivity,
	getActivityEnrollUsers
} from '@/api/activity/activityRecord'
import { toggleUserFollow } from '@/api/my/follow'
import { getCurrentPagePath } from '@/utils/common.js'

// 页面参数
const activityId = ref(null)

// 页面滚动距离
const pageScrollTop = ref(0)

// 加载状态
const loading = ref(false)
const isFollowLoading = ref(false)

// 报名用户列表
const enrollUserList = ref([])
const enrollLoading = ref(false)

// 防抖定时器
let followDebounceTimer = null

// 活动类型列表
const activityTypes = [
	{ value: 0, label: '全部' },
	{ value: BUDDY_ACTIVITY_TYPES.DATING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DATING) },
	{ value: BUDDY_ACTIVITY_TYPES.CHAT, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.CHAT) },
	{ value: BUDDY_ACTIVITY_TYPES.DINING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DINING) },
	{ value: BUDDY_ACTIVITY_TYPES.OUTDOOR, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.OUTDOOR) },
	{ value: BUDDY_ACTIVITY_TYPES.EXHIBITION, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.EXHIBITION) },
	{ value: BUDDY_ACTIVITY_TYPES.SPORTS, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.SPORTS) },
	{ value: BUDDY_ACTIVITY_TYPES.STUDY, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.STUDY) },
	{ value: BUDDY_ACTIVITY_TYPES.DRINKING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.DRINKING) },
	{ value: BUDDY_ACTIVITY_TYPES.GAMING, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.GAMING) },
	{ value: BUDDY_ACTIVITY_TYPES.OTHER, label: getActivityTypeName(BUDDY_ACTIVITY_TYPES.OTHER) }
]

// 活动详情数据
const activityDetail = ref({})

// 加载活动详情
const loadActivityDetail = async () => {
	if (!activityId.value) {
		uni.showToast({
			title: '活动ID不能为空',
			icon: 'none'
		})
		return
	}

	loading.value = true

	try {
		console.log('加载活动详情，ID:', activityId.value)
		const response = await getBuddyActivityDetail(activityId.value)

		console.log('API响应:', response)

		if (response.code === 200 && response.data) {
			// 直接使用API返回的数据更新活动详情
			activityDetail.value = response.data
			console.log('活动详情加载成功:', activityDetail.value)
			console.log('介绍图片字段:', activityDetail.value.introduceImages)
		} else {
			console.error('加载活动详情失败:', response.msg)
			uni.showToast({
				title: response.msg || '加载失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('加载活动详情异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 获取报名用户列表
const loadEnrollUsers = async () => {
	enrollLoading.value = true
	getActivityEnrollUsers({
		activityId: activityId.value,
		pageNum: 1,
		pageSize: 20
	}).then(response => {
		enrollUserList.value = response.rows || []
	}).finally(() => {
		enrollLoading.value = false
	})
}
// 获取状态文本
const getStatusText = (status) => {
	// 使用API中的状态枚举
	if (typeof status === 'number') {
		return getActivityStatusName(status)
	}

	// 兼容旧版本的字符串状态
	const statusMap = {
		notStarted: '未开始',
		enrolling: '报名中',
		enrollEnd: '报名已结束',
		inProgress: '活动进行中',
		ended: '活动已结束'
	}
	return statusMap[status] || ''
}

// 获取状态样式类名
const getStatusClass = (status) => {
	if (typeof status === 'number') {
		// 根据数字状态返回对应的类名
		const statusClassMap = {
			1: 'notStarted',    // 草稿
			2: 'notStarted',    // 已发布/未开始
			3: 'enrolling',     // 报名中
			4: 'enrollEnd',     // 报名已结束
			10: 'inProgress',   // 活动进行中
			11: 'ended'         // 活动已结束
		}
		return statusClassMap[status] || 'notStarted'
	}

	// 兼容旧版本的字符串状态
	return status || 'notStarted'
}

// 解析活动介绍图片
const getIntroduceImages = () => {
	console.log('getIntroduceImages 被调用')
	console.log('activityDetail.value:', activityDetail.value)
	console.log('introduceImages 字段值:', activityDetail.value.introduceImages)

	if (!activityDetail.value.introduceImages) {
		console.log('introduceImages 为空，返回空数组')
		return []
	}

	// 图片用逗号分隔
	const images = activityDetail.value.introduceImages.split(',').filter(img => img.trim())
	console.log('解析后的图片数组:', images)
	return images
}

// 获取完整的图片URL
const getImageUrl = (imageUrl) => {
	if (!imageUrl) return ''

	// 如果已经是完整URL，直接返回
	if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
		console.log('完整URL:', imageUrl)
		return imageUrl
	}

	// 如果是相对路径或OSS ID，需要拼接完整URL
	// 这里需要根据您的实际情况调整URL拼接逻辑
	const baseUrl = 'https://your-oss-domain.com/' // 请替换为实际的OSS域名
	const fullUrl = baseUrl + imageUrl
	console.log('拼接后的URL:', fullUrl)
	return fullUrl
}

// 图片加载成功
const onImageLoad = (e) => {
	console.log('图片加载成功:', e)
}

// 图片加载失败
const onImageError = (e) => {
	console.error('图片加载失败:', e)
	console.error('失败的图片src:', e.target.src)
}



// 获取报名按钮文本
const getEnrollBtnText = () => {
	// 如果已报名，显示已报名状态
	if (activityDetail.value.hasEnrolled) {
		return '已报名'
	}

	const status = activityDetail.value.status
	const statusMap = {
		0: '已下架',
		1: '草稿',
		2: '已发布',
		3: '立即报名',
		4: '报名已结束',
		10: '活动进行中',
		11: '活动已结束'
	}
	return statusMap[status] || '未知状态'
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 关注用户
const handleFollow = async () => {
	// 检查用户信息是否完整
	if (!activityDetail.value.oppUserId) {
		uni.showToast({
			title: '用户信息不完整',
			icon: 'none'
		})
		return
	}

	// 防止重复点击
	if (isFollowLoading.value) {
		return
	}

	// 防抖处理
	if (followDebounceTimer) {
		clearTimeout(followDebounceTimer)
	}

	followDebounceTimer = setTimeout(async () => {
		await performFollowAction()
	}, 300)
}

// 执行关注操作
const performFollowAction = async () => {

	try {
		isFollowLoading.value = true
		console.log('切换关注状态，用户ID:', activityDetail.value.oppUserId)

		const response = await toggleUserFollow(activityDetail.value.oppUserId, activityDetail.value.oppIsFollowed)

		if (response.code === 200) {
			// 切换关注状态
			const wasFollowed = activityDetail.value.oppIsFollowed
			activityDetail.value.oppIsFollowed = !activityDetail.value.oppIsFollowed

			// 显示成功提示
			uni.showToast({
				title: activityDetail.value.oppIsFollowed ? '关注成功' : '已取消关注',
				icon: 'success',
				duration: 1500
			})

			// 如果是关注操作，可以添加一些额外的反馈
			if (!wasFollowed && activityDetail.value.oppIsFollowed) {
				console.log('用户关注了:', activityDetail.value.oppNickName)
			}
		} else {
			console.error('关注操作失败:', response.msg)
			uni.showToast({
				title: response.msg || '操作失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('关注操作异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	} finally {
		isFollowLoading.value = false
	}
}

// 分享活动
const shareActivity = () => {
	// 检查是否在微信小程序环境
	// #ifdef MP-WEIXIN
	uni.showActionSheet({
		itemList: ['分享给好友', '分享到朋友圈', '生成分享海报'],
		success: (res) => {
			if (res.tapIndex === 0) {
				// 分享给好友
				shareToFriend()
			} else if (res.tapIndex === 1) {
				// 分享到朋友圈
				shareToTimeline()
			} else if (res.tapIndex === 2) {
				// 生成分享海报
				generateSharePoster()
			}
		},
		fail: () => {
			console.log('取消分享')
		}
	})
	// #endif

	// #ifndef MP-WEIXIN
	uni.showToast({
		title: '当前环境不支持分享功能',
		icon: 'none'
	})
	// #endif
}

// 分享给好友
const shareToFriend = () => {
	// #ifdef MP-WEIXIN
	const shareData = {
		title: activityDetail.value.name || '精彩活动邀请',
		path: `/pagesubs/activity/buddy/detail?id=${activityId.value}`,
		imageUrl: activityDetail.value.backgroundImage || '',
		success: (res) => {
			console.log('分享成功', res)
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		},
		fail: (err) => {
			console.error('分享失败', err)
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	}

	// 调用微信分享API
	uni.shareAppMessage(shareData)
	// #endif
}

// 分享到朋友圈
const shareToTimeline = () => {
	// #ifdef MP-WEIXIN
	// 微信小程序分享到朋友圈需要通过右上角菜单触发
	// 这里提示用户使用右上角分享功能
	uni.showModal({
		title: '分享到朋友圈',
		content: '请点击右上角的"..."按钮，选择"分享到朋友圈"',
		showCancel: false,
		confirmText: '知道了'
	})
	// #endif
}

// 生成分享海报
const generateSharePoster = () => {
	uni.showLoading({
		title: '生成中...'
	})

	// 这里可以调用海报生成API或使用canvas绘制
	// 暂时模拟生成过程
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: '海报生成功能开发中',
			icon: 'none'
		})
	}, 1500)
}

// 显示更多操作
const showMoreActions = () => {
	uni.showActionSheet({
		itemList: ['举报'],
		success: (res) => {
			console.log('选择了第' + (res.tapIndex + 1) + '个按钮')
			if (res.tapIndex === 0) {
				// 举报
				handleReport()
			}
		},
		fail: () => {
			console.log('取消选择')
		}
	})
}

// 处理举报
const handleReport = () => {
	// 跳转到举报页面，传递举报类型为活动(2)和活动ID
	uni.navigateTo({
		url: `/pagesubs/my/report/report?type=2&targetId=${activityId.value}&targetName=${encodeURIComponent(activityDetail.value.name || '')}`
	})
}

// 报名活动
const enrollActivity = async () => {
	// 如果已报名，显示提示
	if (activityDetail.value.hasEnrolled) {
		uni.showToast({
			title: '您已报名此活动',
			icon: 'none'
		})
		return
	}

	// 只有状态为3（报名中）时才能报名
	if (activityDetail.value.status !== 3) {
		const statusText = getEnrollBtnText()
		uni.showToast({
			title: `当前状态：${statusText}，无法报名`,
			icon: 'none'
		})
		return
	}

	if (!activityId.value) {
		uni.showToast({
			title: '活动ID不能为空',
			icon: 'none'
		})
		return
	}

	try {
		// 判断是否为付费活动
		const amount = activityDetail.value.amount || 0

		if (amount > 0) {
			// 付费活动，需要支付
			await handlePaidEnroll(amount)
		} else {
			// 免费活动，直接报名
			await handleFreeEnroll()
		}
	} catch (error) {
		console.error('报名活动异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	}
}

// 处理免费活动报名
const handleFreeEnroll = async () => {
	try {
		console.log('免费活动报名，活动ID:', activityId.value)
		const response = await enrollFreeActivity(activityId.value)

		if (response.code === 200) {
			uni.showToast({
				title: '报名成功',
				icon: 'success'
			})

			// 重新加载活动详情，更新报名人数等信息
			await loadActivityDetail(activityId.value)
			// 重新加载报名用户列表
			await loadEnrollUsers()
		} else {
			console.error('免费活动报名失败:', response.msg)
			uni.showToast({
				title: response.msg || '报名失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('免费活动报名异常:', error)
		throw error
	}
}

// 处理付费活动报名
const handlePaidEnroll = async (amount) => {
	try {
		// 显示支付确认对话框
		const result = await uni.showModal({
			title: '付费活动',
			content: `此活动需要支付 ¥${amount}，是否继续？`,
			confirmText: '支付',
			cancelText: '取消'
		})

		if (!result.confirm) {
			return
		}

		console.log('付费活动报名，活动ID:', activityId.value, '金额:', amount)

		// 计算花瓣数量（假设1元=10花瓣）
		const coin = amount * 10

		const response = await enrollPaidActivity(activityId.value, amount, coin)

		if (response.code === 200) {
			// 如果返回支付数据，需要调起支付
			if (response.data && response.data.payData) {
				// TODO: 调起支付流程
				console.log('支付数据:', response.data.payData)
				uni.showToast({
					title: '支付功能开发中',
					icon: 'none'
				})
			} else {
				uni.showToast({
					title: '报名成功',
					icon: 'success'
				})

				// 重新加载活动详情
				await loadActivityDetail(activityId.value)
				// 重新加载报名用户列表
				await loadEnrollUsers()
			}
		} else {
			console.error('付费活动报名失败:', response.msg)
			uni.showToast({
				title: response.msg || '报名失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('付费活动报名异常:', error)
		throw error
	}
}

// 处理页面滚动
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
}

// 页面滚动监听
onPageScroll(handlePageScroll)

// 页面加载时接收参数
onLoad((options) => {
	activityId.value = options.id
})

// 页面加载时获取活动详情
onMounted(() => {
	// 获取当前页面完整路径（包含参数）
	const currentPagePath = getCurrentPagePath()
	console.log('当前页面完整路径:', currentPagePath)

	if (activityId.value) {
		loadActivityDetail()
		loadEnrollUsers()
	}
})

// 微信小程序分享给好友配置
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
	return {
		title: activityDetail.value.name || '精彩活动邀请',
		path: `/pagesubs/activity/buddy/detail?id=${activityId.value}`,
		imageUrl: activityDetail.value.backgroundImage || '',
		success: (res) => {
			console.log('分享成功', res)
		},
		fail: (err) => {
			console.error('分享失败', err)
		}
	}
})

// 微信小程序分享到朋友圈配置
onShareTimeline(() => {
	return {
		title: `${activityDetail.value.name || '精彩活动'} - 快来一起参加吧！`,
		query: `id=${activityId.value}`,
		imageUrl: activityDetail.value.backgroundImage || '',
		success: (res) => {
			console.log('分享到朋友圈成功', res)
		},
		fail: (err) => {
			console.error('分享到朋友圈失败', err)
		}
	}
})
// #endif
</script>

<style lang="scss" scoped>
.nav-left {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(0, 0, 0, 0.2);
	border-radius: 50%;
}

.nav-title {
	font-size: $font-size-lg;
	font-weight: $font-weight-bold;
	text-align: center;
}

.cover-image {
	width: 100%;
	height: 400rpx;
}

.content {
	padding: $spacing-lg;
	background: $bg-primary;
	border-radius: $radius-lg $radius-lg 0 0;
	margin-top: -30rpx;
	position: relative;
}

.user-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: $spacing-lg;

	.user-left {
		display: flex;
		align-items: center;
		flex: 1;
		margin-right: $spacing-md;

		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: $spacing-md;
			border: 2rpx solid $border-color;
		}

		.user-detail {
			flex: 1;

			.name {
				font-size: $font-size-lg;
				color: $text-primary;
				font-weight: $font-weight-bold;
				margin-bottom: $spacing-xs;
				display: block;
			}

			.tags {
				display: flex;
				gap: $spacing-sm;
			}
		}
	}

	.follow-btn {
		background: $primary-gradient;
		color: $text-white;
		font-size: $font-size-xs;
		padding: $spacing-xs $spacing-md;
		border-radius: $radius-full;
		border: none;
		box-shadow: $shadow-sm;
		white-space: nowrap;
		min-width: 100rpx;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;

		&::after {
			border: none;
		}

		&:active:not(:disabled) {
			transform: scale(0.95);
		}

		&.followed {
			background: #f5f5f5;
			color: #666;
			border: 2rpx solid #e0e0e0;
			box-shadow: none;
		}

		&.loading,
		&:disabled {
			opacity: 0.6;
			transform: none !important;
			pointer-events: none;
		}

		.loading-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			animation: spin 1s linear infinite;
		}
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}
}

.title-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: $spacing-lg;

	.title-left {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		flex: 1;
		margin-right: $spacing-md;

		.title {
			font-size: $font-size-xl;
			font-weight: $font-weight-bold;
			color: $text-primary;
			margin-bottom: $spacing-sm;
		}
	}

	.title-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;

		.publish-time {
			font-size: $font-size-sm;
			color: $text-secondary;
			white-space: nowrap;
		}
	}

	.status-tag {
		font-size: $font-size-xs;
		padding: $spacing-xs $spacing-md;
		border-radius: $radius-full;

		&.notStarted {
			background: $primary-light;
			color: $primary-color;
		}

		&.enrolling {
			background: $success-light;
			color: $success-color;
		}

		&.enrollEnd {
			background: $warning-light;
			color: $warning-color;
		}

		&.inProgress {
			background: $info-light;
			color: $info-color;
		}

		&.ended {
			background: rgba(0, 0, 0, 0.1);
			color: $text-tertiary;
		}
	}
}

.info-section {
	background: $bg-tertiary;
	border-radius: $radius-md;
	padding: $spacing-md;
	margin-bottom: $spacing-lg;

	.info-item {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-md;

		&:last-child {
			margin-bottom: 0;
		}

		.info-icon {
			font-size: 36rpx;
			color: $primary-color;
			margin-right: $spacing-sm;
			width: 36rpx;
			text-align: center;
		}

		.info-text {
			font-size: $font-size-md;
			color: $text-secondary;
			margin-left: $spacing-sm;
		}
	}
}

.detail-section {
	margin-bottom: $spacing-lg;

	.section-title {
		font-size: $font-size-lg;
		font-weight: $font-weight-bold;
		color: $text-primary;
		margin-bottom: $spacing-md;
		display: block;
	}

	.detail-text {
		font-size: $font-size-md;
		color: $text-secondary;
		line-height: 1.6;
		margin-bottom: $spacing-md;
	}

	.detail-images {
		display: flex;
		flex-direction: column;
		margin-top: $spacing-md;

		.detail-image {
			width: 100%;
			height: auto;
			display: block;
			background-color: #f5f5f5;
			/* 添加背景色，便于调试 */
			min-height: 200rpx;
			/* 最小高度，便于查看是否有图片元素 */
		}
	}
}

.enroll-section {
	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-md;

		.section-title {
			font-size: $font-size-lg;
			font-weight: $font-weight-bold;
			color: $text-primary;
		}

		.enroll-count {
			font-size: $font-size-sm;
			color: $text-secondary;
			margin-left: $spacing-xs;
			flex: 1;
		}

		.refresh-btn {
			padding: $spacing-xs;
			border-radius: 50%;
			transition: all 0.3s ease;

			&:active:not(.loading) {
				transform: scale(0.9);
			}

			&.loading {
				animation: spin 1s linear infinite;
			}
		}
	}

	.loading-container {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: $spacing-xl 0;

		.loading-text {
			font-size: $font-size-sm;
			color: $text-secondary;
			margin-left: $spacing-sm;
		}
	}

	.enroll-list {
		.enroll-item {
			display: flex;
			align-items: center;
			padding: $spacing-md 0;
			border-bottom: 2rpx solid $bg-secondary;

			&:last-child {
				border-bottom: none;
			}

			.enroll-avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: $spacing-md;
			}

			.enroll-info {
				flex: 1;

				.enroll-name {
					font-size: $font-size-md;
					color: $text-primary;
					font-weight: $font-weight-medium;
					margin-bottom: $spacing-xs;
					display: block;
				}

				.enroll-time {
					font-size: $font-size-xs;
					color: $text-tertiary;
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: $spacing-xl 0;

		.empty-text {
			font-size: $font-size-sm;
			color: $text-tertiary;
			margin-top: $spacing-sm;
		}
	}
}

.bottom-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 100rpx;
	background: $bg-primary;
	display: flex;
	align-items: center;
	padding: 0 $spacing-lg;
	box-shadow: $shadow-sm;

	.action-btns {
		display: flex;
		gap: $spacing-lg;
		margin-right: $spacing-lg;

		button {
			background: none;
			padding: 0;
			line-height: 1;
			border: none;
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: $spacing-xs;

			&::after {
				border: none;
			}

			.action-icon {
				font-size: 40rpx;
				color: $text-secondary;
			}

			text:not(.action-icon) {
				font-size: $font-size-xs;
				color: $text-secondary;
			}
		}
	}

	.enroll-btn {
		flex: 1;
		height: 80rpx;
		background: $primary-gradient;
		color: $text-white;
		font-size: $font-size-lg;
		border-radius: $radius-full;
		border: none;

		&::after {
			border: none;
		}

		&.disabled {
			background: $text-tertiary;
			color: $text-white;
			cursor: not-allowed;
		}

		&.enrolled {
			background: $text-tertiary;
			color: $text-white;

			&::after {
				border: none;
			}
		}
	}
}
</style>