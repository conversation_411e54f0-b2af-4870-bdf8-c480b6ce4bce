package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.vo.ManageTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageTagToManageTagVoMapperImpl implements ManageTagToManageTagVoMapper {

    @Override
    public ManageTagVo convert(ManageTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageTagVo manageTagVo = new ManageTagVo();

        manageTagVo.setId( arg0.getId() );
        manageTagVo.setName( arg0.getName() );
        manageTagVo.setIcon( arg0.getIcon() );
        manageTagVo.setRemark( arg0.getRemark() );
        manageTagVo.setSort( arg0.getSort() );
        manageTagVo.setStatus( arg0.getStatus() );

        return manageTagVo;
    }

    @Override
    public ManageTagVo convert(ManageTag arg0, ManageTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
