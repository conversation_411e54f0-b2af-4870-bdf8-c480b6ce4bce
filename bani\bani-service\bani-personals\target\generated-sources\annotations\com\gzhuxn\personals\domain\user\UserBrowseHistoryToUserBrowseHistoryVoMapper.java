package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserBrowseHistoryBoToUserBrowseHistoryMapper;
import com.gzhuxn.personals.domain.user.vo.UserBrowseHistoryVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserBrowseHistoryBoToUserBrowseHistoryMapper.class,UserBrowseHistoryToAppUserHomeBrowseHistoryVoMapper.class},
    imports = {}
)
public interface UserBrowseHistoryToUserBrowseHistoryVoMapper extends BaseMapper<UserBrowseHistory, UserBrowseHistoryVo> {
}
