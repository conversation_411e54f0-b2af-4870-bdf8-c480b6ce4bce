package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserAccountHistory;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAccountHistoryBoToUserAccountHistoryMapperImpl implements UserAccountHistoryBoToUserAccountHistoryMapper {

    @Override
    public UserAccountHistory convert(UserAccountHistoryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAccountHistory userAccountHistory = new UserAccountHistory();

        userAccountHistory.setSearchValue( arg0.getSearchValue() );
        userAccountHistory.setCreateBy( arg0.getCreateBy() );
        userAccountHistory.setCreateTime( arg0.getCreateTime() );
        userAccountHistory.setUpdateBy( arg0.getUpdateBy() );
        userAccountHistory.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userAccountHistory.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userAccountHistory.setCreateDept( arg0.getCreateDept() );
        userAccountHistory.setId( arg0.getId() );
        userAccountHistory.setUserId( arg0.getUserId() );
        userAccountHistory.setType( arg0.getType() );
        userAccountHistory.setBusinessId( arg0.getBusinessId() );
        userAccountHistory.setCoin( arg0.getCoin() );
        userAccountHistory.setCoinBefore( arg0.getCoinBefore() );
        userAccountHistory.setCoinAfter( arg0.getCoinAfter() );
        userAccountHistory.setCoinType( arg0.getCoinType() );
        userAccountHistory.setOpType( arg0.getOpType() );
        userAccountHistory.setStatus( arg0.getStatus() );
        userAccountHistory.setEffectTime( arg0.getEffectTime() );
        userAccountHistory.setDsc( arg0.getDsc() );

        return userAccountHistory;
    }

    @Override
    public UserAccountHistory convert(UserAccountHistoryBo arg0, UserAccountHistory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setCoinBefore( arg0.getCoinBefore() );
        arg1.setCoinAfter( arg0.getCoinAfter() );
        arg1.setCoinType( arg0.getCoinType() );
        arg1.setOpType( arg0.getOpType() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setEffectTime( arg0.getEffectTime() );
        arg1.setDsc( arg0.getDsc() );

        return arg1;
    }
}
