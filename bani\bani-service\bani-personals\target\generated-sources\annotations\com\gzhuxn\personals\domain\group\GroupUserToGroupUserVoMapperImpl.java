package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.domain.group.vo.GroupUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class GroupUserToGroupUserVoMapperImpl implements GroupUserToGroupUserVoMapper {

    @Override
    public GroupUserVo convert(GroupUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupUserVo groupUserVo = new GroupUserVo();

        groupUserVo.setId( arg0.getId() );
        groupUserVo.setGroupId( arg0.getGroupId() );
        groupUserVo.setUserId( arg0.getUserId() );
        groupUserVo.setAdminFlag( arg0.getAdminFlag() );

        return groupUserVo;
    }

    @Override
    public GroupUserVo convert(GroupUser arg0, GroupUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAdminFlag( arg0.getAdminFlag() );

        return arg1;
    }
}
