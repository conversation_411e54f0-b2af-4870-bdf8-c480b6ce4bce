package com.gzhuxn.personals.domain.user;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gzhuxn.personals.domain.UserBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户-打招呼对象 user_greeting
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_greeting")
public class UserGreeting extends UserBaseEntity {

    /**
     * 对方用户ID
     */
    private Long oppositeUserId;

    /**
     * 打招呼内容
     */
    private String content;

    /**
     * 状态：0-待回复，1-已回复，2-已忽略
     */
    private Integer status;
}
