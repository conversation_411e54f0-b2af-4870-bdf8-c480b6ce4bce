package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.controller.app.message.bo.AppMsgGroupCreateBoToMsgGroupBoMapper;
import com.gzhuxn.personals.controller.app.message.bo.AppMsgGroupUpdateBoToMsgGroupBoMapper;
import com.gzhuxn.personals.domain.message.MsgGroup;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppMsgGroupUpdateBoToMsgGroupBoMapper.class,AppMsgGroupCreateBoToMsgGroupBoMapper.class},
    imports = {}
)
public interface MsgGroupBoToMsgGroupMapper extends BaseMapper<MsgGroupBo, MsgGroup> {
}
