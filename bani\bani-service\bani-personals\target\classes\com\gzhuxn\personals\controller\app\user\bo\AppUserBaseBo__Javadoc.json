{"doc": " 用户-用户详情业务对象 user_detail\n\n <AUTHOR>\n @date 2024-12-02\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 昵称\n"}, {"name": "sex", "doc": " 性别,编码;字典：user_sex\n"}, {"name": "birthday", "doc": " 生日/出生日期\n"}, {"name": "height", "doc": " 身高,cm\n"}, {"name": "weight", "doc": " 体重,kg\n"}, {"name": "edu", "doc": " 学历,编码;字典：user_edu\n"}, {"name": "job", "doc": " 职业,编码;字典：user_job\n"}, {"name": "affectiveStatus", "doc": " 个人情感状况,编码;字典：user_affective_status\n"}, {"name": "revenue", "doc": " 收入,编码;字典：user_revenue\n"}, {"name": "addrProvinceCode", "doc": " 户籍地省编码\n"}, {"name": "addrCityCode", "doc": " 户籍地市编码\n"}, {"name": "addrDistrictCode", "doc": " 户籍地区/县编码\n"}, {"name": "addrStreetCode", "doc": " 户籍地街道/镇/乡编码\n"}, {"name": "addr", "doc": " 户籍详细地址\n"}, {"name": "addrNewProvinceCode", "doc": " 现居地省编码\n"}, {"name": "addrNewCityCode", "doc": " 现居地市编码\n"}, {"name": "addrNewDistrictCode", "doc": " 现居地区/县编码\n"}, {"name": "addrNewStreetCode", "doc": " 现居地街道/镇/乡编码\n"}, {"name": "addrNew", "doc": " 现居详细地址\n"}], "enumConstants": [], "methods": [], "constructors": []}