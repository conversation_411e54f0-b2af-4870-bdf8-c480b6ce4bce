package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.bo.ActSafeguardBoToActSafeguardMapper;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardBoToActSafeguardMapper.class,ActSafeguardToActSafeguardStorageVoMapper.class},
    imports = {}
)
public interface ActSafeguardToActSafeguardVoMapper extends BaseMapper<ActSafeguard, ActSafeguardVo> {
}
