package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToAppRecommendMomentDetailVoMapperImpl implements UserMomentToAppRecommendMomentDetailVoMapper {

    @Override
    public AppRecommendMomentDetailVo convert(UserMoment source) {
        if ( source == null ) {
            return null;
        }

        AppRecommendMomentDetailVo appRecommendMomentDetailVo = new AppRecommendMomentDetailVo();

        appRecommendMomentDetailVo.setCityName( source.getCityCode() );
        appRecommendMomentDetailVo.setTime( source.getCreateTime() );
        appRecommendMomentDetailVo.setId( source.getId() );
        appRecommendMomentDetailVo.setContent( source.getContent() );
        appRecommendMomentDetailVo.setImages( source.getImages() );
        appRecommendMomentDetailVo.setLocation( source.getLocation() );
        appRecommendMomentDetailVo.setPv( source.getPv() );
        appRecommendMomentDetailVo.setLv( source.getLv() );
        appRecommendMomentDetailVo.setCv( source.getCv() );

        return appRecommendMomentDetailVo;
    }

    @Override
    public AppRecommendMomentDetailVo convert(UserMoment source, AppRecommendMomentDetailVo target) {
        if ( source == null ) {
            return target;
        }

        target.setCityName( source.getCityCode() );
        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setContent( source.getContent() );
        target.setImages( source.getImages() );
        target.setLocation( source.getLocation() );
        target.setPv( source.getPv() );
        target.setLv( source.getLv() );
        target.setCv( source.getCv() );

        return target;
    }
}
