package com.gzhuxn.personals.controller.app.activity.vo;

import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityToAppDaziActivityPageVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActivityToAppDaziActivityPageVoMapper.class},
    imports = {}
)
public interface AppDaziActivityPageVoToActivityMapper extends BaseMapper<AppDaziActivityPageVo, Activity> {
}
