package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActivityVo;
import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToActivityVoMapperImpl implements ActivityToActivityVoMapper {

    @Override
    public ActivityVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActivityVo activityVo = new ActivityVo();

        activityVo.setId( arg0.getId() );
        activityVo.setGroupId( arg0.getGroupId() );
        activityVo.setName( arg0.getName() );
        activityVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        activityVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            activityVo.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        activityVo.setEndTime( arg0.getEndTime() );
        activityVo.setTimeLength( arg0.getTimeLength() );
        activityVo.setRefundTime( arg0.getRefundTime() );
        activityVo.setOfficialFlag( arg0.getOfficialFlag() );
        activityVo.setIntroduce( arg0.getIntroduce() );
        activityVo.setOriginalAmount( arg0.getOriginalAmount() );
        activityVo.setAmount( arg0.getAmount() );
        activityVo.setStatus( arg0.getStatus() );
        activityVo.setAuditStatus( arg0.getAuditStatus() );
        activityVo.setType( arg0.getType() );
        activityVo.setLon( arg0.getLon() );
        activityVo.setLat( arg0.getLat() );
        activityVo.setCreateByName( arg0.getCreateByName() );

        return activityVo;
    }

    @Override
    public ActivityVo convert(Activity arg0, ActivityVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            arg1.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setStartTime( null );
        }
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
