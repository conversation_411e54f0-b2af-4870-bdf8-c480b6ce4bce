<template>
	<scroll-nav-page :title="pageTitle" :show-back="true">
		<template #content>
			<view class="page-container">
				<!-- 内容区域 -->
				<view class="content-wrapper" :style="{ paddingTop: navBarHeight + 'px' }">
					<view class="help-content">
						<rich-text :nodes="content"></rich-text>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { toast } from '@/utils/common'
import { getHelpByKey } from '@/api/content/help'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)
// 页面标题
const pageTitle = ref('')
// 页面内容
const content = ref('')

// 页面滚动监听
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
}

// 导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}


// 页面加载
onLoad((option) => {
	if (!option.key) {
		toast('不存在的页面Key')
		uni.navigateBack({
			delta: 1
		})
		return
	}
	pageTitle.value = ''
	// 获取帮助内容
	getHelpByKey(option.key).then(res => {
		content.value = res.data.content
		pageTitle.value = res.data.name
	}).catch(err => {
		toast('获取内容失败')
		// 如果接口不存在，可以显示默认内容
		loadDefaultContent(option.key)
	})
})

// 加载默认内容（当接口不可用时）
const loadDefaultContent = (key) => {
	const defaultContents = {
		userGuide: `
			<h2>欢迎使用伴你有约</h2>
			<p>这里是用户指南的默认内容...</p>
		`,
		faq: `
			<h2>常见问题</h2>
			<h3>Q: 如何注册账号？</h3>
			<p>A: 您可以通过手机号码快速注册...</p>
		`,
		contactUs: `
			<h2>联系我们</h2>
			<p>客服电话：400-123-4567</p>
			<p>客服邮箱：<EMAIL></p>
		`,
		feedback: `
			<h2>意见反馈</h2>
			<p>您的意见对我们很重要...</p>
		`,
		safetyTips: `
			<h2>安全提示</h2>
			<p>为了您的账号安全，请注意以下事项...</p>
		`,
		featureIntro: `
			<h2>功能介绍</h2>
			<p>伴你有约为您提供以下功能...</p>
		`
	}

	content.value = defaultContents[key] || '<p>内容加载中...</p>'
}

// 页面滚动监听
onPageScroll(handlePageScroll)

</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.content-wrapper {
	margin-top: 20rpx;
	padding: 20rpx;
	box-sizing: border-box;
}

.help-content {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	font-size: 30rpx;
	line-height: 1.8;
	color: #333;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1px solid rgba(255, 255, 255, 0.2);

	// 富文本内容样式优化
	:deep(p) {
		margin-bottom: 24rpx;
		text-align: justify;
	}

	:deep(h1),
	:deep(h2),
	:deep(h3) {
		color: $primary-color;
		font-weight: 600;
		margin: 40rpx 0 20rpx 0;
	}

	:deep(h1) {
		font-size: 36rpx;
	}

	:deep(h2) {
		font-size: 34rpx;
	}

	:deep(h3) {
		font-size: 32rpx;
	}

	:deep(ul),
	:deep(ol) {
		padding-left: 40rpx;
		margin-bottom: 24rpx;
	}

	:deep(li) {
		margin-bottom: 12rpx;
		line-height: 1.6;
	}

	:deep(strong) {
		color: $primary-color;
		font-weight: 600;
	}

	:deep(a) {
		color: $primary-color;
		text-decoration: underline;
	}

	// 帮助页面特有样式
	:deep(.faq-item) {
		background: rgba(105, 108, 243, 0.05);
		padding: 20rpx;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		border-left: 4rpx solid $primary-color;
	}

	:deep(.contact-info) {
		background: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(105, 108, 243, 0.05));
		padding: 30rpx;
		border-radius: 16rpx;
		text-align: center;
		margin: 30rpx 0;
	}

	:deep(.safety-warning) {
		background: rgba(255, 193, 7, 0.1);
		border: 1px solid rgba(255, 193, 7, 0.3);
		padding: 20rpx;
		border-radius: 12rpx;
		margin: 20rpx 0;
	}
}

// 响应式设计
@media screen and (max-width: 750rpx) {
	.help-content {
		padding: 30rpx 24rpx;
		font-size: 28rpx;

		:deep(h1) {
			font-size: 34rpx;
		}

		:deep(h2) {
			font-size: 32rpx;
		}

		:deep(h3) {
			font-size: 30rpx;
		}
	}
}
</style>
