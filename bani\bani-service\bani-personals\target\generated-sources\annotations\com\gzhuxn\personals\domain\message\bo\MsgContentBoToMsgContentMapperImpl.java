package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.domain.message.MsgContent;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentBoToMsgContentMapperImpl implements MsgContentBoToMsgContentMapper {

    @Override
    public MsgContent convert(MsgContentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContent msgContent = new MsgContent();

        msgContent.setSearchValue( arg0.getSearchValue() );
        msgContent.setCreateBy( arg0.getCreateBy() );
        msgContent.setCreateTime( arg0.getCreateTime() );
        msgContent.setUpdateBy( arg0.getUpdateBy() );
        msgContent.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            msgContent.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        msgContent.setCreateDept( arg0.getCreateDept() );
        msgContent.setId( arg0.getId() );
        msgContent.setSendUserId( arg0.getSendUserId() );
        msgContent.setType( arg0.getType() );
        msgContent.setSubType( arg0.getSubType() );
        msgContent.setGroupId( arg0.getGroupId() );
        msgContent.setContent( arg0.getContent() );
        msgContent.setParamsJs( arg0.getParamsJs() );

        return msgContent;
    }

    @Override
    public MsgContent convert(MsgContentBo arg0, MsgContent arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setSendUserId( arg0.getSendUserId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setContent( arg0.getContent() );
        arg1.setParamsJs( arg0.getParamsJs() );

        return arg1;
    }
}
