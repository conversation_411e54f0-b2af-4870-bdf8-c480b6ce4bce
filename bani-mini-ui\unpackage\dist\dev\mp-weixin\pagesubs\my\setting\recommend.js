"use strict";const e=require("../../../common/vendor.js"),d=require("../../../api/my/config.js"),a=require("../../../store/index.js");if(!Array){const u=e.resolveComponent("uni-icons"),r=e.resolveComponent("slider-range");(u+r)()}const y=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",C=()=>"../../../components/slider-range/slider-range.js";Math||(y+C+I)();const I=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",b={__name:"recommend",setup(u){const r=e.ref(0),n=e.ref({ageMin:18,ageMax:70,heightMin:140,heightMax:220,education:0,location:0}),i=e.ref(0),s=e.ref(0),t=e.ref(!1),l=a.$store.dict.getNames("recommend_set_education"),g=a.$store.dict.getNames("recommend_set_location"),v=o=>{r.value=o},m=o=>o+"岁",_=o=>o+"cm",h=o=>{n.value.ageMin=o[0],n.value.ageMax=o[1]},f=o=>{n.value.heightMin=o[0],n.value.heightMax=o[1]},p=o=>{i.value=o.detail.value},x=o=>{s.value=o.detail.value},M=async()=>{if(!t.value){if(n.value.ageMin>=n.value.ageMax){e.index.showToast({title:"年龄范围设置错误",icon:"none"});return}if(n.value.heightMin>=n.value.heightMax){e.index.showToast({title:"身高范围设置错误",icon:"none"});return}t.value=!0,n.value.education=a.$store.dict.getIdByIndex("recommend_set_education",i.value),n.value.location=a.$store.dict.getIdByIndex("recommend_set_location",s.value),d.updateUserConfig({configKey:"user_recommend_settings",val:JSON.stringify(n.value)}).then(o=>{e.index.showToast({title:"设置保存成功",icon:"success"}),setTimeout(()=>{e.index.navigateBack(),t.value=!1},1500)})}};return e.onLoad(()=>{d.getUserConfigMap(2).then(o=>{if(o.data){const c=JSON.parse(o.data.user_recommend_settings);Object.assign(n.value,c),i.value=a.$store.dict.getIndexById("recommend_set_education",c.education),s.value=a.$store.dict.getIndexById("recommend_set_location",c.location)}})}),(o,c)=>e.e({a:e.p({type:"info-filled",size:"20",color:"#696CF3"}),b:e.t(n.value.ageMin),c:e.t(n.value.ageMax),d:e.o(h),e:e.p({min:18,max:70,step:1,value:[n.value.ageMin,n.value.ageMax],activeColor:"#696CF3",backgroundColor:"#E5E6F3","block-size":"24",format:m}),f:e.t(n.value.heightMin),g:e.t(n.value.heightMax),h:e.o(f),i:e.p({min:140,max:220,step:5,value:[n.value.heightMin,n.value.heightMax],activeColor:"#696CF3",backgroundColor:"#E5E6F3","block-size":"24",format:_}),j:e.t(e.unref(l)[i.value]),k:e.p({type:"right",size:"16",color:"#999"}),l:e.o(p),m:i.value,n:e.unref(l),o:e.t(e.unref(g)[s.value]),p:e.p({type:"right",size:"16",color:"#999"}),q:e.o(x),r:s.value,s:e.unref(g),t:!t.value},t.value?{}:{},{v:e.o(M),w:t.value,x:e.o(v),y:e.p({title:"推荐设置","show-back":!0})})}},B=e._export_sfc(b,[["__scopeId","data-v-3bff7f7f"]]);wx.createPage(B);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/setting/recommend.js.map
