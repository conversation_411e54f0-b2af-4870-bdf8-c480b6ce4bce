package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserWechatApplyVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserWechatApplyToUserWechatApplyVoMapperImpl implements UserWechatApplyToUserWechatApplyVoMapper {

    @Override
    public UserWechatApplyVo convert(UserWechatApply arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWechatApplyVo userWechatApplyVo = new UserWechatApplyVo();

        userWechatApplyVo.setId( arg0.getId() );
        userWechatApplyVo.setUserId( arg0.getUserId() );
        userWechatApplyVo.setOppositeUserId( arg0.getOppositeUserId() );
        userWechatApplyVo.setContent( arg0.getContent() );
        userWechatApplyVo.setStatus( arg0.getStatus() );

        return userWechatApplyVo;
    }

    @Override
    public UserWechatApplyVo convert(UserWechatApply arg0, UserWechatApplyVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
