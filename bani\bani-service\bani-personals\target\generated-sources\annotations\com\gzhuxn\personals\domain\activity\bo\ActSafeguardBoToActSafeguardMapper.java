package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.personals.domain.activity.ActSafeguard;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface ActSafeguardBoToActSafeguardMapper extends BaseMapper<ActSafeguardBo, ActSafeguard> {
}
