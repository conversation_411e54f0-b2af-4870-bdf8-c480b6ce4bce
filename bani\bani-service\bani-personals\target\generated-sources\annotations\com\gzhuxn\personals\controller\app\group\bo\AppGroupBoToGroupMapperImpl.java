package com.gzhuxn.personals.controller.app.group.bo;

import com.gzhuxn.personals.domain.group.Group;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppGroupBoToGroupMapperImpl implements AppGroupBoToGroupMapper {

    @Override
    public Group convert(AppGroupBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Group group = new Group();

        group.setSearchValue( arg0.getSearchValue() );
        group.setCreateBy( arg0.getCreateBy() );
        group.setCreateTime( arg0.getCreateTime() );
        group.setUpdateBy( arg0.getUpdateBy() );
        group.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            group.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        group.setCreateDept( arg0.getCreateDept() );
        group.setId( arg0.getId() );
        group.setName( arg0.getName() );
        group.setAvatar( arg0.getAvatar() );
        group.setIntroduce( arg0.getIntroduce() );
        group.setBackgroundImg( arg0.getBackgroundImg() );
        group.setOfficialFlag( arg0.getOfficialFlag() );
        group.setOwnerUserId( arg0.getOwnerUserId() );

        return group;
    }

    @Override
    public Group convert(AppGroupBo arg0, Group arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setBackgroundImg( arg0.getBackgroundImg() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setOwnerUserId( arg0.getOwnerUserId() );

        return arg1;
    }
}
