package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserMomentTag;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface UserMomentTagBoToUserMomentTagMapper extends BaseMapper<UserMomentTagBo, UserMomentTag> {
}
