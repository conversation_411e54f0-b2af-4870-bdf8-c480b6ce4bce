package com.gzhuxn.personals.domain.user.vo;

import com.gzhuxn.personals.domain.user.UserLike;
import com.gzhuxn.personals.domain.user.UserLikeToUserLikeVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserLikeToUserLikeVoMapper.class},
    imports = {}
)
public interface UserLikeVoToUserLikeMapper extends BaseMapper<UserLikeVo, UserLike> {
}
