package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.wechatapply.AppUserWechatApplyVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserWechatApplyToAppUserWechatApplyVoMapperImpl implements UserWechatApplyToAppUserWechatApplyVoMapper {

    @Override
    public AppUserWechatApplyVo convert(UserWechatApply source) {
        if ( source == null ) {
            return null;
        }

        AppUserWechatApplyVo appUserWechatApplyVo = new AppUserWechatApplyVo();

        appUserWechatApplyVo.setOppUserId( source.getOppositeUserId() );
        appUserWechatApplyVo.setId( source.getId() );
        appUserWechatApplyVo.setUserId( source.getUserId() );
        appUserWechatApplyVo.setContent( source.getContent() );
        appUserWechatApplyVo.setStatus( source.getStatus() );
        appUserWechatApplyVo.setCreateTime( source.getCreateTime() );

        return appUserWechatApplyVo;
    }

    @Override
    public AppUserWechatApplyVo convert(UserWechatApply source, AppUserWechatApplyVo target) {
        if ( source == null ) {
            return target;
        }

        target.setOppUserId( source.getOppositeUserId() );
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );
        target.setContent( source.getContent() );
        target.setStatus( source.getStatus() );
        target.setCreateTime( source.getCreateTime() );

        return target;
    }
}
