"use strict";const e=require("../../../common/vendor.js");if(!Array){const i=e.resolveComponent("uni-segmented-control"),a=e.resolveComponent("uni-icons"),s=e.resolveComponent("z-paging"),n=e.resolveComponent("scroll-nav-page");(i+a+s+n)()}const b=()=>"../../../uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.js",m=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",y=()=>"../../../uni_modules/z-paging/components/z-paging/z-paging.js",f=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(b+m+y+f)();const p={__name:"question",setup(i){const a=e.ref(0),s=e.ref(0);e.computed(()=>a.value>50?"#333":"#fff");const n=e.ref(0),l=e.ref(""),u=e.ref(null),r=e.ref([]);e.onPageScroll(t=>{a.value=t.scrollTop}),e.onLoad(t=>{t.type||(t.type=1),n.value=parseInt(t.type)-1,l.value=t.type});function d(t,g){const o=[{id:1,title:"如何提高个人魅力？",content:"想要在交友中更有吸引力，有什么好的建议吗？",createTime:"2024-01-15 14:30",viewCount:25,answerCount:3,isResolved:!0},{id:2,title:"第一次约会应该注意什么？",content:"马上要和心仪的人第一次见面，有点紧张，求建议！",createTime:"2024-01-14 10:20",viewCount:18,answerCount:5,isResolved:!1}];setTimeout(()=>{u.value.complete(t===1?o:[])},500)}const v=t=>{n.value!==t.currentIndex&&(n.value=t.currentIndex,l.value=n.value+1,u.value.reload())},_=t=>{e.index.navigateTo({url:`/pagesubs/my/question/detail?id=${t.id}&type=${l.value}`})};return(t,g)=>({a:e.o(v),b:e.p({current:n.value,values:["我的提问","我的回答"],styleType:"text",activeColor:"#696CF3"}),c:e.f(r.value,(o,z,c)=>e.e({a:"16cad6be-3-"+c+",16cad6be-2",b:e.t(o.createTime),c:e.t(o.title||o.question),d:o.content},o.content?{e:e.t(o.content)}:{},{f:"16cad6be-4-"+c+",16cad6be-2",g:e.t(o.viewCount||0)},n.value===0?{h:"16cad6be-5-"+c+",16cad6be-2",i:e.p({type:"chatbubble",size:"14",color:"#999"}),j:e.t(o.answerCount||0)}:{},{k:o.isResolved},o.isResolved?{l:"16cad6be-6-"+c+",16cad6be-2",m:e.p({type:"checkmarkempty",size:"14",color:"#4ecb73"})}:{},{n:"16cad6be-7-"+c+",16cad6be-2",o:e.o(C=>_(o),o.id),p:o.id})),d:e.p({type:n.value===0?"help":"chatbubble",size:"16",color:n.value===0?"#ff6b6b":"#4ecb73"}),e:e.t(n.value===0?"提问":"回答"),f:e.p({type:"eye",size:"14",color:"#999"}),g:n.value===0,h:e.p({type:"right",size:"12",color:"#696CF3"}),i:e.p({type:n.value===0?"help":"chatbubble",size:"80",color:"#ddd"}),j:e.t(n.value===0?"暂无提问记录":"暂无回答记录"),k:e.t(n.value===0?"您还没有提出过问题":"您还没有回答过问题"),l:e.sr(u,"16cad6be-2,16cad6be-0",{k:"paging"}),m:e.o(d),n:e.o(o=>r.value=o),o:e.p({fixed:!1,"use-page-scroll":!1,modelValue:r.value}),p:s.value+"px",q:e.p({title:"我的问答","show-back":!0})})}},h=e._export_sfc(p,[["__scopeId","data-v-16cad6be"]]);p.__runtimeHooks=1;wx.createPage(h);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/question/question.js.map
