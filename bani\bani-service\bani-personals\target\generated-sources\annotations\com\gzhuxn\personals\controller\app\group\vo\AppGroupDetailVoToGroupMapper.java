package com.gzhuxn.personals.controller.app.group.vo;

import com.gzhuxn.personals.domain.group.Group;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppGroupDetailVoToGroupMapper extends BaseMapper<AppGroupDetailVo, Group> {
}
