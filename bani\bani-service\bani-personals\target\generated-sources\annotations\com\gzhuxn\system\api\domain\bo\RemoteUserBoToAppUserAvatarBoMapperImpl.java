package com.gzhuxn.system.api.domain.bo;

import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class RemoteUserBoToAppUserAvatarBoMapperImpl implements RemoteUserBoToAppUserAvatarBoMapper {

    @Override
    public AppUserAvatarBo convert(RemoteUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserAvatarBo appUserAvatarBo = new AppUserAvatarBo();

        appUserAvatarBo.setUserId( arg0.getUserId() );
        appUserAvatarBo.setAvatar( arg0.getAvatar() );

        return appUserAvatarBo;
    }

    @Override
    public AppUserAvatarBo convert(RemoteUserBo arg0, AppUserAvatarBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setAvatar( arg0.getAvatar() );

        return arg1;
    }
}
