"use strict";const o=require("../common/vendor.js"),x=require("../store/index.js"),p=require("../api/my/my.js"),_=require("../api/order/order.js");function g(t){o.index.showToast({icon:"none",title:t})}function h(t){return new Promise((e,n)=>{o.index.showModal({title:"提示",content:t,cancelText:"取消",confirmText:"确定",success:function(i){e(i)}})})}function y(t,e={}){const n=t.payData;return new Promise((i,s)=>{const{pollInterval:a=2e3,maxPollCount:c=30}=e;let l=0,m=null;const d=()=>{m&&(clearInterval(m),m=null),o.index.hideLoading()},f=()=>{l++,_.getPayStatus(t.payOrderNo).then(u=>{const r=u.data.status;if(r===10){d(),i({success:!0,status:r,message:"支付成功"});return}if(r===3||r===4){d(),i({success:!1,status:r,message:r===3?"支付失败":"支付已关闭"});return}l>=c&&(d(),i({success:!1,status:r,message:"支付状态查询超时"}))}).catch(u=>{o.index.__f__("error","at utils/common.js:105","支付状态查询异常:",u),l>=c&&(d(),i({success:!1,status:-1,message:"支付状态查询异常"}))})};o.index.showLoading({title:"支付中..."}),o.index.requestPayment({provider:"wxpay",appId:n.appId,timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.packageValue,signType:n.signType,paySign:n.paySign,success:()=>{m=setInterval(f,a),f()},fail:u=>{o.index.__f__("error","at utils/common.js:138","微信支付调用失败:",u),d();let r="支付失败";u.errMsg&&(u.errMsg.includes("cancel")?r="支付已取消":u.errMsg.includes("fail")?r="支付失败，请重试":r="支付异常，请重试"),i({success:!1,status:-1,message:r})}})})}function P(t){let e="";for(const s of Object.keys(t)){const a=t[s];var n=encodeURIComponent(s)+"=";if(a!==null&&a!==""&&typeof a<"u")if(typeof a=="object"){for(const c of Object.keys(a))if(a[c]!==null&&a[c]!==""&&typeof a[c]<"u"){let l=s+"["+c+"]";var i=encodeURIComponent(l)+"=";e+=i+encodeURIComponent(a[c])+"&"}}else e+=n+encodeURIComponent(a)+"&"}return e}function w(){var t=new Date().getTime(),e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){var i=(t+Math.random()*16)%16|0;return t=Math.floor(t/16),(n==="x"?i:i&3|8).toString(16)});return e}function S(){for(var t=[],e=140;e<=220;e++)t.push({id:e,name:e+"cm"});return t}function j(){for(var t=[],e=40;e<=120;e++)t.push({id:e,name:e+"kg"});return t}function C(t){if(!t){g("复制内容为空");return}o.index.setClipboardData({data:String(t),success:function(){g("复制成功")},fail:function(e){g("复制失败")}})}function I(){g("功能开发中，敬请期待！")}function D(t){return typeof t=="string"}function k(t,e){return new Promise(n=>{o.index.compressImage({src:t,quality:e||80,success:i=>{n(i.tempFilePath)},fail:i=>{o.index.__f__("error","at utils/common.js:476","图片压缩失败:",i),n(t)}})})}function U(){try{const t=getCurrentPages();if(!t||!Array.isArray(t)||t.length===0)return o.index.__f__("warn","at utils/common.js:494","无法获取当前页面信息"),"";const e=t[t.length-1];if(!e)return o.index.__f__("warn","at utils/common.js:499","当前页面对象不存在"),"";const n=e.route,i=e.options||{};o.index.__f__("log","at utils/common.js:506","当前页面路径getCurrentPagePath:",n);let s="/"+n;if(i&&Object.keys(i).length>0){const a=Object.keys(i).map(c=>`${c}=${encodeURIComponent(i[c]||"")}`).join("&");s+="?"+a}return s}catch(t){return o.index.__f__("error","at utils/common.js:518","获取当前页面路径失败:",t),""}}function v(){return new Promise((t,e)=>{const n=o.index.getStorageSync("user_location"),i=Date.now();if(n&&n.timestamp&&i-n.timestamp<6e5){t({longitude:n.longitude,latitude:n.latitude});return}o.index.showLoading({title:"获取位置中..."}),o.index.getLocation({type:"gcj02",success:s=>{const a={longitude:s.longitude,latitude:s.latitude,timestamp:i};o.index.setStorageSync("user_location",a),o.index.hideLoading(),x.$store.isUserShort()||p.updateUserLocation(s.longitude,s.latitude),t({longitude:s.longitude,latitude:s.latitude})},fail:s=>{o.index.hideLoading(),o.index.__f__("error","at utils/common.js:570","获取位置失败:",s),n&&n.longitude&&n.latitude?t({longitude:n.longitude,latitude:n.latitude}):e(s)}})})}exports.clickCopy=C;exports.clickDeveloping=I;exports.compressImage=k;exports.createUUID=w;exports.getCurrentPagePath=U;exports.getLocation=v;exports.handlePaymentWithPolling=y;exports.heightDicts=S;exports.isString=D;exports.showConfirm=h;exports.tansParams=P;exports.toast=g;exports.weightDicts=j;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/common.js.map
