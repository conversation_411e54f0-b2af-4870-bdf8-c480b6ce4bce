package com.gzhuxn.personals.domain.audit;

import com.gzhuxn.personals.domain.audit.vo.ContentAuditDetailVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ContentAuditRecordToContentAuditVoMapper.class},
    imports = {}
)
public interface ContentAuditRecordToContentAuditDetailVoMapper extends BaseMapper<ContentAuditRecord, ContentAuditDetailVo> {
}
