package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserGreeting;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * App端用户打招呼视图对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@AutoMapper(target = UserGreeting.class, convertGenerate = false)
public class AppUserGreetingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 对方用户ID
     */
    private Long oppositeUserId;

    /**
     * 打招呼内容
     */
    private String content;

    /**
     * 状态：0-待回复，1-已回复，2-已忽略
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 对方用户昵称
     */
    private String oppositeUserNickName;

    /**
     * 对方用户头像
     */
    private String oppositeUserAvatar;
}
