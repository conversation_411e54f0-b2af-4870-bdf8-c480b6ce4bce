{"version": 3, "file": "add.js", "sources": ["pagesubs/moment/add.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbW9tZW50XGFkZC52dWU"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"写动态\" :show-back=\"true\">\r\n\t\t<template #content>\r\n\t\t\t<view class=\"add-moment-container\">\r\n\t\t\t\t<!-- 主要内容 -->\r\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t\t\t<!-- 文本输入区域 -->\r\n\t\t\t\t\t<view class=\"text-input-section\">\r\n\t\t\t\t\t\t<textarea class=\"moment-textarea\" v-model=\"momentForm.content\" placeholder=\"分享新鲜事...\"\r\n\t\t\t\t\t\t\t:maxlength=\"500\" :auto-height=\"true\" :show-confirm-bar=\"false\"></textarea>\r\n\t\t\t\t\t\t<view class=\"char-count\">\r\n\t\t\t\t\t\t\t<text class=\"count-text\">{{ momentForm.content.length }}/255</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 图片选择区域 -->\r\n\t\t\t\t\t<view class=\"image-section\">\r\n\t\t\t\t\t\t<images-select :limit=\"6\" showTip=\"true\" type=\"user_comment.images\" v-model=\"imageFiles\" />\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 功能选项区域 -->\r\n\t\t\t\t\t<view class=\"options-section\">\r\n\t\t\t\t\t\t<view class=\"option-item\" @click=\"showTopicModal\">\r\n\t\t\t\t\t\t\t<view class=\"option-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"24\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"option-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"option-label\">选择话题</text>\r\n\t\t\t\t\t\t\t\t<view class=\"selected-topics\" v-if=\"selectedTopics.length > 0\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"topic-tag\" v-for=\"topic in selectedTopics\" :key=\"topic.id\">\r\n\t\t\t\t\t\t\t\t\t\t#{{ topic.name }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"option-placeholder\" v-else>添加话题，让更多人看到</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"option-item\" @click=\"showLocationSelect\">\r\n\t\t\t\t\t\t\t<view class=\"option-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"24\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"option-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"option-label\">所在位置</text>\r\n\t\t\t\t\t\t\t\t<text class=\"option-value\" v-if=\"momentForm.location\">{{ momentForm.location }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"option-placeholder\" v-else>你在哪里？</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"option-item\" @click=\"showVisibilityPicker\">\r\n\t\t\t\t\t\t\t<view class=\"option-icon\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"24\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"option-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"option-label\">谁可以看</text>\r\n\t\t\t\t\t\t\t\t<text class=\"option-value\">{{ getVisibilityLabel(momentForm.visibility) }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 发布按钮区域 -->\r\n\t\t\t\t\t<view class=\"publish-area\">\r\n\t\t\t\t\t\t<button class=\"publish-btn\" @click=\"publishMoment\" :disabled=\"!canPublish\">\r\n\t\t\t\t\t\t\t<text class=\"publish-text\">发布</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n\r\n\r\n\t<!-- 选择话题弹窗 -->\r\n\t<uni-popup ref=\"topicPopup\" type=\"bottom\" :mask-click=\"true\">\r\n\t\t<view class=\"topic-modal\">\r\n\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t<text class=\"modal-title\">选择话题</text>\r\n\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\" @click=\"closeTopicModal\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"topic-list\">\r\n\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t<view class=\"loading-container\" v-if=\"isLoadingTopics\">\r\n\t\t\t\t\t<view class=\"loading-content\">\r\n\t\t\t\t\t\t<uni-icons type=\"spinner-cycle\" size=\"24\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t<text class=\"loading-text\">加载话题中...</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 话题列表 -->\r\n\t\t\t\t<view v-else-if=\"topicList.length > 0\">\r\n\t\t\t\t\t<view class=\"topic-item\" :class=\"{ selected: isTopicSelected(topic) }\" v-for=\"topic in topicList\"\r\n\t\t\t\t\t\t:key=\"topic.id\" @click=\"toggleTopic(topic)\">\r\n\t\t\t\t\t\t<view class=\"topic-info\">\r\n\t\t\t\t\t\t\t<text class=\"topic-name\"># {{ topic.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"topic-check\" v-if=\"isTopicSelected(topic)\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"20\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t<view class=\"empty-container\" v-else>\r\n\t\t\t\t\t<view class=\"empty-content\">\r\n\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"48\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t\t<text class=\"empty-text\">暂无话题</text>\r\n\t\t\t\t\t\t<button class=\"retry-btn\" @click=\"loadTopicList\">重新加载</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 确认按钮 -->\r\n\t\t\t<view class=\"topic-confirm\" v-if=\"selectedTopics.length > 0\">\r\n\t\t\t\t<button class=\"confirm-btn\" @click=\"closeTopicModal\">\r\n\t\t\t\t\t<text class=\"confirm-text\">确定 ({{ selectedTopics.length }})</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n\r\n\t<!-- 地址选择组件 -->\r\n\t<location-select ref=\"LocationSelectRef\" @select=\"handleLocationSelect\" />\r\n\r\n\t<!-- 可见性选择弹窗 -->\r\n\t<uni-popup ref=\"visibilityPopupRef\" type=\"bottom\" background-color=\"#fff\">\r\n\t\t<view class=\"visibility-popup\">\r\n\t\t\t<view class=\"popup-header\">\r\n\t\t\t\t<text class=\"popup-title\">谁可以看</text>\r\n\t\t\t\t<view class=\"popup-close\" @click=\"hideVisibilityPicker\">\r\n\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"visibility-options\">\r\n\t\t\t\t<view v-for=\"option in visibilityOptions\" :key=\"option.value\" class=\"visibility-option\"\r\n\t\t\t\t\t:class=\"{ 'selected': momentForm.visibility === option.value }\"\r\n\t\t\t\t\t@click=\"selectVisibility(option.value)\">\r\n\t\t\t\t\t<view class=\"option-info\">\r\n\t\t\t\t\t\t<view class=\"option-icon\">\r\n\t\t\t\t\t\t\t<uni-icons :type=\"option.icon\" size=\"24\"\r\n\t\t\t\t\t\t\t\t:color=\"momentForm.visibility === option.value ? '#696CF3' : '#999'\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"option-text\">\r\n\t\t\t\t\t\t\t<text class=\"option-title\">{{ option.label }}</text>\r\n\t\t\t\t\t\t\t<text class=\"option-desc\">{{ option.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-check\" v-if=\"momentForm.visibility === option.value\">\r\n\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"20\" color=\"#696CF3\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</uni-popup>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed } from 'vue'\r\nimport { onPageScroll } from '@dcloudio/uni-app'\r\nimport { getMomentTagList } from '@/api/manage/tag'\r\nimport { createMoment, VISIBILITY_STATUS, VISIBILITY_STATUS_NAMES } from '@/api/moment/moment'\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n// 导航栏高度\r\nconst navBarHeight = ref(0)\r\n\r\n// 动态表单数据\r\nconst momentForm = reactive({\r\n\tcontent: '',\r\n\tlocation: '',\r\n\tvisibility: VISIBILITY_STATUS.ALL\r\n})\r\n\r\n// 图片文件列表\r\nconst imageFiles = ref([])\r\n\r\n// 选中的话题（支持多选）\r\nconst selectedTopics = ref([])\r\n\r\n// 话题列表\r\nconst topicList = ref([])\r\nconst isLoadingTopics = ref(false)\r\n\r\n// 弹窗引用\r\nconst topicPopup = ref(null)\r\nconst LocationSelectRef = ref(null)\r\nconst visibilityPopupRef = ref(null)\r\n\r\n// 位置相关数据\r\nconst selectedLocationData = ref(null) // 存储完整的位置信息（包含经纬度）\r\n\r\n// 可见性选项\r\nconst visibilityOptions = ref([\r\n\t{\r\n\t\tvalue: VISIBILITY_STATUS.ALL,\r\n\t\tlabel: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.ALL],\r\n\t\tdescription: '所有人都可以看到这条动态',\r\n\t\ticon: 'eye'\r\n\t},\r\n\t{\r\n\t\tvalue: VISIBILITY_STATUS.FOLLOWERS,\r\n\t\tlabel: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.FOLLOWERS],\r\n\t\tdescription: '只有关注我的人可以看到',\r\n\t\ticon: 'heart'\r\n\t},\r\n\t{\r\n\t\tvalue: VISIBILITY_STATUS.MUTUAL,\r\n\t\tlabel: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.MUTUAL],\r\n\t\tdescription: '只有互相关注的人可以看到',\r\n\t\ticon: 'heart-filled'\r\n\t},\r\n\t{\r\n\t\tvalue: VISIBILITY_STATUS.PRIVATE,\r\n\t\tlabel: VISIBILITY_STATUS_NAMES[VISIBILITY_STATUS.PRIVATE],\r\n\t\tdescription: '只有自己可以看到',\r\n\t\ticon: 'locked'\r\n\t}\r\n])\r\n\r\n// 是否可以发布\r\nconst canPublish = computed(() => {\r\n\treturn momentForm.content.trim().length > 0 || imageFiles.value.length > 0\r\n})\r\n\r\n// 页面滚动监听\r\nonPageScroll((e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n})\r\n// 处理导航栏高度变化\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 计算导航栏文字颜色\r\nconst getNavTextColor = () => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? '#333' : '#fff'\r\n}\r\n\r\n// 加载话题列表\r\nconst loadTopicList = () => {\r\n\tisLoadingTopics.value = true\r\n\tgetMomentTagList().then(response => {\r\n\t\t// 转换API数据格式\r\n\t\ttopicList.value = response.data.map(item => ({\r\n\t\t\tid: item.id,\r\n\t\t\tname: item.name,\r\n\t\t\ticon: item.icon\r\n\t\t}))\r\n\t}).finally(() => {\r\n\t\tisLoadingTopics.value = false\r\n\t})\r\n}\r\n\r\n// 显示话题选择弹窗\r\nconst showTopicModal = async () => {\r\n\t// 如果话题列表为空，先加载话题列表\r\n\tif (topicList.value.length === 0) {\r\n\t\tloadTopicList()\r\n\t}\r\n\ttopicPopup.value.open()\r\n}\r\n\r\n// 关闭话题选择弹窗\r\nconst closeTopicModal = () => {\r\n\ttopicPopup.value.close()\r\n}\r\n\r\n// 切换话题选择状态\r\nconst toggleTopic = (topic) => {\r\n\tconst index = selectedTopics.value.findIndex(t => t.id === topic.id)\r\n\tif (index > -1) {\r\n\t\t// 如果已选中，则取消选择\r\n\t\tselectedTopics.value.splice(index, 1)\r\n\t} else {\r\n\t\t// 如果未选中，则添加到选中列表\r\n\t\tselectedTopics.value.push(topic)\r\n\t}\r\n}\r\n\r\n// 检查话题是否已选中\r\nconst isTopicSelected = (topic) => {\r\n\treturn selectedTopics.value.some(t => t.id === topic.id)\r\n}\r\n\r\n// 显示位置选择器\r\nconst showLocationSelect = () => {\r\n\tif (LocationSelectRef.value) {\r\n\t\tLocationSelectRef.value.open()\r\n\t} else {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '组件加载失败，请重试',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 处理位置选择\r\nconst handleLocationSelect = (locationData) => {\r\n\tmomentForm.location = locationData.name\r\n\tselectedLocationData.value = locationData\r\n}\r\n\r\n// 显示可见性选择器\r\nconst showVisibilityPicker = () => {\r\n\tif (visibilityPopupRef.value) {\r\n\t\tvisibilityPopupRef.value.open()\r\n\t}\r\n}\r\n\r\n// 隐藏可见性选择器\r\nconst hideVisibilityPicker = () => {\r\n\tif (visibilityPopupRef.value) {\r\n\t\tvisibilityPopupRef.value.close()\r\n\t}\r\n}\r\n\r\n// 选择可见性\r\nconst selectVisibility = (value) => {\r\n\tmomentForm.visibility = value\r\n\thideVisibilityPicker()\r\n}\r\n\r\n// 获取可见性标签\r\nconst getVisibilityLabel = (value) => {\r\n\tconst option = visibilityOptions.value.find(item => item.value === value)\r\n\treturn option ? option.label : '所有人'\r\n}\r\n\r\n\r\n// 发布动态\r\nconst publishMoment = async () => {\r\n\tif (!canPublish.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入内容或选择图片',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\ttry {\r\n\t\t// 准备提交数据\r\n\t\tconst submitData = {\r\n\t\t\tcontent: momentForm.content,\r\n\t\t\timages: imageFiles.value.map(file => file.ossId).join(','), // 图片ID，用逗号分隔\r\n\t\t\tvisibilityStatus: momentForm.visibility,\r\n\t\t\ttagIds: selectedTopics.value.map(topic => topic.id) // 话题ID数组\r\n\t\t}\r\n\r\n\t\t// 如果有位置信息，添加位置相关字段\r\n\t\tif (selectedLocationData.value) {\r\n\t\t\tconst location = selectedLocationData.value\r\n\t\t\tsubmitData.provinceName = location.province || ''\r\n\t\t\tsubmitData.cityName = location.city || ''\r\n\t\t\tsubmitData.districtName = location.district || ''\r\n\t\t\tsubmitData.location = location.city + \"·\" + momentForm.location\r\n\t\t\tsubmitData.lon = location.longitude || 0\r\n\t\t\tsubmitData.lat = location.latitude || 0\r\n\t\t}\r\n\r\n\t\tconsole.log('发布动态数据:', submitData)\r\n\r\n\t\t// 调用API发布动态\r\n\t\tconst response = await createMoment(submitData)\r\n\r\n\t\tif (response.code === 200) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '发布成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t})\r\n\r\n\t\t\t// 返回上一页\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t}, 1500)\r\n\t\t} else {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: response.msg || '发布失败，请重试',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('发布动态失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '发布失败，请检查网络连接',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/uni.scss';\r\n\r\n.add-moment-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n}\r\n\r\n.main-container {\r\n\tmargin-top: 20rpx;\r\n\tmin-height: calc(100vh - var(--nav-height, 88px));\r\n\tbox-sizing: border-box;\r\n\tpadding: 20rpx;\r\n\tpadding-bottom: 40rpx;\r\n}\r\n\r\n/* 发布按钮区域 */\r\n.publish-area {\r\n\tpadding: 24rpx;\r\n\tborder-top: 1rpx solid #f5f5f5;\r\n\tbackground: #fff;\r\n}\r\n\r\n/* 发布按钮样式 */\r\n.publish-btn {\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\r\n\tborder: none;\r\n\tborder-radius: 44rpx;\r\n\ttransition: all 0.3s ease;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbox-shadow: 0 4rpx 20rpx rgba($primary-color, 0.3);\r\n}\r\n\r\n.publish-btn:disabled {\r\n\tbackground: #ccc;\r\n\tbox-shadow: none;\r\n}\r\n\r\n.publish-btn:active {\r\n\ttransform: translateY(2rpx);\r\n\tbox-shadow: 0 2rpx 10rpx rgba($primary-color, 0.4);\r\n}\r\n\r\n.publish-btn::after {\r\n\tborder: none;\r\n}\r\n\r\n.publish-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #fff;\r\n\tfont-weight: 600;\r\n}\r\n\r\n/* 文本输入区域 */\r\n.text-input-section {\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.moment-textarea {\r\n\twidth: 100%;\r\n\tmin-height: 200rpx;\r\n\tfont-size: 32rpx;\r\n\tline-height: 1.6;\r\n\tcolor: #333;\r\n\tbackground: transparent;\r\n\tborder: none;\r\n\toutline: none;\r\n}\r\n\r\n.char-count {\r\n\tdisplay: flex;\r\n\tjustify-content: flex-end;\r\n\tmargin-top: 16rpx;\r\n}\r\n\r\n.count-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 图片选择区域 */\r\n.image-section {\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* uni-file-picker 样式定制 */\r\n:deep(.uni-file-picker) {\r\n\t.file-picker__progress {\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n}\r\n\r\n/* 功能选项区域 */\r\n.options-section {\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.option-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 24rpx;\r\n\tborder-bottom: 1rpx solid #f5f5f5;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.option-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.option-item:active {\r\n\tbackground: #f8f9fa;\r\n}\r\n\r\n.option-icon {\r\n\twidth: 48rpx;\r\n\theight: 48rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.option-content {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.option-label {\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\tmargin-bottom: 4rpx;\r\n}\r\n\r\n.option-value {\r\n\tfont-size: 26rpx;\r\n\tcolor: $primary-color;\r\n}\r\n\r\n.option-placeholder {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 选中话题标签 */\r\n.selected-topics {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 8rpx;\r\n\tmargin-top: 8rpx;\r\n}\r\n\r\n.topic-tag {\r\n\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(155, 157, 245, 0.15));\r\n\tcolor: #696CF3;\r\n\tfont-size: 24rpx;\r\n\tpadding: 4rpx 12rpx;\r\n\tborder-radius: 12rpx;\r\n\tborder: 1rpx solid rgba(105, 108, 243, 0.2);\r\n}\r\n\r\n/* 话题选择弹窗 */\r\n.topic-modal {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 24rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.modal-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.topic-list {\r\n\tmax-height: 60vh;\r\n\toverflow-y: auto;\r\n\tpadding: 16rpx 0;\r\n\r\n\t/* 自定义滚动条 */\r\n\t&::-webkit-scrollbar {\r\n\t\twidth: 6rpx;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar-track {\r\n\t\tbackground: #f5f5f5;\r\n\t\tborder-radius: 3rpx;\r\n\t}\r\n\r\n\t&::-webkit-scrollbar-thumb {\r\n\t\tbackground: #d0d0d0;\r\n\t\tborder-radius: 3rpx;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: #b0b0b0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.topic-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 24rpx;\r\n\tborder-bottom: 1rpx solid #f5f5f5;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.topic-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.topic-item:active {\r\n\tbackground: #f8f9fa;\r\n}\r\n\r\n.topic-item.selected {\r\n\tbackground: rgba(105, 108, 243, 0.05);\r\n\tborder-left: 4rpx solid #696CF3;\r\n}\r\n\r\n.topic-check {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\twidth: 44rpx;\r\n\theight: 44rpx;\r\n\tbackground: linear-gradient(135deg, #696CF3 0%, lighten(#696CF3, 10%) 100%);\r\n\tborder-radius: 50%;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.3);\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\tanimation: checkIn 0.3s ease;\r\n}\r\n\r\n@keyframes checkIn {\r\n\t0% {\r\n\t\ttransform: scale(0);\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t50% {\r\n\t\ttransform: scale(1.2);\r\n\t}\r\n\r\n\t100% {\r\n\t\ttransform: scale(1);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n.topic-info {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.topic-name {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 600;\r\n\tline-height: 1.4;\r\n\tposition: relative;\r\n\tz-index: 1;\r\n}\r\n\r\n\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 80rpx 40rpx;\r\n}\r\n\r\n.loading-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 80rpx 40rpx;\r\n}\r\n\r\n.empty-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 16rpx;\r\n}\r\n\r\n.retry-btn {\r\n\tbackground: #696CF3;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tpadding: 16rpx 32rpx;\r\n\tborder-radius: 20rpx;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n/* 话题确认按钮 */\r\n.topic-confirm {\r\n\tpadding: 20rpx 24rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tbackground: #fff;\r\n}\r\n\r\n.confirm-btn {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\r\n\tborder: none;\r\n\tborder-radius: 40rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.confirm-btn::after {\r\n\tborder: none;\r\n}\r\n\r\n.confirm-btn:active {\r\n\ttransform: scale(0.98);\r\n}\r\n\r\n.confirm-text {\r\n\tfont-size: 30rpx;\r\n\tcolor: #fff;\r\n\tfont-weight: 600;\r\n}\r\n\r\n/* 可见性选择弹窗样式 */\r\n.visibility-popup {\r\n\tbackground: #fff;\r\n\tborder-radius: 24rpx 24rpx 0 0;\r\n\tpadding: 0;\r\n\tmax-height: 80vh;\r\n}\r\n\r\n.popup-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 32rpx 32rpx 24rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.popup-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.popup-close {\r\n\tpadding: 8rpx;\r\n\tborder-radius: 50%;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.popup-close:active {\r\n\tbackground: #f0f0f0;\r\n}\r\n\r\n.visibility-options {\r\n\tpadding: 24rpx 0;\r\n}\r\n\r\n.visibility-option {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 24rpx 32rpx;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n}\r\n\r\n.visibility-option:active {\r\n\tbackground: #f8f8f8;\r\n}\r\n\r\n.visibility-option.selected {\r\n\tbackground: rgba(105, 108, 243, 0.05);\r\n}\r\n\r\n.option-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.option-icon {\r\n\tmargin-right: 24rpx;\r\n}\r\n\r\n.option-text {\r\n\tflex: 1;\r\n}\r\n\r\n.option-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.option-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n}\r\n\r\n.option-check {\r\n\tmargin-left: 16rpx;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/moment/add.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "momentForm", "reactive", "VISIBILITY_STATUS", "imageFiles", "selectedTopics", "topicList", "isLoadingTopics", "topicPopup", "LocationSelectRef", "visibilityPopupRef", "selectedLocationData", "visibilityOptions", "VISIBILITY_STATUS_NAMES", "canPublish", "computed", "onPageScroll", "e", "loadTopicList", "getMomentTagList", "response", "item", "showTopicModal", "closeTopicModal", "toggleTopic", "topic", "index", "t", "isTopicSelected", "showLocationSelect", "uni", "handleLocationSelect", "locationData", "showVisibilityPicker", "hideVisibilityPicker", "selectVisibility", "value", "getVisibilityLabel", "option", "publishMoment", "submitData", "file", "location", "createMoment", "error", "MiniProgramPage"], "mappings": "ktBAoKA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAC,EAAAA,SAAA,CACA,QAAA,GACA,SAAA,GACA,WAAAC,EAAA,kBAAA,GACA,CAAA,EAGAC,EAAAL,EAAA,IAAA,EAAA,EAGAM,EAAAN,EAAA,IAAA,EAAA,EAGAO,EAAAP,EAAA,IAAA,EAAA,EACAQ,EAAAR,EAAA,IAAA,EAAA,EAGAS,EAAAT,EAAA,IAAA,IAAA,EACAU,EAAAV,EAAA,IAAA,IAAA,EACAW,EAAAX,EAAA,IAAA,IAAA,EAGAY,EAAAZ,EAAA,IAAA,IAAA,EAGAa,EAAAb,EAAAA,IAAA,CACA,CACA,MAAAI,EAAA,kBAAA,IACA,MAAAU,EAAAA,wBAAAV,EAAA,kBAAA,GAAA,EACA,YAAA,eACA,KAAA,KACA,EACA,CACA,MAAAA,EAAA,kBAAA,UACA,MAAAU,EAAAA,wBAAAV,EAAA,kBAAA,SAAA,EACA,YAAA,cACA,KAAA,OACA,EACA,CACA,MAAAA,EAAA,kBAAA,OACA,MAAAU,EAAAA,wBAAAV,EAAA,kBAAA,MAAA,EACA,YAAA,eACA,KAAA,cACA,EACA,CACA,MAAAA,EAAA,kBAAA,QACA,MAAAU,EAAAA,wBAAAV,EAAA,kBAAA,OAAA,EACA,YAAA,WACA,KAAA,QACA,CACA,CAAA,EAGAW,EAAAC,EAAA,SAAA,IACAd,EAAA,QAAA,OAAA,OAAA,GAAAG,EAAA,MAAA,OAAA,CACA,EAGAY,EAAA,aAAAC,GAAA,CACAnB,EAAA,MAAAmB,EAAA,SACA,CAAA,EAaA,MAAAC,EAAA,IAAA,CACAX,EAAA,MAAA,GACAY,mBAAA,EAAA,KAAAC,GAAA,CAEAd,EAAA,MAAAc,EAAA,KAAA,IAAAC,IAAA,CACA,GAAAA,EAAA,GACA,KAAAA,EAAA,KACA,KAAAA,EAAA,IACA,EAAA,CACA,CAAA,EAAA,QAAA,IAAA,CACAd,EAAA,MAAA,EACA,CAAA,CACA,EAGAe,EAAA,SAAA,CAEAhB,EAAA,MAAA,SAAA,GACAY,EAAA,EAEAV,EAAA,MAAA,KAAA,CACA,EAGAe,EAAA,IAAA,CACAf,EAAA,MAAA,MAAA,CACA,EAGAgB,EAAAC,GAAA,CACA,MAAAC,EAAArB,EAAA,MAAA,UAAAsB,GAAAA,EAAA,KAAAF,EAAA,EAAA,EACAC,EAAA,GAEArB,EAAA,MAAA,OAAAqB,EAAA,CAAA,EAGArB,EAAA,MAAA,KAAAoB,CAAA,CAEA,EAGAG,EAAAH,GACApB,EAAA,MAAA,KAAAsB,GAAAA,EAAA,KAAAF,EAAA,EAAA,EAIAI,EAAA,IAAA,CACApB,EAAA,MACAA,EAAA,MAAA,KAAA,EAEAqB,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,CAEA,EAGAC,EAAAC,GAAA,CACA/B,EAAA,SAAA+B,EAAA,KACArB,EAAA,MAAAqB,CACA,EAGAC,EAAA,IAAA,CACAvB,EAAA,OACAA,EAAA,MAAA,KAAA,CAEA,EAGAwB,EAAA,IAAA,CACAxB,EAAA,OACAA,EAAA,MAAA,MAAA,CAEA,EAGAyB,EAAAC,GAAA,CACAnC,EAAA,WAAAmC,EACAF,EAAA,CACA,EAGAG,EAAAD,GAAA,CACA,MAAAE,EAAA1B,EAAA,MAAA,KAAAS,GAAAA,EAAA,QAAAe,CAAA,EACA,OAAAE,EAAAA,EAAA,MAAA,KACA,EAIAC,EAAA,SAAA,CACA,GAAA,CAAAzB,EAAA,MAAA,CACAgB,EAAAA,MAAA,UAAA,CACA,MAAA,aACA,KAAA,MACA,CAAA,EACA,MACA,CACA,GAAA,CAEA,MAAAU,EAAA,CACA,QAAAvC,EAAA,QACA,OAAAG,EAAA,MAAA,IAAAqC,GAAAA,EAAA,KAAA,EAAA,KAAA,GAAA,EACA,iBAAAxC,EAAA,WACA,OAAAI,EAAA,MAAA,IAAAoB,GAAAA,EAAA,EAAA,CACA,EAGA,GAAAd,EAAA,MAAA,CACA,MAAA+B,EAAA/B,EAAA,MACA6B,EAAA,aAAAE,EAAA,UAAA,GACAF,EAAA,SAAAE,EAAA,MAAA,GACAF,EAAA,aAAAE,EAAA,UAAA,GACAF,EAAA,SAAAE,EAAA,KAAA,IAAAzC,EAAA,SACAuC,EAAA,IAAAE,EAAA,WAAA,EACAF,EAAA,IAAAE,EAAA,UAAA,CACA,CAEAZ,EAAAA,MAAA,MAAA,MAAA,iCAAA,UAAAU,CAAA,EAGA,MAAApB,EAAA,MAAAuB,EAAA,aAAAH,CAAA,EAEApB,EAAA,OAAA,KACAU,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,EAGA,WAAA,IAAA,CACAA,EAAAA,MAAA,aAAA,CACA,EAAA,IAAA,GAEAA,EAAAA,MAAA,UAAA,CACA,MAAAV,EAAA,KAAA,WACA,KAAA,MACA,CAAA,CAEA,OAAAwB,EAAA,CACAd,EAAAA,MAAA,MAAA,QAAA,iCAAA,UAAAc,CAAA,EACAd,EAAAA,MAAA,UAAA,CACA,MAAA,eACA,KAAA,MACA,CAAA,CACA,CACA,27DCpYA,GAAG,WAAWe,CAAe"}