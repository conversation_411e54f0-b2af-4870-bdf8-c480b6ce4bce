package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardItemToActSafeguardItemVoMapperImpl implements ActSafeguardItemToActSafeguardItemVoMapper {

    @Override
    public ActSafeguardItemVo convert(ActSafeguardItem arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguardItemVo actSafeguardItemVo = new ActSafeguardItemVo();

        actSafeguardItemVo.setId( arg0.getId() );
        actSafeguardItemVo.setSafeguardId( arg0.getSafeguardId() );
        actSafeguardItemVo.setIcon( arg0.getIcon() );
        actSafeguardItemVo.setName( arg0.getName() );
        actSafeguardItemVo.setDes( arg0.getDes() );

        return actSafeguardItemVo;
    }

    @Override
    public ActSafeguardItemVo convert(ActSafeguardItem arg0, ActSafeguardItemVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSafeguardId( arg0.getSafeguardId() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setName( arg0.getName() );
        arg1.setDes( arg0.getDes() );

        return arg1;
    }
}
