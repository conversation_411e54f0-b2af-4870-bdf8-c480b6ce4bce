package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserCommentVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserCommentToUserCommentVoMapperImpl implements UserCommentToUserCommentVoMapper {

    @Override
    public UserCommentVo convert(UserComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserCommentVo userCommentVo = new UserCommentVo();

        userCommentVo.setId( arg0.getId() );
        userCommentVo.setParentId( arg0.getParentId() );
        userCommentVo.setContent( arg0.getContent() );
        userCommentVo.setType( arg0.getType() );
        userCommentVo.setBusinessId( arg0.getBusinessId() );
        userCommentVo.setImages( arg0.getImages() );

        return userCommentVo;
    }

    @Override
    public UserCommentVo convert(UserComment arg0, UserCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setContent( arg0.getContent() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setImages( arg0.getImages() );

        return arg1;
    }
}
