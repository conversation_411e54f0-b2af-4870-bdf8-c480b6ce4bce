package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageVipVo;
import com.gzhuxn.personals.controller.app.manage.vo.AppManageVipVoToManageVipMapper;
import com.gzhuxn.personals.domain.manage.bo.ManageVipBoToManageVipMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppManageVipVoToManageVipMapper.class,ManageVipBoToManageVipMapper.class,ManageVipToManageVipVoMapper.class},
    imports = {}
)
public interface ManageVipToAppManageVipVoMapper extends BaseMapper<ManageVip, AppManageVipVo> {
}
