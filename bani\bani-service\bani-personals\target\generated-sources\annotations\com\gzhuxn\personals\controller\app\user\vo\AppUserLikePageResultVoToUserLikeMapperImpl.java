package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserLike;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserLikePageResultVoToUserLikeMapperImpl implements AppUserLikePageResultVoToUserLikeMapper {

    @Override
    public UserLike convert(AppUserLikePageResultVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLike userLike = new UserLike();

        userLike.setCreateTime( arg0.getCreateTime() );
        userLike.setId( arg0.getId() );
        userLike.setUserId( arg0.getUserId() );
        userLike.setType( arg0.getType() );
        userLike.setBusinessId( arg0.getBusinessId() );

        return userLike;
    }

    @Override
    public UserLike convert(AppUserLikePageResultVo arg0, UserLike arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
