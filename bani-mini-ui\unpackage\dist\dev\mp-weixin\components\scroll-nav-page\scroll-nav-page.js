"use strict";const t=require("../../common/vendor.js"),m=require("../../config.js");Array||t.resolveComponent("uni-icons")();const H=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";Math||H();var y,x;const z={__name:"scroll-nav-page",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1},leftIcon:{type:String,default:""},leftText:{type:String,default:""},rightIcon:{type:String,default:""},rightText:{type:String,default:""},border:{type:Boolean,default:!1},fixed:{type:Boolean,default:!0},statusBar:{type:Boolean,default:!0},shadow:{type:Boolean,default:!1},height:{type:[String,Number],default:44},initialBgColor:{type:String,default:((x=(y=m.globalConfig)==null?void 0:y.theme)==null?void 0:x.primaryColor)||"#696CF3"},scrolledBgColor:{type:String,default:"#ffffff"},initialTextColor:{type:String,default:"#ffffff"},scrolledTextColor:{type:String,default:"#333333"},scrollThreshold:{type:Number,default:100},fadeTransitionPoint:{type:Number,default:.6,validator:e=>e>=0&&e<=1},enableScrollGradient:{type:Boolean,default:!0}},emits:["clickLeft","clickRight","back","scroll","heightChange"],setup(e,{emit:B}){const o=e,u=B,T=t.ref(0),g=t.ref(0),r=t.ref(o.initialBgColor),a=t.ref(o.initialTextColor),f=t.ref(0),b=t.computed(()=>o.showBack?getCurrentPages().length<=1?"home":"left":void 0),d=t.computed(()=>typeof o.height=="number"?o.height:parseInt(o.height)),I=t.computed(()=>f.value+d.value),h=l=>{if(!o.enableScrollGradient)return;const n=Math.min(l/o.scrollThreshold,1);if(n===0)r.value=o.initialBgColor,a.value=o.initialTextColor;else if(n===1)r.value=o.scrolledBgColor,a.value=o.scrolledTextColor;else{const i=o.fadeTransitionPoint;if(n<=i){const c=1-n/i,s=v(o.initialBgColor);s&&(r.value=`rgba(${s.r}, ${s.g}, ${s.b}, ${c})`,a.value=o.initialTextColor)}else{const C=(n-i)/(1-i),c=v(o.scrolledBgColor);if(c){r.value=`rgba(${c.r}, ${c.g}, ${c.b}, ${C})`;const s=i+(1-i)*.3;a.value=n>s?o.scrolledTextColor:o.initialTextColor}}}},v=l=>{l=l.replace("#",""),l.length===3&&(l=l.split("").map(i=>i+i).join(""));const n=/^([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(l);return n?{r:parseInt(n[1],16),g:parseInt(n[2],16),b:parseInt(n[3],16)}:null},S=l=>{const n=l.scrollTop||0;T.value=n,h(n),u("scroll",{scrollTop:n})};t.onPageScroll(S);const k=()=>{try{const l=t.index.getWindowInfo();f.value=l.statusBarHeight||0}catch{f.value=0}},P=()=>{u("clickLeft")},w=()=>{u("clickRight")},$=()=>{u("back"),getCurrentPages().length<=1?t.index.reLaunch({url:m.globalConfig.homePagePath}):t.index.navigateBack()};return t.watch(I,l=>{g.value=l,u("heightChange",l)},{immediate:!0}),t.onMounted(()=>{k(),h(0)}),(l,n)=>t.e({a:e.statusBar},e.statusBar?{b:f.value+"px",c:r.value}:{},{d:e.showBack},e.showBack?{e:t.p({type:b.value,size:"20",color:a.value}),f:t.o($)}:{},{g:e.leftIcon||e.leftText},e.leftIcon||e.leftText?t.e({h:e.leftIcon},e.leftIcon?{i:t.p({type:e.leftIcon,size:"20",color:a.value})}:{},{j:e.leftText},e.leftText?{k:t.t(e.leftText),l:a.value}:{}):{},{m:t.o(P),n:e.title},e.title?{o:t.t(e.title),p:a.value}:{},{q:e.rightIcon||e.rightText},e.rightIcon||e.rightText?t.e({r:e.rightText},e.rightText?{s:t.t(e.rightText),t:a.value}:{},{v:e.rightIcon},e.rightIcon?{w:t.p({type:e.rightIcon,size:"20",color:a.value})}:{}):{},{x:t.o(w),y:d.value+"px",z:r.value,A:e.border},e.border?{}:{},{B:e.shadow},e.shadow?{}:{},{C:r.value,D:a.value,E:g.value+"px"})}},L=t._export_sfc(z,[["__scopeId","data-v-8943cde8"]]);wx.createComponent(L);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/scroll-nav-page/scroll-nav-page.js.map
