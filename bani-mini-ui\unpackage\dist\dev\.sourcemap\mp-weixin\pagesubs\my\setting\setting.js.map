{"version": 3, "file": "setting.js", "sources": ["pagesubs/my/setting/setting.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcc2V0dGluZ1xzZXR0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"设置\" :show-back=\"true\" @heightChange=\"handleNavHeightChange\">\n\t\t<template #content>\n\t\t\t<!-- 设置列表 -->\n\t\t\t<view class=\"setting-list\">\n\t\t\t<!-- 推荐设置 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">推荐设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToRecommendSetting\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"heart\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">择偶条件</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">设置推荐条件</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 账号设置 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">账号设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToProfile\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"person\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">个人资料</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">编辑个人信息</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToPrivacy\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"locked\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">隐私设置</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">隐私保护</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"showDeleteAccountModal\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">账户注销</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">永久删除账户</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 通用设置 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">通用设置</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToNotification\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"notification\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">消息通知</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">通知设置</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToFeedback\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"chatbubble\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">意见反馈</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">问题反馈与建议</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToAbout\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"info\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">关于我们</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">应用信息</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 法律条款 -->\n\t\t\t<view class=\"setting-section\">\n\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t<text class=\"section-title\">法律条款</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToUserAgreement\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">用户协议</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">服务条款</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"setting-item\" @click=\"goToPrivacyPolicy\">\n\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t<uni-icons type=\"locked-filled\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"item-title\">隐私政策</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t<text class=\"item-desc\">隐私保护条款</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport globalConfig from '@/config'\nimport { getCurrentLogoffRecord, applyLogoff, cancelLogoff } from '@/api/my/logoff'\n\n// 导航栏高度\nconst navBarHeight = ref(0)\n\n// 导航栏高度变化\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 跳转到推荐设置页面\nconst goToRecommendSetting = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/setting/recommend'\n\t})\n}\n\n// 跳转到个人资料\nconst goToProfile = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/profile/profileEdit'\n\t})\n}\n\n// 跳转到隐私设置\nconst goToPrivacy = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/setting/privacy'\n\t})\n}\n\n// 跳转到消息通知设置\nconst goToNotification = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/setting/msRemind'\n\t})\n}\n\n// 跳转到意见反馈\nconst goToFeedback = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/feedback/feedback'\n\t})\n}\n\n// 跳转到关于我们\nconst goToAbout = () => {\n\tuni.navigateTo({\n\t\turl: globalConfig.help.aboutUs\n\t})\n}\n\n// 跳转到用户协议\nconst goToUserAgreement = () => {\n\tuni.navigateTo({\n\t\turl: globalConfig.agreements.userAgreement\n\t})\n}\n\n// 跳转到隐私政策\nconst goToPrivacyPolicy = () => {\n\tuni.navigateTo({\n\t\turl: globalConfig.agreements.privacyAgreement\n\t})\n}\n\n// 显示注销账户确认弹框\nconst showDeleteAccountModal = () => {\n\tgetCurrentLogoffRecord().then(res => {\n\t\tif (res.data) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '您已提交注销申请，系统将在' + res.data.logoffTime + '后删除，请耐心等待!',\n\t\t\t\tshowCancel: true,\n\t\t\t\tcancelText: '取消注销',\n\t\t\t\tcancelColor: '#F56C6C',\n\t\t\t\tsuccess: function (r) {\n\t\t\t\t\tif (r.cancel) {\n\t\t\t\t\t\tcancelLogoff(res.data.id).then(res => {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '取消注销成功!',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\treturn\n\t\t}\n\t\tuni.showModal({\n\t\t\ttitle: '注销账户',\n\t\t\tcontent: '账户将在7天后完成注销，注销后永久删除您的所有数据，包括个人信息、聊天记录、动态等，删除后无法恢复，且账户将无法使用，请确认是否继续？',\n\t\t\tconfirmText: '确认注销',\n\t\t\tcancelText: '取消',\n\t\t\tconfirmColor: '#F56C6C',\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.confirm) {\n\t\t\t\t\t// 用户确认注销\n\t\t\t\t\thandleDeleteAccount()\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t})\n}\n\n// 处理账户注销\nconst handleDeleteAccount = () => {\n\t// 这里可以调用注销账户的API\n\tuni.showLoading({\n\t\ttitle: '处理中...'\n\t})\n\tapplyLogoff().then(() => {\n\t\t// 显示注销成功提示\n\t\tuni.showToast({\n\t\t\ttitle: '注销申请已提交!',\n\t\t\ticon: 'success',\n\t\t\tduration: 3000\n\t\t})\n\t})\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg,\n\t\t\trgba(105, 108, 243, 0.08) 0%,\n\t\t\trgba(105, 108, 243, 0.05) 30%,\n\t\t\trgba(105, 108, 243, 0.02) 60%,\n\t\t\trgba(255, 255, 255, 1) 100%);\n}\n\n.setting-list {\n\tmargin-top: 20rpx;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n}\n\n.setting-section {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 20rpx;\n\tmargin-bottom: 24rpx;\n\tbox-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1px solid rgba(255, 255, 255, 0.2);\n\toverflow: hidden;\n\n\t.section-header {\n\t\tpadding: 24rpx 30rpx 16rpx;\n\t\tborder-bottom: 1px solid rgba(105, 108, 243, 0.08);\n\n\t\t.section-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t\tletter-spacing: 0.5rpx;\n\t\t}\n\t}\n\n\t.setting-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 24rpx 30rpx;\n\t\tborder-bottom: 1px solid rgba(105, 108, 243, 0.05);\n\t\ttransition: all 0.3s ease;\n\t\tcursor: pointer;\n\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground: rgba(105, 108, 243, 0.04);\n\t\t\ttransform: translateX(4rpx);\n\t\t}\n\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t}\n\n\t\t.item-left {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tflex: 1;\n\n\t\t\t.item-title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tletter-spacing: 0.3rpx;\n\t\t\t}\n\t\t}\n\n\t\t.item-right {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.item-desc {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tmargin-right: 12rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 响应式设计\n@media screen and (max-width: 750rpx) {\n\t.setting-section {\n\t\tmargin-bottom: 20rpx;\n\n\t\t.section-header {\n\t\t\tpadding: 20rpx 24rpx 12rpx;\n\n\t\t\t.section-title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t}\n\t\t}\n\n\t\t.setting-item {\n\t\t\tpadding: 20rpx 24rpx;\n\n\t\t\t.item-left .item-title {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tmargin-left: 16rpx;\n\t\t\t}\n\n\t\t\t.item-right .item-desc {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/setting/setting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "navBarHeight", "ref", "handleNavHeightChange", "height", "goToRecommendSetting", "uni", "goToProfile", "goToPrivacy", "goToNotification", "goToFeedback", "goToAbout", "globalConfig", "goToUserAgreement", "goToPrivacyPolicy", "showDeleteAccountModal", "getCurrentLogoffRecord", "res", "r", "<PERSON><PERSON><PERSON><PERSON>", "handleDeleteAccount", "<PERSON><PERSON><PERSON><PERSON>", "MiniProgramPage"], "mappings": "uQAiIA,MAAMA,EAAgB,IAAW,sFAKjC,MAAMC,EAAeC,EAAG,IAAC,CAAC,EAGpBC,EAAyBC,GAAW,CACzCH,EAAa,MAAQG,CACtB,EAGMC,EAAuB,IAAM,CAClCC,EAAAA,MAAI,WAAW,CACd,IAAK,gCACP,CAAE,CACF,EAGMC,EAAc,IAAM,CACzBD,EAAAA,MAAI,WAAW,CACd,IAAK,kCACP,CAAE,CACF,EAGME,EAAc,IAAM,CACzBF,EAAAA,MAAI,WAAW,CACd,IAAK,8BACP,CAAE,CACF,EAGMG,EAAmB,IAAM,CAC9BH,EAAAA,MAAI,WAAW,CACd,IAAK,+BACP,CAAE,CACF,EAGMI,EAAe,IAAM,CAC1BJ,EAAAA,MAAI,WAAW,CACd,IAAK,gCACP,CAAE,CACF,EAGMK,EAAY,IAAM,CACvBL,EAAAA,MAAI,WAAW,CACd,IAAKM,EAAAA,aAAa,KAAK,OACzB,CAAE,CACF,EAGMC,EAAoB,IAAM,CAC/BP,EAAAA,MAAI,WAAW,CACd,IAAKM,EAAAA,aAAa,WAAW,aAC/B,CAAE,CACF,EAGME,EAAoB,IAAM,CAC/BR,EAAAA,MAAI,WAAW,CACd,IAAKM,EAAAA,aAAa,WAAW,gBAC/B,CAAE,CACF,EAGMG,EAAyB,IAAM,CACpCC,yBAAwB,EAAC,KAAKC,GAAO,CACpC,GAAIA,EAAI,KAAM,CACbX,EAAAA,MAAI,UAAU,CACb,MAAO,KACP,QAAS,gBAAkBW,EAAI,KAAK,WAAa,aACjD,WAAY,GACZ,WAAY,OACZ,YAAa,UACb,QAAS,SAAUC,EAAG,CACjBA,EAAE,QACLC,EAAY,aAACF,EAAI,KAAK,EAAE,EAAE,KAAKA,GAAO,CACrCX,EAAAA,MAAI,UAAU,CACb,MAAO,UACP,KAAM,SACd,CAAQ,CACR,CAAO,CAEF,CACL,CAAI,EACD,MACA,CACDA,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,QAAS,sEACT,YAAa,OACb,WAAY,KACZ,aAAc,UACd,QAAUW,GAAQ,CACbA,EAAI,SAEPG,EAAqB,CAEtB,CACJ,CAAG,CACH,CAAE,CACF,EAGMA,EAAsB,IAAM,CAEjCd,EAAAA,MAAI,YAAY,CACf,MAAO,QACT,CAAE,EACDe,EAAW,YAAA,EAAG,KAAK,IAAM,CAExBf,EAAAA,MAAI,UAAU,CACb,MAAO,WACP,KAAM,UACN,SAAU,GACb,CAAG,CACH,CAAE,CACF,siCCzPA,GAAG,WAAWgB,CAAe"}