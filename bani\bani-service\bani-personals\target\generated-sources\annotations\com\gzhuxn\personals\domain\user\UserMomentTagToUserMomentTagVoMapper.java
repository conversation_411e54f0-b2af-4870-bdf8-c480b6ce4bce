package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserMomentTagBoToUserMomentTagMapper;
import com.gzhuxn.personals.domain.user.vo.UserMomentTagVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserMomentTagBoToUserMomentTagMapper.class},
    imports = {}
)
public interface UserMomentTagToUserMomentTagVoMapper extends BaseMapper<UserMomentTag, UserMomentTagVo> {
}
