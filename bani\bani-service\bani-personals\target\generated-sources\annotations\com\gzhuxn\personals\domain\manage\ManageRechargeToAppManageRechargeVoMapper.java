package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageRechargeVo;
import com.gzhuxn.personals.controller.app.manage.vo.AppManageRechargeVoToManageRechargeMapper;
import com.gzhuxn.personals.domain.manage.bo.ManageRechargeBoToManageRechargeMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppManageRechargeVoToManageRechargeMapper.class,ManageRechargeBoToManageRechargeMapper.class,ManageRechargeToManageRechargeVoMapper.class},
    imports = {}
)
public interface ManageRechargeToAppManageRechargeVoMapper extends BaseMapper<ManageRecharge, AppManageRechargeVo> {
}
