package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.admin.audit.vo.AdminContentAuditUserCommentVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserCommentToAdminContentAuditUserCommentVoMapperImpl implements UserCommentToAdminContentAuditUserCommentVoMapper {

    @Override
    public AdminContentAuditUserCommentVo convert(UserComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminContentAuditUserCommentVo adminContentAuditUserCommentVo = new AdminContentAuditUserCommentVo();

        adminContentAuditUserCommentVo.setId( arg0.getId() );
        adminContentAuditUserCommentVo.setRootId( arg0.getRootId() );
        adminContentAuditUserCommentVo.setContent( arg0.getContent() );
        adminContentAuditUserCommentVo.setImages( arg0.getImages() );
        adminContentAuditUserCommentVo.setCreateTime( arg0.getCreateTime() );

        return adminContentAuditUserCommentVo;
    }

    @Override
    public AdminContentAuditUserCommentVo convert(UserComment arg0, AdminContentAuditUserCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRootId( arg0.getRootId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
