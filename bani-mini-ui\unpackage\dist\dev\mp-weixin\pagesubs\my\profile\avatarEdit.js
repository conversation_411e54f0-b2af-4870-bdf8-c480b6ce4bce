"use strict";const e=require("../../../common/vendor.js"),m=require("../../../api/my/my.js"),v=require("../../../utils/common.js"),g=require("../../../store/index.js");if(!Array){const r=e.resolveComponent("uni-steps"),o=e.resolveComponent("images-select"),n=e.resolveComponent("scroll-nav-page");(r+o+n)()}const d=()=>"../../../uni_modules/uni-steps/components/uni-steps/uni-steps.js",f=()=>"../../../components/images-select/images-select.js",y=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(d+f+y)();const l={__name:"avatarEdit",setup(r){const o=e.ref(0),n=e.ref(0),t=e.ref([]),a=e.ref(0),c=e.computed(()=>!(t.value.length>0));e.onPageScroll(s=>{o.value=s.scrollTop}),e.onLoad(s=>{s.type&&(a.value=parseInt(s.type))});const i=s=>{e.index.__f__("log","at pagesubs/my/profile/avatarEdit.vue:70","头像图片变化:",s)},u=()=>{if(t.value.length===0){e.index.showToast({title:"请先上传头像",icon:"none"});return}var s={avatar:t.value[0].ossId};m.updateUserAvatar(s).then(_=>{if(g.$store.user.refreshUserInfo(),v.toast("保存成功！"),a.value===1){e.index.navigateBack();return}e.index.reLaunch({url:"/pages/my/my"})})};return(s,_)=>e.e({a:a.value===0},a.value===0?{b:e.p({options:[{title:"基础信息"},{title:"上传头像"}],active:1})}:{},{c:e.o(i),d:e.o(p=>t.value=p),e:e.p({limit:1,type:"user_detail.avatar",small:!0,modelValue:t.value}),f:c.value,g:e.o(u),h:n.value+"px",i:e.p({title:"上传头像","show-back":!0})})}};l.__runtimeHooks=1;wx.createPage(l);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/profile/avatarEdit.js.map
