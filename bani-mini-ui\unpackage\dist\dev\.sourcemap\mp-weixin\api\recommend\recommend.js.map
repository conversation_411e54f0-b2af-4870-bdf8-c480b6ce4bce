{"version": 3, "file": "recommend.js", "sources": ["api/recommend/recommend.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 查询同城用户列表\n * @param {Object} params 查询参数\n * @param {number} params.userId 用户ID\n * @param {number} params.pid 用户号\n * @param {boolean} params.isMatched 是否是相亲广场列表查询\n * @param {number} params.ageMin 最小年龄\n * @param {number} params.ageMax 最大年龄\n * @param {number} params.heightMin 最小身高\n * @param {number} params.heightMax 最大身高\n * @param {number} params.education 要求学历 不限 0 高中及以上 1 大专及以上 2 本科及以上 3 硕士及以上 4 博士及以上 5\n * @param {number} params.location 推荐城市 0不限、1同城优先、2同省优先\n * @param {number} params.pageSize 分页大小\n * @param {number} params.pageNum 当前页数\n * @returns {Promise} 返回同城用户列表\n */\nexport function getSameCityUsers(params) {\n  return request({\n    url: '/personals/recommend/user/same-city',\n    method: 'get',\n    params\n  }).catch(error => {\n    console.error('同城用户列表API调用失败:', error)\n    // 返回一个符合预期格式的错误响应\n    return {\n      code: 0,\n      msg: '网络错误，请稍后重试',\n      rows: [],\n      total: 0\n    }\n  })\n}\n\n/**\n * 查询附近用户列表\n * @param {Object} params 查询参数\n * @param {number} params.userId 用户ID\n * @param {number} params.pid 用户号\n * @param {boolean} params.isMatched 是否是相亲广场列表查询\n * @param {number} params.lon 最新位置经度\n * @param {number} params.lat 最新位置纬度\n * @param {number} params.distance 查询距离范围（公里），默认100公里\n * @param {number} params.ageMin 最小年龄\n * @param {number} params.ageMax 最大年龄\n * @param {number} params.heightMin 最小身高\n * @param {number} params.heightMax 最大身高\n * @param {number} params.education 要求学历 不限 0 高中及以上 1 大专及以上 2 本科及以上 3 硕士及以上 4 博士及以上 5\n * @param {number} params.location 推荐城市 0不限、1同城优先、2同省优先\n * @param {number} params.pageSize 分页大小\n * @param {number} params.pageNum 当前页数\n * @returns {Promise} 返回附近用户列表\n */\nexport function getNearbyUsers(params) {\n  return request({\n    url: '/personals/recommend/user/nearby',\n    method: 'get',\n    params\n  }).catch(error => {\n    console.error('附近用户列表API调用失败:', error)\n    // 返回一个符合预期格式的错误响应\n    return {\n      code: 0,\n      msg: '网络错误，请稍后重试',\n      rows: [],\n      total: 0\n    }\n  })\n}\n\n/**\n * 数据转换工具函数 - 将API返回的数据转换为页面需要的格式\n * @param {Object} apiData API返回的用户数据\n * @returns {Object} 转换后的用户数据\n */\nexport function transformUserData(apiData) {\n  return {\n    id: apiData.oppUserId,\n    avatar: apiData.oppAvatar,\n    nickname: apiData.oppNickName || '用户',\n    gender: apiData.oppSex === '0' ? 'male' : 'female',\n    isVerified: apiData.oppIsIdentity || false,\n    currentCity: apiData.oppCity || '未知',\n    hometown: apiData.addrProvince || '未知',\n    age: parseInt(apiData.oppAge) || 18,\n    height: parseInt(apiData.oppHeight) || 170,\n    education: apiData.edu || '本科',\n    occupation: apiData.job || '职员',\n    distance: apiData.distance > 1 ? (apiData.distance).toFixed(2) + `km` : '<1km',\n    isFollowed: apiData.oppIsFollowed || false,\n    isMe: apiData.oppIsMe || false,\n    // 为瀑布流布局生成随机高度\n    imageHeight: 250 + Math.floor(Math.random() * 100)\n  }\n}\n\n/**\n * 构建同城用户查询参数\n * @param {number} pageNum 页码\n * @param {number} pageSize 每页大小\n * @param {Object} filters 筛选条件\n * @returns {Object} 查询参数\n */\nexport function buildSameCityParams(pageNum = 1, pageSize = 10, filters = {}) {\n  return {\n    pageNum,\n    pageSize,\n    isMatched: null,\n    ...filters\n  }\n}\n\n/**\n * 构建附近用户查询参数\n * @param {number} pageNum 页码\n * @param {number} pageSize 每页大小\n * @param {number} longitude 经度\n * @param {number} latitude 纬度\n * @param {Object} filters 筛选条件\n * @returns {Object} 查询参数\n */\nexport function buildNearbyParams(pageNum = 1, pageSize = 10, longitude, latitude, filters = {}) {\n  return {\n    pageNum,\n    pageSize,\n    isMatched: null,\n    lon: longitude,\n    lat: latitude,\n    distance: 100, // 默认100公里\n    ...filters\n  }\n}\n"], "names": ["getSameCityUsers", "params", "request", "error", "uni", "getNearbyUsers", "transformUserData", "apiData", "buildSameCityParams", "pageNum", "pageSize", "filters", "buildNearbyParams", "longitude", "latitude"], "mappings": "2FAkBO,SAASA,EAAiBC,EAAQ,CACvC,OAAOC,UAAQ,CACb,IAAK,sCACL,OAAQ,MACR,OAAAD,CACJ,CAAG,EAAE,MAAME,IACPC,EAAAA,MAAc,MAAA,QAAA,mCAAA,iBAAkBD,CAAK,EAE9B,CACL,KAAM,EACN,IAAK,aACL,KAAM,CAAE,EACR,MAAO,CACR,EACF,CACH,CAqBO,SAASE,EAAeJ,EAAQ,CACrC,OAAOC,UAAQ,CACb,IAAK,mCACL,OAAQ,MACR,OAAAD,CACJ,CAAG,EAAE,MAAME,IACPC,EAAAA,MAAc,MAAA,QAAA,mCAAA,iBAAkBD,CAAK,EAE9B,CACL,KAAM,EACN,IAAK,aACL,KAAM,CAAE,EACR,MAAO,CACR,EACF,CACH,CAOO,SAASG,EAAkBC,EAAS,CACzC,MAAO,CACL,GAAIA,EAAQ,UACZ,OAAQA,EAAQ,UAChB,SAAUA,EAAQ,aAAe,KACjC,OAAQA,EAAQ,SAAW,IAAM,OAAS,SAC1C,WAAYA,EAAQ,eAAiB,GACrC,YAAaA,EAAQ,SAAW,KAChC,SAAUA,EAAQ,cAAgB,KAClC,IAAK,SAASA,EAAQ,MAAM,GAAK,GACjC,OAAQ,SAASA,EAAQ,SAAS,GAAK,IACvC,UAAWA,EAAQ,KAAO,KAC1B,WAAYA,EAAQ,KAAO,KAC3B,SAAUA,EAAQ,SAAW,EAAKA,EAAQ,SAAU,QAAQ,CAAC,EAAI,KAAO,OACxE,WAAYA,EAAQ,eAAiB,GACrC,KAAMA,EAAQ,SAAW,GAEzB,YAAa,IAAM,KAAK,MAAM,KAAK,OAAQ,EAAG,GAAG,CAClD,CACH,CASO,SAASC,EAAoBC,EAAU,EAAGC,EAAW,GAAIC,EAAU,GAAI,CAC5E,MAAO,CACL,QAAAF,EACA,SAAAC,EACA,UAAW,KACX,GAAGC,CACJ,CACH,CAWO,SAASC,EAAkBH,EAAU,EAAGC,EAAW,GAAIG,EAAWC,EAAUH,EAAU,GAAI,CAC/F,MAAO,CACL,QAAAF,EACA,SAAAC,EACA,UAAW,KACX,IAAKG,EACL,IAAKC,EACL,SAAU,IACV,GAAGH,CACJ,CACH"}