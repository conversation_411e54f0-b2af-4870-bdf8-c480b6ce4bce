package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserSignInBoToUserSignInMapper;
import com.gzhuxn.personals.domain.user.vo.UserSignInVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserSignInBoToUserSignInMapper.class,UserSignInToAppUserSignInResultVoMapper.class},
    imports = {}
)
public interface UserSignInToUserSignInVoMapper extends BaseMapper<UserSignIn, UserSignInVo> {
}
