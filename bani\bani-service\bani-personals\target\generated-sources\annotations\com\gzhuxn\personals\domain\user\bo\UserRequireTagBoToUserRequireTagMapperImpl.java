package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserRequireTag;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserRequireTagBoToUserRequireTagMapperImpl implements UserRequireTagBoToUserRequireTagMapper {

    @Override
    public UserRequireTag convert(UserRequireTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserRequireTag userRequireTag = new UserRequireTag();

        userRequireTag.setId( arg0.getId() );
        userRequireTag.setNamespace( arg0.getNamespace() );
        userRequireTag.setTagKey( arg0.getTagKey() );
        userRequireTag.setTagVal( arg0.getTagVal() );
        userRequireTag.setTagValName( arg0.getTagValName() );

        return userRequireTag;
    }

    @Override
    public UserRequireTag convert(UserRequireTagBo arg0, UserRequireTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setNamespace( arg0.getNamespace() );
        arg1.setTagKey( arg0.getTagKey() );
        arg1.setTagVal( arg0.getTagVal() );
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
