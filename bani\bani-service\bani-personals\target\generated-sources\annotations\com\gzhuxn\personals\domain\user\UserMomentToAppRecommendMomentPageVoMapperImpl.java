package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentPageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToAppRecommendMomentPageVoMapperImpl implements UserMomentToAppRecommendMomentPageVoMapper {

    @Override
    public AppRecommendMomentPageVo convert(UserMoment source) {
        if ( source == null ) {
            return null;
        }

        AppRecommendMomentPageVo appRecommendMomentPageVo = new AppRecommendMomentPageVo();

        appRecommendMomentPageVo.setCityName( source.getCityCode() );
        appRecommendMomentPageVo.setTime( source.getCreateTime() );
        appRecommendMomentPageVo.setId( source.getId() );
        appRecommendMomentPageVo.setUserId( source.getUserId() );
        appRecommendMomentPageVo.setContent( source.getContent() );
        appRecommendMomentPageVo.setImages( source.getImages() );
        appRecommendMomentPageVo.setLocation( source.getLocation() );
        appRecommendMomentPageVo.setPv( source.getPv() );
        appRecommendMomentPageVo.setLv( source.getLv() );
        appRecommendMomentPageVo.setCv( source.getCv() );
        appRecommendMomentPageVo.setCreateTime( source.getCreateTime() );

        return appRecommendMomentPageVo;
    }

    @Override
    public AppRecommendMomentPageVo convert(UserMoment source, AppRecommendMomentPageVo target) {
        if ( source == null ) {
            return target;
        }

        target.setCityName( source.getCityCode() );
        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );
        target.setContent( source.getContent() );
        target.setImages( source.getImages() );
        target.setLocation( source.getLocation() );
        target.setPv( source.getPv() );
        target.setLv( source.getLv() );
        target.setCv( source.getCv() );
        target.setCreateTime( source.getCreateTime() );

        return target;
    }
}
