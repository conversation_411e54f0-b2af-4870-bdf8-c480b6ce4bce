package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserAlbumVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAlbumToUserAlbumVoMapperImpl implements UserAlbumToUserAlbumVoMapper {

    @Override
    public UserAlbumVo convert(UserAlbum arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAlbumVo userAlbumVo = new UserAlbumVo();

        userAlbumVo.setId( arg0.getId() );
        userAlbumVo.setUserId( arg0.getUserId() );
        userAlbumVo.setImage( arg0.getImage() );

        return userAlbumVo;
    }

    @Override
    public UserAlbumVo convert(UserAlbum arg0, UserAlbumVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setImage( arg0.getImage() );

        return arg1;
    }
}
