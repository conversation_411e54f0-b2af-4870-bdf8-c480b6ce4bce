package com.gzhuxn.personals.domain.audit;

import com.gzhuxn.personals.domain.audit.vo.ContentAuditDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ContentAuditRecordToContentAuditDetailVoMapperImpl implements ContentAuditRecordToContentAuditDetailVoMapper {

    @Override
    public ContentAuditDetailVo convert(ContentAuditRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ContentAuditDetailVo contentAuditDetailVo = new ContentAuditDetailVo();

        contentAuditDetailVo.setId( arg0.getId() );
        contentAuditDetailVo.setBusinessId( arg0.getBusinessId() );
        contentAuditDetailVo.setType( arg0.getType() );
        contentAuditDetailVo.setAuditStatus( arg0.getAuditStatus() );
        contentAuditDetailVo.setAuditTime( arg0.getAuditTime() );
        contentAuditDetailVo.setAuditDesc( arg0.getAuditDesc() );
        contentAuditDetailVo.setAuditUserId( arg0.getAuditUserId() );
        contentAuditDetailVo.setAuditUserName( arg0.getAuditUserName() );
        contentAuditDetailVo.setUserId( arg0.getUserId() );
        contentAuditDetailVo.setUserName( arg0.getUserName() );
        contentAuditDetailVo.setCreateTime( arg0.getCreateTime() );

        return contentAuditDetailVo;
    }

    @Override
    public ContentAuditDetailVo convert(ContentAuditRecord arg0, ContentAuditDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setType( arg0.getType() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setAuditTime( arg0.getAuditTime() );
        arg1.setAuditDesc( arg0.getAuditDesc() );
        arg1.setAuditUserId( arg0.getAuditUserId() );
        arg1.setAuditUserName( arg0.getAuditUserName() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
