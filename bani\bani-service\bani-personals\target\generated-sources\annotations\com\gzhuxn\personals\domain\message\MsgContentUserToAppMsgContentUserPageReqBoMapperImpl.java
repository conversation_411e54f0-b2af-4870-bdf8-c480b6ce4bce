package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.bo.AppMsgContentUserPageReqBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentUserToAppMsgContentUserPageReqBoMapperImpl implements MsgContentUserToAppMsgContentUserPageReqBoMapper {

    @Override
    public AppMsgContentUserPageReqBo convert(MsgContentUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppMsgContentUserPageReqBo appMsgContentUserPageReqBo = new AppMsgContentUserPageReqBo();

        appMsgContentUserPageReqBo.setId( arg0.getId() );
        appMsgContentUserPageReqBo.setUserId( arg0.getUserId() );
        appMsgContentUserPageReqBo.setType( arg0.getType() );
        appMsgContentUserPageReqBo.setSubType( arg0.getSubType() );
        appMsgContentUserPageReqBo.setGroupId( arg0.getGroupId() );
        appMsgContentUserPageReqBo.setSendUserId( arg0.getSendUserId() );

        return appMsgContentUserPageReqBo;
    }

    @Override
    public AppMsgContentUserPageReqBo convert(MsgContentUser arg0, AppMsgContentUserPageReqBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setSendUserId( arg0.getSendUserId() );

        return arg1;
    }
}
