package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserMomentVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToUserMomentVoMapperImpl implements UserMomentToUserMomentVoMapper {

    @Override
    public UserMomentVo convert(UserMoment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMomentVo userMomentVo = new UserMomentVo();

        userMomentVo.setId( arg0.getId() );
        userMomentVo.setContent( arg0.getContent() );
        userMomentVo.setImages( arg0.getImages() );
        userMomentVo.setStatus( arg0.getStatus() );

        return userMomentVo;
    }

    @Override
    public UserMomentVo convert(UserMoment arg0, UserMomentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
