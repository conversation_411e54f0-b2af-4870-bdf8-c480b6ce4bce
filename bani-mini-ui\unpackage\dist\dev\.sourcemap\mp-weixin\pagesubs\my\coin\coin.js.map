{"version": 3, "file": "coin.js", "sources": ["pagesubs/my/coin/coin.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcY29pblxjb2luLnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"我的花瓣\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"main-container\">\n\t\t\t\t<!-- 花瓣余额卡片 -->\n\t\t\t\t<view class=\"balance-card\">\n\t\t\t\t\t<view class=\"balance-header\">\n\t\t\t\t\t\t<view class=\"balance-left\">\n\t\t\t\t\t\t\t<image class=\"coin-icon\" src=\"/static/image/icons/coin.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t<view class=\"balance-info\">\n\t\t\t\t\t\t\t\t<text class=\"balance-title\">全部花瓣</text>\n\t\t\t\t\t\t\t\t<uni-icons type=\"help\" size=\"16\" color=\"#999\" @click=\"showBalanceHelp\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"balance-amount\">\n\t\t\t\t\t\t<view class=\"amount-with-arrow\" @click=\"goToCoinDetail\">\n\t\t\t\t\t\t\t<text class=\"amount-number\">{{ userCoinBalance }}</text>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"recharge-btn\" @click=\"showRechargePopup\">去充值</button>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"balance-tips\">\n\t\t\t\t\t\t<text class=\"tips-text\">{{ accountInfo.coin }} 赠送花瓣 + {{ accountInfo.withdrawCoin }} 永久花瓣</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 签到区域 -->\n\t\t\t\t<signin />\n\t\t\t\t<!-- 新手任务 -->\n\t\t\t\t<view class=\"newbie-tasks\" v-if=\"newbieTasks.length > 0\">\n\t\t\t\t\t<view class=\"section-title\">新手任务</view>\n\t\t\t\t\t<view class=\"task-list\">\n\t\t\t\t\t\t<view class=\"task-item\" v-for=\"task in newbieTasks\" :key=\"task.id\"\n\t\t\t\t\t\t\t@click=\"handleTaskAction(task)\">\n\t\t\t\t\t\t\t<view class=\"task-left\">\n\t\t\t\t\t\t\t\t<text class=\"task-title\">{{ task.title }}</text>\n\t\t\t\t\t\t\t\t<view class=\"task-reward\">\n\t\t\t\t\t\t\t\t\t<image class=\"reward-icon\" src=\"/static/image/icons/coin.png\" mode=\"aspectFit\">\n\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t\t<text class=\"reward-text\">+{{ task.reward }}花瓣</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<button class=\"task-btn\" :class=\"{ 'completed': task.completed }\"\n\t\t\t\t\t\t\t\t:disabled=\"task.completed\">\n\t\t\t\t\t\t\t\t{{ task.completed ? '已完成' : task.buttonText }}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 第三等级任务 -->\n\t\t\t\t<view class=\"level-tasks\">\n\t\t\t\t\t<view class=\"section-title\">新手任务</view>\n\n\t\t\t\t\t<!-- 加载状态 -->\n\t\t\t\t\t<view class=\"loading-container\" v-if=\"isLoadingLevelTasks\">\n\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 任务列表 -->\n\t\t\t\t\t<view class=\"task-list\" v-else-if=\"levelThreeTasks.length > 0\">\n\t\t\t\t\t\t<view class=\"task-item\" v-for=\"task in levelThreeTasks\" :key=\"task.taskId\"\n\t\t\t\t\t\t\t:class=\"{ 'completed': task.isCompleted }\">\n\t\t\t\t\t\t\t<view class=\"task-left\">\n\t\t\t\t\t\t\t\t<text class=\"task-title\">{{ task.taskName }}</text>\n\t\t\t\t\t\t\t\t<view class=\"task-reward\">\n\t\t\t\t\t\t\t\t\t<image class=\"reward-icon\" src=\"/static/image/icons/coin.png\" mode=\"aspectFit\">\n\t\t\t\t\t\t\t\t\t</image>\n\t\t\t\t\t\t\t\t\t<text class=\"reward-text\">+{{ task.coin }}花瓣</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<button class=\"task-btn\" :class=\"{ 'completed': task.isCompleted }\"\n\t\t\t\t\t\t\t\t:disabled=\"task.isCompleted\" @click=\"handleLevelTaskAction(task)\">\n\t\t\t\t\t\t\t\t{{ task.isCompleted ? '已完成' : '去完成' }}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t<view class=\"empty-tasks\" v-else>\n\t\t\t\t\t\t<view class=\"empty-icon\">\n\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"48\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"empty-text\">暂无任务</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\n\t\t\t</view>\n\n\t\t\t<!-- 充值弹窗组件 -->\n\t\t\t<RechargePopup ref=\"rechargePopupRef\" @close=\"loadUserCoinBalance\" />\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport { toast } from '@/utils/common'\nimport { getTaskListByLevel, USER_LEVELS } from '@/api/my/coin'\nimport { getUserAccountInfo } from '@/api/my/account'\nimport RechargePopup from './components/recharge-popup.vue'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport globalConfig from '@/config'\n// 导航栏高度\nconst navBarHeight = ref(0)\n\n// 用户账户信息\nconst accountInfo = ref({\n\tuserId: 0,\n\tcoin: 0,                    // 赠送类型花瓣数余额\n\tlockCoin: 0,               // 锁住的赠送花瓣数\n\tavailableCoin: 0,      // 赠送花瓣数类型可用余额\n\twithdrawCoin: 0,           // 可提现类型花瓣数余额\n\tlockWithdrawCoin: 0,       // 锁住的可提现花瓣数\n\tavailableWithdrawCoin: 0,  // 可提现花瓣数\n\ttotalCoin: 0               // 总花瓣数\n})\n\n// 用户花瓣余额（兼容原有代码）\nconst userCoinBalance = ref(0)\n\n// 新手任务数据\nconst newbieTasks = ref([])\n\n// 第三等级任务数据\nconst levelThreeTasks = ref([])\nconst isLoadingLevelTasks = ref(false)\n\n// 充值弹窗组件引用\nconst rechargePopupRef = ref(null)\n\n\n\n// 页面加载标志\nconst isFirstLoad = ref(true)\n\n// 页面加载\nonLoad(() => {\n\tloadUserCoinBalance()\n\tloadLevelThreeTasks()\n\tisFirstLoad.value = false\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n\n// 显示更多菜单\nconst showMoreMenu = () => {\n\t// TODO: 实现更多菜单功能\n\ttoast('功能开发中')\n}\n\n// 显示帮助\nconst showHelp = () => {\n\t// TODO: 实现帮助功能\n\ttoast('功能开发中')\n}\n\n// 显示余额帮助\nconst showBalanceHelp = () => {\n\tuni.navigateTo({\n\t\turl: globalConfig.help.coinIntroduction\n\t})\n}\n\n// 加载用户账户信息\nconst loadUserCoinBalance = async () => {\n\ttry {\n\t\tconst res = await getUserAccountInfo()\n\t\tif (res.code === 200) {\n\t\t\taccountInfo.value = res.data\n\t\t\tuserCoinBalance.value = res.data.totalCoin || 0\n\t\t} else {\n\t\t\tthrow new Error(res.msg || '获取账户信息失败')\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('获取账户信息失败:', error)\n\t\t// 使用默认值\n\t\tuserCoinBalance.value = 0\n\t}\n}\n\n// 加载第三等级任务列表\nconst loadLevelThreeTasks = async () => {\n\ttry {\n\t\tisLoadingLevelTasks.value = true\n\t\tconst res = await getTaskListByLevel(USER_LEVELS.EXPERT) // 第三等级：情咖\n\t\tif (res.code === 200 && res.data) {\n\t\t\tlevelThreeTasks.value = res.data\n\t\t\tconsole.log('第三等级任务加载成功:', res.data)\n\n\t\t\t// 检查每个任务的path字段\n\t\t\tres.data.forEach((task, index) => {\n\t\t\t\tconsole.log(`任务${index + 1}:`, {\n\t\t\t\t\ttaskId: task.taskId,\n\t\t\t\t\ttaskName: task.taskName,\n\t\t\t\t\tisCompleted: task.isCompleted,\n\t\t\t\t\tpath: task.path,\n\t\t\t\t\ttaskType: task.taskType,\n\t\t\t\t\tcoin: task.coin\n\t\t\t\t})\n\t\t\t})\n\t\t} else {\n\t\t\tlevelThreeTasks.value = []\n\t\t\tconsole.log('第三等级任务为空或加载失败:', res)\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('获取第三等级任务失败:', error)\n\t\tlevelThreeTasks.value = []\n\t\tuni.showToast({\n\t\t\ttitle: '加载任务失败',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tisLoadingLevelTasks.value = false\n\t}\n}\n\n// 处理任务操作\nconst handleTaskAction = (task) => {\n\tif (task.completed) return\n\n\t// 根据任务类型跳转到相应页面\n\tswitch (task.id) {\n\t\tcase 3:\n\t\t\t// 实名认证\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/identity/identity'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 4:\n\t\t\t// 精美照片和关于我\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/profile-edit/profile-edit'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 5:\n\t\t\t// 学历认证\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/education/education'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 6:\n\t\t\t// 工作认证\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/work/work'\n\t\t\t})\n\t\t\tbreak\n\t\tdefault:\n\t\t\ttoast('功能开发中')\n\t}\n}\n\n// 处理第三等级任务操作\nconst handleLevelTaskAction = (task) => {\n\tconsole.log('点击任务:', task)\n\n\t// 检查任务是否已完成\n\tif (task.isCompleted) {\n\t\ttoast('任务已完成')\n\t\treturn\n\t}\n\n\t// 优先使用path字段进行跳转\n\tif (task.path && task.path.trim()) {\n\t\tconst targetPath = task.path.trim()\n\t\tconsole.log('使用path跳转到:', targetPath)\n\n\t\t// 判断是否为外部链接\n\t\tif (targetPath.startsWith('http://') || targetPath.startsWith('https://')) {\n\t\t\t// 外部链接，使用webview打开\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/webview/webview?url=${encodeURIComponent(targetPath)}`\n\t\t\t})\n\t\t} else {\n\t\t\t// 内部页面路径\n\t\t\tuni.navigateTo({\n\t\t\t\turl: targetPath,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tconsole.log('页面跳转成功:', targetPath)\n\t\t\t\t},\n\t\t\t\tfail: (error) => {\n\t\t\t\t\tconsole.error('页面跳转失败:', error)\n\t\t\t\t\ttoast('页面跳转失败')\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t\treturn\n\t}\n\n\t// 如果没有path字段，使用taskType作为备用方案\n\tconsole.log('使用taskType跳转:', task.taskType)\n\tswitch (task.taskType) {\n\t\tcase 'profile':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/profile/baseEdit'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'avatar':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/profile/profileEdit'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'auth_identity':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/auth/identity'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'photos':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/profile-edit/profile-edit'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'auth_education':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/education/education'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'auth_work':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/work/work'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'auth_car':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/auth/car'\n\t\t\t})\n\t\t\tbreak\n\t\tcase 'auth_house':\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pagesubs/my/auth/house'\n\t\t\t})\n\t\t\tbreak\n\t\tdefault:\n\t\t\ttoast('功能开发中')\n\t}\n}\n\n// 显示充值弹窗\nconst showRechargePopup = () => {\n\trechargePopupRef.value?.openPopup()\n}\n\n// 跳转到花瓣明细页面\nconst goToCoinDetail = () => {\n\tuni.navigateTo({\n\t\turl: '/pagesubs/my/coin/coin-detail'\n\t})\n}\n\n\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(180deg, $primary-color 0%, #f5f5f5 40%);\n}\n\n.nav-right-icons {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.main-container {\n\tpadding: 0 30rpx 120rpx;\n}\n\n// 余额卡片\n.balance-card {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: $radius-lg;\n\tpadding: 40rpx 30rpx;\n\tmargin: 30rpx 0;\n\tbackdrop-filter: blur(10px);\n\n\t.balance-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 30rpx;\n\n\t\t.balance-left {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 20rpx;\n\n\t\t\t.coin-icon {\n\t\t\t\twidth: 60rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tbackground: linear-gradient(135deg, #FFD700, #FFA500);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tpadding: 10rpx;\n\t\t\t}\n\n\t\t\t.balance-info {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tgap: 10rpx;\n\n\t\t\t\t.balance-title {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\n\t}\n\n\t.balance-amount {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 20rpx;\n\n\t\t.amount-with-arrow {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tgap: 8rpx;\n\t\t\tflex: 1;\n\t\t\tcursor: pointer;\n\n\t\t\t.amount-number {\n\t\t\t\tfont-size: 72rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: $text-primary;\n\t\t\t}\n\t\t}\n\n\t\t.recharge-btn {\n\t\t\tbackground: $primary-color;\n\t\t\tcolor: white;\n\t\t\tborder: none;\n\t\t\theight: $btn-sm-height;\n\t\t\tpadding: $btn-sm-padding;\n\t\t\tfont-size: $btn-sm-font-size;\n\t\t\tborder-radius: $btn-sm-border-radius;\n\t\t\tfont-weight: 500;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tline-height: 1;\n\t\t\tmargin-left: 20rpx;\n\t\t\tflex-shrink: 0;\n\t\t}\n\t}\n\n\t.balance-tips {\n\t\t.tips-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: $text-tertiary;\n\t\t}\n\t}\n}\n\n\n\n// 新手任务\n.newbie-tasks {\n\tbackground: white;\n\tborder-radius: $radius-lg;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 30rpx;\n}\n\n// 第三等级任务\n.level-tasks {\n\tbackground: white;\n\tborder-radius: $radius-lg;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 30rpx;\n\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: $text-primary;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.task-list {\n\t\t.task-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 30rpx 0;\n\t\t\tborder-bottom: 1rpx solid $bg-secondary;\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\n\t\t\t// 已完成状态 - 移除置灰效果，保持任务项正常显示\n\n\t\t\t.task-left {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.task-title {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: $text-primary;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.task-reward {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tgap: 8rpx;\n\n\t\t\t\t\t.reward-icon {\n\t\t\t\t\t\twidth: 24rpx;\n\t\t\t\t\t\theight: 24rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.reward-text {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #FFA500;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.task-btn {\n\t\t\t\tbackground: $primary-color;\n\t\t\t\tcolor: white;\n\t\t\t\tborder: none;\n\t\t\t\theight: $btn-sm-height;\n\t\t\t\tpadding: $btn-sm-padding;\n\t\t\t\tfont-size: $btn-sm-font-size;\n\t\t\t\tborder-radius: $btn-sm-border-radius;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tline-height: 1;\n\t\t\t\tcursor: pointer;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tmin-width: 120rpx;\n\n\t\t\t\t// 可点击状态的交互效果\n\t\t\t\t&:not(.completed):not(:disabled) {\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tbackground: lighten($primary-color, 10%);\n\t\t\t\t\t\ttransform: translateY(-1rpx);\n\t\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.3);\n\t\t\t\t\t}\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground: darken($primary-color, 5%);\n\t\t\t\t\t\ttransform: translateY(0);\n\t\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(105, 108, 243, 0.2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.completed {\n\t\t\t\t\tbackground: $bg-secondary;\n\t\t\t\t\tcolor: $text-tertiary;\n\t\t\t\t\tcursor: not-allowed;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// 加载状态样式\n\t.loading-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 80rpx 40rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t// 空状态样式\n\t.empty-tasks {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 80rpx 40rpx;\n\t}\n\n\t.empty-icon {\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/coin/coin.vue'\nwx.createPage(MiniProgramPage)"], "names": ["RechargePopup", "ScrollNavPage", "ref", "accountInfo", "userCoinBalance", "newbieTasks", "levelThreeTasks", "isLoadingLevelTasks", "rechargePopupRef", "isFirstLoad", "onLoad", "loadUserCoinBalance", "loadLevelThreeTasks", "showBalanceHelp", "uni", "globalConfig", "res", "getUserAccountInfo", "error", "getTaskListByLevel", "USER_LEVELS", "task", "index", "handleTaskAction", "toast", "handleLevelTaskAction", "targetPath", "showRechargePopup", "_a", "goToCoinDetail", "MiniProgramPage"], "mappings": "4dAwGA,MAAMA,EAAgB,IAAW,iCAC3BC,EAAgB,IAAW,mFAGZC,EAAG,IAAC,CAAC,EAG1B,MAAMC,EAAcD,EAAAA,IAAI,CACvB,OAAQ,EACR,KAAM,EACN,SAAU,EACV,cAAe,EACf,aAAc,EACd,iBAAkB,EAClB,sBAAuB,EACvB,UAAW,CACZ,CAAC,EAGKE,EAAkBF,EAAG,IAAC,CAAC,EAGvBG,EAAcH,EAAG,IAAC,EAAE,EAGpBI,EAAkBJ,EAAG,IAAC,EAAE,EACxBK,EAAsBL,EAAG,IAAC,EAAK,EAG/BM,EAAmBN,EAAG,IAAC,IAAI,EAK3BO,EAAcP,EAAG,IAAC,EAAI,EAG5BQ,EAAAA,OAAO,IAAM,CACZC,EAAqB,EACrBC,EAAqB,EACrBH,EAAY,MAAQ,EACrB,CAAC,EAqBD,MAAMI,EAAkB,IAAM,CAC7BC,EAAAA,MAAI,WAAW,CACd,IAAKC,EAAAA,aAAa,KAAK,gBACzB,CAAE,CACF,EAGMJ,EAAsB,SAAY,CACvC,GAAI,CACH,MAAMK,EAAM,MAAMC,qBAAoB,EACtC,GAAID,EAAI,OAAS,IAChBb,EAAY,MAAQa,EAAI,KACxBZ,EAAgB,MAAQY,EAAI,KAAK,WAAa,MAE9C,OAAM,IAAI,MAAMA,EAAI,KAAO,UAAU,CAEtC,OAAQE,EAAO,CACfJ,EAAAA,uDAAc,YAAaI,CAAK,EAEhCd,EAAgB,MAAQ,CACxB,CACF,EAGMQ,EAAsB,SAAY,CACvC,GAAI,CACHL,EAAoB,MAAQ,GAC5B,MAAMS,EAAM,MAAMG,qBAAmBC,EAAAA,YAAY,MAAM,EACnDJ,EAAI,OAAS,KAAOA,EAAI,MAC3BV,EAAgB,MAAQU,EAAI,KAC5BF,EAAA,MAAA,MAAA,MAAA,mCAAY,cAAeE,EAAI,IAAI,EAGnCA,EAAI,KAAK,QAAQ,CAACK,EAAMC,IAAU,CACjCR,QAAA,MAAA,MAAA,mCAAY,KAAKQ,EAAQ,CAAC,IAAK,CAC9B,OAAQD,EAAK,OACb,SAAUA,EAAK,SACf,YAAaA,EAAK,YAClB,KAAMA,EAAK,KACX,SAAUA,EAAK,SACf,KAAMA,EAAK,IAChB,CAAK,CACL,CAAI,IAEDf,EAAgB,MAAQ,CAAE,EAC1BQ,EAAAA,MAAA,MAAA,MAAA,mCAAY,iBAAkBE,CAAG,EAElC,OAAQE,EAAO,CACfJ,EAAAA,uDAAc,cAAeI,CAAK,EAClCZ,EAAgB,MAAQ,CAAE,EAC1BQ,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,MACT,CAAG,CACH,QAAW,CACTP,EAAoB,MAAQ,EAC5B,CACF,EAGMgB,EAAoBF,GAAS,CAClC,GAAI,CAAAA,EAAK,UAGT,OAAQA,EAAK,GAAE,CACd,IAAK,GAEJP,EAAAA,MAAI,WAAW,CACd,IAAK,gCACT,CAAI,EACD,MACD,IAAK,GAEJA,EAAAA,MAAI,WAAW,CACd,IAAK,wCACT,CAAI,EACD,MACD,IAAK,GAEJA,EAAAA,MAAI,WAAW,CACd,IAAK,kCACT,CAAI,EACD,MACD,IAAK,GAEJA,EAAAA,MAAI,WAAW,CACd,IAAK,wBACT,CAAI,EACD,MACD,QACCU,EAAAA,MAAM,OAAO,CACd,CACF,EAGMC,EAAyBJ,GAAS,CAIvC,GAHAP,EAAAA,MAAY,MAAA,MAAA,mCAAA,QAASO,CAAI,EAGrBA,EAAK,YAAa,CACrBG,EAAAA,MAAM,OAAO,EACb,MACA,CAGD,GAAIH,EAAK,MAAQA,EAAK,KAAK,KAAI,EAAI,CAClC,MAAMK,EAAaL,EAAK,KAAK,KAAM,EACnCP,EAAAA,MAAY,MAAA,MAAA,mCAAA,aAAcY,CAAU,EAGhCA,EAAW,WAAW,SAAS,GAAKA,EAAW,WAAW,UAAU,EAEvEZ,EAAAA,MAAI,WAAW,CACd,IAAK,8BAA8B,mBAAmBY,CAAU,CAAC,EACrE,CAAI,EAGDZ,EAAAA,MAAI,WAAW,CACd,IAAKY,EACL,QAAS,IAAM,CACdZ,EAAAA,MAAA,MAAA,MAAA,mCAAY,UAAWY,CAAU,CACjC,EACD,KAAOR,GAAU,CAChBJ,EAAAA,uDAAc,UAAWI,CAAK,EAC9BM,EAAAA,MAAM,QAAQ,CACd,CACL,CAAI,EAEF,MACA,CAID,OADAV,EAAY,MAAA,MAAA,MAAA,mCAAA,gBAAiBO,EAAK,QAAQ,EAClCA,EAAK,SAAQ,CACpB,IAAK,UACJP,EAAAA,MAAI,WAAW,CACd,IAAK,+BACT,CAAI,EACD,MACD,IAAK,SACJA,EAAAA,MAAI,WAAW,CACd,IAAK,kCACT,CAAI,EACD,MACD,IAAK,gBACJA,EAAAA,MAAI,WAAW,CACd,IAAK,4BACT,CAAI,EACD,MACD,IAAK,SACJA,EAAAA,MAAI,WAAW,CACd,IAAK,wCACT,CAAI,EACD,MACD,IAAK,iBACJA,EAAAA,MAAI,WAAW,CACd,IAAK,kCACT,CAAI,EACD,MACD,IAAK,YACJA,EAAAA,MAAI,WAAW,CACd,IAAK,wBACT,CAAI,EACD,MACD,IAAK,WACJA,EAAAA,MAAI,WAAW,CACd,IAAK,uBACT,CAAI,EACD,MACD,IAAK,aACJA,EAAAA,MAAI,WAAW,CACd,IAAK,yBACT,CAAI,EACD,MACD,QACCU,EAAAA,MAAM,OAAO,CACd,CACF,EAGMG,EAAoB,IAAM,QAC/BC,EAAApB,EAAiB,QAAjB,MAAAoB,EAAwB,WACzB,EAGMC,EAAiB,IAAM,CAC5Bf,EAAAA,MAAI,WAAW,CACd,IAAK,+BACP,CAAE,CACF,u4BClWA,GAAG,WAAWgB,CAAe"}