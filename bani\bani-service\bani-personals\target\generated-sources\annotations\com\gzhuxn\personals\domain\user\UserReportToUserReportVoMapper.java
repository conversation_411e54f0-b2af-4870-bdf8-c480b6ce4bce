package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.AppUserReportCreateBoToUserReportMapper;
import com.gzhuxn.personals.domain.user.bo.UserReportBoToUserReportMapper;
import com.gzhuxn.personals.domain.user.vo.UserReportVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserReportCreateBoToUserReportMapper.class,UserReportBoToUserReportMapper.class},
    imports = {}
)
public interface UserReportToUserReportVoMapper extends BaseMapper<UserReport, UserReportVo> {
}
