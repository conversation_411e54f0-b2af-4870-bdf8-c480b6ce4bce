package com.gzhuxn.personals.domain.group.bo;

import com.gzhuxn.personals.domain.group.GroupTag;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class GroupTagBoToGroupTagMapperImpl implements GroupTagBoToGroupTagMapper {

    @Override
    public GroupTag convert(GroupTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupTag groupTag = new GroupTag();

        groupTag.setSearchValue( arg0.getSearchValue() );
        groupTag.setCreateBy( arg0.getCreateBy() );
        groupTag.setCreateTime( arg0.getCreateTime() );
        groupTag.setUpdateBy( arg0.getUpdateBy() );
        groupTag.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            groupTag.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        groupTag.setCreateDept( arg0.getCreateDept() );
        groupTag.setId( arg0.getId() );
        groupTag.setGroupId( arg0.getGroupId() );
        groupTag.setVal( arg0.getVal() );
        groupTag.setName( arg0.getName() );

        return groupTag;
    }

    @Override
    public GroupTag convert(GroupTagBo arg0, GroupTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setVal( arg0.getVal() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
