package com.gzhuxn.personals.controller.app.user.bo.questionapply;

import com.gzhuxn.personals.domain.user.UserQuestionApply;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserQuestionApplyBoToUserQuestionApplyMapperImpl implements AppUserQuestionApplyBoToUserQuestionApplyMapper {

    @Override
    public UserQuestionApply convert(AppUserQuestionApplyBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserQuestionApply userQuestionApply = new UserQuestionApply();

        userQuestionApply.setSearchValue( arg0.getSearchValue() );
        userQuestionApply.setCreateBy( arg0.getCreateBy() );
        userQuestionApply.setCreateTime( arg0.getCreateTime() );
        userQuestionApply.setUpdateBy( arg0.getUpdateBy() );
        userQuestionApply.setUpdateTime( arg0.getUpdateTime() );
        userQuestionApply.setDelFlag( arg0.getDelFlag() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userQuestionApply.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userQuestionApply.setCreateDept( arg0.getCreateDept() );
        userQuestionApply.setId( arg0.getId() );
        userQuestionApply.setUserId( arg0.getUserId() );
        userQuestionApply.setOppositeUserId( arg0.getOppositeUserId() );
        userQuestionApply.setName( arg0.getName() );
        userQuestionApply.setContent( arg0.getContent() );
        userQuestionApply.setAnswerContent( arg0.getAnswerContent() );
        userQuestionApply.setCoin( arg0.getCoin() );
        userQuestionApply.setStatus( arg0.getStatus() );

        return userQuestionApply;
    }

    @Override
    public UserQuestionApply convert(AppUserQuestionApplyBo arg0, UserQuestionApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setDelFlag( arg0.getDelFlag() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setName( arg0.getName() );
        arg1.setContent( arg0.getContent() );
        arg1.setAnswerContent( arg0.getAnswerContent() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
