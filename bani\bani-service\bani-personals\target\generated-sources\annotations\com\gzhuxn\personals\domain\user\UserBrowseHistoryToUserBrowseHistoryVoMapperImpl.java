package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserBrowseHistoryVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBrowseHistoryToUserBrowseHistoryVoMapperImpl implements UserBrowseHistoryToUserBrowseHistoryVoMapper {

    @Override
    public UserBrowseHistoryVo convert(UserBrowseHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserBrowseHistoryVo userBrowseHistoryVo = new UserBrowseHistoryVo();

        userBrowseHistoryVo.setId( arg0.getId() );
        userBrowseHistoryVo.setUserId( arg0.getUserId() );
        userBrowseHistoryVo.setType( arg0.getType() );
        userBrowseHistoryVo.setBusinessId( arg0.getBusinessId() );

        return userBrowseHistoryVo;
    }

    @Override
    public UserBrowseHistoryVo convert(UserBrowseHistory arg0, UserBrowseHistoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
