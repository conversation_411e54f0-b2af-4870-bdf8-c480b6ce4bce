package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.bo.ManageCoinBoToManageCoinMapper;
import com.gzhuxn.personals.domain.manage.vo.ManageCoinVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageCoinBoToManageCoinMapper.class},
    imports = {}
)
public interface ManageCoinToManageCoinVoMapper extends BaseMapper<ManageCoin, ManageCoinVo> {
}
