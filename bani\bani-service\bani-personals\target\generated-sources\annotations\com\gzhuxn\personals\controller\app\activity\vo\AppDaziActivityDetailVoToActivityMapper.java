package com.gzhuxn.personals.controller.app.activity.vo;

import com.gzhuxn.personals.domain.activity.ActSafeguardToActSafeguardStorageVoMapper;
import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityFeeToAppActivityDetailFeeVoMapper;
import com.gzhuxn.personals.domain.activity.ActivityToAppDaziActivityDetailVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardToActSafeguardStorageVoMapper.class,AppActivityDetailFeeVoToActivityFeeMapper.class,ActivityFeeToAppActivityDetailFeeVoMapper.class,ActivityToAppDaziActivityDetailVoMapper.class},
    imports = {}
)
public interface AppDaziActivityDetailVoToActivityMapper extends BaseMapper<AppDaziActivityDetailVo, Activity> {
}
