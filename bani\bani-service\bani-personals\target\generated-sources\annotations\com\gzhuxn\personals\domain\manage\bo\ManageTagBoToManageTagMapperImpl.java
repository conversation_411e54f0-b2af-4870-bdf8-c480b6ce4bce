package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageTag;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageTagBoToManageTagMapperImpl implements ManageTagBoToManageTagMapper {

    @Override
    public ManageTag convert(ManageTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageTag manageTag = new ManageTag();

        manageTag.setSearchValue( arg0.getSearchValue() );
        manageTag.setCreateBy( arg0.getCreateBy() );
        manageTag.setCreateTime( arg0.getCreateTime() );
        manageTag.setUpdateBy( arg0.getUpdateBy() );
        manageTag.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            manageTag.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        manageTag.setCreateDept( arg0.getCreateDept() );
        manageTag.setId( arg0.getId() );
        manageTag.setName( arg0.getName() );
        manageTag.setIcon( arg0.getIcon() );
        manageTag.setRemark( arg0.getRemark() );
        manageTag.setSort( arg0.getSort() );
        manageTag.setStatus( arg0.getStatus() );

        return manageTag;
    }

    @Override
    public ManageTag convert(ManageTagBo arg0, ManageTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
