package com.gzhuxn.personals.controller.app.user.bo.greeting;

import com.gzhuxn.personals.domain.user.UserGreeting;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserGreetingCreateBoToUserGreetingMapperImpl implements AppUserGreetingCreateBoToUserGreetingMapper {

    @Override
    public UserGreeting convert(AppUserGreetingCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGreeting userGreeting = new UserGreeting();

        userGreeting.setOppositeUserId( arg0.getOppositeUserId() );
        userGreeting.setContent( arg0.getContent() );

        return userGreeting;
    }

    @Override
    public UserGreeting convert(AppUserGreetingCreateBo arg0, UserGreeting arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}
