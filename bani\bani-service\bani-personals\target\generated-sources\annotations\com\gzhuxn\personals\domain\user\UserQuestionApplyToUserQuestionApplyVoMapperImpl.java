package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserQuestionApplyVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserQuestionApplyToUserQuestionApplyVoMapperImpl implements UserQuestionApplyToUserQuestionApplyVoMapper {

    @Override
    public UserQuestionApplyVo convert(UserQuestionApply arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserQuestionApplyVo userQuestionApplyVo = new UserQuestionApplyVo();

        userQuestionApplyVo.setId( arg0.getId() );
        userQuestionApplyVo.setUserId( arg0.getUserId() );
        userQuestionApplyVo.setOppositeUserId( arg0.getOppositeUserId() );
        userQuestionApplyVo.setName( arg0.getName() );
        userQuestionApplyVo.setContent( arg0.getContent() );
        userQuestionApplyVo.setAnswerContent( arg0.getAnswerContent() );
        userQuestionApplyVo.setStatus( arg0.getStatus() );

        return userQuestionApplyVo;
    }

    @Override
    public UserQuestionApplyVo convert(UserQuestionApply arg0, UserQuestionApplyVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setName( arg0.getName() );
        arg1.setContent( arg0.getContent() );
        arg1.setAnswerContent( arg0.getAnswerContent() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
