package com.gzhuxn.personals.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.base.service.impl.BaniServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.base.utils.Func;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.core.utils.StreamUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.redis.utils.RedisUtils;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.common.tenant.helper.TenantHelper;
import com.gzhuxn.personals.controller.admin.audit.vo.AdminContentAuditUserDetailVo;
import com.gzhuxn.personals.controller.admin.user.bo.AdminUserDetailQueryBo;
import com.gzhuxn.personals.controller.admin.user.vo.AdminUserDetailInfoVo;
import com.gzhuxn.personals.controller.admin.user.vo.AdminUserDetailPageVo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserAvatarBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserBaseBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserFullBaseBo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserLocationBo;
import com.gzhuxn.personals.controller.app.user.bo.tag.AppUserTagBo;
import com.gzhuxn.personals.controller.app.user.vo.*;
import com.gzhuxn.personals.controller.app.user.vo.tag.AppTagVo;
import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.vo.UserDetailVo;
import com.gzhuxn.personals.domain.user.vo.UserRequireTagVo;
import com.gzhuxn.personals.domain.user.vo.UserTagVo;
import com.gzhuxn.personals.domain.user.vo.UserTransitionTagVo;
import com.gzhuxn.personals.enums.UserAnimalVal;
import com.gzhuxn.personals.enums.UserRequireTagKeyVal;
import com.gzhuxn.personals.enums.UserStarVal;
import com.gzhuxn.personals.enums.UserTagKeyVal;
import com.gzhuxn.personals.enums.audit.AuditStatus;
import com.gzhuxn.personals.enums.audit.AuditType;
import com.gzhuxn.personals.enums.dict.UserJob;
import com.gzhuxn.personals.enums.dict.UserRevenue;
import com.gzhuxn.personals.enums.user.follow.FollowType;
import com.gzhuxn.personals.mapper.user.UserDetailMapper;
import com.gzhuxn.personals.service.audit.IContentAuditRecordService;
import com.gzhuxn.personals.service.user.*;
import com.gzhuxn.personals.utils.UserUtils;
import com.gzhuxn.resource.api.RemoteContentAuditService;
import com.gzhuxn.resource.api.RemoteFileService;
import com.gzhuxn.resource.api.domain.RemoteCheckText;
import com.gzhuxn.system.api.RemoteDictService;
import com.gzhuxn.system.api.RemoteUserService;
import com.gzhuxn.system.api.domain.bo.RemoteUserBo;
import com.gzhuxn.system.api.domain.vo.RemoteUserVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户-用户详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Slf4j
@Service
public class UserDetailServiceImpl extends BaniServiceImpl<UserDetailMapper, UserDetail> implements IUserDetailService {
    private static final String USER_PID_PREFIX = "%s:user:pid:";
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteDictService remoteDictService;
    @DubboReference
    private RemoteFileService remoteFileService;

    @DubboReference
    private RemoteContentAuditService remoteContentAuditService;

    @Lazy
    @Resource
    private UserDetailServiceImpl self;

    @Resource
    private IUserTagService userTagService;

    @Resource
    private IUserRequireTagService userRequireTagService;

    @Resource
    private IContentAuditRecordService userDetailAuditService;

    @Lazy
    @Resource
    private IUserFollowService userFollowService;

    @Resource
    private IUserRewardService userRewardService;

    /**
     * 查询用户-用户详情
     *
     * @param userId 主键
     * @return 用户-用户详情
     */
    @Override
    public UserDetailVo queryById(Long userId) {
        return baseMapper.selectVoById(userId);
    }

    @Override
    public AdminContentAuditUserDetailVo queryContentAuditDetailById(Long userId) {
        AdminContentAuditUserDetailVo resultVo = baseMapper.selectVoById(userId, AdminContentAuditUserDetailVo.class);
        resultVo.setAge(UserUtils.getAgeStr(resultVo.getBirthday()));
        return resultVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBase(AppUserBaseBo bo) {
        // 更新基础数据
        RemoteUserBo sysUserBo = MapstructUtils.convert(bo, RemoteUserBo.class);
        sysUserBo.setUserName(bo.getNickName());
        RemoteCheckText checkText = remoteContentAuditService.checkText(bo.getNickName());
        AssertUtils.isTrue(checkText.isPass(), checkText.failMag());

        remoteUserService.updateUserInfo(sysUserBo);

        RemoteUserVo remoteUserVo = remoteUserService.selectById(bo.getUserId());
        AssertUtils.isTrue(null != remoteUserVo, "用户不存在");
        // 更新用户详情
        UserDetail oldUserDetail = getById(bo.getUserId());
        UserDetail userDetail = MapstructUtils.convert(bo, UserDetail.class);
        boolean addUserDetail = false;
        if (null == oldUserDetail) {
            addUserDetail = true;
            userDetail = new UserDetail();
            userDetail.setUserId(bo.getUserId());
            userDetail.setPid(self.createPid());
        }

        // 更新生日信息
        if (!bo.getBirthday().equals(userDetail.getBirthday())) {
            userDetail.setStar(UserStarVal.ofValue(userDetail.getBirthday()));
            userDetail.setAnimal(UserAnimalVal.ofValue(userDetail.getBirthday()));
        }
        boolean result;
        if (addUserDetail) {
            result = save(userDetail);
        } else {
            result = updateById(userDetail);
        }
        // 新用户创建详情后，检查是否完成基础资料奖励
        if (result) {
            userRewardService.checkAndRewardDataFillBase(bo.getUserId());
        }
        return result;
    }

    @Override
    public boolean updateFullBase(AppUserFullBaseBo bo) {
        if (Func.isAllFieldsNull(bo)) {
            return true;
        }
        Long userId = LoginHelper.getUserId();
        // 校验用户存在
        validateExists(userId);

        RemoteUserBo remoteUserBo = buildRemoteUserBo(bo);
        if (!Func.isAllFieldsNull(remoteUserBo)) {
            remoteUserBo.setUserId(userId);
            remoteUserService.updateUserInfo(remoteUserBo);
        }
        UserDetail newUserDetail = MapstructUtils.convert(bo, UserDetail.class);
        newUserDetail.setUserId(userId);
        return updateById(newUserDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserTag(AppUserTagBo bo) {
        UserTagKeyVal tagKey = UserTagKeyVal.valid(bo.getTagKey());
        Long userId = LoginHelper.getUserId();
        boolean result = userTagService.updateTag(userId, tagKey, bo.getTagVal());

        // 如果是关于我标签，检查是否完成数据填写奖励
        if (result && UserTagKeyVal.ABOUT_ME.equals(tagKey)) {
            userRewardService.checkAndRewardDataFillDetail(userId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean userRequireTag(AppUserTagBo bo) {
        UserRequireTagKeyVal tagKey = UserRequireTagKeyVal.valid(bo.getTagKey());
        return userRequireTagService.updateTag(LoginHelper.getUserId(), tagKey, bo.getTagVal());
    }

    private RemoteUserBo buildRemoteUserBo(AppUserFullBaseBo bo) {
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setNickName(bo.getNickName());
        remoteUserBo.setSex(bo.getSex());
        return remoteUserBo;
    }

    @Override
    public AppUserBaseVo getUserBaseByUserId(Long userId) {
        UserDetail userDetail = baseMapper.selectById(userId);
        AppUserBaseVo returnVo = null;
        if (null != userDetail) {
            returnVo = MapstructUtils.convert(userDetail, AppUserBaseVo.class);
            RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
            if (null != remoteUserVo) {
                returnVo.setPhoneNumber(remoteUserVo.getPhoneNumber());
                returnVo.setSex(remoteUserVo.getSex());
                returnVo.setNickName(remoteUserVo.getNickName());
            }
        }
        return returnVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserAvatar(AppUserAvatarBo bo) {
        RemoteUserBo remoteUserBo = MapstructUtils.convert(bo, RemoteUserBo.class);
        UserDetail userDetail = existById(bo.getUserId());
        UserDetail upDetail = new UserDetail();
        upDetail.setUserId(bo.getUserId());
        upDetail.setAvatar(bo.getAvatar());
        remoteUserService.updateUserAvatar(remoteUserBo);

        userDetailAuditService.createAudit(userDetail.getUserId(), AuditType.USER_BASE_INFO);
        upDetail.setAuditStatus(AuditStatus.WAIT_AUDIT.getValue());
        return updateById(upDetail);
    }

    @Override
    public boolean updateUserLocation(AppUserLocationBo bo) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(bo.getUserId());
        userDetail.setLon(bo.getLon());
        userDetail.setLat(bo.getLat());
        return updateById(userDetail);
    }

    @Override
    public AppUserFullBaseVo getFullBase() {
        UserDetail userDetail = existById(LoginHelper.getUserId());
        return MapstructUtils.convert(userDetail, AppUserFullBaseVo.class);
    }

    @Override
    public AppUserEditDetailVo getEditDetail(Long userId) {
        UserDetail userDetail = existById(userId);
        AppUserEditDetailVo retUser = MapstructUtils.convert(userDetail, AppUserEditDetailVo.class);
        RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
        AssertUtils.notNull(remoteUserVo, "用户信息不存在");
        retUser.setNickName(remoteUserVo.getNickName());
        retUser.setAvatar(remoteUserVo.getAvatar());
        retUser.setSex(remoteUserVo.getSex());

        List<UserTagVo> tags = userTagService.listVoByUserId(userId);
        retUser.setTags(tags);

        List<UserRequireTagVo> requireTags = userRequireTagService.listVoByUserId(userId);
        retUser.setRequireTags(requireTags);
        return retUser;
    }

    @Override
    public AppUserDetailVo getDetail(Long userId) {
        AppUserEditDetailVo editDetail = getEditDetail(userId);
        AppUserDetailVo ret = MapstructUtils.convert(editDetail, AppUserDetailVo.class);
        ret.setBirthdayYear(UserUtils.getBirthdayYearStr(editDetail.getBirthday()));
        ret.setAge(UserUtils.getAgeStr(editDetail.getBirthday()));
        List<UserTagVo> tags = editDetail.getTags();
        if (CollUtil.isNotEmpty(tags)) {
            ret.setTags(new ArrayList<>(tags.size()));
            tags.forEach(tag -> {
                UserTransitionTagVo tagVo = new UserTransitionTagVo();
                tagVo.setNamespace(tag.getNamespace());
                tagVo.setTagKey(tag.getTagKey());

                UserTagKeyVal tagKey = UserTagKeyVal.valid(tag.getTagKey());
                tagVo.setTagVal(tag.getTagVal());
                // 字典转换
                if (tagKey.isDict()) {
                    tagVo.setTagVal(remoteDictService.selectLabelByType(tagKey.getDictType(), tag.getTagVal()));
                }
                tagVo.setTagValName(tag.getTagValName());
                ret.getTags().add(tagVo);
            });
        }

        List<UserRequireTagVo> requireTags = editDetail.getRequireTags();
        if (CollUtil.isNotEmpty(requireTags)) {
            ret.setRequireTags(new ArrayList<>(requireTags.size()));
            requireTags.forEach(tag -> {
                UserTransitionTagVo tagVo = new UserTransitionTagVo();
                tagVo.setNamespace(tag.getNamespace());
                tagVo.setTagKey(tag.getTagKey());

                UserRequireTagKeyVal tagKey = UserRequireTagKeyVal.valid(tag.getTagKey());
                tagVo.setTagVal(tag.getTagVal());
                // 字典转换
                if (tagKey.isDict()) {
                    tagVo.setTagVal(remoteDictService.selectLabelByType(tagKey.getDictType(), tag.getTagVal()));
                }
                tagVo.setTagValName(tag.getTagValName());
                ret.getRequireTags().add(tagVo);
            });
        }
        ret.setIsFollowed(userFollowService.isFollowed(LoginHelper.getUserId(), FollowType.USER, ret.getUserId()));
        return ret;
    }

    @Override
    public AppUserMyProfileVo getMyProfileByUserId(Long userId) {
        UserDetail userDetail = existById(userId);
        AppUserMyProfileVo ret = MapstructUtils.convert(userDetail, AppUserMyProfileVo.class);

        AppUserMyProfileStatCountVo count = baseMapper.myStat(userId);
        ret.setCount(count);
        return ret;
    }

    @Override
    public List<AppTagVo> getUserTag(Long userId) {
        List<UserTagVo> tags = userTagService.listVoByUserId(userId);
        return BeanUtil.copyToList(tags, AppTagVo.class);
    }

    @Override
    public List<AppTagVo> getUserRequireTags(Long userId) {
        List<UserRequireTagVo> tags = userRequireTagService.listVoByUserId(userId);
        return BeanUtil.copyToList(tags, AppTagVo.class);
    }

    @Override
    public void validExistByUserId(Long userId) {
        validateExists(userId, "拉黑用户信息不存在！");
    }

    @Override
    public Map<Long, String> queryUserTitleMapByUserIds(List<Long> oppositeUserIds) {
        List<UserDetail> userDetails = listByIds(oppositeUserIds);
        return StreamUtils.toMap(userDetails, UserDetail::getUserId, user -> {
            String ageStr = UserUtils.getAgeStr(user.getBirthday());
            String userJobVal = remoteDictService.selectLabelByType(UserJob.getDictType(), user.getJob());
            String userRevenueVal = remoteDictService.selectLabelByType(UserRevenue.getDictType(), user.getRevenue());
            return String.join(",", userJobVal, userRevenueVal, ageStr);
        });
    }

    @Override
    public UserDetail getByUserId(Long userId) {
        return baseMapper.selectById(userId);
    }

    @Override
    public void updateVip(Long userId, LocalDate startDate, LocalDate endDate) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setVipStartDate(startDate);
        userDetail.setVipEndDate(endDate);
        userDetail.setIsVip(Boolean.TRUE);
        updateById(userDetail);
    }

    @Override
    public void updateAuthIdentityStatus(Long userId, Boolean isIdentity) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setIsIdentity(isIdentity);
        updateById(userDetail);
    }

    @Override
    public UserDetail existById(Long userId) {
        return existById(userId, "用户信息不存在！");
    }

    @Override
    public boolean isIdentityVerified(Long userId) {
        UserDetail userDetail = existById(userId);
        return null != userDetail && Boolean.TRUE.equals(userDetail.getIsIdentity());
    }

    /**
     * 创建用户pid
     *
     * @return pid
     */
    @Lock4j
    public int createPid() {
        String pidKey = String.format(USER_PID_PREFIX, TenantHelper.getTenantId());
        long pid = RedisUtils.getAtomicValue(pidKey);
        if (pid == 0) {
            UserDetail userDetail = baseMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().select(UserDetail::getPid).orderByDesc(UserDetail::getPid).last("limit 1"));
            pid = null == userDetail ? 756895L : userDetail.getPid();
            RedisUtils.setAtomicValue(pidKey, pid);
        }
        return Math.toIntExact(RedisUtils.incrAtomicValue(pidKey));
    }

    @Override
    public AppUserLevelResultVo getUserLevel(Long userId) {
        UserDetail userDetail = existById(userId);

        AppUserLevelResultVo result = new AppUserLevelResultVo();
        result.setUserId(userDetail.getUserId());
        result.setUserLevel(userDetail.getUserLevel());
        result.setNickName(userDetail.getNickName());
        result.setIsVip(userDetail.getIsVip());

        return result;
    }

    @Override
    public TableDataInfo<AdminUserDetailPageVo> queryAdminPageList(AdminUserDetailQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserDetail> lqw = baseMapper.buildAdminQueryWrapper(bo);
        Page<UserDetail> result = baseMapper.selectPage(pageQuery.build(), lqw);

        List<AdminUserDetailPageVo> voList = result.getRecords().stream().map(detail -> {
            AdminUserDetailPageVo vo = MapstructUtils.convert(detail, AdminUserDetailPageVo.class);
            vo.setAge(UserUtils.getAgeStr(detail.getBirthday()));
            return vo;
        }).toList();

        return TableDataInfo.build(voList);
    }

    @Override
    public AdminUserDetailInfoVo queryAdminUserDetailInfo(Long userId) {
        UserDetail userDetail = existById(userId);

        AdminUserDetailInfoVo vo = MapstructUtils.convert(userDetail, AdminUserDetailInfoVo.class);
        vo.setAge(UserUtils.getAgeStr(userDetail.getBirthday()));

        // 查询用户标签
        vo.setUserTags(userTagService.listVoByUserId(userId));

        // 查询用户要求标签
        vo.setUserRequireTags(userRequireTagService.listVoByUserId(userId));

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long userId, Integer status) {
        UserDetail userDetail = new UserDetail();
        userDetail.setUserId(userId);
        userDetail.setStatus(status);

        // 这里应该调用RemoteUserService来实现用户状态管理
        remoteUserService.updateUserStatus(userId, status.toString());
        updateById(userDetail);
    }

    @Override
    public AppUserBasicInfoVo getUserBasicInfo(Long userId) {
        // 查询用户基本信息
        RemoteUserVo remoteUserVo = remoteUserService.selectById(userId);
        AssertUtils.notNull(remoteUserVo, "用户信息不存在");

        AppUserBasicInfoVo vo = new AppUserBasicInfoVo();
        vo.setUserId(userId);
        vo.setNickname(remoteUserVo.getNickName());
        vo.setAvatar(remoteUserVo.getAvatar() != null ? remoteUserVo.getAvatar().toString() : null);
        vo.setSex(remoteUserVo.getSex());

        return vo;
    }
}
