<scroll-nav-page wx:if="{{t}}" class="data-v-7a17e612" u-s="{{['content']}}" u-i="7a17e612-0" bind:__l="__l" u-p="{{t}}"><view slot="content"><view class="main-container data-v-7a17e612"><view class="balance-card data-v-7a17e612"><view class="balance-header data-v-7a17e612"><view class="balance-left data-v-7a17e612"><image class="coin-icon data-v-7a17e612" src="{{a}}" mode="aspectFit"></image><view class="balance-info data-v-7a17e612"><text class="balance-title data-v-7a17e612">全部花瓣</text><uni-icons wx:if="{{c}}" class="data-v-7a17e612" bindclick="{{b}}" u-i="7a17e612-1,7a17e612-0" bind:__l="__l" u-p="{{c}}"></uni-icons></view></view></view><view class="balance-amount data-v-7a17e612"><view class="amount-with-arrow data-v-7a17e612" bindtap="{{f}}"><text class="amount-number data-v-7a17e612">{{d}}</text><uni-icons wx:if="{{e}}" class="data-v-7a17e612" u-i="7a17e612-2,7a17e612-0" bind:__l="__l" u-p="{{e}}"></uni-icons></view><button class="recharge-btn data-v-7a17e612" bindtap="{{g}}">去充值</button></view><view class="balance-tips data-v-7a17e612"><text class="tips-text data-v-7a17e612">{{h}} 赠送花瓣 + {{i}} 永久花瓣</text></view></view><signin class="data-v-7a17e612" u-i="7a17e612-3,7a17e612-0" bind:__l="__l"/><view wx:if="{{j}}" class="newbie-tasks data-v-7a17e612"><view class="section-title data-v-7a17e612">新手任务</view><view class="task-list data-v-7a17e612"><view wx:for="{{k}}" wx:for-item="task" wx:key="f" class="task-item data-v-7a17e612" bindtap="{{task.g}}"><view class="task-left data-v-7a17e612"><text class="task-title data-v-7a17e612">{{task.a}}</text><view class="task-reward data-v-7a17e612"><image class="reward-icon data-v-7a17e612" src="{{l}}" mode="aspectFit"></image><text class="reward-text data-v-7a17e612">+{{task.b}}花瓣</text></view></view><button class="{{['task-btn', 'data-v-7a17e612', task.d && 'completed']}}" disabled="{{task.e}}">{{task.c}}</button></view></view></view><view class="level-tasks data-v-7a17e612"><view class="section-title data-v-7a17e612">新手任务</view><view wx:if="{{m}}" class="loading-container data-v-7a17e612"><text class="loading-text data-v-7a17e612">加载中...</text></view><view wx:elif="{{n}}" class="task-list data-v-7a17e612"><view wx:for="{{o}}" wx:for-item="task" wx:key="g" class="{{['task-item', 'data-v-7a17e612', task.h && 'completed']}}"><view class="task-left data-v-7a17e612"><text class="task-title data-v-7a17e612">{{task.a}}</text><view class="task-reward data-v-7a17e612"><image class="reward-icon data-v-7a17e612" src="{{p}}" mode="aspectFit"></image><text class="reward-text data-v-7a17e612">+{{task.b}}花瓣</text></view></view><button class="{{['task-btn', 'data-v-7a17e612', task.d && 'completed']}}" disabled="{{task.e}}" bindtap="{{task.f}}">{{task.c}}</button></view></view><view wx:else class="empty-tasks data-v-7a17e612"><view class="empty-icon data-v-7a17e612"><uni-icons wx:if="{{q}}" class="data-v-7a17e612" u-i="7a17e612-4,7a17e612-0" bind:__l="__l" u-p="{{q}}"></uni-icons></view><text class="empty-text data-v-7a17e612">暂无任务</text></view></view></view><recharge-popup class="r data-v-7a17e612" u-r="rechargePopupRef" bindclose="{{s}}" u-i="7a17e612-5,7a17e612-0" bind:__l="__l"/></view></scroll-nav-page>