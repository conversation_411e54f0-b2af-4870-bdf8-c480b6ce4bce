{"version": 3, "file": "album.js", "sources": ["pagesubs/my/album/album.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcYWxidW1cYWxidW0udnVl"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"我的相册\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<!-- 相册内容 -->\n\t\t\t<view class=\"main-container\">\n\t\t\t<!-- 相册统计 -->\n\t\t\t<view class=\"album-stats\">\n\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t<text class=\"stats-number\">{{ albumStats.imageCount }}</text>\n\t\t\t\t\t<text class=\"stats-label\">张照片</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t<text class=\"stats-number\">1</text>\n\t\t\t\t\t<text class=\"stats-label\">个相册</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-item\">\n\t\t\t\t\t<text class=\"stats-number\">{{ albumStats.totalBrowseCount }}</text>\n\t\t\t\t\t<text class=\"stats-label\">次浏览</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 相册网格 -->\n\t\t\t<view class=\"photo-grid\" v-if=\"photoList.length > 0\">\n\t\t\t\t<view class=\"photo-item\" v-for=\"(photo, index) in photoList\" :key=\"photo.id\"\n\t\t\t\t\t@click=\"previewPhoto(index)\"\n\t\t\t\t\t@longpress=\"showPhotoActions(photo, index)\">\n\t\t\t\t\t<image :src=\"photo.imageUrl\" mode=\"aspectFill\" class=\"photo-image\" :lazy-load=\"true\"></image>\n\t\t\t\t\t<view class=\"photo-overlay\" :class=\"{ 'show': showActionsIndex === index }\">\n\t\t\t\t\t\t<view class=\"photo-actions\">\n\t\t\t\t\t\t\t<view class=\"action-btn\" @click.stop=\"editPhoto(photo)\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"compose\" size=\"16\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"action-btn\" @click.stop=\"deletePhoto(photo)\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"trash\" size=\"16\" color=\"#ff6b6b\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 空状态 - 只在没有照片时显示 -->\n\t\t\t<view class=\"empty-state\" v-if=\"photoList.length === 0\">\n\t\t\t\t<view class=\"empty-icon\">\n\t\t\t\t\t<uni-icons type=\"image\" size=\"80\" color=\"#E5E6F3\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"empty-text\">还没有上传照片</text>\n\t\t\t\t<text class=\"empty-desc\">上传照片让更多人了解你</text>\n\t\t\t</view>\n\n\t\t\t<!-- 上传按钮区域 - 在照片少于6张时显示 -->\n\t\t\t<view class=\"upload-section\" v-if=\"photoList.length < 6\">\n\t\t\t\t<button class=\"upload-btn\" @click=\"handleAddPhoto\">\n\t\t\t\t\t<uni-icons type=\"camera\" size=\"20\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t<text>上传照片</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { uploadImgAndSmallFile } from '@/api/oss/oss'\nimport { uploadAlbumImage, deleteAlbumImages, getStatisticsAlbumData } from '@/api/my/album'\nimport { toast } from '@/utils/common'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\n\n// 相册数据\nconst photoList = ref([])\nconst albumStats = ref({\n\timageCount: 0,\n\ttotalBrowseCount: 0\n})\n\n// 加载状态\nconst isLoading = ref(false)\n\n// 显示操作按钮的图片索引\nconst showActionsIndex = ref(-1)\n\n// 加载相册数据\nconst loadAlbumData = async () => {\n\ttry {\n\t\tisLoading.value = true\n\t\tconst response = await getStatisticsAlbumData()\n\n\t\tif (response.code === 200) {\n\t\t\tphotoList.value = response.data.albumList || []\n\t\t\talbumStats.value = response.data.statistics || {\n\t\t\t\timageCount: 0,\n\t\t\t\ttotalBrowseCount: 0\n\t\t\t}\n\t\t} else {\n\t\t\ttoast(response.msg || '加载相册数据失败')\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载相册数据失败:', error)\n\t\ttoast('网络错误，请重试')\n\t} finally {\n\t\tisLoading.value = false\n\t}\n}\n\n// 添加照片\nconst handleAddPhoto = () => {\n\tuni.chooseImage({\n\t\tcount: 9,\n\t\tsizeType: ['original', 'compressed'],\n\t\tsourceType: ['album', 'camera'],\n\t\tsuccess: async (res) => {\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '照片上传中...'\n\t\t\t\t})\n\n\t\t\t\t// 逐个上传照片\n\t\t\t\tfor (let i = 0; i < res.tempFilePaths.length; i++) {\n\t\t\t\t\tconst filePath = res.tempFilePaths[i]\n\n\t\t\t\t\t// 1. 先上传到OSS\n\t\t\t\t\tconst ossResult = await uploadImgAndSmallFile(filePath, 'user_album.image')\n\t\t\t\t\tconst ossData = ossResult\n\t\t\t\t\tif (ossData.code == 200) {\n\t\t\t\t\t\t// 2. 将图片添加到相册\n\t\t\t\t\t\tconst albumResult = await uploadAlbumImage(ossData.data.ossId)\n\n\t\t\t\t\t\tif (albumResult.code !== 200) {\n\t\t\t\t\t\t\tthrow new Error(albumResult.msg || '添加到相册失败')\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tuni.hideLoading()\n\t\t\t\ttoast('照片上传成功')\n\n\t\t\t\t// 重新加载相册数据\n\t\t\t\tawait loadAlbumData()\n\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading()\n\t\t\t\tconsole.error('上传照片失败:', error)\n\t\t\t\ttoast(error.message || '上传失败，请重试')\n\t\t\t}\n\t\t},\n\t\tfail: (err) => {\n\t\t\tconsole.error('选择照片失败:', err)\n\t\t\ttoast('选择照片失败')\n\t\t}\n\t})\n}\n\n// 预览照片\nconst previewPhoto = (index) => {\n\t// 如果当前显示操作按钮，则隐藏它们\n\tif (showActionsIndex.value === index) {\n\t\tshowActionsIndex.value = -1\n\t\treturn\n\t}\n\n\tconst urls = photoList.value.map(photo => photo.imageUrl)\n\tuni.previewImage({\n\t\tcurrent: index,\n\t\turls: urls\n\t})\n}\n\n// 显示照片操作按钮\nconst showPhotoActions = (photo, index) => {\n\tshowActionsIndex.value = showActionsIndex.value === index ? -1 : index\n}\n\n// 编辑照片\nconst editPhoto = (photo) => {\n\tuni.showActionSheet({\n\t\titemList: ['下载到本地', '删除照片'],\n\t\tsuccess: (res) => {\n\t\t\tswitch (res.tapIndex) {\n\t\t\t\tcase 0:\n\t\t\t\t\tdownloadPhoto(photo)\n\t\t\t\t\tbreak\n\t\t\t\tcase 1:\n\t\t\t\t\tdeletePhoto(photo)\n\t\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t})\n}\n\n// 删除照片\nconst deletePhoto = (photo) => {\n\tuni.showModal({\n\t\ttitle: '确认删除',\n\t\tcontent: '确定要删除这张照片吗？删除后无法恢复。',\n\t\tsuccess: async (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '删除中...'\n\t\t\t\t\t})\n\n\t\t\t\t\tconst result = await deleteAlbumImages([photo.id])\n\n\t\t\t\t\tif (result.code === 200) {\n\t\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\t\ttoast('删除成功')\n\t\t\t\t\t\t// 重新加载相册数据\n\t\t\t\t\t\tawait loadAlbumData()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(result.msg || '删除失败')\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tconsole.error('删除照片失败:', error)\n\t\t\t\t\ttoast(error.message || '删除失败，请重试')\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t})\n}\n\n// 下载照片\nconst downloadPhoto = (photo) => {\n\tuni.downloadFile({\n\t\turl: photo.url,\n\t\tsuccess: (res) => {\n\t\t\tuni.saveImageToPhotosAlbum({\n\t\t\t\tfilePath: res.tempFilePath,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\tfail: () => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '下载失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t})\n}\n\n// 页面初始化\nonMounted(() => {\n\tloadAlbumData()\n})\n</script>\n\n<style lang=\"scss\" scoped>\n// 引入uni.scss变量\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg,\n\t\t\trgba(105, 108, 243, 0.08) 0%,\n\t\t\trgba(105, 108, 243, 0.05) 30%,\n\t\t\trgba(105, 108, 243, 0.02) 60%,\n\t\t\trgba(255, 255, 255, 1) 100%);\n}\n\n.nav-right-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\twidth: 44rpx;\n\theight: 44rpx;\n\tborder-radius: 50%;\n\ttransition: all 0.3s ease;\n\n\t&:active {\n\t\ttransform: scale(0.9);\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t}\n}\n\n.main-container {\n\tmargin-top: 20rpx;\n\tpadding: 24rpx;\n\n\t.album-stats {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.95) 100%);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 32rpx 24rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tbox-shadow: 0 12rpx 40rpx rgba(105, 108, 243, 0.12);\n\t\tbackdrop-filter: blur(15rpx);\n\t\tborder: 1rpx solid rgba(105, 108, 243, 0.1);\n\n\t\t.stats-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\n\t\t\t.stats-number {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #696CF3;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t}\n\n\t\t\t.stats-label {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\t}\n\n\t.photo-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(3, 1fr);\n\t\tgap: 8rpx;\n\n\t\t.photo-item {\n\t\t\tposition: relative;\n\t\t\taspect-ratio: 1;\n\t\t\tborder-radius: 16rpx;\n\t\t\toverflow: hidden;\n\t\t\tbackground: linear-gradient(135deg, #f8f9ff 0%, #e8ebff 100%);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.1);\n\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\n\t\t\t.photo-image {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\ttransition: transform 0.3s ease;\n\t\t\t}\n\n\t\t\t.photo-overlay {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.8) 0%, rgba(155, 157, 245, 0.8) 100%);\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\topacity: 0;\n\t\t\t\ttransition: opacity 0.3s ease;\n\t\t\t\tpointer-events: none;\n\n\t\t\t\t&.show {\n\t\t\t\t\topacity: 1;\n\t\t\t\t\tpointer-events: auto;\n\t\t\t\t}\n\n\t\t\t\t.photo-actions {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tgap: 16rpx;\n\n\t\t\t\t\t.action-btn {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\twidth: 48rpx;\n\t\t\t\t\t\theight: 48rpx;\n\t\t\t\t\t\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.95) 100%);\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.2);\n\t\t\t\t\t\tborder: 1rpx solid rgba(255, 255, 255, 0.8);\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\ttransform: scale(0.9);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\t.photo-image {\n\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.empty-state {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 80rpx 40rpx;\n\t\ttext-align: center;\n\n\t\t.empty-icon {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 32rpx;\n\t\t\tbackground: linear-gradient(135deg, rgba(105, 108, 243, 0.08) 0%, rgba(155, 157, 245, 0.08) 100%);\n\t\t\tborder-radius: 50%;\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.1);\n\t\t\tborder: 2rpx solid rgba(105, 108, 243, 0.1);\n\t\t}\n\n\t\t.empty-text {\n\t\t\tfont-size: 32rpx;\n\t\t\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n\t\t\tbackground-clip: text;\n\t\t\t-webkit-background-clip: text;\n\t\t\t-webkit-text-fill-color: transparent;\n\t\t\tfont-weight: 600;\n\t\t\tmargin-bottom: 16rpx;\n\t\t}\n\n\t\t.empty-desc {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #666;\n\t\t\tline-height: 1.5;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\n\t}\n\n\t.upload-section {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tpadding: 40rpx;\n\n\t\t.upload-btn {\n\t\t\tposition: relative;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tgap: 12rpx;\n\t\t\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n\t\t\tcolor: white;\n\t\t\tborder: none;\n\t\t\tborder-radius: 50rpx;\n\t\t\tpadding: 14rpx 32rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 600;\n\t\t\tmin-width: 200rpx;\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\tbox-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.25);\n\t\t\toverflow: hidden;\n\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: -100%;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n\t\t\t\ttransition: left 0.6s ease;\n\t\t\t}\n\n\t\t\t&::after {\n\t\t\t\tborder: none;\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\ttransform: translateY(-2rpx);\n\t\t\t\tbox-shadow: 0 12rpx 32rpx rgba(105, 108, 243, 0.35);\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\ttransform: translateY(0) scale(0.98);\n\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.2);\n\n\t\t\t\t&::before {\n\t\t\t\t\tleft: 100%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tuni-icons {\n\t\t\t\tfont-size: 32rpx !important;\n\t\t\t}\n\n\t\t\ttext {\n\t\t\t\tfont-weight: 600;\n\t\t\t\tletter-spacing: 1rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/album/album.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "photoList", "ref", "albumStats", "isLoading", "showActionsIndex", "loadAlbumData", "response", "getStatisticsAlbumData", "toast", "error", "uni", "handleAddPhoto", "res", "i", "filePath", "ossData", "uploadImgAndSmallFile", "albumResult", "uploadAlbumImage", "err", "previewPhoto", "index", "urls", "photo", "showPhotoActions", "editPhoto", "downloadPhoto", "deletePhoto", "result", "deleteAlbumImages", "onMounted", "MiniProgramPage"], "mappings": "iTAkEA,MAAMA,EAAgB,IAAW,oFAGjC,MAAMC,EAAYC,EAAG,IAAC,EAAE,EAClBC,EAAaD,EAAAA,IAAI,CACtB,WAAY,EACZ,iBAAkB,CACnB,CAAC,EAGKE,EAAYF,EAAG,IAAC,EAAK,EAGrBG,EAAmBH,EAAAA,IAAI,EAAE,EAGzBI,EAAgB,SAAY,CACjC,GAAI,CACHF,EAAU,MAAQ,GAClB,MAAMG,EAAW,MAAMC,yBAAwB,EAE3CD,EAAS,OAAS,KACrBN,EAAU,MAAQM,EAAS,KAAK,WAAa,CAAE,EAC/CJ,EAAW,MAAQI,EAAS,KAAK,YAAc,CAC9C,WAAY,EACZ,iBAAkB,CAClB,GAEDE,QAAMF,EAAS,KAAO,UAAU,CAEjC,OAAQG,EAAO,CACfC,EAAAA,wDAAc,YAAaD,CAAK,EAChCD,EAAAA,MAAM,UAAU,CAClB,QAAW,CACTL,EAAU,MAAQ,EAClB,CACF,EAGMQ,EAAiB,IAAM,CAC5BD,EAAAA,MAAI,YAAY,CACf,MAAO,EACP,SAAU,CAAC,WAAY,YAAY,EACnC,WAAY,CAAC,QAAS,QAAQ,EAC9B,QAAS,MAAOE,GAAQ,CACvB,GAAI,CACHF,EAAAA,MAAI,YAAY,CACf,MAAO,UACZ,CAAK,EAGD,QAASG,EAAI,EAAGA,EAAID,EAAI,cAAc,OAAQC,IAAK,CAClD,MAAMC,EAAWF,EAAI,cAAcC,CAAC,EAI9BE,EADY,MAAMC,wBAAsBF,EAAU,kBAAkB,EAE1E,GAAIC,EAAQ,MAAQ,IAAK,CAExB,MAAME,EAAc,MAAMC,EAAAA,iBAAiBH,EAAQ,KAAK,KAAK,EAE7D,GAAIE,EAAY,OAAS,IACxB,MAAM,IAAI,MAAMA,EAAY,KAAO,SAAS,CAE7C,CACD,CAEDP,EAAAA,MAAI,YAAa,EACjBF,EAAAA,MAAM,QAAQ,EAGd,MAAMH,EAAe,CAErB,OAAQI,EAAO,CACfC,EAAAA,MAAI,YAAa,EACjBA,EAAAA,MAAA,MAAA,QAAA,qCAAc,UAAWD,CAAK,EAC9BD,QAAMC,EAAM,SAAW,UAAU,CACjC,CACD,EACD,KAAOU,GAAQ,CACdT,EAAAA,MAAA,MAAA,QAAA,qCAAc,UAAWS,CAAG,EAC5BX,EAAAA,MAAM,QAAQ,CACd,CACH,CAAE,CACF,EAGMY,EAAgBC,GAAU,CAE/B,GAAIjB,EAAiB,QAAUiB,EAAO,CACrCjB,EAAiB,MAAQ,GACzB,MACA,CAED,MAAMkB,EAAOtB,EAAU,MAAM,IAAIuB,GAASA,EAAM,QAAQ,EACxDb,EAAAA,MAAI,aAAa,CAChB,QAASW,EACT,KAAMC,CACR,CAAE,CACF,EAGME,EAAmB,CAACD,EAAOF,IAAU,CAC1CjB,EAAiB,MAAQA,EAAiB,QAAUiB,EAAQ,GAAKA,CAClE,EAGMI,EAAaF,GAAU,CAC5Bb,EAAAA,MAAI,gBAAgB,CACnB,SAAU,CAAC,QAAS,MAAM,EAC1B,QAAUE,GAAQ,CACjB,OAAQA,EAAI,SAAQ,CACnB,IAAK,GACJc,EAAcH,CAAK,EACnB,MACD,IAAK,GACJI,EAAYJ,CAAK,EACjB,KACD,CACD,CACH,CAAE,CACF,EAGMI,EAAeJ,GAAU,CAC9Bb,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,QAAS,sBACT,QAAS,MAAOE,GAAQ,CACvB,GAAIA,EAAI,QACP,GAAI,CACHF,EAAAA,MAAI,YAAY,CACf,MAAO,QACb,CAAM,EAED,MAAMkB,EAAS,MAAMC,EAAAA,kBAAkB,CAACN,EAAM,EAAE,CAAC,EAEjD,GAAIK,EAAO,OAAS,IACnBlB,EAAAA,MAAI,YAAa,EACjBF,EAAAA,MAAM,MAAM,EAEZ,MAAMH,EAAe,MAErB,OAAM,IAAI,MAAMuB,EAAO,KAAO,MAAM,CAErC,OAAQnB,EAAO,CACfC,EAAAA,MAAI,YAAa,EACjBA,EAAAA,yDAAc,UAAWD,CAAK,EAC9BD,QAAMC,EAAM,SAAW,UAAU,CACjC,CAEF,CACH,CAAE,CACF,EAGMiB,EAAiBH,GAAU,CAChCb,EAAAA,MAAI,aAAa,CAChB,IAAKa,EAAM,IACX,QAAUX,GAAQ,CACjBF,EAAAA,MAAI,uBAAuB,CAC1B,SAAUE,EAAI,aACd,QAAS,IAAM,CACdF,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,SACZ,CAAM,CACD,EACD,KAAM,IAAM,CACXA,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,MACZ,CAAM,CACD,CACL,CAAI,CACD,EACD,KAAM,IAAM,CACXA,EAAAA,MAAI,UAAU,CACb,MAAO,OACP,KAAM,MACV,CAAI,CACD,CACH,CAAE,CACF,EAGAoB,OAAAA,EAAAA,UAAU,IAAM,CACfzB,EAAe,CAChB,CAAC,ysBC7PD,GAAG,WAAW0B,CAAe"}