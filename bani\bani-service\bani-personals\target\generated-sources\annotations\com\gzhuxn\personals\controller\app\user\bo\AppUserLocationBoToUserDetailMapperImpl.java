package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserDetail;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserLocationBoToUserDetailMapperImpl implements AppUserLocationBoToUserDetailMapper {

    @Override
    public UserDetail convert(AppUserLocationBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetail userDetail = new UserDetail();

        userDetail.setUserId( arg0.getUserId() );
        userDetail.setLon( arg0.getLon() );
        userDetail.setLat( arg0.getLat() );

        return userDetail;
    }

    @Override
    public UserDetail convert(AppUserLocationBo arg0, UserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
