package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupToAppMsgGroupDetailVoMapperImpl implements MsgGroupToAppMsgGroupDetailVoMapper {

    @Override
    public AppMsgGroupDetailVo convert(MsgGroup arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppMsgGroupDetailVo appMsgGroupDetailVo = new AppMsgGroupDetailVo();

        appMsgGroupDetailVo.setId( arg0.getId() );
        appMsgGroupDetailVo.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            appMsgGroupDetailVo.setNum( arg0.getNum().longValue() );
        }
        appMsgGroupDetailVo.setType( arg0.getType() );
        appMsgGroupDetailVo.setBusinessId( arg0.getBusinessId() );
        appMsgGroupDetailVo.setStatus( arg0.getStatus() );
        appMsgGroupDetailVo.setRemark( arg0.getRemark() );

        return appMsgGroupDetailVo;
    }

    @Override
    public AppMsgGroupDetailVo convert(MsgGroup arg0, AppMsgGroupDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getNum() != null ) {
            arg1.setNum( arg0.getNum().longValue() );
        }
        else {
            arg1.setNum( null );
        }
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
