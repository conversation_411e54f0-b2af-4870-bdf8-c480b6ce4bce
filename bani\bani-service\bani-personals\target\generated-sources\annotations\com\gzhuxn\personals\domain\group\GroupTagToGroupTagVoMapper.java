package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.controller.app.group.bo.AppGroupTagBoToGroupTagMapper;
import com.gzhuxn.personals.domain.group.bo.GroupTagBoToGroupTagMapper;
import com.gzhuxn.personals.domain.group.vo.GroupTagVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {GroupTagBoToGroupTagMapper.class,AppGroupTagBoToGroupTagMapper.class},
    imports = {}
)
public interface GroupTagToGroupTagVoMapper extends BaseMapper<GroupTag, GroupTagVo> {
}
