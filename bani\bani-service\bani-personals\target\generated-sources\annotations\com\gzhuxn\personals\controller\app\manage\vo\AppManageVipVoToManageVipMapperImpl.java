package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageVip;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppManageVipVoToManageVipMapperImpl implements AppManageVipVoToManageVipMapper {

    @Override
    public ManageVip convert(AppManageVipVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageVip manageVip = new ManageVip();

        manageVip.setId( arg0.getId() );
        manageVip.setMonths( arg0.getMonths() );
        manageVip.setOriginalAmount( arg0.getOriginalAmount() );
        manageVip.setAmount( arg0.getAmount() );
        manageVip.setCoin( arg0.getCoin() );

        return manageVip;
    }

    @Override
    public ManageVip convert(AppManageVipVo arg0, ManageVip arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMonths( arg0.getMonths() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
