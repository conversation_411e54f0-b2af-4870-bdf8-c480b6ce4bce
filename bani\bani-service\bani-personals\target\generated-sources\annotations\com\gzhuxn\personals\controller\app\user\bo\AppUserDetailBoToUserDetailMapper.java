package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.bo.UserRequireTagBoToUserRequireTagMapper;
import com.gzhuxn.personals.domain.user.bo.UserTagBoToUserTagMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserRequireTagBoToUserRequireTagMapper.class,UserTagBoToUserTagMapper.class},
    imports = {}
)
public interface AppUserDetailBoToUserDetailMapper extends BaseMapper<AppUserDetailBo, UserDetail> {
}
