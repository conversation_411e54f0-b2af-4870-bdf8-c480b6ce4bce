"use strict";const e=require("../../common/vendor.js"),y=require("../../api/my/my.js");if(!Array){const c=e.resolveComponent("nav-tabs"),n=e.resolveComponent("uni-icons"),i=e.resolveComponent("scroll-nav-page");(c+n+i)()}const g=()=>"../../components/nav-tabs/nav-tabs.js",m=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",h=()=>"../../components/scroll-nav-page/scroll-nav-page.js";Math||(g+x+b+m+h)();const b=()=>"./components/buddy-list.js",x=()=>"./components/official-list.js",r={__name:"activity",setup(c){const n=e.ref(0),i=e.ref(0),t=e.ref("buddy"),o=e.ref(!1),u=e.ref([{label:"找搭子",value:"buddy"}]),l=a=>{n.value=a.scrollTop,e.index.__f__("log","at pages/activity/activity.vue:48","页面滚动监听触发:",a.scrollTop)},v=a=>{i.value=a},_=()=>Math.min(n.value/100,1)>.5?"#333333":"#ffffff";e.onPageScroll(l),e.onMounted(()=>{e.index.__f__("log","at pages/activity/activity.vue:71","Activity页面已挂载，滚动监听已注册"),e.index.__f__("log","at pages/activity/activity.vue:72","初始滚动距离:",n.value)}),e.onShow(()=>{e.index.__f__("log","at pages/activity/activity.vue:77","活动页面显示，重新加载数据")});const f=a=>{t.value=a},p=async()=>{if(!o.value){o.value=!0;try{const a=await y.isIdentityVerified();if(a.code===200)if(a.data){const s=t.value==="official"?"/pages/activity/publish/publish":"/pagesubs/activity/buddy/add";e.index.navigateTo({url:s})}else e.index.showModal({title:"需要实名认证",content:"发布活动需要先完成实名认证，是否前往认证？",confirmText:"去认证",cancelText:"取消",success:s=>{s.confirm&&e.index.navigateTo({url:"/pagesubs/my/auth/auth"})}});else e.index.__f__("error","at pages/activity/activity.vue:125","查询认证状态失败:",a.msg),e.index.showToast({title:a.msg||"查询认证状态失败",icon:"none"})}catch(a){e.index.__f__("error","at pages/activity/activity.vue:132","查询认证状态异常:",a),e.index.showToast({title:"网络异常，请重试",icon:"none"})}finally{o.value=!1}}};return(a,s)=>e.e({a:e.o(f),b:e.o(d=>t.value=d),c:e.p({tabs:u.value,"text-color":_(),modelValue:t.value}),d:t.value==="official"},t.value==="official"?{e:e.p({"nav-bar-height":i.value})}:{f:e.p({"nav-bar-height":i.value})},{g:o.value},o.value?{h:e.p({type:"spinner-cycle",size:"24",color:"#fff"})}:{i:e.p({type:"plus",size:"24",color:"#fff"})},{j:o.value?1:"",k:e.o(p),l:e.o(l),m:e.o(v),n:e.p({enableScrollGradient:!1})})}},T=e._export_sfc(r,[["__scopeId","data-v-da48f91d"]]);r.__runtimeHooks=1;wx.createPage(T);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/activity/activity.js.map
