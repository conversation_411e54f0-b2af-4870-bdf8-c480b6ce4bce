package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserGreeting;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * App端用户打招呼创建业务对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@AutoMapper(target = UserGreeting.class, reverseConvertGenerate = false)
public class AppUserGreetingCreateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 对方用户ID
     */
    @NotNull(message = "对方用户ID不能为空")
    private Long oppositeUserId;

    /**
     * 打招呼内容
     */
    @NotBlank(message = "打招呼内容不能为空")
    private String content;
}
