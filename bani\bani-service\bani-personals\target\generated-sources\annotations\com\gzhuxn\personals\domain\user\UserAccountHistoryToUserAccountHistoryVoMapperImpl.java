package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserAccountHistoryVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAccountHistoryToUserAccountHistoryVoMapperImpl implements UserAccountHistoryToUserAccountHistoryVoMapper {

    @Override
    public UserAccountHistoryVo convert(UserAccountHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAccountHistoryVo userAccountHistoryVo = new UserAccountHistoryVo();

        userAccountHistoryVo.setId( arg0.getId() );
        userAccountHistoryVo.setUserId( arg0.getUserId() );
        userAccountHistoryVo.setType( arg0.getType() );
        userAccountHistoryVo.setBusinessId( arg0.getBusinessId() );
        userAccountHistoryVo.setCoin( arg0.getCoin() );
        userAccountHistoryVo.setCoinBefore( arg0.getCoinBefore() );
        userAccountHistoryVo.setCoinAfter( arg0.getCoinAfter() );
        userAccountHistoryVo.setCoinType( arg0.getCoinType() );
        userAccountHistoryVo.setOpType( arg0.getOpType() );
        userAccountHistoryVo.setStatus( arg0.getStatus() );

        return userAccountHistoryVo;
    }

    @Override
    public UserAccountHistoryVo convert(UserAccountHistory arg0, UserAccountHistoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setCoinBefore( arg0.getCoinBefore() );
        arg1.setCoinAfter( arg0.getCoinAfter() );
        arg1.setCoinType( arg0.getCoinType() );
        arg1.setOpType( arg0.getOpType() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
