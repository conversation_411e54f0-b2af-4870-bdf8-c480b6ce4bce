{"version": 3, "file": "gift.js", "sources": ["pagesubs/personals/gift/gift.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccGVyc29uYWxzXGdpZnRcZ2lmdC52dWU"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"点亮礼物\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-content\">\n\t\t\t\t<!-- 背景装饰 -->\n\t\t\t\t<view class=\"background-decoration\">\n\t\t\t\t\t<view class=\"star star-1\"></view>\n\t\t\t\t\t<view class=\"star star-2\"></view>\n\t\t\t\t\t<view class=\"star star-3\"></view>\n\t\t\t\t\t<view class=\"star star-4\"></view>\n\t\t\t\t\t<view class=\"star star-5\"></view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 主要内容 -->\n\t\t\t\t<view class=\"main-container\">\n\t\t\t\t\t<!-- 用户信息头部 -->\n\t\t\t\t\t<view class=\"user-header\" v-if=\"userInfo\">\n\t\t\t\t\t\t<view class=\"user-avatar-container\">\n\t\t\t\t\t\t\t<view class=\"avatar-ring\"></view>\n\t\t\t\t\t\t\t<image :src=\"userInfo.avatar\" mode=\"aspectFill\" class=\"user-avatar\"></image>\n\t\t\t\t\t\t\t<view class=\"avatar-glow\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t<text class=\"user-nickname\">{{ userInfo.nickname }}</text>\n\t\t\t\t\t\t\t<view class=\"user-gender\" v-if=\"userInfo.sex !== undefined\">\n\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\n\t\t\t\t\t\t\t\t\t:class=\"userInfo.sex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\n\t\t\t\t\t\t\t\t\t:style=\"{ color: userInfo.sex === '0' ? '#4A90E2' : '#E91E63' }\"></text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"gift-count-info\">\n\t\t\t\t\t\t\t<view class=\"count-badge\">\n\t\t\t\t\t\t\t\t<text class=\"count-text\">已点亮 {{ totalGiftCount }}/{{ giftList.length }}</text>\n\t\t\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: (totalGiftCount / giftList.length * 100) + '%' }\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 礼物网格 -->\n\t\t\t\t\t<view class=\"gift-grid\" v-if=\"giftList.length > 0\">\n\t\t\t\t\t\t<view class=\"gift-item\" v-for=\"(gift, index) in giftList\" :key=\"gift.id\"\n\t\t\t\t\t\t\t@click=\"handleGiftClick(gift)\"\n\t\t\t\t\t\t\t:class=\"{ 'gift-received': gift.isReceived }\">\n\t\t\t\t\t\t\t<view class=\"gift-icon-container\" :class=\"{ 'lit': gift.isReceived }\">\n\t\t\t\t\t\t\t\t<view class=\"gift-border\"></view>\n\t\t\t\t\t\t\t\t<image :src=\"gift.icon\" mode=\"aspectFill\" class=\"gift-icon\"\n\t\t\t\t\t\t\t\t\t:class=\"{ 'grayscale': !gift.isReceived }\"></image>\n\t\t\t\t\t\t\t\t<view class=\"gift-glow\" v-if=\"gift.isReceived\"></view>\n\t\t\t\t\t\t\t\t<view class=\"gift-sparkle\" v-if=\"gift.isReceived\">\n\t\t\t\t\t\t\t\t\t<view class=\"sparkle sparkle-1\"></view>\n\t\t\t\t\t\t\t\t\t<view class=\"sparkle sparkle-2\"></view>\n\t\t\t\t\t\t\t\t\t<view class=\"sparkle sparkle-3\"></view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"lock-icon\" v-if=\"!gift.isReceived\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"locked\" size=\"20\" color=\"rgba(255,255,255,0.5)\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"gift-name\">{{ gift.name }}</text>\n\t\t\t\t\t\t\t<view class=\"gift-count-badge\" v-if=\"gift.isReceived && gift.receivedCount > 0\">\n\t\t\t\t\t\t\t\t<text class=\"gift-count\">{{ gift.receivedCount }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 加载状态 -->\n\t\t\t\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t\t\t\t<uni-load-more status=\"loading\"></uni-load-more>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t<view v-else-if=\"giftList.length === 0\" class=\"empty-container\">\n\t\t\t\t\t\t<view class=\"empty-content\">\n\t\t\t\t\t\t\t<uni-icons type=\"gift\" size=\"80\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无礼物数据</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\nimport { getUserBasicInfo } from '@/api/user/user'\nimport { getLitGiftList } from '@/api/personals/gift'\nimport globalConfig from '@/config'\n\n// 页面状态\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0)\nconst loading = ref(true)\n\n// 用户信息\nconst userInfo = ref(null)\nconst userId = ref(null)\n\n// 礼物数据\nconst giftList = ref([])\nconst totalGiftCount = ref(0)\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 页面加载\nonLoad((options) => {\n\tif (options.userId) {\n\t\tuserId.value = options.userId\n\t\tloadData()\n\t} else {\n\t\tloading.value = false\n\t\tuni.showToast({\n\t\t\ttitle: '参数错误',\n\t\t\ticon: 'none'\n\t\t})\n\t}\n})\n\n// 加载数据\nconst loadData = async () => {\n\ttry {\n\t\tloading.value = true\n\n\t\t// 并行加载用户信息和礼物列表\n\t\tconst [userResponse, giftResponse] = await Promise.all([\n\t\t\tgetUserBasicInfo(userId.value),\n\t\t\tgetLitGiftList(userId.value)\n\t\t])\n\n\t\tif (userResponse.code === 200) {\n\t\t\tuserInfo.value = userResponse.data\n\t\t}\n\n\t\tif (giftResponse.code === 200) {\n\t\t\tgiftList.value = giftResponse.data.giftList || []\n\t\t\ttotalGiftCount.value = giftResponse.data.totalGiftCount || 0\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载数据失败:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '加载失败',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tloading.value = false\n\t}\n}\n\n// 处理礼物点击\nconst handleGiftClick = (gift) => {\n\tif (gift.isReceived) {\n\t\t// 显示礼物详情或其他操作\n\t\tuni.showToast({\n\t\t\ttitle: `${gift.name} x${gift.receivedCount}`,\n\t\t\ticon: 'none'\n\t\t})\n\t} else {\n\t\t// 显示未点亮提示\n\t\tuni.showToast({\n\t\t\ttitle: '该礼物尚未点亮',\n\t\t\ticon: 'none'\n\t\t})\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-content {\n\tposition: relative;\n\tmin-height: 100vh;\n\tbackground: #ffffff;\n\toverflow: hidden;\n}\n\n.background-decoration {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tpointer-events: none;\n\t\tz-index: 0;\n\n\t\t.star {\n\t\t\tposition: absolute;\n\t\t\twidth: 4rpx;\n\t\t\theight: 4rpx;\n\t\t\tbackground: rgba(200, 200, 200, 0.6);\n\t\t\tborder-radius: 50%;\n\t\t\tanimation: twinkle 3s infinite;\n\n\t\t&.star-1 {\n\t\t\ttop: 20%;\n\t\t\tleft: 10%;\n\t\t\tanimation-delay: 0s;\n\t\t}\n\n\t\t&.star-2 {\n\t\t\ttop: 40%;\n\t\t\tright: 15%;\n\t\t\tanimation-delay: 1s;\n\t\t}\n\n\t\t&.star-3 {\n\t\t\ttop: 60%;\n\t\t\tleft: 20%;\n\t\t\tanimation-delay: 2s;\n\t\t}\n\n\t\t&.star-4 {\n\t\t\ttop: 80%;\n\t\t\tright: 25%;\n\t\t\tanimation-delay: 0.5s;\n\t\t}\n\n\t\t&.star-5 {\n\t\t\ttop: 30%;\n\t\t\tleft: 50%;\n\t\t\tanimation-delay: 1.5s;\n\t\t}\n\t}\n}\n\n.main-container {\n\tposition: relative;\n\tz-index: 1;\n\tmin-height: 100vh;\n\tpadding: 20rpx;\n}\n\n.user-header {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 60rpx 20rpx 40rpx;\n\t\tmargin-bottom: 40rpx;\n\t\tbackground: rgba(248, 248, 248, 0.8);\n\t\tborder-radius: 30rpx;\n\t\tbackdrop-filter: blur(20rpx);\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\tborder: 1rpx solid rgba(230, 230, 230, 0.5);\n\n\t.user-avatar-container {\n\t\tposition: relative;\n\t\tmargin-bottom: 24rpx;\n\n\t\t.avatar-ring {\n\t\t\tposition: absolute;\n\t\t\ttop: -8rpx;\n\t\t\tleft: -8rpx;\n\t\t\twidth: 136rpx;\n\t\t\theight: 136rpx;\n\t\t\tborder: 3rpx solid transparent;\n\t\t\tborder-radius: 50%;\n\t\t\tbackground: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4) border-box;\n\t\t\tmask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);\n\t\t\tmask-composite: exclude;\n\t\t\tanimation: rotate 4s linear infinite;\n\t\t}\n\n\t\t.user-avatar {\n\t\t\tposition: relative;\n\t\t\tz-index: 2;\n\t\t\twidth: 120rpx;\n\t\t\theight: 120rpx;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: 4rpx solid rgba(255, 255, 255, 0.8);\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);\n\t\t}\n\n\t\t.avatar-glow {\n\t\t\tposition: absolute;\n\t\t\ttop: -20rpx;\n\t\t\tleft: -20rpx;\n\t\t\twidth: 160rpx;\n\t\t\theight: 160rpx;\n\t\t\tbackground: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);\n\t\t\tborder-radius: 50%;\n\t\t\tanimation: pulse 2s ease-in-out infinite;\n\t\t}\n\t}\n\n\t.user-info {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 12rpx;\n\t\tmargin-bottom: 16rpx;\n\n\t\t.user-nickname {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333333;\n\t\t}\n\n\t\t.user-gender {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.gender-icon {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.gift-count-info {\n\t\t.count-badge {\n\t\t\t\tbackground: rgba(240, 240, 240, 0.8);\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tpadding: 12rpx 24rpx;\n\t\t\t\tbackdrop-filter: blur(10rpx);\n\t\t\t\tborder: 1rpx solid rgba(220, 220, 220, 0.5);\n\n\t\t\t.count-text {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666666;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\n\t\t\t.progress-bar {\n\t\t\t\t\twidth: 200rpx;\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\tbackground: rgba(200, 200, 200, 0.5);\n\t\t\t\t\tborder-radius: 3rpx;\n\t\t\t\t\toverflow: hidden;\n\n\t\t\t\t.progress-fill {\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tbackground: linear-gradient(90deg, #FFD700, #FFA500);\n\t\t\t\t\tborder-radius: 3rpx;\n\t\t\t\t\ttransition: width 0.8s ease;\n\t\t\t\t\tbox-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.gift-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(4, 1fr);\n\tgap: 24rpx;\n\tpadding: 0 20rpx;\n\n\t.gift-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 24rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tbackground: rgba(250, 250, 250, 0.9);\n\t\t\tbackdrop-filter: blur(15rpx);\n\t\t\tborder: 1rpx solid rgba(230, 230, 230, 0.6);\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tleft: -100%;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground: linear-gradient(90deg, transparent, rgba(240, 240, 240, 0.3), transparent);\n\t\t\ttransition: left 0.6s ease;\n\t\t}\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\n\t\t&:hover::before {\n\t\t\tleft: 100%;\n\t\t}\n\n\t\t&.gift-received {\n\t\t\t\tbackground: rgba(255, 248, 220, 0.9);\n\t\t\t\tborder-color: rgba(255, 215, 0, 0.4);\n\t\t\t\tbox-shadow: 0 6rpx 30rpx rgba(255, 215, 0, 0.2);\n\t\t\t}\n\n\t\t.gift-icon-container {\n\t\t\tposition: relative;\n\t\t\twidth: 88rpx;\n\t\t\theight: 88rpx;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tborder-radius: 16rpx;\n\t\t\toverflow: visible;\n\n\t\t\t.gift-border {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -2rpx;\n\t\t\t\tleft: -2rpx;\n\t\t\t\twidth: 92rpx;\n\t\t\t\theight: 92rpx;\n\t\t\t\tborder: 2rpx solid rgba(220, 220, 220, 0.6);\n\t\t\t\tborder-radius: 18rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t}\n\n\t\t\t.gift-icon {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\ttransition: all 0.4s ease;\n\n\t\t\t\t&.grayscale {\n\t\t\t\t\tfilter: grayscale(100%) brightness(0.5) contrast(0.8);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.gift-glow {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: -15rpx;\n\t\t\t\tleft: -15rpx;\n\t\t\t\tright: -15rpx;\n\t\t\t\tbottom: -15rpx;\n\t\t\t\tbackground: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 165, 0, 0.2) 50%, transparent 80%);\n\t\t\t\tborder-radius: 25rpx;\n\t\t\t\tanimation: glow 2.5s ease-in-out infinite alternate;\n\t\t\t}\n\n\t\t\t.gift-sparkle {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tpointer-events: none;\n\n\t\t\t\t.sparkle {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\twidth: 6rpx;\n\t\t\t\t\theight: 6rpx;\n\t\t\t\t\tbackground: #FFD700;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tanimation: sparkle 2s infinite;\n\n\t\t\t\t\t&.sparkle-1 {\n\t\t\t\t\t\ttop: 10rpx;\n\t\t\t\t\t\tleft: 10rpx;\n\t\t\t\t\t\tanimation-delay: 0s;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sparkle-2 {\n\t\t\t\t\t\ttop: 20rpx;\n\t\t\t\t\t\tright: 15rpx;\n\t\t\t\t\t\tanimation-delay: 0.7s;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.sparkle-3 {\n\t\t\t\t\t\tbottom: 15rpx;\n\t\t\t\t\t\tleft: 20rpx;\n\t\t\t\t\t\tanimation-delay: 1.4s;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.lock-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: -5rpx;\n\t\t\t\tright: -5rpx;\n\t\t\t\twidth: 32rpx;\n\t\t\t\theight: 32rpx;\n\t\t\t\tbackground: rgba(0, 0, 0, 0.6);\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\n\t\t\t}\n\n\t\t\t&.lit {\n\t\t\t\t.gift-border {\n\t\t\t\t\tborder-color: rgba(255, 215, 0, 0.8);\n\t\t\t\t\tbox-shadow: 0 0 20rpx rgba(255, 215, 0, 0.3);\n\t\t\t\t}\n\n\t\t\t\t.gift-icon {\n\t\t\t\t\tfilter: none;\n\t\t\t\t\tbox-shadow: 0 4rpx 25rpx rgba(255, 215, 0, 0.4);\n\t\t\t\t\ttransform: scale(1.05);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.gift-name {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #333333;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 8rpx;\n\t\t\tfont-weight: 500;\n\t\t\tline-height: 1.2;\n\t\t}\n\n\t\t.gift-count-badge {\n\t\t\tbackground: linear-gradient(45deg, #FFD700, #FFA500);\n\t\t\tborder-radius: 12rpx;\n\t\t\tpadding: 4rpx 12rpx;\n\t\t\tmin-width: 32rpx;\n\t\t\theight: 24rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);\n\n\t\t\t.gift-count {\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tline-height: 1;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.loading-container,\n.empty-container {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\theight: 400rpx;\n}\n\n.empty-content {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 20rpx;\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t}\n}\n\n@keyframes glow {\n\t0% {\n\t\topacity: 0.4;\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\topacity: 0.8;\n\t\ttransform: scale(1.05);\n\t}\n\t100% {\n\t\topacity: 0.4;\n\t\ttransform: scale(1);\n\t}\n}\n\n@keyframes sparkle {\n\t0%, 100% {\n\t\topacity: 0;\n\t\ttransform: scale(0);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: scale(1);\n\t}\n}\n\n@keyframes twinkle {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\topacity: 1;\n\t\ttransform: scale(1.2);\n\t}\n}\n\n@keyframes rotate {\n\t0% {\n\t\ttransform: rotate(0deg);\n\t}\n\t100% {\n\t\ttransform: rotate(360deg);\n\t}\n}\n\n@keyframes pulse {\n\t0%, 100% {\n\t\topacity: 0.3;\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\topacity: 0.6;\n\t\ttransform: scale(1.1);\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/personals/gift/gift.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "loading", "userInfo", "userId", "giftList", "totalGiftCount", "onPageScroll", "e", "onLoad", "options", "loadData", "uni", "userResponse", "giftResponse", "getUserBasicInfo", "getLitGiftList", "error", "handleGiftClick", "gift", "MiniProgramPage"], "mappings": "2iBA4FA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAA,EAAA,IAAA,CAAA,EACA,MAAAC,EAAAD,EAAA,IAAA,EAAA,EAGAE,EAAAF,EAAA,IAAA,IAAA,EACAG,EAAAH,EAAA,IAAA,IAAA,EAGAI,EAAAJ,EAAA,IAAA,EAAA,EACAK,EAAAL,EAAA,IAAA,CAAA,EAGAM,EAAA,aAAAC,GAAA,CACAR,EAAA,MAAAQ,EAAA,SACA,CAAA,EAGAC,EAAA,OAAAC,GAAA,CACAA,EAAA,QACAN,EAAA,MAAAM,EAAA,OACAC,EAAA,IAEAT,EAAA,MAAA,GACAU,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,MACA,CAAA,EAEA,CAAA,EAGA,MAAAD,EAAA,SAAA,CACA,GAAA,CACAT,EAAA,MAAA,GAGA,KAAA,CAAAW,EAAAC,CAAA,EAAA,MAAA,QAAA,IAAA,CACAC,EAAA,iBAAAX,EAAA,KAAA,EACAY,EAAA,eAAAZ,EAAA,KAAA,CACA,CAAA,EAEAS,EAAA,OAAA,MACAV,EAAA,MAAAU,EAAA,MAGAC,EAAA,OAAA,MACAT,EAAA,MAAAS,EAAA,KAAA,UAAA,CAAA,EACAR,EAAA,MAAAQ,EAAA,KAAA,gBAAA,EAEA,OAAAG,EAAA,CACAL,EAAAA,MAAA,MAAA,QAAA,0CAAA,UAAAK,CAAA,EACAL,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,MACA,CAAA,CACA,QAAA,CACAV,EAAA,MAAA,EACA,CACA,EAGAgB,EAAAC,GAAA,CACAA,EAAA,WAEAP,EAAAA,MAAA,UAAA,CACA,MAAA,GAAAO,EAAA,IAAA,KAAAA,EAAA,aAAA,GACA,KAAA,MACA,CAAA,EAGAP,EAAAA,MAAA,UAAA,CACA,MAAA,UACA,KAAA,MACA,CAAA,CAEA,uhCCvKA,GAAG,WAAWQ,CAAe"}