package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageRecharge;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageRechargeBoToManageRechargeMapperImpl implements ManageRechargeBoToManageRechargeMapper {

    @Override
    public ManageRecharge convert(ManageRechargeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageRecharge manageRecharge = new ManageRecharge();

        manageRecharge.setSearchValue( arg0.getSearchValue() );
        manageRecharge.setCreateDept( arg0.getCreateDept() );
        manageRecharge.setCreateBy( arg0.getCreateBy() );
        manageRecharge.setCreateTime( arg0.getCreateTime() );
        manageRecharge.setUpdateBy( arg0.getUpdateBy() );
        manageRecharge.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            manageRecharge.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        manageRecharge.setId( arg0.getId() );
        manageRecharge.setOriginalAmount( arg0.getOriginalAmount() );
        manageRecharge.setAmount( arg0.getAmount() );
        manageRecharge.setDiscountRate( arg0.getDiscountRate() );
        manageRecharge.setDiscountType( arg0.getDiscountType() );
        manageRecharge.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        manageRecharge.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        manageRecharge.setRechargeNum( arg0.getRechargeNum() );
        manageRecharge.setCoin( arg0.getCoin() );

        return manageRecharge;
    }

    @Override
    public ManageRecharge convert(ManageRechargeBo arg0, ManageRecharge arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setDiscountRate( arg0.getDiscountRate() );
        arg1.setDiscountType( arg0.getDiscountType() );
        arg1.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        arg1.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        arg1.setRechargeNum( arg0.getRechargeNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
