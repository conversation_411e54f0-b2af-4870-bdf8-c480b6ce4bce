package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserFollowBoToUserFollowMapper;
import com.gzhuxn.personals.domain.user.vo.UserFollowVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserFollowBoToUserFollowMapper.class,UserFollowToAppFollowUserFollowVoMapper.class},
    imports = {}
)
public interface UserFollowToUserFollowVoMapper extends BaseMapper<UserFollow, UserFollowVo> {
}
