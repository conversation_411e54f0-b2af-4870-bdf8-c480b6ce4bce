package com.gzhuxn.personals.domain.order;

import com.gzhuxn.personals.controller.app.order.bo.AppCreateOrderBoToUserOrderMapper;
import com.gzhuxn.personals.controller.app.order.vo.AppUserOrderVo;
import com.gzhuxn.personals.domain.order.bo.UserOrderBoToUserOrderMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppCreateOrderBoToUserOrderMapper.class,UserOrderBoToUserOrderMapper.class,UserOrderToUserOrderVoMapper.class},
    imports = {}
)
public interface UserOrderToAppUserOrderVoMapper extends BaseMapper<UserOrder, AppUserOrderVo> {
}
