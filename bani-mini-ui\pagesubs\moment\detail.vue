<template>
	<scroll-nav-page title="动态详情" :show-back="true">
		<template #content>
			<view class="moment-detail-page">
				<!-- 主要内容区域 -->
				<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
					<!-- 动态内容 -->
					<view class="moment-content">
						<!-- 用户信息 -->
						<view class="user-info">
							<view class="user-avatar">
								<image :src="momentDetail.user.avatar" mode="aspectFill" class="avatar-img"></image>
								<view class="verified-badge" v-if="momentDetail.user.isIdentity">
									<uni-icons type="checkmarkempty" size="12" color="#fff"></uni-icons>
								</view>
							</view>
							<view class="user-details">
								<view class="user-name-row">
									<text class="username">{{ momentDetail.user.nickname }}</text>
									<view class="user-tags">
										<text class="age-tag">{{ momentDetail.user.age }}年</text>
										<text class="location-tag">{{ momentDetail.user.height }}</text>
										<text class="occupation-tag">{{ momentDetail.user.city }}</text>
									</view>
								</view>
							</view>
							<view class="follow-btn" @click.stop="handleFollow" v-if="!momentDetail.user.isMe">
								<text class="follow-text">{{ momentDetail.isFollowed ? '已关注' : '关注' }}</text>
							</view>
						</view>

						<!-- 动态文本内容 -->
						<view class="moment-text" v-if="momentDetail.content">
							<text class="content-text">{{ momentDetail.content }}</text>
						</view>

						<!-- 动态图片 -->
						<view class="moment-images" v-if="momentDetail.images && momentDetail.images.length > 0">
							<view class="image-grid" :class="getImageGridClass(momentDetail.images.length)">
								<view class="image-item" v-for="(image, index) in momentDetail.images" :key="index"
									@click="previewImage(momentDetail.images, image)">
									<image :src="image" mode="aspectFill" class="moment-image"></image>
								</view>
							</view>
						</view>

						<!-- 话题标签 -->
						<view class="topic-tags" v-if="momentDetail.tags && momentDetail.tags.length > 0">
							<view class="topic-tag" v-for="(tag, index) in momentDetail.tags" :key="index"
								@click="handleTopicTagClick(tag)">
								<text class="topic-tag-text">#{{ tag.tagValName }}</text>
							</view>
						</view>

						<!-- 发布时间和位置 -->
						<view class="moment-meta">
							<text class="publish-time">{{ momentDetail.time }}</text>
							<text class="location" v-if="momentDetail.location">{{ momentDetail.location }}</text>
						</view>

						<!-- 互动统计 -->
						<view class="interaction-stats">
							<view class="stats-item">
								<text class="iconfont bani-dianzan" :style="{
									color: '#999',
									fontSize: '16px'
								}"></text>
								<text class="stats-text">{{ momentDetail.likes }}人觉得很赞</text>
							</view>
							<view class="stats-item" v-if="momentDetail.pv > 0">
								<uni-icons type="eye" size="16" color="#999"></uni-icons>
								<text class="stats-text">{{ momentDetail.pv }}次浏览</text>
							</view>
						</view>
					</view>

					<!-- 评论区域 -->
					<view class="comments-section">
						<view class="section-header">
							<text class="section-title">评论 {{ commentTotal || commentList.length }}</text>
						</view>

						<!-- 评论列表 -->
						<view class="comment-list">
							<view class="comment-item" v-for="comment in commentList" :key="comment.id">
								<view class="comment-avatar">
									<image :src="comment.avatar" mode="aspectFill" class="avatar-img"></image>
								</view>
								<view class="comment-content">
									<view class="comment-header">
										<text class="comment-username">{{ comment.nickname }}</text>
										<view class="comment-menu" v-if="comment.isMe"
											@click="showCommentMenu(comment)">
											<text class="menu-dots">...</text>
										</view>
									</view>
									<view class="comment-text">
										<text class="comment-content-text">{{ comment.content }}</text>
									</view>
									<view class="comment-meta">
										<text class="comment-time">{{ comment.createTime }}</text>
									</view>

									<!-- 评论操作按钮 -->
									<view class="comment-actions">
										<view class="action-btn" @click="handleReplyComment(comment)">
											<uni-icons type="chat" size="14" color="#999"></uni-icons>
											<text class="action-text">回复</text>
										</view>
										<view class="action-btn" v-if="comment.childCount > 0"
											@click="handleShowReplies(comment)">
											<uni-icons :type="expandedComments.has(comment.id) ? 'up' : 'down'"
												size="14" color="#999"></uni-icons>
											<text class="action-text reply-count-text">{{ comment.childCount
												}}条回复</text>
										</view>

									</view>

									<!-- 子评论列表 -->
									<view class="child-comments"
										v-if="expandedComments.has(comment.id) && childCommentsMap.get(comment.id)">
										<view class="child-comment-item"
											v-for="childComment in childCommentsMap.get(comment.id)"
											:key="childComment.id">
											<view class="child-comment-content">
												<image class="child-avatar" :src="childComment.avatar"
													mode="aspectFill"></image>
												<view class="child-comment-main">
													<view class="child-comment-header">
														<view class="child-header-left">
															<text class="child-nickname">{{ childComment.nickname
																}}</text>
															<text class="child-comment-time">{{ childComment.createTime
																}}</text>
														</view>
														<view class="child-comment-menu" v-if="childComment.isMe"
															@click="showCommentMenu(childComment)">
															<text class="menu-dots">...</text>
														</view>
													</view>
													<text class="child-comment-text">{{ childComment.content }}</text>
													<view class="child-comment-actions">
														<view class="action-btn"
															@click="handleReplyComment(childComment)">
															<uni-icons type="chat" size="12" color="#999"></uni-icons>
															<text class="action-text">回复</text>
														</view>

													</view>
												</view>
											</view>
										</view>

										<!-- 加载更多子评论 -->
										<view class="load-more-child" v-if="childCommentLoading.get(comment.id)">
											<uni-icons type="spinner-cycle" size="16" color="#999"></uni-icons>
											<text class="loading-text">加载中...</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 底部操作栏 -->
				<view class="bottom-action-bar">
					<view class="comment-input-area" @click="focusCommentInput">
						<!-- 回复前缀显示 -->
						<view class="reply-prefix" v-if="replyPrefix">
							<text class="prefix-text">{{ replyPrefix }}</text>
							<view class="cancel-reply" @click="cancelReply">
								<uni-icons type="close" size="16" color="#999"></uni-icons>
							</view>
						</view>
						<!-- 输入框和发送按钮行 -->
						<view class="input-row">
							<input ref="commentInputRef" class="comment-input"
								:placeholder="replyPrefix ? '输入回复内容...' : '说点什么吧'" v-model="commentText"
								@focus="handleInputFocus" @blur="handleInputBlur" @confirm="handleSendComment"
								confirm-type="send" />
							<view class="send-btn" v-if="commentText.trim()" @click="handleSendComment">
								<uni-icons type="paperplane" size="18" color="#fff"></uni-icons>
							</view>
						</view>
					</view>
					<view class="action-buttons" v-if="!isInputFocused">
						<view class="action-btn" @click="handleShare">
							<uni-icons type="redo" size="20" color="#999"></uni-icons>
							<text class="action-text">分享</text>
						</view>
						<view class="action-btn" @click="handleLike">
							<text class="iconfont" :class="momentDetail.isLiked ? 'bani-dianzan-fill' : 'bani-dianzan'"
								:style="{
									color: momentDetail.isLiked ? '#696CF3' : '#999',
									fontSize: '20px'
								}"></text>
							<text class="action-text" :style="{ color: momentDetail.isLiked ? '#696CF3' : '#999' }">
								{{ momentDetail.likes }}
							</text>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { getRecommendMomentDetail, formatRecommendMoment } from '@/api/moment/recommend'
import { toggleMomentLike } from '@/api/my/like'
import { toggleUserFollow } from '@/api/my/follow'
import {
	getRootComments,
	getChildComments,
	addRootComment,
	replyComment,
	deleteSingleComment,
	COMMENT_TYPE
} from '@/api/my/comment'
import $store from '@/store'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)

// 动态详情数据
const momentDetail = ref({})
// 评论列表
const commentList = ref([])
// 评论输入内容
const commentText = ref('')
// 输入框聚焦状态
const isInputFocused = ref(false)
// 输入框引用
const commentInputRef = ref(null)

// 评论相关状态
const commentLoading = ref(false)
const commentTotal = ref(0)
const commentPageNum = ref(1)
const commentPageSize = ref(10)
const hasMoreComments = ref(true)

// 当前动态ID
const currentMomentId = ref(null)

// 回复相关状态
const replyTarget = ref(null) // 当前回复的目标评论
const replyPrefix = ref('') // 回复前缀文本

// 子评论相关状态
const childCommentsMap = ref(new Map()) // 存储每个根评论的子评论列表
const childCommentLoading = ref(new Map()) // 存储每个根评论的加载状态
const expandedComments = ref(new Set()) // 存储已展开的评论ID

// 页面加载
onLoad((options) => {
	const momentId = options.id
	if (momentId) {
		currentMomentId.value = momentId
		loadMomentDetail(momentId)
		loadCommentList(momentId)
	}
})

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 加载动态详情
const loadMomentDetail = async (momentId) => {
	getRecommendMomentDetail(momentId).then(response => {
		// 格式化动态数据
		momentDetail.value = formatRecommendMoment(response.data)
	})
}


// 加载评论列表
const loadCommentList = async (momentId, isRefresh = true) => {
	if (commentLoading.value) return

	try {
		commentLoading.value = true

		// 如果是刷新，重置分页
		if (isRefresh) {
			commentPageNum.value = 1
			commentList.value = []
		}

		const params = {
			type: COMMENT_TYPE.MOMENT,
			businessId: momentId,
			pageSize: commentPageSize.value,
			pageNum: commentPageNum.value,
			orderByColumn: 'createTime',
			isAsc: 'desc'
		}

		console.log('加载评论列表，参数:', params)
		const response = await getRootComments(params)

		if (response.code === 200 && response.data) {
			const { total, rows } = response.data

			// 格式化评论数据
			const formattedComments = rows.map(comment => ({
				id: comment.id,
				rootId: comment.rootId || null, // 根评论ID，根评论为null
				avatar: comment.oppAvatar,
				nickname: comment.oppNickName,
				uid: comment.uid,
				content: comment.content,
				images: comment.images,
				createTime: comment.createTime,
				childCount: comment.childCount || 0,
				isMe: $store.isMe(comment.uid) // 使用store判断是否为本人评论
			}))

			if (isRefresh) {
				commentList.value = formattedComments
			} else {
				commentList.value.push(...formattedComments)
			}

			commentTotal.value = response.data.totalCount
			hasMoreComments.value = commentList.value.length < total

			console.log('评论列表加载成功:', commentList.value)
		} else {
			console.error('加载评论列表失败:', response.msg)
			uni.showToast({
				title: response.msg || '加载失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('加载评论列表异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	} finally {
		commentLoading.value = false
	}
}

// 获取图片网格样式类
const getImageGridClass = (imageCount) => {
	if (imageCount === 1) return 'single-image'
	if (imageCount === 2) return 'two-images'
	if (imageCount === 3) return 'three-images'
	return 'multiple-images'
}

// 图片预览
const previewImage = (images, current) => {
	uni.previewImage({
		urls: images,
		current: current
	})
}

// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}

// 计算导航栏图标颜色
const getNavIconColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#696CF3' : '#ffffff'
}

// 显示更多选项
const showMoreOptions = () => {
	uni.showActionSheet({
		itemList: ['举报', '屏蔽'],
		success: (res) => {
			console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
		}
	})
}

// 点赞
const handleLike = async () => {
	try {
		console.log('点赞动态:', momentDetail.value.id, '当前状态:', momentDetail.value.isLiked)

		// 调用点赞API
		const response = await toggleMomentLike(momentDetail.value.id, momentDetail.value.isLiked)

		if (response.code === 200) {
			// 更新本地状态
			momentDetail.value.isLiked = !momentDetail.value.isLiked
			if (momentDetail.value.isLiked) {
				momentDetail.value.likes = (momentDetail.value.likes || 0) + 1
				uni.showToast({
					title: '点赞成功',
					icon: 'none',
					duration: 1000
				})
			} else {
				momentDetail.value.likes = Math.max((momentDetail.value.likes || 0) - 1, 0)
				uni.showToast({
					title: '取消点赞',
					icon: 'none',
					duration: 1000
				})
			}
		} else {
			console.error('点赞操作失败:', response.msg || '未知错误')
			uni.showToast({
				title: response.msg || '操作失败，请重试',
				icon: 'none',
				duration: 2000
			})
		}
	} catch (error) {
		console.error('点赞操作异常:', error)
		uni.showToast({
			title: '网络错误，请检查网络连接',
			icon: 'none',
			duration: 2000
		})
	}
}

// 关注用户
const handleFollow = async () => {
	console.log('关注用户:', momentDetail.value.user.id, '当前状态:', momentDetail.value.isFollowed)

	// 调用关注/取消关注API
	const response = await toggleUserFollow(momentDetail.value.user.id, momentDetail.value.isFollowed)

	if (response.code === 200) {
		// 更新本地状态
		momentDetail.value.isFollowed = !momentDetail.value.isFollowed
		uni.showToast({
			title: momentDetail.value.isFollowed ? '关注成功' : '已取消关注',
			icon: 'none',
			duration: 1500
		})
	} else {
		console.error('关注操作失败:', response.msg || '未知错误')
		uni.showToast({
			title: response.msg || '操作失败，请重试',
			icon: 'none',
			duration: 2000
		})
	}
}

// 分享
const handleShare = () => {
	uni.showToast({
		title: '分享功能开发中',
		icon: 'none'
	})
}

// 点击输入区域聚焦输入框
const focusCommentInput = () => {
	if (commentInputRef.value) {
		commentInputRef.value.focus()
	}
}

// 输入框聚焦
const handleInputFocus = () => {
	isInputFocused.value = true
}

// 输入框失焦
const handleInputBlur = () => {
	isInputFocused.value = false
}

// 发送评论
const handleSendComment = async () => {
	if (!commentText.value.trim()) {
		return
	}

	if (!currentMomentId.value) {
		uni.showToast({
			title: '动态ID不能为空',
			icon: 'none'
		})
		return
	}

	try {
		let response

		if (replyTarget.value) {
			// 回复评论模式
			const rootId = replyTarget.value.rootId || replyTarget.value.id
			const parentId = replyTarget.value.id

			// 拼接回复前缀和回复内容
			const fullReplyContent = replyPrefix.value + commentText.value.trim()

			console.log('发送回复:', {
				businessId: currentMomentId.value,
				type: COMMENT_TYPE.MOMENT,
				rootId: rootId,
				parentId: parentId,
				content: fullReplyContent,
				replyTarget: replyTarget.value.nickname,
				originalContent: commentText.value.trim(),
				prefix: replyPrefix.value
			})

			response = await replyComment(
				currentMomentId.value,
				COMMENT_TYPE.MOMENT,
				rootId,
				parentId,
				fullReplyContent
			)
		} else {
			// 普通评论模式
			console.log('发送评论:', {
				businessId: currentMomentId.value,
				type: COMMENT_TYPE.MOMENT,
				content: commentText.value.trim()
			})

			response = await addRootComment(
				currentMomentId.value,
				COMMENT_TYPE.MOMENT,
				commentText.value.trim()
			)
		}

		if (response.code === 200) {
			// 清空输入框和回复状态
			commentText.value = ''
			replyTarget.value = null
			replyPrefix.value = ''

			// 显示成功提示
			uni.showToast({
				title: replyTarget.value ? '回复成功' : '评论成功',
				icon: 'success'
			})

			// 重新加载评论列表
			await loadCommentList(currentMomentId.value, true)

			// 更新动态的评论数量
			if (momentDetail.value.comments !== undefined) {
				momentDetail.value.comments = commentList.value.length
			}
		} else {
			console.error('发送失败:', response.msg)
			uni.showToast({
				title: response.msg || '发送失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('发送异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	}
}

// 回复评论
const handleReplyComment = (comment) => {
	// 设置回复目标
	replyTarget.value = comment
	replyPrefix.value = `回复${comment.nickname}: `

	// 聚焦输入框
	focusCommentInput()

	console.log('设置回复目标:', {
		target: comment,
		prefix: replyPrefix.value
	})
}

// 取消回复
const cancelReply = () => {
	replyTarget.value = null
	replyPrefix.value = ''
	commentText.value = ''

	console.log('取消回复')
}

// 显示评论菜单
const showCommentMenu = (comment) => {
	// 验证是否为本人评论
	if (!$store.isMe(comment.uid)) {
		return
	}

	uni.showActionSheet({
		itemList: ['删除评论'],
		itemColor: '#ff4757',
		success: (res) => {
			if (res.tapIndex === 0) {
				// 删除评论
				handleDeleteComment(comment)
			}
		},
		fail: (err) => {
			console.log('取消操作:', err)
		}
	})
}

// 显示回复列表
const handleShowReplies = async (comment) => {
	const commentId = comment.id

	// 如果已经展开，则收起
	if (expandedComments.value.has(commentId)) {
		expandedComments.value.delete(commentId)
		return
	}

	// 如果正在加载，直接返回
	if (childCommentLoading.value.get(commentId)) {
		return
	}

	try {
		// 设置加载状态
		childCommentLoading.value.set(commentId, true)

		const params = {
			type: COMMENT_TYPE.MOMENT,
			businessId: currentMomentId.value,
			rootId: commentId, // 根评论ID
			pageSize: 10,
			pageNum: 1,
			orderByColumn: 'createTime',
			isAsc: 'asc' // 子评论按时间正序排列
		}

		console.log('加载子评论，参数:', params)
		const response = await getChildComments(params)

		if (response.code === 200 && response.data) {
			const { rows } = response.data

			// 格式化子评论数据
			const formattedChildComments = rows.map(childComment => ({
				id: childComment.id,
				rootId: childComment.rootId,
				avatar: childComment.oppAvatar,
				nickname: childComment.oppNickName,
				uid: childComment.uid,
				content: childComment.content,
				images: childComment.images,
				createTime: childComment.createTime,
				publishTime: childComment.createTime,
				isMe: $store.isMe(childComment.uid) // 使用store判断是否为本人评论
			}))

			// 存储子评论列表
			childCommentsMap.value.set(commentId, formattedChildComments)

			// 标记为已展开
			expandedComments.value.add(commentId)

			console.log('子评论加载成功:', {
				rootCommentId: commentId,
				childComments: formattedChildComments
			})
		} else {
			console.error('加载子评论失败:', response.msg)
			uni.showToast({
				title: response.msg || '加载失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('加载子评论异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	} finally {
		// 清除加载状态
		childCommentLoading.value.set(commentId, false)
	}
}

// 删除评论
const handleDeleteComment = async (comment) => {
	// 验证是否为本人评论
	if (!$store.isMe(comment.uid)) {
		uni.showToast({
			title: '只能删除自己的评论',
			icon: 'none'
		})
		return
	}

	try {
		const result = await uni.showModal({
			title: '确认删除',
			content: '确定要删除这条评论吗？',
			confirmText: '删除',
			confirmColor: '#ff4757'
		})

		if (result.confirm) {
			console.log('删除评论:', {
				commentId: comment.id,
				uid: comment.uid,
				isMe: $store.isMe(comment.uid)
			})

			const response = await deleteSingleComment(comment.id)

			if (response.code === 200) {
				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})

				// 重新加载评论列表
				await loadCommentList(currentMomentId.value, true)

				// 更新动态的评论数量
				if (momentDetail.value.comments !== undefined) {
					momentDetail.value.comments = commentList.value.length
				}
			} else {
				console.error('删除评论失败:', response.msg)
				uni.showToast({
					title: response.msg || '删除失败',
					icon: 'none'
				})
			}
		}
	} catch (error) {
		console.error('删除评论异常:', error)
		uni.showToast({
			title: '网络异常，请稍后重试',
			icon: 'none'
		})
	}
}

// 话题标签点击
const handleTopicTagClick = (tag) => {
	uni.showToast({
		title: `点击了话题: #${tag.name}`,
		icon: 'none'
	})
	// TODO: 跳转到话题详情页面
	// uni.navigateTo({
	//   url: `/pages/topic/detail?title=${tag.name}&id=${tag.id}`
	// })
}
</script>

<style lang="scss" scoped>
@import '@/static/fonts/iconfont.css';

.moment-detail-page {
	min-height: 100vh;
	background: #f8f9fa;
}

.nav-more-btn {
	padding: 8rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.nav-more-btn:active {
	background-color: rgba(255, 255, 255, 0.1);
}

.main-container {
	margin-top: 20rpx;
	padding: 20rpx;
	padding-bottom: 120rpx;
	/* 为底部操作栏留出空间 */
}

/* 动态内容 */
.moment-content {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* 用户信息 */
.user-info {
	display: flex;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.user-avatar {
	position: relative;
	margin-right: 16rpx;
}

.avatar-img {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
}

.verified-badge {
	position: absolute;
	bottom: -2rpx;
	right: -2rpx;
	width: 24rpx;
	height: 24rpx;
	background: #696CF3;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid #fff;
}

.user-details {
	flex: 1;
}

.follow-btn {
	padding: 12rpx 24rpx;
	background: linear-gradient(135deg, #696CF3 0%, #5A5FE8 100%);
	border-radius: 50rpx;
	margin-left: 16rpx;
}

.follow-text {
	font-size: 24rpx;
	color: #fff;
	font-weight: 500;
}

.user-name-row {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 12rpx;
}

.username {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.user-tags {
	display: flex;
	gap: 8rpx;
}

.age-tag,
.location-tag,
.occupation-tag {
	font-size: 22rpx;
	color: #666;
	background: #f5f5f5;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

/* 动态文本 */
.moment-text {
	margin-bottom: 20rpx;
}

.content-text {
	font-size: 30rpx;
	line-height: 1.6;
	color: #333;
}

/* 话题标签 */
.topic-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
	margin-bottom: 20rpx;
}

.topic-tag {
	display: inline-flex;
	align-items: center;
	background: rgba(105, 108, 243, 0.08);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.topic-tag:active {
	transform: scale(0.96);
	background: rgba(105, 108, 243, 0.12);
}

.topic-tag-text {
	font-size: 24rpx;
	color: #696CF3;
	font-weight: 500;
	line-height: 1.2;
}

/* 动态图片 */
.moment-images {
	margin-bottom: 20rpx;
}

.image-grid {
	display: grid;
	gap: 8rpx;
	border-radius: 12rpx;
	overflow: hidden;
}

.single-image {
	grid-template-columns: 1fr;
	max-width: 400rpx;
}

.two-images {
	grid-template-columns: 1fr 1fr;
}

.three-images {
	grid-template-columns: 1fr 1fr 1fr;
}

.multiple-images {
	grid-template-columns: 1fr 1fr 1fr;
}

.image-item {
	position: relative;
	aspect-ratio: 1;
	overflow: hidden;
	border-radius: 8rpx;
}

.moment-image {
	width: 100%;
	height: 100%;
}

/* 动态元信息 */
.moment-meta {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.publish-time,
.location {
	font-size: 24rpx;
	color: #999;
}

/* 互动统计 */
.interaction-stats {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 20rpx;
}

.stats-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.stats-text {
	font-size: 26rpx;
	color: #666;
}

/* 评论区域 */
.comments-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

/* 评论列表 */
.comment-item {
	display: flex;
	align-items: flex-start;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.comment-item:last-child {
	border-bottom: none;
}

.comment-avatar {
	margin-right: 16rpx;
}

.comment-content {
	flex: 1;
}

.comment-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.comment-menu {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.comment-menu:active {
	background: rgba(0, 0, 0, 0.05);
}

.menu-dots {
	font-size: 32rpx;
	color: #999;
	font-weight: bold;
	line-height: 1;
	transform: rotate(90deg);
}

.comment-username {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.comment-tags {
	display: flex;
	gap: 6rpx;
}

.comment-verified {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 20rpx;
	height: 20rpx;
	background: #696CF3;
	border-radius: 50%;
}

.comment-text {
	margin-bottom: 8rpx;
}

.comment-content-text {
	font-size: 28rpx;
	line-height: 1.5;
	color: #333;
}

.comment-meta {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.comment-time,
.comment-location {
	font-size: 22rpx;
	color: #999;
}

/* 评论操作按钮 */
.comment-actions {
	display: flex;
	align-items: center;
	gap: 24rpx;
	margin-top: 8rpx;
}

.comment-actions .action-btn {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 8rpx 12rpx;
	border-radius: 16rpx;
	background: transparent;
	transition: all 0.3s ease;
}

.comment-actions .action-btn:active {
	background: rgba(0, 0, 0, 0.05);
}

.comment-actions .action-text {
	font-size: 22rpx;
	color: #999;
}

.comment-actions .delete-btn:active {
	background: rgba(255, 71, 87, 0.1);
}

.comment-actions .delete-text {
	color: #ff4757;
}

.comment-actions .reply-count-text {
	color: #696CF3;
}

/* 底部操作栏 */
.bottom-action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 16rpx 20rpx;
	padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	gap: 16rpx;
	z-index: 100;
}

.comment-input-area {
	flex: 1;
	position: relative;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	border-radius: 32rpx;
	padding: 0 20rpx;
	transition: all 0.3s ease;
}

.comment-input-area:active {
	background: #eeeeee;
}

/* 回复前缀样式 */
.reply-prefix {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8rpx 0 4rpx 0;
	border-bottom: 1rpx solid #e0e0e0;
	margin-bottom: 4rpx;
}

.prefix-text {
	font-size: 24rpx;
	color: #696CF3;
	font-weight: 500;
	flex: 1;
}

.cancel-reply {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.cancel-reply:active {
	background: rgba(0, 0, 0, 0.1);
}

/* 输入框行样式 */
.input-row {
	display: flex;
	align-items: center;
	width: 100%;
}

.comment-input {
	flex: 1;
	height: 64rpx;
	background: transparent;
	border: none;
	outline: none;
	font-size: 28rpx;
	color: #333;
	padding: 0;
	margin: 0;
}

.send-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 56rpx;
	height: 56rpx;
	background: linear-gradient(135deg, #696CF3 0%, #8B7CF6 100%);
	border-radius: 50%;
	margin-left: 12rpx;
	transition: all 0.3s ease;
}

.send-btn:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.action-buttons {
	display: flex;
	gap: 24rpx;
}

.action-btn {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 8rpx;
	border-radius: 8rpx;
	transition: all 0.3s ease;
}

.action-btn:active {
	background: #f5f5f5;
}

.action-text {
	font-size: 24rpx;
	color: #999;
}

/* 点赞图标样式 */
.action-btn .bani-dianzan,
.action-btn .bani-dianzan-fill,
.stats-item .bani-dianzan {
	transition: all 0.3s ease;
	transform-origin: center;
}

.action-btn .bani-dianzan:active,
.action-btn .bani-dianzan-fill:active {
	transform: scale(1.2);
}

/* 子评论样式 */
.child-comments {
	margin-top: 16rpx;
	padding-left: 20rpx;
	border-left: 2rpx solid #f0f0f0;
	margin-left: 10rpx;
}

.child-comment-item {
	margin-bottom: 16rpx;
}

.child-comment-item:last-child {
	margin-bottom: 0;
}

.child-comment-content {
	display: flex;
	gap: 12rpx;
}

.child-avatar {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	flex-shrink: 0;
}

.child-comment-main {
	flex: 1;
}

.child-comment-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.child-header-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.child-comment-menu {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.child-comment-menu:active {
	background: rgba(0, 0, 0, 0.05);
}

.child-comment-menu .menu-dots {
	font-size: 28rpx;
}

.child-nickname {
	font-size: 24rpx;
	font-weight: 500;
	color: #333;
}

.child-comment-time {
	font-size: 20rpx;
	color: #999;
}

.child-comment-text {
	font-size: 26rpx;
	color: #333;
	line-height: 1.5;
	margin-bottom: 8rpx;
}

.child-comment-actions {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.child-comment-actions .action-btn {
	padding: 4rpx 8rpx;
}

.child-comment-actions .action-text {
	font-size: 20rpx;
}

/* 加载更多子评论 */
.load-more-child {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	padding: 16rpx;
	color: #999;
}

.loading-text {
	font-size: 24rpx;
	color: #999;
}
</style>
