package com.gzhuxn.personals.controller.app.recommend.bo.user;

import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppNearbyUserQueryBoToAppRecommendUserPageBoMapper extends BaseMapper<AppNearbyUserQueryBo, AppRecommendUserPageBo> {
}
