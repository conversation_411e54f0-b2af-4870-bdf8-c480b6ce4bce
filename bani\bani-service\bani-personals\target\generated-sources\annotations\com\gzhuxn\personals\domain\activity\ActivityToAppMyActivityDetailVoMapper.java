package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.bo.AppActivityBoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.bo.AppActivityBuddyCreateBoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.bo.AppActivityBuddyUpdateBoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailVoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.vo.AppActivityPageVoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.vo.AppDaziActivityDetailVoToActivityMapper;
import com.gzhuxn.personals.controller.app.activity.vo.AppDaziActivityPageVoToActivityMapper;
import com.gzhuxn.personals.controller.app.recommend.bo.activity.AppRecommendActivityQueryBoToActivityMapper;
import com.gzhuxn.personals.controller.app.recommend.vo.activity.AppRecommendActivityPageVoToActivityMapper;
import com.gzhuxn.personals.controller.app.user.vo.activity.AppMyActivityDetailVo;
import com.gzhuxn.personals.domain.activity.bo.ActivityBoToActivityMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppRecommendActivityPageVoToActivityMapper.class,AppActivityBuddyUpdateBoToActivityMapper.class,AppActivityBuddyCreateBoToActivityMapper.class,AppDaziActivityPageVoToActivityMapper.class,AppDaziActivityDetailVoToActivityMapper.class,AppActivityDetailVoToActivityMapper.class,AppRecommendActivityQueryBoToActivityMapper.class,AppActivityBoToActivityMapper.class,AppActivityPageVoToActivityMapper.class,ActivityBoToActivityMapper.class,ActivityToAppRecommendActivityPageVoMapper.class,ActivityToActivityVoMapper.class,ActivityToAppDaziActivityPageVoMapper.class,ActivityToAppDaziActivityDetailVoMapper.class,ActivityToAppActivityDetailVoMapper.class,ActivityToAppActivityPageVoMapper.class,ActivityToAppBuddyActivityDetailVoMapper.class,ActivityToAppBuddyActivityVoMapper.class},
    imports = {}
)
public interface ActivityToAppMyActivityDetailVoMapper extends BaseMapper<Activity, AppMyActivityDetailVo> {
  @Mapping(
      target = "uid",
      source = "createBy"
  )
  AppMyActivityDetailVo convert(Activity source);

  @Mapping(
      target = "uid",
      source = "createBy"
  )
  AppMyActivityDetailVo convert(Activity source, @MappingTarget AppMyActivityDetailVo target);
}
