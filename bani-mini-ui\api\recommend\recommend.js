import request from '@/utils/request'

/**
 * 查询同城用户列表
 * @param {Object} params 查询参数
 * @param {number} params.userId 用户ID
 * @param {number} params.pid 用户号
 * @param {boolean} params.isMatched 是否是相亲广场列表查询
 * @param {number} params.ageMin 最小年龄
 * @param {number} params.ageMax 最大年龄
 * @param {number} params.heightMin 最小身高
 * @param {number} params.heightMax 最大身高
 * @param {number} params.education 要求学历 不限 0 高中及以上 1 大专及以上 2 本科及以上 3 硕士及以上 4 博士及以上 5
 * @param {number} params.location 推荐城市 0不限、1同城优先、2同省优先
 * @param {number} params.pageSize 分页大小
 * @param {number} params.pageNum 当前页数
 * @returns {Promise} 返回同城用户列表
 */
export function getSameCityUsers(params) {
  return request({
    url: '/personals/recommend/user/same-city',
    method: 'get',
    params
  }).catch(error => {
    console.error('同城用户列表API调用失败:', error)
    // 返回一个符合预期格式的错误响应
    return {
      code: 0,
      msg: '网络错误，请稍后重试',
      rows: [],
      total: 0
    }
  })
}

/**
 * 查询附近用户列表
 * @param {Object} params 查询参数
 * @param {number} params.userId 用户ID
 * @param {number} params.pid 用户号
 * @param {boolean} params.isMatched 是否是相亲广场列表查询
 * @param {number} params.lon 最新位置经度
 * @param {number} params.lat 最新位置纬度
 * @param {number} params.distance 查询距离范围（公里），默认100公里
 * @param {number} params.ageMin 最小年龄
 * @param {number} params.ageMax 最大年龄
 * @param {number} params.heightMin 最小身高
 * @param {number} params.heightMax 最大身高
 * @param {number} params.education 要求学历 不限 0 高中及以上 1 大专及以上 2 本科及以上 3 硕士及以上 4 博士及以上 5
 * @param {number} params.location 推荐城市 0不限、1同城优先、2同省优先
 * @param {number} params.pageSize 分页大小
 * @param {number} params.pageNum 当前页数
 * @returns {Promise} 返回附近用户列表
 */
export function getNearbyUsers(params) {
  return request({
    url: '/personals/recommend/user/nearby',
    method: 'get',
    params
  }).catch(error => {
    console.error('附近用户列表API调用失败:', error)
    // 返回一个符合预期格式的错误响应
    return {
      code: 0,
      msg: '网络错误，请稍后重试',
      rows: [],
      total: 0
    }
  })
}

/**
 * 数据转换工具函数 - 将API返回的数据转换为页面需要的格式
 * @param {Object} apiData API返回的用户数据
 * @returns {Object} 转换后的用户数据
 */
export function transformUserData(apiData) {
  return {
    id: apiData.oppUserId,
    avatar: apiData.oppAvatar,
    nickname: apiData.oppNickName || '用户',
    gender: apiData.oppSex === '0' ? 'male' : 'female',
    isVerified: apiData.oppIsIdentity || false,
    currentCity: apiData.oppCity || '未知',
    hometown: apiData.addrProvince || '未知',
    age: parseInt(apiData.oppAge) || 18,
    height: parseInt(apiData.oppHeight) || 170,
    education: apiData.edu || '本科',
    occupation: apiData.job || '职员',
    distance: apiData.distance > 1 ? (apiData.distance).toFixed(2) + `km` : '<1km',
    isFollowed: apiData.oppIsFollowed || false,
    isMe: apiData.oppIsMe || false,
    // 为瀑布流布局生成随机高度
    imageHeight: 250 + Math.floor(Math.random() * 100)
  }
}

/**
 * 构建同城用户查询参数
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {Object} filters 筛选条件
 * @returns {Object} 查询参数
 */
export function buildSameCityParams(pageNum = 1, pageSize = 10, filters = {}) {
  return {
    pageNum,
    pageSize,
    isMatched: null,
    ...filters
  }
}

/**
 * 构建附近用户查询参数
 * @param {number} pageNum 页码
 * @param {number} pageSize 每页大小
 * @param {number} longitude 经度
 * @param {number} latitude 纬度
 * @param {Object} filters 筛选条件
 * @returns {Object} 查询参数
 */
export function buildNearbyParams(pageNum = 1, pageSize = 10, longitude, latitude, filters = {}) {
  return {
    pageNum,
    pageSize,
    isMatched: null,
    lon: longitude,
    lat: latitude,
    distance: 100, // 默认100公里
    ...filters
  }
}
