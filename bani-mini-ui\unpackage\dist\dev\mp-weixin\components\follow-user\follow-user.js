"use strict";const e=require("../../common/vendor.js"),g=require("../../api/my/follow.js");if(!Array){const r=e.resolveComponent("uni-segmented-control"),i=e.resolveComponent("z-paging");(r+i)()}const F=()=>"../../uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.js",S=()=>"../../uni_modules/z-paging/components/z-paging/z-paging.js";Math||(j+F+S)();const j=()=>"../custom-refresher/custom-refresher.js",q={__name:"follow-user",props:{navBarHeight:{type:Number,default:0},initialCategory:{type:String,default:"mutual"},showCategory:{type:Boolean,default:!0},themeColor:{type:String,default:"#696CF3"}},emits:["userClick","userAction","categoryChange"],setup(r,{expose:i,emit:h}){const b=r,p=h,n=e.ref(b.initialCategory),f=e.ref([]),l=e.ref(null),d=e.ref(0),w=e.ref(0),v=e.ref(0),u=e.ref(0),x=e.computed(()=>{const o=[];return w.value>0?o.push(`关注(${w.value})`):o.push("关注"),v.value>0?o.push(`粉丝(${v.value})`):o.push("粉丝"),d.value>0?o.push(`互关(${d.value})`):o.push("互关"),o}),y=e.computed(()=>{switch(n.value){case"mutual":return"暂无互关好友";case"following":return"暂无关注用户";case"followers":return"暂无粉丝";default:return"暂无数据"}});e.watch(n,o=>{p("categoryChange",o),m(),l.value&&l.value.reload()});const m=()=>{switch(n.value){case"following":u.value=0;break;case"followers":u.value=1;break;case"mutual":u.value=2;break}},C=o=>{n.value!==o&&(n.value=o)},k=o=>{if(u.value!==o.currentIndex)switch(u.value=o.currentIndex,o.currentIndex){case 0:n.value="following";break;case 1:n.value="followers";break;case 2:n.value="mutual";break}},I=o=>{switch(o){case"following":return 2;case"followers":return 1;case"mutual":return 3;default:return 2}},T=(o,a)=>{const t=I(n.value);g.followUserPage({type:t,pageSize:a,pageNum:o}).then(s=>{const c=s.rows;e.index.__f__("log","at components/follow-user/follow-user.vue:212","用户列表加载成功:",c.length,"条数据"),l.value.complete(c)})},U=o=>{e.index.__f__("log","at components/follow-user/follow-user.vue:221","点击用户:",o),p("userClick",o)},A=async o=>{switch(e.index.__f__("log","at components/follow-user/follow-user.vue:227","用户操作:",o),n.value){case"mutual":e.index.navigateTo({url:`/pagesubs/personals/greeting/greeting?userId=${o.oppUserId}&nickName=${encodeURIComponent(o.oppNickName)}&avatar=${o.oppAvatar}`,fail:a=>{e.index.__f__("error","at components/follow-user/follow-user.vue:235","跳转打招呼页面失败:",a),e.index.showToast({title:"跳转失败",icon:"error"})}});break;case"following":await N(o);break;case"followers":await _(o);break;default:await _(o);break}p("userAction",o)},_=async o=>{e.index.__f__("log","at components/follow-user/follow-user.vue:262","关注用户:",o.oppUserId);const a=await g.toggleUserFollow(o.oppUserId,!1);a.code===1?(e.index.showToast({title:"关注成功",icon:"none",duration:1500}),l.value&&l.value.reload()):(e.index.__f__("error","at components/follow-user/follow-user.vue:277","关注操作失败:",a.msg||"未知错误"),e.index.showToast({title:a.msg||"关注失败，请重试",icon:"none",duration:2e3}))},N=o=>{e.index.__f__("log","at components/follow-user/follow-user.vue:288","取消关注用户:",o.oppUserId),e.index.showModal({title:"确认",content:`确定要取消关注 ${o.oppNickName} 吗？`,confirmText:"取消关注",cancelText:"再想想",success:a=>{a.confirm&&g.toggleUserFollow(o.oppUserId,!0).then(t=>{e.index.showToast({title:"取消关注成功",icon:"none",duration:1500})})}})},$=o=>{switch(n.value){case"mutual":return"打招呼";case"following":return"取消关注";case"followers":return o.oppIsFollowed?"打招呼":"回关";default:return"关注"}};return i({refresh:()=>{l.value&&l.value.reload()},switchCategory:C}),e.onMounted(()=>{e.index.__f__("log","at components/follow-user/follow-user.vue:343","follow-user组件mounted"),m()}),(o,a)=>e.e({a:e.w(({refresherStatus:t},s,c)=>({a:"7b622b4b-1-"+c+",7b622b4b-0",b:e.p({"refresher-status":t}),c,d:s}),{name:"refresher",path:"a",vueId:"7b622b4b-0"}),b:r.showCategory},r.showCategory?{c:e.o(k),d:e.p({current:u.value,values:x.value,styleType:"text",activeColor:"#696CF3"}),e:r.navBarHeight+"px"}:{},{f:e.f(f.value,(t,s,c)=>e.e({a:t.oppAvatar,b:t.isOnline},t.isOnline?{}:{},{c:e.t(t.oppNickName),d:e.n(t.oppSex==="0"?"bani-xingbie-nan":"bani-xingbie-nv"),e:t.oppSex==="0"?"#4A90E2":"#E91E63",f:e.t(t.oppAge),g:e.t(t.oppHeight),h:e.t(t.oppCity?" · "+t.oppCity:""),i:e.t(t.createTime),j:e.t($(t)),k:e.o(z=>A(t),s),l:s,m:e.o(z=>U(t),s)})),g:e.sr(l,"7b622b4b-0",{k:"paging"}),h:e.o(T),i:n.value,j:e.o(t=>f.value=t),k:e.p({auto:!0,"refresher-enabled":!0,"loading-more-enabled":!0,"empty-view-text":y.value,"empty-view-img":"",modelValue:f.value})})}},B=e._export_sfc(q,[["__scopeId","data-v-7b622b4b"]]);wx.createComponent(B);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/follow-user/follow-user.js.map
