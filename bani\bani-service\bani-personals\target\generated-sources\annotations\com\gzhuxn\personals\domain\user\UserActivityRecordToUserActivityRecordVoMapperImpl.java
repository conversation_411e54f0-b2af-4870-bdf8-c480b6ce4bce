package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserActivityRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserActivityRecordToUserActivityRecordVoMapperImpl implements UserActivityRecordToUserActivityRecordVoMapper {

    @Override
    public UserActivityRecordVo convert(UserActivityRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserActivityRecordVo userActivityRecordVo = new UserActivityRecordVo();

        userActivityRecordVo.setId( arg0.getId() );
        userActivityRecordVo.setUserId( arg0.getUserId() );
        userActivityRecordVo.setOrderId( arg0.getOrderId() );
        userActivityRecordVo.setActivityId( arg0.getActivityId() );
        userActivityRecordVo.setOriginalAmount( arg0.getOriginalAmount() );
        userActivityRecordVo.setAmount( arg0.getAmount() );
        userActivityRecordVo.setCoin( arg0.getCoin() );
        userActivityRecordVo.setSignIn( arg0.getSignIn() );
        userActivityRecordVo.setSignInTime( arg0.getSignInTime() );
        userActivityRecordVo.setSignInAddr( arg0.getSignInAddr() );
        userActivityRecordVo.setSignInLon( arg0.getSignInLon() );
        userActivityRecordVo.setSignInLat( arg0.getSignInLat() );
        userActivityRecordVo.setPayTime( arg0.getPayTime() );
        userActivityRecordVo.setPayStatus( arg0.getPayStatus() );

        return userActivityRecordVo;
    }

    @Override
    public UserActivityRecordVo convert(UserActivityRecord arg0, UserActivityRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setSignIn( arg0.getSignIn() );
        arg1.setSignInTime( arg0.getSignInTime() );
        arg1.setSignInAddr( arg0.getSignInAddr() );
        arg1.setSignInLon( arg0.getSignInLon() );
        arg1.setSignInLat( arg0.getSignInLat() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );

        return arg1;
    }
}
