package com.gzhuxn.personals.controller.app.user.bo.gift;

import com.gzhuxn.personals.domain.user.UserGift;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserGiftCreateBoToUserGiftMapperImpl implements AppUserGiftCreateBoToUserGiftMapper {

    @Override
    public UserGift convert(AppUserGiftCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGift userGift = new UserGift();

        userGift.setUserId( arg0.getUserId() );
        userGift.setOppositeUserId( arg0.getOppositeUserId() );
        userGift.setGiftId( arg0.getGiftId() );
        userGift.setGiftName( arg0.getGiftName() );
        userGift.setGiftPrice( arg0.getGiftPrice() );
        userGift.setGiftNum( arg0.getGiftNum() );

        return userGift;
    }

    @Override
    public UserGift convert(AppUserGiftCreateBo arg0, UserGift arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setGiftId( arg0.getGiftId() );
        arg1.setGiftName( arg0.getGiftName() );
        arg1.setGiftPrice( arg0.getGiftPrice() );
        arg1.setGiftNum( arg0.getGiftNum() );

        return arg1;
    }
}
