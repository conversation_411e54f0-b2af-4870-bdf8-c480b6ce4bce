package com.gzhuxn.personals.controller.app.message.bo;

import com.gzhuxn.personals.domain.message.bo.MsgGroupUserBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppMsgGroupUserUpdateBoToMsgGroupUserBoMapperImpl implements AppMsgGroupUserUpdateBoToMsgGroupUserBoMapper {

    @Override
    public MsgGroupUserBo convert(AppMsgGroupUserUpdateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroupUserBo msgGroupUserBo = new MsgGroupUserBo();

        msgGroupUserBo.setId( arg0.getId() );
        msgGroupUserBo.setName( arg0.getName() );
        msgGroupUserBo.setDisturb( arg0.getDisturb() );
        msgGroupUserBo.setStatus( arg0.getStatus() );

        return msgGroupUserBo;
    }

    @Override
    public MsgGroupUserBo convert(AppMsgGroupUserUpdateBo arg0, MsgGroupUserBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setDisturb( arg0.getDisturb() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
