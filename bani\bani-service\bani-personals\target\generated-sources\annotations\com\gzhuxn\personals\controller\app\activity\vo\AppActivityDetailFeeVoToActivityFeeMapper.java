package com.gzhuxn.personals.controller.app.activity.vo;

import com.gzhuxn.personals.domain.activity.ActivityFee;
import com.gzhuxn.personals.domain.activity.ActivityFeeToAppActivityDetailFeeVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActivityFeeToAppActivityDetailFeeVoMapper.class},
    imports = {}
)
public interface AppActivityDetailFeeVoToActivityFeeMapper extends BaseMapper<AppActivityDetailFeeVo, ActivityFee> {
}
