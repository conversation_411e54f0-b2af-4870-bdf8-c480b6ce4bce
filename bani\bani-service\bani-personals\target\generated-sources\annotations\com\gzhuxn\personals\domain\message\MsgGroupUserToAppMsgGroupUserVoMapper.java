package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupUserVo;
import com.gzhuxn.personals.domain.message.bo.MsgGroupUserBoToMsgGroupUserMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupUserBoToMsgGroupUserMapper.class,MsgGroupUserToMsgGroupUserVoMapper.class,MsgGroupUserToAppMsgGroupDetailUserVoMapper.class},
    imports = {}
)
public interface MsgGroupUserToAppMsgGroupUserVoMapper extends BaseMapper<MsgGroupUser, AppMsgGroupUserVo> {
}
