package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.audit.vo.UserDetailAuditVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToUserDetailAuditVoMapperImpl implements UserDetailToUserDetailAuditVoMapper {

    @Override
    public UserDetailAuditVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetailAuditVo userDetailAuditVo = new UserDetailAuditVo();

        return userDetailAuditVo;
    }

    @Override
    public UserDetailAuditVo convert(UserDetail arg0, UserDetailAuditVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        return arg1;
    }
}
