package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserVipRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserVipRecordBoToUserVipRecordMapperImpl implements UserVipRecordBoToUserVipRecordMapper {

    @Override
    public UserVipRecord convert(UserVipRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserVipRecord userVipRecord = new UserVipRecord();

        userVipRecord.setSearchValue( arg0.getSearchValue() );
        userVipRecord.setCreateBy( arg0.getCreateBy() );
        userVipRecord.setCreateTime( arg0.getCreateTime() );
        userVipRecord.setUpdateBy( arg0.getUpdateBy() );
        userVipRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userVipRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userVipRecord.setCreateDept( arg0.getCreateDept() );
        userVipRecord.setId( arg0.getId() );
        userVipRecord.setUserId( arg0.getUserId() );
        userVipRecord.setOrderId( arg0.getOrderId() );
        userVipRecord.setOriginalAmount( arg0.getOriginalAmount() );
        userVipRecord.setAmount( arg0.getAmount() );
        userVipRecord.setCoin( arg0.getCoin() );
        userVipRecord.setPayTime( arg0.getPayTime() );
        userVipRecord.setPayStatus( arg0.getPayStatus() );
        userVipRecord.setManageId( arg0.getManageId() );
        userVipRecord.setStartDate( arg0.getStartDate() );
        userVipRecord.setEndDate( arg0.getEndDate() );
        userVipRecord.setMonths( arg0.getMonths() );

        return userVipRecord;
    }

    @Override
    public UserVipRecord convert(UserVipRecordBo arg0, UserVipRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );
        arg1.setManageId( arg0.getManageId() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setEndDate( arg0.getEndDate() );
        arg1.setMonths( arg0.getMonths() );

        return arg1;
    }
}
