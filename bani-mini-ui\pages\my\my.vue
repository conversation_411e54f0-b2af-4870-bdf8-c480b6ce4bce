<template>
	<scroll-nav-page title="个人中心"  @heightChange="handleNavHeightChange">
		<template #content>
			<!-- 未注册 -->
			<unregistered-user v-if="$store.isUserShort()"></unregistered-user>
			<!-- 已注册 -->
			<view v-else class="login content-with-nav page-content">
				<view class="main-container">
					<!-- 用户信息卡片 -->
					<view class="user-card">
						<!-- 用户基本信息 -->
						<view class="user-info">
							<view class="avatar-section">
								<image class="avatar" :src="userDetail.avatar.smallUrl" mode="aspectFill"></image>
								<view v-show="userDetail.isIdentity" class="identity-badge">
									<uni-icons type="checkmarkempty" size="14" color="#fff"></uni-icons>
								</view>
							</view>
							<view class="info-section">
								<view class="name-row">
									<text class="name">{{ userDetail.nickName }}</text>
									<text class="iconfont gender-icon"
										:class="userDetail.sex === '0' ? 'icon-gender-male' : 'icon-gender-female'"
										:style="{ color: userDetail.sex === '0' ? '#4A90E2' : '#E91E63' }"></text>
								</view>
								<view class="id-row">
									<text class="pid">伴你ID:{{ userDetail.pid }}</text>
									<view class="copy-btn" @click="handleCopyId">
										<uni-icons type="wallet" size="14" color="#696CF3"></uni-icons>
									</view>
								</view>
							</view>
							<navigator url="/pagesubs/my/profile/profileEdit" class="edit-btn-wrapper">
								<view class="edit-btn">编辑资料</view>
							</navigator>
						</view>

						<!-- 数据统计 -->
						<view class="stats-section">
							<navigator url="/pagesubs/my/greeting/greeting" class="stat-item">
								<text class="count">{{ userDetail.count.wantKowMe }}</text>
								<text class="label">想认识</text>
							</navigator>
							<navigator url="/pagesubs/my/follow/follow?type=1" class="stat-item">
								<text class="count">{{ userDetail.count.myFollow }}</text>
								<text class="label">关注</text>
							</navigator>
							<navigator url="/pagesubs/my/follow/follow?type=2" class="stat-item">
								<text class="count">{{ userDetail.count.followMy }}</text>
								<text class="label">粉丝</text>
							</navigator>
							<navigator url="/pagesubs/my/browse/browse?type=1" class="stat-item">
								<text class="count">{{ userDetail.count.browseMy }}</text>
								<text class="label">足迹</text>
							</navigator>
						</view>
					</view>

					<!-- 功能卡片区域 -->
					<view class="feature-cards">
						<navigator url="/pagesubs/my/coin/coin" class="feature-card">
							<view class="card-content">
								<view class="icon-column">
									<image class="feature-icon" src="/static/image/icons/coin.png" mode="aspectFit">
									</image>
								</view>
								<view class="text-column">
									<text class="title">我的花瓣</text>
									<text class="count">{{ userDetail.count.coin || 0 }}</text>
								</view>
							</view>
						</navigator>

						<navigator url="/pagesubs/my/gift/gift" class="feature-card">
							<view class="card-content">
								<view class="icon-column">
									<uni-icons type="gift" :size="32" color="#FF6B35"></uni-icons>
								</view>
								<view class="text-column">
									<text class="title">礼物</text>
									<text class="count">{{ userDetail.count.gift || 0 }}</text>
								</view>
							</view>
						</navigator>
						<navigator url="/pagesubs/my/activity/activity" class="feature-card">
							<view class="card-content">
								<view class="icon-column">
									<uni-icons type="calendar" :size="32" color="#4ECDC4"></uni-icons>
								</view>
								<view class="text-column">
									<text class="title">活动</text>
									<text class="count">{{ userDetail.count.activity || 0 }}</text>
								</view>
							</view>
						</navigator>
					</view>

					<!-- 资料等级卡片 -->
					<view class="level-card">
						<view class="level-content">
							<view class="level-icon">
								<uni-icons type="star-filled" size="42" :color="$primaryColor"></uni-icons>
							</view>
							<view class="level-info">
								<text class="level-title">资料等级</text>
								<text class="level-desc">升级至高级，可查看更多条件</text>
							</view>
							<view class="upgrade-btn" @click="navigateTo('/pagesubs/my/upgrade/upgrade')">
								<text>去升级</text>
							</view>
						</view>
					</view>

					<!-- 功能菜单 -->
					<view class="menu-grid">
						<!-- 第一行 -->
						<view class="menu-item" @click="navigateTo('/pagesubs/my/auth/auth')">
							<view class="menu-icon">
								<uni-icons type="checkmarkempty" size="32" color="#67C23A"></uni-icons>
							</view>
							<text class="menu-title">我的认证</text>
						</view>

						<view class="menu-item" @click="navigateTo('/pagesubs/my/moment/moment')">
							<view class="menu-icon">
								<uni-icons type="pyq" size="32" color="#F56C6C"></uni-icons>
							</view>
							<text class="menu-title">我的动态</text>
						</view>

						<view class="menu-item" @click="navigateTo('/pagesubs/my/question/question')">
							<view class="menu-icon">
								<uni-icons type="help-filled" size="32" color="#409EFF"></uni-icons>
							</view>
							<text class="menu-title">我的问答</text>
						</view>

						<view class="menu-item" @click="navigateTo('/pagesubs/my/album/album')">
							<view class="menu-icon">
								<uni-icons type="image-filled" size="32" color="#E6A23C"></uni-icons>
							</view>
							<text class="menu-title">我的相册</text>
						</view>

						<!-- 第二行 -->
						<view class="menu-item" @click="navigateTo('/pagesubs/my/order/order-list')">
							<view class="menu-icon">
								<uni-icons type="list" size="32" :color="globalConfig.theme.primaryColor"></uni-icons>
							</view>
							<text class="menu-title">我的订单</text>
						</view>
						<!-- <view class="menu-item" @click="navigateTo('/pagesubs/promotion-center/withdraw/withdraw')">
						<view class="menu-icon">
							<uni-icons type="wallet-filled" size="32" color="#FF6B6B"></uni-icons>
						</view>
						<text class="menu-title">助力计划</text>
					</view> -->

						<!-- <view class="menu-item" @click="handleShare">
						<view class="menu-icon">
							<uni-icons type="redo-filled" size="32" :color="globalConfig.theme.primaryColor"></uni-icons>
						</view>
						<text class="menu-title">邀请朋友</text>
					</view> -->
					</view>

					<!-- 更多菜单区域 -->
					<view class="more-menu-section">
						<view class="more-indicator">
							<text class="more-text">更多</text>
							<uni-icons type="down" size="16" color="#999"></uni-icons>
						</view>
					</view>

					<!-- 底部菜单 -->
					<view class="bottom-menu">
						<view class="menu-item" @click="navigateTo('/pagesubs/my/setting/setting')">
							<view class="menu-icon">
								<uni-icons type="gear-filled" size="32"
									:color="globalConfig.theme.primaryColor"></uni-icons>
							</view>
							<text class="menu-title">设置及隐私</text>
						</view>

						<view class="menu-item" @click="navigateTo(globalConfig.help.antiFraudReminder)">
							<view class="menu-icon">
								<uni-icons type="info-filled" size="32"
									:color="globalConfig.theme.primaryColor"></uni-icons>
							</view>
							<text class="menu-title">防骗提醒</text>
						</view>

						<view class="menu-item" @click="clickDeveloping()">
							<view class="menu-icon">
								<uni-icons type="headphones" size="32"
									:color="globalConfig.theme.primaryColor"></uni-icons>
							</view>
							<text class="menu-title">联系客服</text>
						</view>
						<!-- 空占位 -->
						<view class="menu-placeholder"></view>
					</view>

					<view class="bottom">
						<view class="companion-card">
							<uni-icons type="heart-filled" size="22" color="#FF69B4"></uni-icons>
							<text class="days-text">伴你有约与您相伴</text>
							<text class="days-count">{{ userDetail.count.registerDays }}</text>
							<text class="days-unit">天</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 关注订阅号组件 -->
			<mp-subscribe :navBarHeight="navBarHeight" />
		</template>
	</scroll-nav-page>
</template>

<script setup>
import {
	onLoad,
	onShow,
	onPageScroll
} from "@dcloudio/uni-app"
import {
	ref,
	computed
} from 'vue'
import {
	clickCopy, clickDeveloping
} from "@/utils/common"
import {
	getMy
} from "@/api/my/my"
import $store from '@/store'
import globalConfig from '@/config'
import UnregisteredUser from '@/components/unregistered-user/unregistered-user.vue'
import MpSubscribe from '@/components/mp-subscribe/mp-subscribe.vue'

// 主题色
const $primaryColor = '#696CF3'

// 页面滚动距离
const pageScrollTop = ref(0)

// 导航栏高度
const navBarHeight = ref(0)

// 导航栏文字颜色（用于插槽中的图标）
const navTextColor = computed(() => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
})

// 用户详情
const userDetail = ref({
	avatar: '',
	nickName: '',
	sex: '',
	pid: '',
	// 统计信息
	count: {
		wantKowMe: 0,
		myFollow: 0,
		followMy: 0,
		browseMy: 0,
		coin: 0,
		moment: 0,
		gift: 0,
		activity: 0,
		registerDays: 0
	}
})



// 处理页面滚动
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
}

// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}


// 扫码功能
const handleScan = () => {
	uni.showToast({ title: '扫码功能', icon: 'none' })
}

// 设置功能
const handleSettings = () => {
	uni.navigateTo({ url: '/pages/my/setting/setting' })
}

// 处理分享功能
const handleShare = () => {
	uni.showActionSheet({
		itemList: ['分享给微信好友', '分享到朋友圈', '复制链接'],
		success: (res) => {
			switch (res.tapIndex) {
				case 0:
					shareToWechat()
					break
				case 1:
					shareToMoments()
					break
				case 2:
					copyShareLink()
					break
			}
		}
	})
}

// 分享到微信好友
const shareToWechat = () => {
	uni.share({
		provider: 'weixin',
		scene: 'WXSceneSession',
		type: 0,
		href: 'https://www.banyouyue.com',
		title: '伴你有约 - 真实的社交平台',
		summary: '发现身边有趣的人，开启美好的社交体验',
		imageUrl: '/static/logo.png',
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		},
		fail: (err) => {
			console.error('分享失败:', err)
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	})
}

// 分享到朋友圈
const shareToMoments = () => {
	uni.share({
		provider: 'weixin',
		scene: 'WXSceneTimeline',
		type: 0,
		href: 'https://www.banyouyue.com',
		title: '伴你有约 - 真实的社交平台',
		summary: '发现身边有趣的人，开启美好的社交体验',
		imageUrl: '/static/logo.png',
		success: () => {
			uni.showToast({
				title: '分享成功',
				icon: 'success'
			})
		},
		fail: (err) => {
			console.error('分享失败:', err)
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	})
}

// 复制用户ID
const handleCopyId = () => {
	const pid = userDetail.value.pid
	clickCopy(pid)
}

// 复制分享链接
const copyShareLink = () => {
	const shareLink = 'https://www.banyouyue.com'
	clickCopy(shareLink)
}

// 通用导航方法
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	})
}

// 处理关注订阅号组件关闭事件
const handleMpSubscribeClose = () => {
	console.log('关注订阅号弹框已关闭')
}



// 获取用户数据的方法
const loadUserData = () => {
	if ($store.isUserShort()) {
		return;
	}
	// get
	getMy().then(res => {
		userDetail.value = res.data;
	})
}

// init
onLoad(() => {
	// loadUserData()
})

// 页面显示时重新加载数据（tabBar重复点击时会触发）
onShow(() => {
	console.log('个人中心页面显示，重新加载用户数据')
	loadUserData()
})

// 页面滚动监听
onPageScroll(handlePageScroll)

function onShareAppMessage(res) {
	if (res.from === 'button') { // 来自页面内分享按钮
		console.log(res.target)
	}
	return {
		title: '伴你有约',
		path: '/pages/index/index',
		imageUrl: '/static/logo.png',
		success: function (e) {
			console.log('分享成功')
		},
		fail: function (e) {
			console.log('分享失败')
		}
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';
@import '@/static/fonts/iconfont.css';



.nav-right-content {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(105, 108, 243, 0.1);
}

.login {
	flex: 1;
	flex-direction: column;

	.main-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	// 用户信息卡片
	.user-card {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 24rpx 20rpx;
		box-shadow: 0 8rpx 32rpx rgba(105, 108, 243, 0.08);
		backdrop-filter: blur(10rpx);

		.user-info {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.avatar-section {
				position: relative;
				margin-right: 24rpx;

				.avatar {
					width: 120rpx;
					height: 120rpx;
					border-radius: 50%;
					border: 4rpx solid rgba(105, 108, 243, 0.1);
				}

				.identity-badge {
					position: absolute;
					bottom: 0;
					right: 0;
					width: 32rpx;
					height: 32rpx;
					background: linear-gradient(135deg, #696CF3, #9B9DF5);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 3rpx solid #fff;
				}
			}

			.info-section {
				flex: 1;

				.name-row {
					display: flex;
					align-items: center;
					gap: 8rpx;
					margin-bottom: 12rpx;

					.name {
						font-size: 36rpx;
						font-weight: bold;
						color: #333;
					}

					.gender-icon {
						font-size: 32rpx;
						line-height: 1;
					}
				}

				.id-row {
					display: flex;
					align-items: center;

					.pid {
						font-size: 26rpx;
						color: #999;
						margin-right: 12rpx;
					}

					.copy-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 32rpx;
						height: 32rpx;
						background: rgba(105, 108, 243, 0.1);
						border-radius: 8rpx;
						transition: all 0.3s ease;

						&:active {
							transform: scale(0.9);
							background: rgba(105, 108, 243, 0.2);
						}
					}
				}
			}

			.edit-btn-wrapper {
				.edit-btn {
					background: linear-gradient(135deg, $primary-color, #9B9DF5);
					color: white;
					border-radius: 30rpx;
					font-size: 26rpx;
					padding: 16rpx 24rpx;
					text-align: center;
					box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);
				}
			}
		}

		.stats-section {
			display: flex;
			justify-content: space-around;
			padding-top: 24rpx;
			border-top: 1rpx solid rgba(105, 108, 243, 0.1);

			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				flex: 1;

				.count {
					font-size: 32rpx;
					color: $primary-color;
					font-weight: bold;
					margin-bottom: 8rpx;
				}

				.label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}

	// 功能卡片区域
	.feature-cards {
		display: flex;
		gap: 12rpx;

		.feature-card {
			background: rgba(255, 255, 255, 0.95);
			border-radius: 16rpx;
			padding: 16rpx 12rpx;
			box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
			backdrop-filter: blur(10rpx);
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: 120rpx;

			// 我的花瓣占4/8
			&:nth-child(1) {
				flex: 4;
			}

			// 礼物和活动各占2/8
			&:nth-child(2),
			&:nth-child(3) {
				flex: 2;
			}

			&:active {
				transform: translateY(-4rpx);
				box-shadow: 0 12rpx 32rpx rgba(105, 108, 243, 0.15);
			}



			.card-content {
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center; // 保持整体内容居中
				width: 100%;
				height: 100%;
				gap: 16rpx; // 设置图标和文字之间的间距，约2px

				.icon-column {
					width: 48rpx;
					height: 48rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: rgba(255, 255, 255, 0.8);
					border-radius: 12rpx;
					flex-shrink: 0;

					.feature-icon {
						width: 32px;
						height: 32px;
					}
				}

				.text-column {
					display: flex;
					flex-direction: column;
					align-items: flex-start; // 文字左对齐，与图标紧挨着
					flex-shrink: 0; // 防止文字区域被压缩

					.title {
						font-size: 26rpx;
						color: #333;
						font-weight: 500;
						margin-bottom: 4rpx;
						line-height: 1.1;
						white-space: nowrap; // 防止文字换行
					}

					.count {
						font-size: 26rpx;
						color: $primary-color;
						font-weight: bold;
						line-height: 1;
						white-space: nowrap; // 防止文字换行
					}
				}
			}

			// 礼物和活动卡片采用两列布局，图标和文字紧挨着
			&:nth-child(2),
			&:nth-child(3) {
				.card-content {
					flex-direction: row;
					align-items: center;
					justify-content: center; // 保持整体内容居中
					gap: 16rpx; // 设置图标和文字之间的间距，约2px

					.icon-column {
						width: 36rpx;
						height: 36rpx;
						border-radius: 10rpx;
						flex-shrink: 0;
					}

					.text-column {
						display: flex;
						flex-direction: column;
						align-items: flex-start; // 文字左对齐，与图标紧挨着
						flex-shrink: 0; // 防止文字区域被压缩

						.title {
							font-size: 26rpx;
							margin-bottom: 2rpx;
							line-height: 1.1;
							white-space: nowrap; // 防止文字换行
						}

						.count {
							font-size: 26rpx;
							line-height: 1;
							white-space: nowrap; // 防止文字换行
						}
					}
				}
			}
		}
	}
}

// 资料等级卡片
.level-card {
	background: linear-gradient(135deg, rgba(105, 108, 243, 0.1), rgba(155, 157, 245, 0.15));
	border: 2rpx solid rgba(105, 108, 243, 0.2);
	border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.15);
	backdrop-filter: blur(10rpx);

	.level-content {
		display: flex;
		align-items: center;

		.level-icon {
			margin-right: 20rpx;
			width: 60rpx;
			height: 60rpx;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.2);
		}

		.level-info {
			flex: 1;

			.level-title {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 8rpx;
				display: block;
			}

			.level-desc {
				font-size: 26rpx;
				color: #666;
				display: block;
			}
		}

		.upgrade-btn {
			background: linear-gradient(135deg, $primary-color, #9B9DF5);
			color: white;
			border-radius: 30rpx;
			padding: 16rpx 24rpx;
			font-size: 26rpx;
			font-weight: 500;
			box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.3);
			}
		}
	}
}

// 菜单网格
.menu-grid {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
	backdrop-filter: blur(10rpx);
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx 16rpx;

	.menu-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}

		&.more-item {
			grid-column: span 2;
		}

		.menu-icon {
			width: 64rpx;
			height: 64rpx;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		}

		.menu-title {
			font-size: 26rpx;
			color: #333;
			text-align: center;
			font-weight: 500;
		}
	}
}

// 底部菜单
.bottom-menu {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
	backdrop-filter: blur(10rpx);
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx;

	.menu-placeholder {
		// 空占位元素，不显示任何内容
	}

	.menu-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}

		.menu-icon {
			width: 52rpx;
			height: 52rpx;
			background: rgba(255, 255, 255, 0.8);
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 12rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.menu-title {
			font-size: 24rpx;
			color: #666;
			text-align: center;
		}
	}
}

.bottom {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 24rpx 0 20rpx;

	.companion-card {
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(105, 108, 243, 0.1));
		border-radius: 24rpx;
		padding: 16rpx 24rpx;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 105, 180, 0.2);
		box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.15);

		.days-text {
			color: #666;
			font-size: 24rpx;
			margin-left: 8rpx;
			font-weight: 400;
		}

		.days-count {
			color: #FF69B4;
			font-size: 28rpx;
			font-weight: bold;
			margin-left: 6rpx;
		}

		.days-unit {
			color: #666;
			font-size: 24rpx;
			margin-left: 2rpx;
			font-weight: 400;
		}
	}

	// 更多菜单区域
	.more-menu-section {
		margin: 20rpx 0;
		padding: 0 20rpx;
		display: flex;
		justify-content: center;

		.more-indicator {
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 16rpx 24rpx;
			background: rgba(255, 255, 255, 0.6);
			border-radius: 24rpx;
			backdrop-filter: blur(8rpx);
			transition: all 0.3s ease;

			&:active {
				background: rgba(255, 255, 255, 0.8);
				transform: scale(0.98);
			}

			.more-text {
				font-size: 24rpx;
				color: #666;
				margin-right: 8rpx;
				font-weight: 500;
			}
		}
	}
}
</style>