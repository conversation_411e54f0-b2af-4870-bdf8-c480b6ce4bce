{"version": 3, "file": "message.js", "sources": ["pages/message/message.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVzc2FnZS9tZXNzYWdlLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<!-- 自定义导航栏 -->\r\n\t<scroll-nav-page :enableScrollGradient=\"false\" @scroll=\"handlePageScroll\" @heightChange=\"handleNavHeightChange\">\r\n\t\t<template v-if=\"$store.isUserShort()\" #nav-center>\r\n\t\t\t<text class=\"nav-title\">消息中心</text>\r\n\t\t</template>\r\n\t\t<template v-else #nav-left>\r\n\t\t\t<nav-tabs v-model=\"currentTab\" :tabs=\"messageTabs\" :text-color=\"getNavTextColor()\"\r\n\t\t\t\t@change=\"handleTabChange\" />\r\n\t\t</template>\r\n\t\t<template #content>\r\n\t\t\t<!-- 未注册 -->\r\n\t\t\t<unregistered-user v-if=\"$store.isUserShort()\"></unregistered-user>\r\n\t\t\t<!-- 已注册 -->\r\n\t\t\t<view v-else class=\"main-container\">\r\n\t\t\t\t<!-- 消息标签页内容 -->\r\n\t\t\t\t<view v-if=\"currentTab === 'message'\" class=\"message-list\">\r\n\t\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t\t\t\t<view class=\"loading-spinner\"></view>\r\n\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 消息内容 -->\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<!-- 新关注的 -->\r\n\t\t\t\t\t\t<view class=\"message-item system-message\" @click=\"navigateTo('/pagesubs/message/follow')\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"avatar system-avatar follow-avatar\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"staff-filled\" size=\"32\" color=\"#ffffff\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"avatar-icon\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"badge\" v-if=\"followCount > 0\">{{ followCount }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t<view class=\"top-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">新关注的</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"time-badge-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ followMessage.time }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"subtitle\">{{ followMessage.content }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 互动消息 -->\r\n\t\t\t\t\t\t<view class=\"message-item system-message\" @click=\"navigateTo('/pagesubs/message/interaction')\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"avatar system-avatar interaction-avatar\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"notification-filled\" size=\"32\" color=\"#ffffff\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"avatar-icon\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"badge\" v-if=\"interactionCount > 0\">{{ interactionCount }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t<view class=\"top-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">互动消息</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"time-badge-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ interactionMessage.time }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"subtitle\">{{ interactionMessage.content }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 系统消息 -->\r\n\t\t\t\t\t\t<view class=\"message-item system-message\" @click=\"navigateTo('/pagesubs/message/system')\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"avatar system-avatar system-avatar-red\">\r\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"gear\" size=\"32\" color=\"#ffffff\" class=\"avatar-icon\"></uni-icons>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"badge\" v-if=\"systemCount > 0\">{{ systemCount }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t<view class=\"top-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">系统消息</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"time-badge-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ systemMessage.time }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"subtitle\">{{ systemMessage.content }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 用户消息列表 -->\r\n\t\t\t\t\t\t<view class=\"message-item user-message\" v-for=\"(item, index) in userMessages\" :key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"navigateToChat(item)\">\r\n\t\t\t\t\t\t\t<view class=\"avatar-container\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"badge\" v-if=\"item.unreadCount > 0\">{{ item.unreadCount }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t\t\t\t<view class=\"top-row\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"title\">{{ item.name }}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"time-badge-container\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ item.time }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"subtitle\">{{ item.lastMessage }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t\t\t<view class=\"empty-state\" v-if=\"userMessages.length === 0\">\r\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂时没有更多了</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 好友标签页内容 -->\r\n\t\t\t\t<view v-else-if=\"currentTab === 'friend'\" class=\"friend-list message-list\">\r\n\t\t\t\t\t<follow-user :navBarHeight=\"navBarHeight\" :initial-category=\"currentFriendCategory\"\r\n\t\t\t\t\t\t@userClick=\"handleUserClick\" @userAction=\"handleUserAction\"\r\n\t\t\t\t\t\t@categoryChange=\"handleCategoryChange\">\r\n\t\t\t\t\t</follow-user>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 关注订阅号组件 -->\r\n\t\t\t<mp-subscribe :navBarHeight=\"navBarHeight\" />\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n\r\n<script setup>\r\nimport {\r\n\tref,\r\n\tcomputed,\r\n\tonMounted\r\n} from 'vue'\r\nimport { onPageScroll, onShow } from '@dcloudio/uni-app'\r\nimport { getMessageGroup } from '@/api/message/message'\r\n\r\nimport $store from '@/store'\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n// 导航栏高度\r\nconst navBarHeight = ref(0)\r\n\r\n// 当前标签页\r\nconst currentTab = ref('message')\r\n// 当前好友分类\r\nconst currentFriendCategory = ref('following')\r\n\r\n// 消息页面标签数据\r\nconst messageTabs = ref([\r\n\t{ label: '消息', value: 'message' },\r\n\t{ label: '好友', value: 'friend' }\r\n])\r\n\r\n// 消息计数\r\nconst followCount = ref(0)\r\nconst interactionCount = ref(0)\r\nconst systemCount = ref(0)\r\n\r\n// 系统消息内容和时间\r\nconst followMessage = ref({\r\n\tcontent: '没有新通知',\r\n\ttime: null,\r\n\tunreadNum: 0\r\n})\r\nconst interactionMessage = ref({\r\n\tcontent: '暂无互动消息',\r\n\ttime: null,\r\n\tunreadNum: 0\r\n})\r\nconst systemMessage = ref({\r\n\tcontent: '暂无系统消息',\r\n\ttime: null,\r\n\tunreadNum: 0\r\n})\r\n\r\n// 加载状态\r\nconst loading = ref(false)\r\n\r\n// 用户消息列表\r\nconst userMessages = ref([])\r\n\r\n// 好友列表数据\r\n// 加载消息数据\r\nconst loadMessageData = () => {\r\n\tif (loading.value) return\r\n\tgetMessageGroup().then(response => {\r\n\t\tconst messageData = response.data\r\n\t\tconsole.log('消息数据详情:', messageData)\r\n\r\n\t\t// 更新系统消息计数和内容\r\n\t\tif (messageData.follow) {\r\n\t\t\tconsole.log('关注消息数据:', messageData.follow)\r\n\t\t\tfollowCount.value = messageData.follow.unreadNum || 0\r\n\t\t\tfollowMessage.value = {\r\n\t\t\t\tcontent: messageData.follow.content || '没有新通知',\r\n\t\t\t\ttime: messageData.follow.time,\r\n\t\t\t\tunreadNum: messageData.follow.unreadNum\r\n\t\t\t}\r\n\t\t\tconsole.log('处理后的关注消息:', followMessage.value)\r\n\t\t}\r\n\t\tif (messageData.related) {\r\n\t\t\tconsole.log('互动消息数据:', messageData.related)\r\n\t\t\tinteractionCount.value = messageData.related.unreadNum || 0\r\n\t\t\tinteractionMessage.value = {\r\n\t\t\t\tcontent: messageData.related.content || '暂无互动消息',\r\n\t\t\t\ttime: messageData.related.time,\r\n\t\t\t\tunreadNum: messageData.related.unreadNum\r\n\t\t\t}\r\n\t\t\tconsole.log('处理后的互动消息:', interactionMessage.value)\r\n\t\t}\r\n\t\tif (messageData.system) {\r\n\t\t\tconsole.log('系统消息数据:', messageData.system)\r\n\t\t\tsystemCount.value = messageData.system.unreadNum || 0\r\n\t\t\tsystemMessage.value = {\r\n\t\t\t\tcontent: messageData.system.content || '暂无系统消息',\r\n\t\t\t\ttime: messageData.system.time,\r\n\t\t\t\tunreadNum: messageData.system.unreadNum\r\n\t\t\t}\r\n\t\t\tconsole.log('处理后的系统消息:', systemMessage.value)\r\n\t\t}\r\n\r\n\t\t// 更新用户消息列表\r\n\t\tif (messageData.uMsgList && Array.isArray(messageData.uMsgList)) {\r\n\t\t\tconsole.log('用户消息列表原始数据:', messageData.uMsgList)\r\n\r\n\t\t\tuserMessages.value = messageData.uMsgList.map(item => {\r\n\t\t\t\tconsole.log('处理消息项:', {\r\n\t\t\t\t\tuid: item.uid,\r\n\t\t\t\t\tuName: item.uName,\r\n\t\t\t\t\tcontent: item.content,\r\n\t\t\t\t\ttime: item.time,\r\n\t\t\t\t\ttype: item.type\r\n\t\t\t\t})\r\n\r\n\t\t\t\treturn {\r\n\t\t\t\t\tid: item.uid,\r\n\t\t\t\t\tname: item.uName,\r\n\t\t\t\t\tavatar: item.uAvatar,\r\n\t\t\t\t\ttime: item.time,\r\n\t\t\t\t\tlastMessage: item.content,\r\n\t\t\t\t\tunreadCount: item.unreadNum || 0,\r\n\t\t\t\t\tgroupId: item.groupId,\r\n\t\t\t\t\tsendUserId: item.sendUserId,\r\n\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\tsubType: item.subType,\r\n\t\t\t\t\tcontentId: item.contentId,\r\n\t\t\t\t\tread: item.read\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\tconsole.log('处理后的用户消息列表:', userMessages.value)\r\n\t\t}\r\n\r\n\t\tconsole.log('消息数据加载成功，用户消息数量:', userMessages.value.length)\r\n\t}).finally(() => {\r\n\r\n\t\tloading.value = false\r\n\t})\r\n}\r\n\r\n// 页面跳转\r\nconst navigateTo = (url) => {\r\n\tuni.navigateTo({\r\n\t\turl\r\n\t})\r\n}\r\n\r\n// 跳转到聊天页面\r\nconst navigateToChat = (item) => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/message/chat/chat?id=${item.id}&name=${item.name}`\r\n\t})\r\n}\r\n\r\n// 处理页面滚动\r\nconst handlePageScroll = (e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n}\r\n\r\n// 处理导航栏高度变化\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 计算导航栏文字颜色\r\nconst getNavTextColor = () => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\r\n}\r\n\r\n// 标签切换处理\r\nconst handleTabChange = (tab) => {\r\n\tcurrentTab.value = tab\r\n}\r\n\r\n// follow-user组件事件处理\r\nconst handleUserClick = (user) => {\r\n\tconsole.log('点击用户:', user)\r\n\t// 跳转到用户详情页\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/user/profile?id=${user.id}`\r\n\t})\r\n}\r\n\r\nconst handleUserAction = (user) => {\r\n\tconsole.log('用户操作:', user)\r\n\t// 根据关系类型执行不同操作\r\n\tswitch (user.relationship) {\r\n\t\tcase 'mutual':\r\n\t\t\t// 互关用户，可以发消息\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/message/chat/chat?id=${user.id}&name=${user.name}`\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'following':\r\n\t\t\t// 已关注用户，可以取消关注\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认',\r\n\t\t\t\tcontent: `确定要取消关注 ${user.name} 吗？`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// TODO: 调用取消关注API\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '已取消关注',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t\tcase 'followers':\r\n\t\t\t// 粉丝，可以回关\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认',\r\n\t\t\t\tcontent: `确定要关注 ${user.name} 吗？`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// TODO: 调用关注API\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '关注成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\tbreak\r\n\t}\r\n}\r\n\r\nconst handleCategoryChange = (category) => {\r\n\tconsole.log('分类变化:', category)\r\n\tcurrentFriendCategory.value = category\r\n}\r\n\r\n\r\n// 页面滚动监听\r\nonPageScroll(handlePageScroll)\r\n\r\n// 页面加载时获取数据\r\nonMounted(() => {\r\n\t\r\n})\r\n\r\n// 页面显示时重新加载数据（tabBar重复点击时会触发）\r\nonShow(() => {\r\n\tconsole.log('消息页面显示，重新加载数据')\r\n\tif (!$store.isUserShort()) {\r\n\t\tloadMessageData()\r\n\t}\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(135deg,\r\n\t\t\t$primary-lighter 0%,\r\n\t\t\trgba(105, 108, 243, 0.03) 30%,\r\n\t\t\trgba(105, 108, 243, 0.01) 60%,\r\n\t\t\t$bg-primary 100%);\r\n}\r\n\r\n// nav-tabs 样式已移至独立组件中\r\n\r\n// 好友页面样式\r\n.friend-list {\r\n\tpadding: $spacing-md;\r\n\tbox-sizing: border-box;\r\n\r\n\t.friend-category {\r\n\t\tbackground: $bg-primary;\r\n\t\tborder-radius: $radius-md;\r\n\t\tpadding: $spacing-md $spacing-lg;\r\n\t\tmargin-bottom: $spacing-md;\r\n\t\tbox-shadow: $shadow-md;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tborder: 1px solid $border-color-light;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\r\n\t\t.category-item {\r\n\t\t\tflex: 1;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: $spacing-md $spacing-sm;\r\n\t\t\tborder-radius: $radius-sm;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\tcursor: pointer;\r\n\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: $font-size-sm;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\tmargin-bottom: $spacing-xs;\r\n\t\t\t}\r\n\r\n\t\t\t.count {\r\n\t\t\t\tbackground: $primary-light;\r\n\t\t\t\tcolor: $primary-color;\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\t\tpadding: 4rpx $spacing-sm;\r\n\t\t\t\tborder-radius: $radius-full;\r\n\t\t\t\tmin-width: 40rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tbackground: $primary-lighter;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.count {\r\n\t\t\t\t\tbackground: $primary-color;\r\n\t\t\t\t\tcolor: $text-white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: $primary-lighter;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.user-list {\r\n\t\t.user-item {\r\n\t\t\tbackground: $bg-primary;\r\n\t\t\tborder-radius: $radius-md;\r\n\t\t\tpadding: $spacing-md $spacing-lg;\r\n\t\t\tmargin-bottom: $spacing-md;\r\n\t\t\tbox-shadow: $shadow-md;\r\n\t\t\tbackdrop-filter: blur(10rpx);\r\n\t\t\tborder: 1px solid $border-color-light;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: $primary-lighter;\r\n\t\t\t\ttransform: translateX(4rpx);\r\n\t\t\t}\r\n\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 88rpx;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tborder-radius: $radius-full;\r\n\t\t\t\tmargin-right: $spacing-lg;\r\n\t\t\t\tbox-shadow: $shadow-sm;\r\n\t\t\t\tborder: 2rpx solid $border-color-light;\r\n\t\t\t}\r\n\r\n\t\t\t.user-info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmin-width: 0;\r\n\r\n\t\t\t\t.top {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: $spacing-xs;\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: $title-size-md;\r\n\t\t\t\t\t\tcolor: $text-primary;\r\n\t\t\t\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\t\t\t\tmargin-right: $spacing-sm;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.status {\r\n\t\t\t\t\t\tbackground: $success-color;\r\n\t\t\t\t\t\tcolor: $text-white;\r\n\t\t\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\t\t\tpadding: 2rpx $spacing-xs;\r\n\t\t\t\t\t\tborder-radius: $radius-sm;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bio {\r\n\t\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.action-btn {\r\n\t\t\t\tbackground: $primary-gradient;\r\n\t\t\t\tcolor: $text-white;\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\tpadding: $spacing-sm $spacing-lg;\r\n\t\t\t\tborder-radius: $radius-lg;\r\n\t\t\t\tbox-shadow: $primary-shadow;\r\n\t\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\ttransform: scale(1.05);\r\n\t\t\t\t\tbox-shadow: 0 6rpx 16rpx $primary-shadow;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.message-list {\r\n\tpadding: 0;\r\n\tbackground: $bg-primary;\r\n\r\n\t.loading-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 80rpx 0;\r\n\r\n\t\t.loading-spinner {\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tborder: 4rpx solid #f3f3f3;\r\n\t\t\tborder-top: 4rpx solid $primary-color;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tanimation: spin 1s linear infinite;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t}\r\n\r\n\t\t.loading-text {\r\n\t\t\tfont-size: $font-size-md;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t}\r\n\r\n\t.message-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: $spacing-lg $spacing-xl;\r\n\t\tborder-bottom: 1rpx solid $bg-secondary;\r\n\t\tposition: relative;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t&:active {\r\n\t\t\tbackground-color: $primary-lighter;\r\n\t\t}\r\n\r\n\t\t.avatar-container {\r\n\t\t\tposition: relative;\r\n\t\t\tmargin-right: $spacing-lg;\r\n\r\n\t\t\t.avatar {\r\n\t\t\t\twidth: 96rpx;\r\n\t\t\t\theight: 96rpx;\r\n\t\t\t\tborder-radius: $radius-full;\r\n\t\t\t\tbackground: $bg-secondary;\r\n\t\t\t\tbox-shadow: $shadow-sm;\r\n\t\t\t}\r\n\r\n\t\t\t.badge {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -8rpx;\r\n\t\t\t\tright: -8rpx;\r\n\t\t\t\tbackground: $error-color;\r\n\t\t\t\tcolor: $text-white;\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\tpadding: 2rpx $spacing-xs;\r\n\t\t\t\tborder-radius: $radius-full;\r\n\t\t\t\tmin-width: 28rpx;\r\n\t\t\t\theight: 28rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 24rpx;\r\n\t\t\t\tbox-shadow: $shadow-sm;\r\n\t\t\t\tz-index: 10;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\tflex: 1;\r\n\t\t\tmin-width: 0;\r\n\r\n\t\t\t.top-row {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: flex-start;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\tfont-size: $title-size-md;\r\n\t\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\t\tcolor: $text-primary;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\tmax-width: 400rpx;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.time-badge-container {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tmargin-left: $spacing-md;\r\n\r\n\t\t\t\t\t.time {\r\n\t\t\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\t\t\tcolor: $text-tertiary;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.subtitle {\r\n\t\t\t\tfont-size: $font-size-sm;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.status-tags {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tgap: $spacing-xs;\r\n\r\n\t\t\t\t.tag {\r\n\t\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\t\tcolor: $text-tertiary;\r\n\t\t\t\t\tbackground: $bg-secondary;\r\n\t\t\t\t\tpadding: 4rpx $spacing-xs;\r\n\t\t\t\t\tborder-radius: $radius-sm;\r\n\t\t\t\t\tborder: 1rpx solid $border-color-light;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t// 系统消息特殊样式\r\n\t\t&.system-message {\r\n\t\t\t.avatar-container .avatar {\r\n\t\t\t\tbackground: $primary-gradient;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbox-shadow: $shadow-md;\r\n\r\n\t\t\t\t&.system-avatar {\r\n\t\t\t\t\t.avatar-icon {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.follow-avatar {\r\n\t\t\t\t\t\tbackground: $primary-gradient;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.interaction-avatar {\r\n\t\t\t\t\t\tbackground: $primary-gradient;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.system-avatar-red {\r\n\t\t\t\t\t\tbackground: $primary-gradient;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 用户消息特殊样式\r\n\t\t&.user-message {\r\n\t\t\t.avatar-container .avatar {\r\n\t\t\t\tborder: 2rpx solid $border-color-light;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.empty-state {\r\n\t\tpadding: 120rpx $spacing-xl;\r\n\t\ttext-align: center;\r\n\r\n\t\t.empty-text {\r\n\t\t\tfont-size: $font-size-sm;\r\n\t\t\tcolor: $text-tertiary;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 响应式设计 - 针对不同屏幕尺寸优化\r\n@media screen and (max-width: 750rpx) {\r\n\t.message-list {\r\n\t\tpadding: 16rpx;\r\n\r\n\t\t.message-section {\r\n\t\t\tpadding: 16rpx 20rpx;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\r\n\t\t\t.section-header .title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.message-item {\r\n\t\t\t\tpadding: 14rpx 8rpx;\r\n\r\n\t\t\t\t.avatar {\r\n\t\t\t\t\twidth: 68rpx;\r\n\t\t\t\t\theight: 68rpx;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.content {\r\n\t\t\t\t\t.top .name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.message {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 深色模式适配（预留）\r\n@media (prefers-color-scheme: dark) {\r\n\t.page-container {\r\n\t\tbackground: linear-gradient(135deg,\r\n\t\t\t\trgba(105, 108, 243, 0.15) 0%,\r\n\t\t\t\trgba(105, 108, 243, 0.10) 30%,\r\n\t\t\t\trgba(105, 108, 243, 0.05) 60%,\r\n\t\t\t\trgba(30, 30, 30, 1) 100%);\r\n\t}\r\n\r\n\t.message-list .message-section {\r\n\t\tbackground: rgba(40, 40, 40, 0.95);\r\n\t\tborder: 1px solid rgba(255, 255, 255, 0.1);\r\n\r\n\t\t.section-header .title {\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t.message-item {\r\n\t\t\tborder-bottom-color: rgba(255, 255, 255, 0.1);\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\tbackground: rgba(105, 108, 243, 0.1);\r\n\t\t\t}\r\n\r\n\t\t\t.content {\r\n\t\t\t\t.top .name {\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.message {\r\n\t\t\t\t\tcolor: #ccc;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes spin {\r\n\t0% {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pages/message/message.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "currentTab", "currentFriendCategory", "messageTabs", "followCount", "interactionCount", "systemCount", "followMessage", "interactionMessage", "systemMessage", "loading", "userMessages", "loadMessageData", "getMessageGroup", "response", "messageData", "uni", "item", "navigateTo", "url", "navigateToChat", "handlePageScroll", "e", "handleNavHeightChange", "height", "getNavTextColor", "handleTabChange", "tab", "handleUserClick", "user", "handleUserAction", "res", "handleCategoryChange", "category", "onPageScroll", "onMounted", "onShow", "$store", "MiniProgramPage"], "mappings": "2xBAuIA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EAEAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,SAAA,EAEAG,EAAAH,EAAA,IAAA,WAAA,EAGAI,EAAAJ,EAAAA,IAAA,CACA,CAAA,MAAA,KAAA,MAAA,SAAA,EACA,CAAA,MAAA,KAAA,MAAA,QAAA,CACA,CAAA,EAGAK,EAAAL,EAAA,IAAA,CAAA,EACAM,EAAAN,EAAA,IAAA,CAAA,EACAO,EAAAP,EAAA,IAAA,CAAA,EAGAQ,EAAAR,EAAAA,IAAA,CACA,QAAA,QACA,KAAA,KACA,UAAA,CACA,CAAA,EACAS,EAAAT,EAAAA,IAAA,CACA,QAAA,SACA,KAAA,KACA,UAAA,CACA,CAAA,EACAU,EAAAV,EAAAA,IAAA,CACA,QAAA,SACA,KAAA,KACA,UAAA,CACA,CAAA,EAGAW,EAAAX,EAAA,IAAA,EAAA,EAGAY,EAAAZ,EAAA,IAAA,EAAA,EAIAa,EAAA,IAAA,CACAF,EAAA,OACAG,kBAAA,EAAA,KAAAC,GAAA,CACA,MAAAC,EAAAD,EAAA,KACAE,EAAAA,MAAA,MAAA,MAAA,mCAAA,UAAAD,CAAA,EAGAA,EAAA,SACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,UAAAD,EAAA,MAAA,EACAX,EAAA,MAAAW,EAAA,OAAA,WAAA,EACAR,EAAA,MAAA,CACA,QAAAQ,EAAA,OAAA,SAAA,QACA,KAAAA,EAAA,OAAA,KACA,UAAAA,EAAA,OAAA,SACA,EACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,YAAAT,EAAA,KAAA,GAEAQ,EAAA,UACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,UAAAD,EAAA,OAAA,EACAV,EAAA,MAAAU,EAAA,QAAA,WAAA,EACAP,EAAA,MAAA,CACA,QAAAO,EAAA,QAAA,SAAA,SACA,KAAAA,EAAA,QAAA,KACA,UAAAA,EAAA,QAAA,SACA,EACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,YAAAR,EAAA,KAAA,GAEAO,EAAA,SACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,UAAAD,EAAA,MAAA,EACAT,EAAA,MAAAS,EAAA,OAAA,WAAA,EACAN,EAAA,MAAA,CACA,QAAAM,EAAA,OAAA,SAAA,SACA,KAAAA,EAAA,OAAA,KACA,UAAAA,EAAA,OAAA,SACA,EACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,YAAAP,EAAA,KAAA,GAIAM,EAAA,UAAA,MAAA,QAAAA,EAAA,QAAA,IACAC,EAAA,MAAA,MAAA,MAAA,mCAAA,cAAAD,EAAA,QAAA,EAEAJ,EAAA,MAAAI,EAAA,SAAA,IAAAE,IACAD,EAAAA,MAAA,MAAA,MAAA,mCAAA,SAAA,CACA,IAAAC,EAAA,IACA,MAAAA,EAAA,MACA,QAAAA,EAAA,QACA,KAAAA,EAAA,KACA,KAAAA,EAAA,IACA,CAAA,EAEA,CACA,GAAAA,EAAA,IACA,KAAAA,EAAA,MACA,OAAAA,EAAA,QACA,KAAAA,EAAA,KACA,YAAAA,EAAA,QACA,YAAAA,EAAA,WAAA,EACA,QAAAA,EAAA,QACA,WAAAA,EAAA,WACA,KAAAA,EAAA,KACA,QAAAA,EAAA,QACA,UAAAA,EAAA,UACA,KAAAA,EAAA,IACA,EACA,EAEAD,EAAA,MAAA,MAAA,MAAA,mCAAA,cAAAL,EAAA,KAAA,GAGAK,EAAA,MAAA,MAAA,MAAA,mCAAA,mBAAAL,EAAA,MAAA,MAAA,CACA,CAAA,EAAA,QAAA,IAAA,CAEAD,EAAA,MAAA,EACA,CAAA,CACA,EAGAQ,EAAAC,GAAA,CACAH,EAAAA,MAAA,WAAA,CACA,IAAAG,CACA,CAAA,CACA,EAGAC,EAAAH,GAAA,CACAD,EAAAA,MAAA,WAAA,CACA,IAAA,+BAAAC,EAAA,EAAA,SAAAA,EAAA,IAAA,EACA,CAAA,CACA,EAGAI,EAAAC,GAAA,CACAxB,EAAA,MAAAwB,EAAA,SACA,EAGAC,EAAAC,GAAA,CACAxB,EAAA,MAAAwB,CACA,EAGAC,EAAA,IACA,KAAA,IAAA3B,EAAA,MAAA,IAAA,CAAA,EACA,GAAA,UAAA,UAIA4B,EAAAC,GAAA,CACA1B,EAAA,MAAA0B,CACA,EAGAC,EAAAC,GAAA,CACAb,EAAAA,MAAA,MAAA,MAAA,mCAAA,QAAAa,CAAA,EAEAb,EAAAA,MAAA,WAAA,CACA,IAAA,0BAAAa,EAAA,EAAA,EACA,CAAA,CACA,EAEAC,EAAAD,GAAA,CAGA,OAFAb,EAAAA,MAAA,MAAA,MAAA,mCAAA,QAAAa,CAAA,EAEAA,EAAA,aAAA,CACA,IAAA,SAEAb,EAAAA,MAAA,WAAA,CACA,IAAA,+BAAAa,EAAA,EAAA,SAAAA,EAAA,IAAA,EACA,CAAA,EACA,MACA,IAAA,YAEAb,EAAAA,MAAA,UAAA,CACA,MAAA,KACA,QAAA,WAAAa,EAAA,IAAA,MACA,QAAAE,GAAA,CACAA,EAAA,SAEAf,EAAAA,MAAA,UAAA,CACA,MAAA,QACA,KAAA,SACA,CAAA,CAEA,CACA,CAAA,EACA,MACA,IAAA,YAEAA,EAAAA,MAAA,UAAA,CACA,MAAA,KACA,QAAA,SAAAa,EAAA,IAAA,MACA,QAAAE,GAAA,CACAA,EAAA,SAEAf,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,CAEA,CACA,CAAA,EACA,KACA,CACA,EAEAgB,EAAAC,GAAA,CACAjB,EAAAA,MAAA,MAAA,MAAA,mCAAA,QAAAiB,CAAA,EACA/B,EAAA,MAAA+B,CACA,EAIAC,OAAAA,EAAA,aAAAb,CAAA,EAGAc,EAAAA,UAAA,IAAA,CAEA,CAAA,EAGAC,EAAAA,OAAA,IAAA,CACApB,EAAAA,MAAA,MAAA,MAAA,mCAAA,eAAA,EACAqB,EAAAA,OAAA,eACAzB,EAAA,CAEA,CAAA,22CC7WA,GAAG,WAAW0B,CAAe"}