package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserAlbum;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAlbumBoToUserAlbumMapperImpl implements UserAlbumBoToUserAlbumMapper {

    @Override
    public UserAlbum convert(UserAlbumBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAlbum userAlbum = new UserAlbum();

        userAlbum.setId( arg0.getId() );

        return userAlbum;
    }

    @Override
    public UserAlbum convert(UserAlbumBo arg0, UserAlbum arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );

        return arg1;
    }
}
