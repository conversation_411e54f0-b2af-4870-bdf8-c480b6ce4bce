package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.AppUserInvitationCreateBoToUserInvitationMapper;
import com.gzhuxn.personals.controller.app.user.vo.invitation.AppUserInvitationVoToUserInvitationMapper;
import com.gzhuxn.personals.domain.user.bo.UserInvitationBoToUserInvitationMapper;
import com.gzhuxn.personals.domain.user.vo.UserInvitationVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserInvitationVoToUserInvitationMapper.class,UserInvitationBoToUserInvitationMapper.class,AppUserInvitationCreateBoToUserInvitationMapper.class,UserInvitationToAppUserInvitationVoMapper.class},
    imports = {}
)
public interface UserInvitationToUserInvitationVoMapper extends BaseMapper<UserInvitation, UserInvitationVo> {
}
