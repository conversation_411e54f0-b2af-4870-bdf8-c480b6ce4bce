package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserAuthApplyBoToUserAuthApplyMapper;
import com.gzhuxn.personals.domain.user.vo.UserAuthApplyVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAuthApplyBoToUserAuthApplyMapper.class},
    imports = {}
)
public interface UserAuthApplyToUserAuthApplyVoMapper extends BaseMapper<UserAuthApply, UserAuthApplyVo> {
}
