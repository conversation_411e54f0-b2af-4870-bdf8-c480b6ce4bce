package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserAccountVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAccountToUserAccountVoMapperImpl implements UserAccountToUserAccountVoMapper {

    @Override
    public UserAccountVo convert(UserAccount arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAccountVo userAccountVo = new UserAccountVo();

        userAccountVo.setUserId( arg0.getUserId() );
        userAccountVo.setCoin( arg0.getCoin() );
        userAccountVo.setLockCoin( arg0.getLockCoin() );
        userAccountVo.setWithdrawCoin( arg0.getWithdrawCoin() );
        userAccountVo.setLockWithdrawCoin( arg0.getLockWithdrawCoin() );

        return userAccountVo;
    }

    @Override
    public UserAccountVo convert(UserAccount arg0, UserAccountVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setLockCoin( arg0.getLockCoin() );
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setLockWithdrawCoin( arg0.getLockWithdrawCoin() );

        return arg1;
    }
}
