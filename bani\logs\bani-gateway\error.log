2025-08-12 21:59:33 [reactor-http-nio-10] ERROR c.gzhuxn.gateway.filter.AuthFilter - AuthFilter.error
org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187764399 [redisClient=[addr=redis://101.200.242.239:8762], channel=[id: 0x39cdcbcb, L:/192.168.0.108:63394 ! R:101.200.242.239/101.200.242.239:8762], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJtaW5pOjE5NTM0NzQ1ODA0NjMyMDY0MDIiLCJyblN0ciI6InhCUFBMekY3M3pFUjJYa01vMVgyVWdBUUsza0ZNSWIxIiwiQ2xpZW50LUlkIjoiNTIwMDEiLCJ1c2VySWQiOjE5NTM0NzQ1ODA0NjMyMDY0MDIsInVzZXJOYW1lIjoi5b6u5L-h55So5oi3In0.cpaPvyFo04EJEMO7ZIMLqatB7kKzFtuiOO7ekpCsSeY] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:364)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:194)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-12 22:02:14 [reactor-http-nio-10] ERROR c.g.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/app-api/auth/login,异常信息:Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1464691232 [redisClient=[addr=redis://101.200.242.239:8762], channel=[id: 0x398ec9f8, L:/192.168.0.108:63195 ! R:101.200.242.239/101.200.242.239:8762], currentCommand=null, usage=1], command: (GET), params: [Authorization:var:same-token] after 3 retry attempts
