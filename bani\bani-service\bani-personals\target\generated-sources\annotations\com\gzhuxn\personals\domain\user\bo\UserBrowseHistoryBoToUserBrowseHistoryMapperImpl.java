package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserBrowseHistory;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBrowseHistoryBoToUserBrowseHistoryMapperImpl implements UserBrowseHistoryBoToUserBrowseHistoryMapper {

    @Override
    public UserBrowseHistory convert(UserBrowseHistoryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserBrowseHistory userBrowseHistory = new UserBrowseHistory();

        userBrowseHistory.setUserId( arg0.getUserId() );
        userBrowseHistory.setType( arg0.getType() );
        userBrowseHistory.setBusinessId( arg0.getBusinessId() );

        return userBrowseHistory;
    }

    @Override
    public UserBrowseHistory convert(UserBrowseHistoryBo arg0, UserBrowseHistory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );

        return arg1;
    }
}
