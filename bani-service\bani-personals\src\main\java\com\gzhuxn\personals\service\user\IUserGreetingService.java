package com.gzhuxn.personals.service.user;

import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.personals.controller.app.user.bo.AppUserGreetingCreateBo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserGreetingVo;
import com.gzhuxn.personals.domain.user.UserGreeting;
import com.gzhuxn.personals.domain.user.vo.UserGreetingVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户-打招呼Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IUserGreetingService {

    /**
     * 查询用户-打招呼
     *
     * @param id 主键
     * @return 用户-打招呼
     */
    UserGreetingVo queryById(Long id);

    /**
     * 分页查询收到的打招呼列表
     *
     * @param pageQuery 分页参数
     * @return 打招呼分页列表
     */
    TableDataInfo<AppUserGreetingVo> queryReceivedPageList(PageQuery pageQuery);

    /**
     * 分页查询发送的打招呼列表
     *
     * @param pageQuery 分页参数
     * @return 打招呼分页列表
     */
    TableDataInfo<AppUserGreetingVo> querySentPageList(PageQuery pageQuery);

    /**
     * 新增用户-打招呼
     *
     * @param bo 用户-打招呼
     * @return 是否新增成功
     */
    boolean insertByBo(AppUserGreetingCreateBo bo);

    /**
     * 校验并批量删除用户-打招呼信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    boolean deleteWithValidByIds(Collection<Long> ids, boolean isValid);

    /**
     * 检查用户是否已经向对方打过招呼
     *
     * @param userId         当前用户ID
     * @param oppositeUserId 对方用户ID
     * @return 是否已打招呼
     */
    boolean hasGreeted(Long userId, Long oppositeUserId);

    /**
     * 回复打招呼
     *
     * @param id      打招呼记录ID
     * @param content 回复内容
     * @return 是否回复成功
     */
    boolean reply(Long id, String content);

    /**
     * 忽略打招呼
     *
     * @param id 打招呼记录ID
     * @return 是否忽略成功
     */
    boolean ignore(Long id);
}
