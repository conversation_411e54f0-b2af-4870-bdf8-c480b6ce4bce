<template>
	<scroll-nav-page title="发现" @heightChange="handleNavHeightChange">
		<template #nav-left>
			<text class="petal-text with-red-dot" :style="{ color: getNavTextColor() }" @click="showPetalModal">签到</text>
		</template>
		<template #content>
			<view class="moment-container">
				<!-- 内容区域 - 使用z-paging-swiper作为根节点免计算高度 -->
				<view class="content-wrapper">
					<!-- z-paging主体内容 -->
					<z-paging ref="paging" v-model="momentList" @query="queryMomentList">
						<template #top>
							<!-- 顶部间距，避免被导航栏覆盖 -->
							<view class="nav-spacer" :style="{ paddingTop: navBarHeight + 'px' }"></view>

							<!-- 标签导航区域 -->
							<z-tabs :list="tabList" :current="currentTab" @change="handleTabChange"
								active-color="#696CF3" inactive-color="#666666">
							</z-tabs>
						</template>
						<!-- 自定义刷新组件 -->
						<template #refresher="{ refresherStatus }">
							<custom-refresher :refresher-status="refresherStatus" />
						</template>
						<!-- 热门话题区域 -->
						<view class="hot-topics-section">
							<view class="section-header">
								<view class="header-left">
									<view class="topic-icon">
										<text class="icon-text">#</text>
									</view>
									<text class="section-title">热门话题</text>
								</view>
								<view class="more-btn" @click="handleMoreTopics">
									<text class="more-text">更多话题</text>
									<uni-icons type="right" size="16" color="#999"></uni-icons>
								</view>
							</view>

							<view class="topics-grid">
								<view class="topic-item" v-for="(topic, index) in hotTopics" :key="index"
									@click="handleTopicClick(topic)">
									<image :src="topic.icon" mode="aspectFill" class="topic-avatar"></image>
									<view class="topic-info">
										<text class="topic-title">{{ topic.title }}</text>
										<text class="topic-count">{{ topic.count }}条动态</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 动态列表组件 -->
						<MomentList :moment-list="momentList" :show-follow-btn="true" @moment-click="handleMomentClick" />
					</z-paging>
				</view>
				<!-- 右下角浮动按钮 -->
				<view class="floating-btn" @click="goToAddMoment">
					<uni-icons type="plus" size="28" color="#fff"></uni-icons>
				</view>

				<!-- 签到弹窗 -->
				<SigninModal ref="signinModalRef">
				</SigninModal>
			</view>
		</template>
	</scroll-nav-page>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { onPageScroll, onShow } from '@dcloudio/uni-app'
import { getRecommendMomentPage, formatRecommendMoment, RECOMMEND_TYPE, getTopicList, formatTopic } from '@/api/moment/recommend'
import SigninModal from '@/components/signin/signin-modal.vue'
import MomentList from '@/components/moment-list/moment-list.vue'

// 如果z-tabs没有自动识别，取消下面的注释
// import zTabs from '@/uni_modules/z-tabs/components/z-tabs/z-tabs.vue'

// 如果uni-popup没有自动识别，取消下面的注释
// import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)
// 当前选中的标签
const currentTab = ref(1) // 默认选中"最新"

// 标签导航数据
const tabList = ref([
	'关注',
	'最新',
	'最热'
])

// 热门话题数据
const hotTopics = ref([])
const isLoadingTopics = ref(false)

// 动态列表数据
const momentList = ref([])

// z-paging引用
const paging = ref(null)

// 签到弹窗引用
const signinModalRef = ref(null)



// 处理标签切换
const handleTabChange = (index) => {
	console.log('Tab changed to:', index, tabList.value[index])
	currentTab.value = index
	// 当切换tab时请调用组件的reload方法，请勿直接调用：queryMomentList方法！！
	if (paging.value) {
		paging.value.reload()
	}
}

// 话题点击
const handleTopicClick = (topic) => {
	console.log('点击话题:', topic)
	uni.navigateTo({
		url: `/pagesubs/moment/tag/detail?id=${topic.id}&name=${encodeURIComponent(topic.title)}`
	})
}

// 加载热门话题列表
const loadHotTopics = () => {
	isLoadingTopics.value = true
	getTopicList().then(response => {
		// 格式化话题数据
		const formattedTopics = response.data.map(item => formatTopic(item))
		// 只显示前4个话题
		hotTopics.value = formattedTopics.slice(0, 4).map(topic => ({
			id: topic.id,
			title: topic.name,
			count: topic.momentCount,
			icon: topic.icon
		}))
	}).finally(() => {
		isLoadingTopics.value = false
	})
}

// 更多话题
const handleMoreTopics = () => {
	uni.navigateTo({
		url: '/pagesubs/moment/tag/list'
	})
}

// z-paging查询数据 - 必须使用async函数
const queryMomentList = async (pageNo, pageSize) => {
	// 根据当前标签页确定查询类型
	let queryType = RECOMMEND_TYPE.LATEST // 默认最新
	switch (currentTab.value) {
		case 0:
			queryType = RECOMMEND_TYPE.FOLLOWING // 关注
			break
		case 1:
			queryType = RECOMMEND_TYPE.LATEST // 最新
			break
		case 2:
			queryType = RECOMMEND_TYPE.HOT // 最热
			break
	}

	// 准备查询参数
	const params = {
		type: queryType,
		pageNum: pageNo,
		pageSize: pageSize,
	}
	// 调用API查询推荐动态列表
	getRecommendMomentPage(params).then(response => {
		// 格式化数据
		const formattedData = response.rows.map(item => formatRecommendMoment(item))
		paging.value.complete(formattedData)
	})
}

// 显示签到弹窗
const showPetalModal = () => {
	if (signinModalRef.value) {
		signinModalRef.value.openModal()
	}
}

// 跳转到新增动态页面
const goToAddMoment = () => {
	uni.navigateTo({
		url: '/pagesubs/moment/add'
	})
}

// 处理动态点击
const handleMomentClick = (item) => {
	uni.navigateTo({
		url: `/pagesubs/moment/detail?id=${item.id}`
	})
}


// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}

// 页面挂载时加载话题
onMounted(() => {
	loadHotTopics()
})

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 调试信息
console.log('tabList:', tabList.value)
console.log('currentTab:', currentTab.value)
</script>

<style>
@import '@/static/fonts/iconfont.css';

/* 标签导航样式优化 */
.z-tabs {
	display: flex;
	justify-content: center;
	padding: 0 60rpx;
}

/* 深度选择器调整z-tabs内部样式 */
:deep(.z-tabs-scroll) {
	display: flex;
	justify-content: center;
}

:deep(.z-tabs-scroll-view) {
	display: flex;
	justify-content: center;
}

:deep(.z-tabs-item) {
	margin: 0 16rpx !important;
	/* 减少标签间距 */
	padding: 16rpx 20rpx !important;
	/* 调整内边距 */
	min-width: auto !important;
}

:deep(.z-tabs-item-text) {
	font-size: 30rpx !important;
	font-weight: 500 !important;
}

.moment-container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.content-wrapper {
	height: 100vh;
	box-sizing: border-box;
}

/* 普通模式需要设置容器高度 */
.content-wrapper z-paging {
	height: 100%;
}

/* 导航栏间距 */
.nav-spacer {
	width: 100%;
	flex-shrink: 0;
}

/* 顶部导航栏样式 */
.nav-left {
	display: flex;
	align-items: center;
	padding: 10rpx 2rpx;
	cursor: pointer;
	min-height: 44rpx;
	min-width: 120rpx;
	position: relative;
	z-index: 1000;

	&:active {
		opacity: 0.7;
		transform: scale(0.95);
	}
}

.test-btn {
	padding: 8rpx 16rpx;
	font-size: 28rpx;
	cursor: pointer;

	&:active {
		opacity: 0.7;
	}
}



.petal-icon {
	width: 40rpx;
	height: 40rpx;
}

.petal-text {
	font-size: 28rpx;
	margin-left: 4rpx;
	white-space: nowrap;
	position: relative;
}

.petal-text.with-red-dot::after {
	content: '';
	position: absolute;
	top: -2rpx;
	right: -14rpx;
	width: 10rpx;
	height: 10rpx;
	background-color: #ff4757;
	border-radius: 50%;
	border: 1rpx solid #fff;
}

.nav-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #696CF3;
	text-align: center;
	width: 100%;
	position: absolute;
	left: 0;
	right: 0;
}

.nav-right {
	width: 60rpx;
}



/* 热门话题样式 */
.hot-topics-section {
	background-color: rgba(240, 248, 255, 0.8);
	margin: 12rpx 16rpx;
	border-radius: 12rpx;
	padding: 16rpx;
	flex-shrink: 0;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.header-left {
	display: flex;
	align-items: center;
}

.topic-icon {
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #4285f4, #34a853);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12rpx;
}

.icon-text {
	color: white;
	font-size: 22rpx;
	font-weight: bold;
}

.section-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.more-btn {
	display: flex;
	align-items: center;
}

.more-text {
	font-size: 24rpx;
	color: #999;
	margin-right: 6rpx;
}

.topics-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12rpx;
}

.topic-item {
	display: flex;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 10rpx;
	padding: 14rpx;
	transition: all 0.3s ease;
}

.topic-item:active {
	transform: scale(0.98);
	background-color: rgba(66, 133, 244, 0.1);
}

.topic-avatar {
	width: 50rpx;
	height: 50rpx;
	border-radius: 10rpx;
	margin-right: 12rpx;
}

.topic-info {
	flex: 1;
}

.topic-title {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 4rpx;
	display: block;
}

.topic-count {
	font-size: 22rpx;
	color: #666;
	display: block;
}

/* 使用z-paging-swiper时无需设置额外高度和样式覆盖 */

/* 右下角浮动按钮 */
.floating-btn {
	position: fixed;
	right: 30rpx;
	bottom: 120rpx;
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.4);
	z-index: 999;
	transition: all 0.3s ease;
}

.floating-btn:active {
	transform: scale(0.9);
	box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.6);
}
</style>