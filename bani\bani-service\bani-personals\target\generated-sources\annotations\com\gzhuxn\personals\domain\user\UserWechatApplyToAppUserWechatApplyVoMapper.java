package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.wechatapply.AppUserWechatApplyAuditBoToUserWechatApplyMapper;
import com.gzhuxn.personals.controller.app.user.bo.wechatapply.AppUserWechatApplyCreateBoToUserWechatApplyMapper;
import com.gzhuxn.personals.controller.app.user.vo.wechatapply.AppUserWechatApplyVo;
import com.gzhuxn.personals.domain.user.bo.UserWechatApplyBoToUserWechatApplyMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserWechatApplyBoToUserWechatApplyMapper.class,AppUserWechatApplyAuditBoToUserWechatApplyMapper.class,AppUserWechatApplyCreateBoToUserWechatApplyMapper.class,UserWechatApplyToUserWechatApplyVoMapper.class},
    imports = {}
)
public interface UserWechatApplyToAppUserWechatApplyVoMapper extends BaseMapper<UserWechatApply, AppUserWechatApplyVo> {
  @Mapping(
      target = "oppUserId",
      source = "oppositeUserId"
  )
  AppUserWechatApplyVo convert(UserWechatApply source);

  @Mapping(
      target = "oppUserId",
      source = "oppositeUserId"
  )
  AppUserWechatApplyVo convert(UserWechatApply source, @MappingTarget AppUserWechatApplyVo target);
}
