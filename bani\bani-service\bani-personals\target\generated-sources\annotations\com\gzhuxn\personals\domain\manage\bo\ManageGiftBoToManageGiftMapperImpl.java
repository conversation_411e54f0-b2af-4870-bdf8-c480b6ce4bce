package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageGift;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageGiftBoToManageGiftMapperImpl implements ManageGiftBoToManageGiftMapper {

    @Override
    public ManageGift convert(ManageGiftBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageGift manageGift = new ManageGift();

        manageGift.setSearchValue( arg0.getSearchValue() );
        manageGift.setCreateBy( arg0.getCreateBy() );
        manageGift.setCreateTime( arg0.getCreateTime() );
        manageGift.setUpdateBy( arg0.getUpdateBy() );
        manageGift.setUpdateTime( arg0.getUpdateTime() );
        manageGift.setDelFlag( arg0.getDelFlag() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            manageGift.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        manageGift.setCreateDept( arg0.getCreateDept() );
        manageGift.setId( arg0.getId() );
        manageGift.setName( arg0.getName() );
        manageGift.setIcon( arg0.getIcon() );
        manageGift.setPrice( arg0.getPrice() );
        manageGift.setFreeFlag( arg0.getFreeFlag() );
        manageGift.setSort( arg0.getSort() );
        manageGift.setStatus( arg0.getStatus() );

        return manageGift;
    }

    @Override
    public ManageGift convert(ManageGiftBo arg0, ManageGift arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setDelFlag( arg0.getDelFlag() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setFreeFlag( arg0.getFreeFlag() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
