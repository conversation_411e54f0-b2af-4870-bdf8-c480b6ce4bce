package com.gzhuxn.personals.service.user.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gzhuxn.common.base.utils.AssertUtils;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.controller.app.manage.vo.AppManageGiftVo;
import com.gzhuxn.personals.controller.app.user.bo.gift.AppUserGiftBo;
import com.gzhuxn.personals.controller.app.user.bo.gift.AppUserGiftCreateBo;
import com.gzhuxn.personals.controller.app.user.vo.gift.*;
import com.gzhuxn.personals.domain.manage.ManageGift;
import com.gzhuxn.personals.domain.user.UserGift;
import com.gzhuxn.personals.domain.user.bo.UserAccountUpdateBo;
import com.gzhuxn.personals.domain.user.bo.UserGiftBo;
import com.gzhuxn.personals.domain.user.vo.UserGiftVo;
import com.gzhuxn.personals.enums.coin.CoinType;
import com.gzhuxn.personals.enums.user.account.AccountUpType;
import com.gzhuxn.personals.enums.user.gift.GiftQueryType;
import com.gzhuxn.personals.mapper.user.UserGiftMapper;
import com.gzhuxn.personals.service.manage.IManageGiftService;
import com.gzhuxn.personals.service.user.IUserAccountService;
import com.gzhuxn.personals.service.user.IUserDetailTransitionService;
import com.gzhuxn.personals.service.user.IUserGiftService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户-发出的礼物Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@RequiredArgsConstructor
@Service
public class UserGiftServiceImpl extends ServiceImpl<UserGiftMapper, UserGift> implements IUserGiftService {
    private final IManageGiftService giftManageService;

    private final IUserAccountService userAccountService;

    private final IUserDetailTransitionService userDetailTransitionService;

    @Override
    public UserGiftVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<UserGiftVo> queryPageList(UserGiftBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserGift> lqw = baseMapper.buildQueryWrapper(bo);
        Page<UserGiftVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<AppUserGiftVo> queryAppPageList(AppUserGiftBo bo, PageQuery pageQuery) {
        // 设置当前登录用户ID
        Long loginUserId = LoginHelper.getUserId();
        bo.setUserId(loginUserId);

        // 如果没有指定查询类型，默认查询我送给他人的礼物
        if (bo.getQueryType() == null) {
            bo.setQueryType(GiftQueryType.SENT_BY_ME.getValue());
        }

        // 验证查询类型
        GiftQueryType.valid(bo.getQueryType());

        // 使用自定义SQL查询
        Page<AppUserGiftVo> result = baseMapper.selectAppPageListByQueryType(pageQuery.build(), bo);

        // 构建对方用户信息
        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            userDetailTransitionService.buildOppositeUsers(result.getRecords());
        }

        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = "#bo.userId", expire = 60000, acquireTimeout = 1000)
    public boolean insertByBo(AppUserGiftCreateBo bo) {
        UserGift add = MapstructUtils.convert(bo, UserGift.class);
        validEntityBeforeSave(add);

        // 保存数据
        save(add);

        // 更新用户账户
        return userAccountService.update(AccountUpType.GIFT_OUT, UserAccountUpdateBo.builder()
            .userId(add.getUserId())
            .oppositeUserId(add.getOppositeUserId())
            .businessId(add.getId())
            .coin(add.getGiftNum() * add.getGiftPrice())
            .coinType(CoinType.WITHDRAW)
            .dsc("送出礼物")
            .build());
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserGift entity) {
        ManageGift giftManage = giftManageService.getById(entity.getGiftId());
        AssertUtils.notNull(giftManage, "礼物不存在，请刷新页面！");

        AssertUtils.isTrue(giftManage.getPrice().equals(entity.getGiftPrice()), "礼物价格已发生变化，请刷新页面！");

        // 计算总花瓣
        int totalCoin = entity.getGiftPrice() * entity.getGiftNum();
        entity.setCoin(totalCoin);
        // 判断用户花瓣是否充足
        boolean isEnough = userAccountService.isEnough(entity.getUserId(), CoinType.WITHDRAW, totalCoin);
        AssertUtils.isTrue(isEnough, "{coin.name}不足，请充值！");
    }

    @Override
    public List<String> queryUsedByIds(List<Long> giftIds) {
        return baseMapper.queryUsedByIds(giftIds);
    }

    @Override
    public AppUserGiftWallVo queryGiftWallByUserId(Long userId, PageQuery pageQuery) {
        // 查询用户收到的礼物总数量
        Integer totalGiftCount = baseMapper.selectReceivedGiftTotalCount(userId);

        // 分页查询礼物墙列表（按礼物类型聚合）
        Page<AppUserGiftWallItemVo> giftPage = baseMapper.selectGiftWallPageList(pageQuery.build(), userId);

        // 构建返回结果
        AppUserGiftWallVo giftWallVo = new AppUserGiftWallVo();
        giftWallVo.setTotalGiftCount(totalGiftCount != null ? totalGiftCount : 0);
        giftWallVo.setGiftList(giftPage.getRecords());

        return giftWallVo;
    }

    @Override
    public AppUserLitGiftListVo queryLitGiftListByUserId(Long userId) {
        // 查询所有礼物列表
        List<AppManageGiftVo> allGifts = giftManageService.queryAppList();

        // 查询用户收到的礼物统计
        List<AppUserLitGiftItemVo> receivedGifts = baseMapper.selectReceivedGiftListByUserId(userId);
        Map<Long, AppUserLitGiftItemVo> receivedGiftMap = receivedGifts.stream()
            .collect(Collectors.toMap(AppUserLitGiftItemVo::getId, item -> item));

        // 查询用户收到的礼物总数量
        Integer totalGiftCount = baseMapper.selectReceivedGiftTotalCount(userId);

        // 构建点亮礼物列表
        List<AppUserLitGiftItemVo> giftList = allGifts.stream().map(gift -> {
            AppUserLitGiftItemVo item = new AppUserLitGiftItemVo();
            item.setId(gift.getId());
            item.setName(gift.getName());
            item.setIcon(gift.getIcon());
            item.setPrice(gift.getPrice());

            // 检查是否已收到该礼物
            AppUserLitGiftItemVo receivedGift = receivedGiftMap.get(gift.getId());
            item.setIsReceived(receivedGift != null);
            item.setReceivedCount(receivedGift != null ? receivedGift.getReceivedCount() : 0);

            return item;
        }).collect(Collectors.toList());

        // 构建返回结果
        AppUserLitGiftListVo result = new AppUserLitGiftListVo();
        result.setGiftList(giftList);
        result.setTotalGiftCount(totalGiftCount != null ? totalGiftCount : 0);

        return result;
    }
}
