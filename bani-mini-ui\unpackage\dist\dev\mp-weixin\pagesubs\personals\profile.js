"use strict";const e=require("../../common/vendor.js"),ae=require("../../api/my/my.js"),te=require("../../api/moment/moment.js"),oe=require("../../api/my/album.js"),M=require("../../api/my/browse.js"),le=require("../../api/my/follow.js"),y=require("../../api/my/gift.js"),se=require("../../api/my/blacklist.js");Array||e.resolveComponent("uni-icons")();const re=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";Math||(re+ue+ie+ne)();const ne=()=>"./gift/gift-modal.js",ie=()=>"../../components/scroll-nav-page/scroll-nav-page.js",ue=()=>"../../components/auth-card/auth-card.js",ce={__name:"profile",setup(I){let n=e.ref(1);const U=e.ref({}),r=e.ref([]),v=e.ref({isAuthenticated:!1,authTypes:[]});e.onLoad(a=>{e.index.__f__("log","at pagesubs/personals/profile.vue:271","页面参数:",a),a&&a.userId&&(n.value=a.userId,X(a.userId),ee())});const t=e.ref({});e.ref(!1);const m=e.ref(!1),d=e.ref(0),q=e.ref(new Set),f=e.ref(!1),_=e.ref(23),c=e.ref(!1),g=e.ref(!1),h=e.ref(!1),u=e.ref({totalGiftCount:0,giftList:[]}),i=e.reactive({recentCount:0,text:"暂无动态内容",time:"",image:null}),F=()=>{f.value=!f.value},P=a=>{a!==d.value&&(d.value=a,r.value[a]&&r.value[a].id&&A(r.value[a].id))},C=a=>{q.value.add(a)},z=()=>{if(!c.value){if(!t.value.userId){e.index.showToast({title:"用户信息无效",icon:"none"});return}c.value=!0,le.toggleUserFollow(t.value.userId,t.value.isFollowed).then(a=>{t.value.isFollowed=!t.value.isFollowed,m.value=t.value.isFollowed,e.index.showToast({title:t.value.isFollowed?"关注成功":"取消关注成功",icon:"success",duration:1e3}),e.index.__f__("log","at pagesubs/personals/profile.vue:365","关注状态切换成功:",t.value.isFollowed)}).finally(()=>{c.value=!1})}},G=()=>{e.index.setClipboardData({data:t.value.userId,success:()=>{e.index.showToast({title:"用户ID已复制",icon:"success",duration:1500})},fail:()=>{e.index.showToast({title:"复制失败",icon:"none",duration:1500})}})},k=()=>{h.value=!0},T=()=>{h.value=!1,g.value=!1},L=async a=>{g.value||(g.value=!0,y.sendGiftToUser(n.value,{id:a.gift.id,name:a.gift.name,price:a.gift.price},a.quantity||1).then(o=>{e.index.showToast({title:`成功赠送${a.gift.name}`,icon:"success"}),T(),K()}).catch(o=>{e.index.__f__("error","at pagesubs/personals/profile.vue:432","送礼物失败:",o),e.index.showToast({title:"送礼物失败，请重试",icon:"none",duration:2e3})}).finally(()=>{g.value=!1}))},N=()=>{e.index.navigateTo({url:`/pagesubs/personals/greeting/greeting?userId=${n.value}&nickName=${encodeURIComponent(t.value.nickName)}&avatar=${t.value.avatar}`,fail:a=>{e.index.__f__("error","at pagesubs/personals/profile.vue:450","跳转打招呼页面失败:",a),e.index.showToast({title:"跳转失败",icon:"error"})}})},$=()=>{e.index.showToast({title:"开始聊天",icon:"success"})},V=()=>{e.index.showToast({title:"分享用户资料",icon:"success"})},R=()=>{f.value=!1,e.index.navigateTo({url:`/pagesubs/my/report/report?type=1&targetId=${n.value}&targetName=${encodeURIComponent(t.value.nickName)}`})},S=()=>{f.value=!1,e.index.showModal({title:"拉黑用户",content:"拉黑后将不再看到该用户的信息，确定要拉黑吗？",confirmText:"确定拉黑",cancelText:"取消",success:a=>{a.confirm&&se.addBlacklist({oppositeUserId:n.value}).then(o=>{e.index.showToast({title:"已拉黑该用户",icon:"success"})})}})},w=()=>{e.index.navigateTo({url:`/pagesubs/personals/moments?userId=${n.value}`,fail:a=>{e.index.__f__("error","at pagesubs/personals/profile.vue:511","跳转动态页面失败:",a),e.index.showToast({title:"跳转失败",icon:"error"})}})},j=()=>{e.index.navigateTo({url:`/pagesubs/personals/gift/gift?userId=${n.value}`,fail:a=>{e.index.__f__("error","at pagesubs/personals/profile.vue:525","跳转礼物页面失败:",a),e.index.showToast({title:"跳转失败",icon:"error"})}})},x=()=>{ae.getUserDetail(n.value).then(a=>{U.value=a.data,Q(a.data),B()})},K=()=>{n.value&&n.value!=="default"||y.getMyReceivedGifts({pageSize:1,pageNum:1}).then(a=>{(a.code===200||a.code===1)&&(_.value=a.total||0,e.index.__f__("log","at pagesubs/personals/profile.vue:558","收到的礼物数量:",_.value))}).catch(a=>{e.index.__f__("error","at pagesubs/personals/profile.vue:561","获取礼物数量失败:",a)})},B=()=>{y.getUserGiftWall({userId:n.value,pageSize:10,pageNum:1}).then(a=>{u.value=a.data||{totalGiftCount:0,giftList:[]},_.value=u.value.totalGiftCount||0,e.index.__f__("log","at pagesubs/personals/profile.vue:575","礼物墙数据:",u.value)})},H=()=>{te.getUserLatestMoment(n.value).then(a=>{a.data&&(i.text=a.data.content,i.time=a.data.time,i.image=a.data.firstImage,i.recentCount=a.data.recentMomentCount)})},E=()=>{oe.getAlbums(n.value).then(a=>{r.value=a.data,a.data.length>0&&a.data[0].id&&A(a.data[0].id)})},W=a=>{if(!Array.isArray(a))return"";const o=a.find(l=>l.tagKey==="about_me");return o?o.tagVal:""},Y=a=>{if(!Array.isArray(a))return"";const o=a.find(l=>l.tagKey==="advantages");return o?o.tagVal:""},D=a=>Array.isArray(a)?a.filter(o=>o.namespace==="matched").map(o=>({key:o.tagKey,value:o.tagVal,label:o.tagVal})):[],J=a=>Array.isArray(a)?a.filter(o=>o.namespace==="matched").map(o=>({key:o.tagKey,value:o.tagVal,label:o.tagVal})):[],O=a=>{if(!Array.isArray(a))return"";const o=a.find(l=>l.namespace==="profile"&&l.tagKey==="dream_partner");return o?o.tagVal:""},Q=a=>{t.value=a,t.value.gender=a.sex==="0"?"male":"female",t.value.introduction=W(a.tags)||"",t.value.advantages=Y(a.tags)||"",t.value.aboutMeTags=D(a.tags)||[],t.value.requirementTags=J(a.requireTags)||[],t.value.dreamPartnerDescription=O(a.requireTags)||"",m.value=a.isFollowed||!1,e.index.__f__("log","at pagesubs/personals/profile.vue:679","用户介绍:",t.value.introduction),e.index.__f__("log","at pagesubs/personals/profile.vue:680","个人优势:",t.value.advantages),e.index.__f__("log","at pagesubs/personals/profile.vue:681","关于我标签:",t.value.aboutMeTags),e.index.__f__("log","at pagesubs/personals/profile.vue:682","要求标签:",t.value.requirementTags),e.index.__f__("log","at pagesubs/personals/profile.vue:683","理想型描述:",t.value.dreamPartnerDescription),e.index.__f__("log","at pagesubs/personals/profile.vue:684","关注状态:",m.value),v.value.isAuthenticated=a.isAuthenticated||!1,v.value.authTypes=a.authTypes||[]},X=a=>{M.createUserHomeBrowseHistory(a)},A=a=>{M.createUserAlbumBrowseHistory(a)},Z=a=>{e.index.__f__("log","at pagesubs/personals/profile.vue:716","认证完成:",a),x(),e.index.showToast({title:"认证完成",icon:"success"})},ee=async()=>{await Promise.all([x(),H(),E()])};return(a,o)=>e.e({a:r.value&&r.value.length>0},r.value&&r.value.length>0?e.e({b:e.f(r.value,(l,s,p)=>({a:s,b:s===d.value?1:"",c:l.imageUrl,d:e.o((...b)=>a.previewPhoto&&a.previewPhoto(...b),s),e:e.o(b=>C(s),s)})),c:r.value.length>1},r.value.length>1?{d:e.f(r.value,(l,s,p)=>({a:l.imageUrl,b:s,c:s===d.value?1:"",d:e.o(b=>P(s),s)}))}:{},{e:r.value.length>1},r.value.length>1?{f:e.t(d.value+1),g:e.t(r.value.length)}:{}):{},{h:e.t(t.value.nickName),i:e.n(t.value.gender==="male"?"bani-xingbie-nan":"bani-xingbie-nv"),j:t.value.gender==="male"?"#4A90E2":"#E91E63",k:e.t(t.value.pid),l:e.o(G),m:!c.value},c.value?{o:e.p({type:"spinner-cycle",size:"20",color:"#fff"})}:{n:e.p({type:t.value.isFollowed?"star-filled":"star",size:"20",color:"#fff"})},{p:e.t(c.value?"处理中...":t.value.isFollowed?"已关注":"关注"),q:t.value.isFollowed?1:"",r:c.value?1:"",s:e.o(z),t:e.t(t.value.age),v:e.t(t.value.height),w:e.t(t.value.weight),x:e.t(t.value.edu),y:e.t(t.value.revenue),z:e.p({type:"calendar",size:"16",color:"#999"}),A:e.t(t.value.birthdayYear),B:e.t(t.value.star),C:e.p({type:"wallet",size:"16",color:"#999"}),D:e.t(t.value.job),E:e.p({type:"location",size:"16",color:"#999"}),F:e.t(t.value.addrNew),G:e.t(t.value.addr),H:!v.value.isAuthenticated},v.value.isAuthenticated?{}:{I:e.o(Z),J:e.p({"user-id":e.unref(n),"auth-types":v.value.authTypes})},{K:t.value.aboutMeTags&&t.value.aboutMeTags.length>0},t.value.aboutMeTags&&t.value.aboutMeTags.length>0?{L:e.f(t.value.aboutMeTags,(l,s,p)=>({a:e.t(l.label),b:s}))}:{},{M:t.value.introduction},t.value.introduction?{N:e.t(t.value.introduction)}:{},{O:t.value.advantages},t.value.advantages?{P:e.t(t.value.advantages)}:{},{Q:e.t(i.recentCount||0),R:e.p({type:"right",size:"16",color:"#999"}),S:e.o(w),T:e.t(i.text),U:e.t(i.time),V:i.image},i.image?{W:i.image}:{},{X:e.o(w),Y:t.value.isMatched},t.value.isMatched?e.e({Z:t.value.requirementTags&&t.value.requirementTags.length>0},t.value.requirementTags&&t.value.requirementTags.length>0?{aa:e.f(t.value.requirementTags,(l,s,p)=>({a:e.t(l.label),b:s}))}:{},{ab:t.value.dreamPartnerDescription},t.value.dreamPartnerDescription?{ac:e.t(t.value.dreamPartnerDescription)}:{}):{},{ad:e.t(u.value.totalGiftCount),ae:e.p({type:"right",size:"16",color:"#999"}),af:e.o(j),ag:u.value.giftList&&u.value.giftList.length>0},u.value.giftList&&u.value.giftList.length>0?{ah:e.f(u.value.giftList,(l,s,p)=>({a:l.giftIcon,b:e.t(l.giftName),c:e.t(l.giftNum),d:l.id||s}))}:{},{ai:e.p({title:t.value.nickName,"show-back":!0}),aj:e.o(T),ak:e.o(L),al:e.p({visible:h.value,"target-user":t.value,sending:g.value}),am:e.p({type:"gift",size:"18",color:"#696CF3"}),an:e.o(k),ao:e.o(N),ap:e.p({type:"chat",size:"18",color:"#fff"}),aq:e.o($),ar:e.p({type:"redo",size:"20",color:"#fff"}),as:e.o(V),at:e.p({type:"more-filled",size:"20",color:"#fff"}),av:e.o(F),aw:e.p({type:"info",size:"18",color:"#ff6b6b"}),ax:e.o(R),ay:e.p({type:"minus-filled",size:"18",color:"#666"}),az:e.o(S),aA:f.value?1:""})}},ve=e._export_sfc(ce,[["__scopeId","data-v-f3491ad8"]]);wx.createPage(ve);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesubs/personals/profile.js.map
