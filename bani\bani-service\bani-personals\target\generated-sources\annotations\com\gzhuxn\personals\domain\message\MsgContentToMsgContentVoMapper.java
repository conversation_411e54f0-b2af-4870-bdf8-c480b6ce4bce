package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.bo.MsgContentBoToMsgContentMapper;
import com.gzhuxn.personals.domain.message.vo.MsgContentVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgContentBoToMsgContentMapper.class},
    imports = {}
)
public interface MsgContentToMsgContentVoMapper extends BaseMapper<MsgContent, MsgContentVo> {
}
