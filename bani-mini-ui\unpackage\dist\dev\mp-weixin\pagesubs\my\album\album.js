"use strict";const e=require("../../../common/vendor.js"),y=require("../../../api/oss/oss.js"),d=require("../../../api/my/album.js"),n=require("../../../utils/common.js");Array||e.resolveComponent("uni-icons")();const x=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";Math||(x+P)();const P=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",A={__name:"album",setup(h){const o=e.ref([]),r=e.ref({imageCount:0,totalBrowseCount:0}),g=e.ref(!1),l=e.ref(-1),m=async()=>{try{g.value=!0;const t=await d.getStatisticsAlbumData();t.code===200?(o.value=t.data.albumList||[],r.value=t.data.statistics||{imageCount:0,totalBrowseCount:0}):n.toast(t.msg||"加载相册数据失败")}catch(t){e.index.__f__("error","at pagesubs/my/album/album.vue:98","加载相册数据失败:",t),n.toast("网络错误，请重试")}finally{g.value=!1}},b=()=>{e.index.chooseImage({count:9,sizeType:["original","compressed"],sourceType:["album","camera"],success:async t=>{try{e.index.showLoading({title:"照片上传中..."});for(let a=0;a<t.tempFilePaths.length;a++){const s=t.tempFilePaths[a],c=await y.uploadImgAndSmallFile(s,"user_album.image");if(c.code==200){const i=await d.uploadAlbumImage(c.data.ossId);if(i.code!==200)throw new Error(i.msg||"添加到相册失败")}}e.index.hideLoading(),n.toast("照片上传成功"),await m()}catch(a){e.index.hideLoading(),e.index.__f__("error","at pagesubs/my/album/album.vue:142","上传照片失败:",a),n.toast(a.message||"上传失败，请重试")}},fail:t=>{e.index.__f__("error","at pagesubs/my/album/album.vue:147","选择照片失败:",t),n.toast("选择照片失败")}})},f=t=>{if(l.value===t){l.value=-1;return}const a=o.value.map(s=>s.imageUrl);e.index.previewImage({current:t,urls:a})},v=(t,a)=>{l.value=l.value===a?-1:a},p=t=>{e.index.showActionSheet({itemList:["下载到本地","删除照片"],success:a=>{switch(a.tapIndex){case 0:w(t);break;case 1:_(t);break}}})},_=t=>{e.index.showModal({title:"确认删除",content:"确定要删除这张照片吗？删除后无法恢复。",success:async a=>{if(a.confirm)try{e.index.showLoading({title:"删除中..."});const s=await d.deleteAlbumImages([t.id]);if(s.code===200)e.index.hideLoading(),n.toast("删除成功"),await m();else throw new Error(s.msg||"删除失败")}catch(s){e.index.hideLoading(),e.index.__f__("error","at pagesubs/my/album/album.vue:214","删除照片失败:",s),n.toast(s.message||"删除失败，请重试")}}})},w=t=>{e.index.downloadFile({url:t.url,success:a=>{e.index.saveImageToPhotosAlbum({filePath:a.tempFilePath,success:()=>{e.index.showToast({title:"保存成功",icon:"success"})},fail:()=>{e.index.showToast({title:"保存失败",icon:"none"})}})},fail:()=>{e.index.showToast({title:"下载失败",icon:"none"})}})};return e.onMounted(()=>{m()}),(t,a)=>e.e({a:e.t(r.value.imageCount),b:e.t(r.value.totalBrowseCount),c:o.value.length>0},o.value.length>0?{d:e.f(o.value,(s,u,c)=>({a:s.imageUrl,b:"8687b077-1-"+c+",8687b077-0",c:e.o(i=>p(s),s.id),d:"8687b077-2-"+c+",8687b077-0",e:e.o(i=>_(s),s.id),f:l.value===u?1:"",g:s.id,h:e.o(i=>f(u),s.id),i:e.o(i=>v(s,u),s.id)})),e:e.p({type:"compose",size:"16",color:"#696CF3"}),f:e.p({type:"trash",size:"16",color:"#ff6b6b"})}:{},{g:o.value.length===0},o.value.length===0?{h:e.p({type:"image",size:"80",color:"#E5E6F3"})}:{},{i:o.value.length<6},o.value.length<6?{j:e.p({type:"camera",size:"20",color:"#fff"}),k:e.o(b)}:{},{l:e.p({title:"我的相册","show-back":!0})})}},I=e._export_sfc(A,[["__scopeId","data-v-8687b077"]]);wx.createPage(I);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/album/album.js.map
