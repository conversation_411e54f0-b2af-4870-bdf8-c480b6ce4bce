{"version": 3, "file": "msRemind.js", "sources": ["pagesubs/my/setting/msRemind.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcc2V0dGluZ1xtc1JlbWluZC52dWU"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"消息通知\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<!-- 主要内容 -->\n\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t<!-- 页面描述 -->\n\t\t\t\t<view class=\"page-description\">\n\t\t\t\t\t<view class=\"desc-content\">\n\t\t\t\t\t\t<text>管理您的消息通知设置，控制接收的通知类型</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 通知设置列表 -->\n\t\t\t\t<view class=\"notification-list\">\n\t\t\t\t\t<!-- 互动通知 -->\n\t\t\t\t\t<view class=\"notification-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">互动通知</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"notification-item\">\n\t\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">每日来访提醒</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-desc\">每天定时提醒您有新的访客查看了您的资料</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t\t<switch :checked=\"notificationSettings.ms_interaction_daily_visit\"\n\t\t\t\t\t\t\t\t\t@change=\"handleDailyVisitChange\" color=\"#696CF3\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"notification-item\">\n\t\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"chatbubble\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">被获取微信</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-desc\">当有人获取您的微信联系方式时通知您</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t\t<switch :checked=\"notificationSettings.ms_interaction_apply_wechat\"\n\t\t\t\t\t\t\t\t\t@change=\"handleWechatObtainedChange\" color=\"#696CF3\"\n\t\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 推荐通知 -->\n\t\t\t\t\t<view class=\"notification-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">推荐通知</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"notification-item\">\n\t\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"heart\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">每日推荐消息</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-desc\">每天为您推送符合条件的优质用户</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t\t<switch :checked=\"notificationSettings.ms_recommend_daily\"\n\t\t\t\t\t\t\t\t\t@change=\"handleDailyRecommendationChange\" color=\"#696CF3\"\n\t\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 活动通知 -->\n\t\t\t\t\t<view class=\"notification-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">活动通知</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"notification-item\">\n\t\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"gift\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">活动订阅通知</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-desc\">接收平台活动、优惠和新功能的通知</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t\t<switch :checked=\"notificationSettings.ms_activity_subscription\"\n\t\t\t\t\t\t\t\t\t@change=\"handleActivitySubscriptionChange\" color=\"#696CF3\"\n\t\t\t\t\t\t\t\t\tstyle=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 系统通知 -->\n\t\t\t\t\t<view class=\"notification-section\">\n\t\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t\t<text class=\"section-title\">系统通知</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"notification-item\">\n\t\t\t\t\t\t\t<view class=\"item-left\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"notification\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"item-title\">系统消息</text>\n\t\t\t\t\t\t\t\t\t<text class=\"item-desc\">接收系统重要通知和安全提醒</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"item-right\">\n\t\t\t\t\t\t\t\t<switch :checked=\"notificationSettings.ms_system\" @change=\"handleSystemMessageChange\"\n\t\t\t\t\t\t\t\t\tcolor=\"#696CF3\" style=\"transform: scale(0.8);\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, reactive } from 'vue'\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\nimport { toast } from '@/utils/common'\nimport globalConfig from '@/config'\nimport { getUserConfigMap, updateUserConfig } from '@/api/my/config'\n\n// 页面状态\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0)\nconst saving = ref(false)\n\n// 通知设置数据\nconst notificationSettings = reactive({\n\tms_interaction_daily_visit: true,      // 每日来访提醒\n\tms_interaction_apply_wechat: true,          // 被获取微信\n\tms_recommend_daily: true,     // 每日推荐消息\n\tms_activity_subscription: true,    // 活动订阅通知\n\tms_system: true,           // 系统消息\n})\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 计算导航栏文字颜色\nconst getNavTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor\n}\n\n// 每日来访提醒切换\nconst handleDailyVisitChange = (e) => {\n\tnotificationSettings.ms_interaction_daily_visit = e.detail.value\n\ttoast(e.detail.value ? '已开启每日来访提醒' : '已关闭每日来访提醒')\n\tupdateUserConfig({\n\t\tconfigKey: 'ms_interaction_daily_visit',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 每日推荐消息切换\nconst handleDailyRecommendationChange = (e) => {\n\tnotificationSettings.ms_recommend_daily = e.detail.value\n\ttoast(e.detail.value ? '已开启每日推荐消息' : '已关闭每日推荐消息')\n\tupdateUserConfig({\n\t\tconfigKey: 'ms_recommend_daily',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 被获取微信切换\nconst handleWechatObtainedChange = (e) => {\n\tnotificationSettings.ms_interaction_apply_wechat = e.detail.value\n\ttoast(e.detail.value ? '已开启微信获取通知' : '已关闭微信获取通知')\n\tupdateUserConfig({\n\t\tconfigKey: 'ms_interaction_apply_wechat',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 活动订阅通知切换\nconst handleActivitySubscriptionChange = (e) => {\n\tnotificationSettings.ms_activity_subscription = e.detail.value\n\ttoast(e.detail.value ? '已开启活动订阅通知' : '已关闭活动订阅通知')\n\tupdateUserConfig({\n\t\tconfigKey: 'ms_activity_subscription',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 系统消息切换\nconst handleSystemMessageChange = (e) => {\n\tnotificationSettings.ms_system = e.detail.value\n\ttoast(e.detail.value ? '已开启系统消息' : '已关闭系统消息')\n\tupdateUserConfig({\n\t\tconfigKey: 'ms_system',\n\t\tval: e.detail.value ? 1 : 0\n\t})\n}\n\n// 页面加载时获取当前设置\nonLoad(() => {\n\tgetUserConfigMap(1).then(result => {\n\t\tif (result.data) {\n\t\t\tfor (let key in result.data) {\n\t\t\t\tif (notificationSettings[key] instanceof Boolean || typeof notificationSettings[key] === 'boolean') {\n\t\t\t\t\tnotificationSettings[key] = result.data[key] === '1' ? true : false\n\t\t\t\t} else {\n\t\t\t\t\tnotificationSettings[key] = result.data[key]\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t})\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: #f5f5f5;\n}\n\n.main-container {\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx 120rpx;\n}\n\n.page-description {\n\ttext-align: center;\n\tmargin: 20rpx 0 32rpx;\n\tpadding: 20rpx 16rpx;\n\tbackground: rgba($primary-color, 0.03);\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba($primary-color, 0.08);\n\n\t.desc-content {\n\t\ttext {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t\tline-height: 1.4;\n\t\t}\n\t}\n}\n\n.notification-list {\n\t.notification-section {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tborder-radius: 20rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tbox-shadow: 0 6rpx 24rpx rgba($primary-color, 0.08);\n\t\tbackdrop-filter: blur(10rpx);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\t\toverflow: hidden;\n\n\t\t.section-header {\n\t\t\tpadding: 24rpx 30rpx 16rpx;\n\t\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\n\t\t\t.section-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tletter-spacing: 0.5rpx;\n\t\t\t}\n\t\t}\n\n\t\t.notification-item {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tpadding: 24rpx 30rpx;\n\t\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.05);\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\n\t\t\t&:hover {\n\t\t\t\tbackground: rgba($primary-color, 0.02);\n\t\t\t}\n\n\t\t\t.item-left {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\t\t\t\tflex: 1;\n\n\t\t\t\t.item-info {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tflex: 1;\n\n\t\t\t\t\t.item-title {\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tdisplay: block;\n\t\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.item-desc {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.save-section {\n\tmargin-top: 40rpx;\n\tpadding: 0 20rpx;\n\n\t.save-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tline-height: 88rpx;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tbox-shadow: 0 6rpx 20rpx rgba($primary-color, 0.2);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:active {\n\t\t\ttransform: scale(0.98);\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.15);\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/setting/msRemind.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "notificationSettings", "reactive", "onPageScroll", "handleDailyVisitChange", "toast", "updateUserConfig", "handleDailyRecommendationChange", "handleWechatObtainedChange", "handleActivitySubscriptionChange", "handleSystemMessageChange", "onLoad", "getUserConfigMap", "result", "key", "MiniProgramPage"], "mappings": "0aA2HA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EACAA,EAAA,IAAA,EAAA,EAGA,MAAAE,EAAAC,EAAAA,SAAA,CACA,2BAAA,GACA,4BAAA,GACA,mBAAA,GACA,yBAAA,GACA,UAAA,EACA,CAAA,EAGAC,EAAA,aAAA,GAAA,CACAL,EAAA,MAAA,EAAA,SACA,CAAA,EAcA,MAAAM,EAAA,GAAA,CACAH,EAAA,2BAAA,EAAA,OAAA,MACAI,EAAAA,MAAA,EAAA,OAAA,MAAA,YAAA,WAAA,EACAC,mBAAA,CACA,UAAA,6BACA,IAAA,EAAA,OAAA,MAAA,EAAA,CACA,CAAA,CACA,EAGAC,EAAA,GAAA,CACAN,EAAA,mBAAA,EAAA,OAAA,MACAI,EAAAA,MAAA,EAAA,OAAA,MAAA,YAAA,WAAA,EACAC,mBAAA,CACA,UAAA,qBACA,IAAA,EAAA,OAAA,MAAA,EAAA,CACA,CAAA,CACA,EAGAE,EAAA,GAAA,CACAP,EAAA,4BAAA,EAAA,OAAA,MACAI,EAAAA,MAAA,EAAA,OAAA,MAAA,YAAA,WAAA,EACAC,mBAAA,CACA,UAAA,8BACA,IAAA,EAAA,OAAA,MAAA,EAAA,CACA,CAAA,CACA,EAGAG,EAAA,GAAA,CACAR,EAAA,yBAAA,EAAA,OAAA,MACAI,EAAAA,MAAA,EAAA,OAAA,MAAA,YAAA,WAAA,EACAC,mBAAA,CACA,UAAA,2BACA,IAAA,EAAA,OAAA,MAAA,EAAA,CACA,CAAA,CACA,EAGAI,EAAA,GAAA,CACAT,EAAA,UAAA,EAAA,OAAA,MACAI,EAAAA,MAAA,EAAA,OAAA,MAAA,UAAA,SAAA,EACAC,mBAAA,CACA,UAAA,YACA,IAAA,EAAA,OAAA,MAAA,EAAA,CACA,CAAA,CACA,EAGAK,OAAAA,EAAAA,OAAA,IAAA,CACAC,EAAAA,iBAAA,CAAA,EAAA,KAAAC,GAAA,CACA,GAAAA,EAAA,KACA,QAAAC,KAAAD,EAAA,KACAZ,EAAAa,CAAA,YAAA,SAAA,OAAAb,EAAAa,CAAA,GAAA,UACAb,EAAAa,CAAA,EAAAD,EAAA,KAAAC,CAAA,IAAA,IAEAb,EAAAa,CAAA,EAAAD,EAAA,KAAAC,CAAA,CAIA,CAAA,CACA,CAAA,kjBCtNA,GAAG,WAAWC,CAAe"}