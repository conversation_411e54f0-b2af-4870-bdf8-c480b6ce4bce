package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.album.AppUserAlbumListVo;
import com.gzhuxn.personals.domain.user.bo.UserAlbumBoToUserAlbumMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAlbumBoToUserAlbumMapper.class,UserAlbumToUserAlbumVoMapper.class},
    imports = {}
)
public interface UserAlbumToAppUserAlbumListVoMapper extends BaseMapper<UserAlbum, AppUserAlbumListVo> {
}
