package com.gzhuxn.personals.controller.app.activity.bo;

import com.gzhuxn.personals.domain.activity.Activity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppActivityBuddyUpdateBoToActivityMapperImpl implements AppActivityBuddyUpdateBoToActivityMapper {

    @Override
    public Activity convert(AppActivityBuddyUpdateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Activity activity = new Activity();

        activity.setId( arg0.getId() );
        activity.setName( arg0.getName() );
        activity.setBackgroundImage( arg0.getBackgroundImage() );
        activity.setEnrollStartTime( arg0.getEnrollStartTime() );
        activity.setEnrollEndTime( arg0.getEnrollEndTime() );
        activity.setStartTime( arg0.getStartTime() );
        activity.setEndTime( arg0.getEndTime() );
        activity.setLocation( arg0.getLocation() );
        activity.setAddress( arg0.getAddress() );
        activity.setIntroduce( arg0.getIntroduce() );
        activity.setIntroduceImages( arg0.getIntroduceImages() );
        activity.setOriginalAmount( arg0.getOriginalAmount() );
        activity.setAmount( arg0.getAmount() );
        activity.setClassify( arg0.getClassify() );
        activity.setLon( arg0.getLon() );
        activity.setLat( arg0.getLat() );

        return activity;
    }

    @Override
    public Activity convert(AppActivityBuddyUpdateBo arg0, Activity arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setBackgroundImage( arg0.getBackgroundImage() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setAddress( arg0.getAddress() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setIntroduceImages( arg0.getIntroduceImages() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setClassify( arg0.getClassify() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
