{"version": 3, "file": "greeting.js", "sources": ["pagesubs/personals/greeting/greeting.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccGVyc29uYWxzXGdyZWV0aW5nXGdyZWV0aW5nLnZ1ZQ"], "sourcesContent": ["<template>\n\t<!-- 自定义导航栏 -->\n\t<scroll-nav-page title=\"林风婚恋\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"greeting-container\">\n\t\t\t\t<!-- 背景装饰 -->\n\t\t\t\t<view class=\"background-decoration\">\n\t\t\t\t\t<!-- Hi 气泡 -->\n\t\t\t\t\t<view class=\"hi-bubble\">\n\t\t\t\t\t\t<text class=\"hi-text\">Hi</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 用户信息区域 -->\n\t\t\t\t<view class=\"user-info-section\">\n\t\t\t\t\t<view class=\"greeting-title\">\n\t\t\t\t\t\t<uni-icons type=\"hand\" size=\"20\" color=\"#696CF3\" />\n\t\t\t\t\t\t<text class=\"title-text\">打招呼</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"greeting-subtitle\">私信对方，ilia马上看到你</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 输入区域 -->\n\t\t\t\t<view class=\"input-section\">\n\t\t\t\t\t<view class=\"input-container\">\n\t\t\t\t\t\t<textarea \n\t\t\t\t\t\t\tv-model=\"greetingContent\" \n\t\t\t\t\t\t\tclass=\"greeting-input\" \n\t\t\t\t\t\t\tplaceholder=\"嗨，想和你认识一下～\"\n\t\t\t\t\t\t\t:maxlength=\"60\"\n\t\t\t\t\t\t\tauto-height\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<view class=\"char-count\">{{ greetingContent.length }}/60</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 底部按钮区域 -->\n\t\t\t\t<view class=\"bottom-actions\">\n\t\t\t\t\t<view class=\"action-btn secondary-btn\" @click=\"goBack\">\n\t\t\t\t\t\t<text class=\"btn-text\">再想想</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn primary-btn\" @click=\"sendGreeting\" :class=\"{ 'disabled': !canSend }\">\n\t\t\t\t\t\t<text class=\"btn-text\">发送</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- VIP 提示 -->\n\t\t\t\t<view class=\"vip-tip\">\n\t\t\t\t\t<uni-icons type=\"vip-filled\" size=\"16\" color=\"#FFD700\" />\n\t\t\t\t\t<text class=\"vip-text\">开通会员每天免费打招呼10次 ></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\nimport ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'\nimport { sendUserGreeting, checkUserGreeted } from '@/api/user/greeting'\n\n// 页面参数\nlet oppositeUserId = ref(null)\nlet oppositeUserInfo = ref({})\n\n// 打招呼内容\nconst greetingContent = ref('嗨，想和你认识一下～')\n\n// 是否可以发送\nconst canSend = computed(() => {\n\treturn greetingContent.value.trim().length > 0 && greetingContent.value.trim().length <= 60\n})\n\n// 页面加载时获取参数\nonLoad((options) => {\n\tconsole.log('打招呼页面参数:', options)\n\tif (options && options.userId) {\n\t\toppositeUserId.value = options.userId\n\t\toppositeUserInfo.value = {\n\t\t\tuserId: options.userId,\n\t\t\tnickName: options.nickName || '用户',\n\t\t\tavatar: options.avatar || ''\n\t\t}\n\t\tcheckIfAlreadyGreeted()\n\t}\n})\n\n// 检查是否已经打过招呼\nconst checkIfAlreadyGreeted = async () => {\n\ttry {\n\t\tconst response = await checkUserGreeted(oppositeUserId.value)\n\t\tif (response.data) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '您已经向该用户打过招呼了，请等待对方回复',\n\t\t\t\tshowCancel: false,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('检查打招呼状态失败:', error)\n\t}\n}\n\n// 发送打招呼\nconst sendGreeting = async () => {\n\tif (!canSend.value) {\n\t\tuni.showToast({\n\t\t\ttitle: '请输入打招呼内容',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\tif (!oppositeUserId.value) {\n\t\tuni.showToast({\n\t\t\ttitle: '用户信息错误',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\n\ttry {\n\t\tuni.showLoading({\n\t\t\ttitle: '发送中...'\n\t\t})\n\n\t\tawait sendUserGreeting({\n\t\t\toppositeUserId: oppositeUserId.value,\n\t\t\tcontent: greetingContent.value.trim()\n\t\t})\n\n\t\tuni.hideLoading()\n\t\tuni.showToast({\n\t\t\ttitle: '打招呼发送成功',\n\t\t\ticon: 'success'\n\t\t})\n\n\t\t// 延迟返回上一页\n\t\tsetTimeout(() => {\n\t\t\tuni.navigateBack()\n\t\t}, 1500)\n\n\t} catch (error) {\n\t\tuni.hideLoading()\n\t\tconsole.error('发送打招呼失败:', error)\n\t\tuni.showToast({\n\t\t\ttitle: error.message || '发送失败，请重试',\n\t\t\ticon: 'none'\n\t\t})\n\t}\n}\n\n// 返回上一页\nconst goBack = () => {\n\tuni.navigateBack()\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 引入uni.scss变量\n@import '@/uni.scss';\n\n.greeting-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(180deg, #E3F2FD 0%, #BBDEFB 100%);\n\tpadding: 40rpx 32rpx;\n\tposition: relative;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.background-decoration {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 400rpx;\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\talign-items: flex-start;\n\tpadding: 80rpx 60rpx 0 0;\n}\n\n.hi-bubble {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tbackground: white;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.2);\n}\n\n.hi-text {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: #696CF3;\n}\n\n.user-info-section {\n\tmargin-top: 200rpx;\n\tmargin-bottom: 60rpx;\n\tz-index: 2;\n}\n\n.greeting-title {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n\tmargin-bottom: 16rpx;\n}\n\n.title-text {\n\tfont-size: 48rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.greeting-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-left: 32rpx;\n}\n\n.input-section {\n\tflex: 1;\n\tmargin-bottom: 40rpx;\n}\n\n.input-container {\n\tbackground: white;\n\tborder-radius: 24rpx;\n\tpadding: 32rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\n\tposition: relative;\n}\n\n.greeting-input {\n\twidth: 100%;\n\tmin-height: 200rpx;\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tline-height: 1.6;\n\tborder: none;\n\toutline: none;\n\tresize: none;\n}\n\n.char-count {\n\tposition: absolute;\n\tbottom: 16rpx;\n\tright: 24rpx;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.bottom-actions {\n\tdisplay: flex;\n\tgap: 24rpx;\n\tmargin-bottom: 40rpx;\n}\n\n.action-btn {\n\tflex: 1;\n\theight: 88rpx;\n\tborder-radius: 44rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.3s ease;\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n}\n\n.secondary-btn {\n\tbackground: white;\n\tborder: 2rpx solid #E0E0E0;\n}\n\n.secondary-btn .btn-text {\n\tcolor: #666;\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n}\n\n.primary-btn {\n\tbackground: linear-gradient(135deg, #696CF3, #9B9DF5);\n\tbox-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.3);\n}\n\n.primary-btn .btn-text {\n\tcolor: white;\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n}\n\n.primary-btn.disabled {\n\tbackground: #CCCCCC;\n\tbox-shadow: none;\n}\n\n.vip-tip {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tgap: 8rpx;\n\tpadding: 16rpx;\n}\n\n.vip-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/personals/greeting/greeting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ScrollNavPage", "oppositeUserId", "ref", "oppositeUserInfo", "<PERSON><PERSON><PERSON>nt", "canSend", "computed", "onLoad", "options", "uni", "checkIfAlreadyGreeted", "checkUserGreeted", "error", "sendGreeting", "sendUserGreeting", "goBack", "MiniProgramPage"], "mappings": "2OA2DA,MAAMA,EAAgB,IAAW,uFAIjC,IAAIC,EAAiBC,EAAG,IAAC,IAAI,EACzBC,EAAmBD,EAAG,IAAC,EAAE,EAG7B,MAAME,EAAkBF,EAAG,IAAC,YAAY,EAGlCG,EAAUC,EAAQ,SAAC,IACjBF,EAAgB,MAAM,KAAI,EAAG,OAAS,GAAKA,EAAgB,MAAM,KAAM,EAAC,QAAU,EACzF,EAGDG,EAAM,OAAEC,GAAY,CACnBC,EAAAA,MAAA,MAAA,MAAA,iDAAY,WAAYD,CAAO,EAC3BA,GAAWA,EAAQ,SACtBP,EAAe,MAAQO,EAAQ,OAC/BL,EAAiB,MAAQ,CACxB,OAAQK,EAAQ,OAChB,SAAUA,EAAQ,UAAY,KAC9B,OAAQA,EAAQ,QAAU,EAC1B,EACDE,EAAuB,EAEzB,CAAC,EAGD,MAAMA,EAAwB,SAAY,CACzC,GAAI,EACc,MAAMC,mBAAiBV,EAAe,KAAK,GAC/C,MACZQ,EAAAA,MAAI,UAAU,CACb,MAAO,KACP,QAAS,uBACT,WAAY,GACZ,QAAS,IAAM,CACdA,EAAAA,MAAI,aAAc,CAClB,CACL,CAAI,CAEF,OAAQG,EAAO,CACfH,EAAAA,sEAAc,aAAcG,CAAK,CACjC,CACF,EAGMC,EAAe,SAAY,CAChC,GAAI,CAACR,EAAQ,MAAO,CACnBI,EAAAA,MAAI,UAAU,CACb,MAAO,WACP,KAAM,MACT,CAAG,EACD,MACA,CAED,GAAI,CAACR,EAAe,MAAO,CAC1BQ,EAAAA,MAAI,UAAU,CACb,MAAO,SACP,KAAM,MACT,CAAG,EACD,MACA,CAED,GAAI,CACHA,EAAAA,MAAI,YAAY,CACf,MAAO,QACV,CAAG,EAED,MAAMK,mBAAiB,CACtB,eAAgBb,EAAe,MAC/B,QAASG,EAAgB,MAAM,KAAM,CACxC,CAAG,EAEDK,EAAAA,MAAI,YAAa,EACjBA,EAAAA,MAAI,UAAU,CACb,MAAO,UACP,KAAM,SACT,CAAG,EAGD,WAAW,IAAM,CAChBA,EAAAA,MAAI,aAAc,CAClB,EAAE,IAAI,CAEP,OAAQG,EAAO,CACfH,EAAAA,MAAI,YAAa,EACjBA,EAAAA,MAAc,MAAA,QAAA,kDAAA,WAAYG,CAAK,EAC/BH,EAAAA,MAAI,UAAU,CACb,MAAOG,EAAM,SAAW,WACxB,KAAM,MACT,CAAG,CACD,CACF,EAGMG,EAAS,IAAM,CACpBN,EAAAA,MAAI,aAAc,CACnB,qTC9JA,GAAG,WAAWO,CAAe"}