package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.AppUserFullBaseVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAppUserFullBaseVoMapperImpl implements UserDetailToAppUserFullBaseVoMapper {

    @Override
    public AppUserFullBaseVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserFullBaseVo appUserFullBaseVo = new AppUserFullBaseVo();

        appUserFullBaseVo.setNickName( arg0.getNickName() );
        appUserFullBaseVo.setSex( arg0.getSex() );
        appUserFullBaseVo.setBirthday( arg0.getBirthday() );
        appUserFullBaseVo.setHeight( arg0.getHeight() );
        appUserFullBaseVo.setWeight( arg0.getWeight() );
        appUserFullBaseVo.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        appUserFullBaseVo.setAddrCityCode( arg0.getAddrCityCode() );
        appUserFullBaseVo.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        appUserFullBaseVo.setAddrStreetCode( arg0.getAddrStreetCode() );
        appUserFullBaseVo.setAddr( arg0.getAddr() );
        appUserFullBaseVo.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        appUserFullBaseVo.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        appUserFullBaseVo.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        appUserFullBaseVo.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        appUserFullBaseVo.setAddrNew( arg0.getAddrNew() );
        appUserFullBaseVo.setAffectiveStatus( arg0.getAffectiveStatus() );
        appUserFullBaseVo.setEdu( arg0.getEdu() );
        appUserFullBaseVo.setJob( arg0.getJob() );
        appUserFullBaseVo.setRevenue( arg0.getRevenue() );
        appUserFullBaseVo.setWechat( arg0.getWechat() );

        return appUserFullBaseVo;
    }

    @Override
    public AppUserFullBaseVo convert(UserDetail arg0, AppUserFullBaseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        arg1.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        arg1.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        arg1.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setAffectiveStatus( arg0.getAffectiveStatus() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );

        return arg1;
    }
}
