package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.blacklist.AppUserBlacklistVo;
import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBlacklistToAppUserBlacklistVoMapperImpl implements UserBlacklistToAppUserBlacklistVoMapper {

    @Override
    public AppUserBlacklistVo convert(UserBlacklist source) {
        if ( source == null ) {
            return null;
        }

        AppUserBlacklistVo appUserBlacklistVo = new AppUserBlacklistVo();

        appUserBlacklistVo.setUid( source.getOppositeUserId() );
        appUserBlacklistVo.setId( source.getId() );
        appUserBlacklistVo.setUserId( source.getUserId() );
        if ( source.getCreateTime() != null ) {
            appUserBlacklistVo.setCreateTime( Date.from( source.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }

        return appUserBlacklistVo;
    }

    @Override
    public AppUserBlacklistVo convert(UserBlacklist source, AppUserBlacklistVo target) {
        if ( source == null ) {
            return target;
        }

        target.setUid( source.getOppositeUserId() );
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );
        if ( source.getCreateTime() != null ) {
            target.setCreateTime( Date.from( source.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            target.setCreateTime( null );
        }

        return target;
    }
}
