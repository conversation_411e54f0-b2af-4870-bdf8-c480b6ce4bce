package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserRechargeRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserRechargeRecordToUserRechargeRecordVoMapperImpl implements UserRechargeRecordToUserRechargeRecordVoMapper {

    @Override
    public UserRechargeRecordVo convert(UserRechargeRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserRechargeRecordVo userRechargeRecordVo = new UserRechargeRecordVo();

        userRechargeRecordVo.setId( arg0.getId() );
        userRechargeRecordVo.setUserId( arg0.getUserId() );
        userRechargeRecordVo.setManageId( arg0.getManageId() );
        userRechargeRecordVo.setOrderId( arg0.getOrderId() );
        userRechargeRecordVo.setOriginalAmount( arg0.getOriginalAmount() );
        userRechargeRecordVo.setAmount( arg0.getAmount() );
        userRechargeRecordVo.setCoin( arg0.getCoin() );
        userRechargeRecordVo.setPayTime( arg0.getPayTime() );
        userRechargeRecordVo.setPayStatus( arg0.getPayStatus() );

        return userRechargeRecordVo;
    }

    @Override
    public UserRechargeRecordVo convert(UserRechargeRecord arg0, UserRechargeRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setManageId( arg0.getManageId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );

        return arg1;
    }
}
