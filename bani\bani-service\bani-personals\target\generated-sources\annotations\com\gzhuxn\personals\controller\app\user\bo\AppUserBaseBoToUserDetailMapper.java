package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserDetail;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserBaseBoToRemoteUserBoMapper.class},
    imports = {}
)
public interface AppUserBaseBoToUserDetailMapper extends BaseMapper<AppUserBaseBo, UserDetail> {
}
