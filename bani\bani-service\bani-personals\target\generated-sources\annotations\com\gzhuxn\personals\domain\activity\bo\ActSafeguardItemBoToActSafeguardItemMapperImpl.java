package com.gzhuxn.personals.domain.activity.bo;

import com.gzhuxn.personals.domain.activity.ActSafeguardItem;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardItemBoToActSafeguardItemMapperImpl implements ActSafeguardItemBoToActSafeguardItemMapper {

    @Override
    public ActSafeguardItem convert(ActSafeguardItemBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguardItem actSafeguardItem = new ActSafeguardItem();

        actSafeguardItem.setSearchValue( arg0.getSearchValue() );
        actSafeguardItem.setCreateBy( arg0.getCreateBy() );
        actSafeguardItem.setCreateTime( arg0.getCreateTime() );
        actSafeguardItem.setUpdateBy( arg0.getUpdateBy() );
        actSafeguardItem.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            actSafeguardItem.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        actSafeguardItem.setCreateDept( arg0.getCreateDept() );
        actSafeguardItem.setId( arg0.getId() );
        actSafeguardItem.setSafeguardId( arg0.getSafeguardId() );
        actSafeguardItem.setIcon( arg0.getIcon() );
        actSafeguardItem.setName( arg0.getName() );
        actSafeguardItem.setDes( arg0.getDes() );

        return actSafeguardItem;
    }

    @Override
    public ActSafeguardItem convert(ActSafeguardItemBo arg0, ActSafeguardItem arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setSafeguardId( arg0.getSafeguardId() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setName( arg0.getName() );
        arg1.setDes( arg0.getDes() );

        return arg1;
    }
}
