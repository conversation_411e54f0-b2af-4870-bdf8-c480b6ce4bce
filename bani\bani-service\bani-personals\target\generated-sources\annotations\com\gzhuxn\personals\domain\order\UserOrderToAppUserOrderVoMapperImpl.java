package com.gzhuxn.personals.domain.order;

import com.gzhuxn.personals.controller.app.order.vo.AppUserOrderVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserOrderToAppUserOrderVoMapperImpl implements UserOrderToAppUserOrderVoMapper {

    @Override
    public AppUserOrderVo convert(UserOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserOrderVo appUserOrderVo = new AppUserOrderVo();

        appUserOrderVo.setId( arg0.getId() );
        appUserOrderVo.setUserId( arg0.getUserId() );
        appUserOrderVo.setOriginalAmount( arg0.getOriginalAmount() );
        appUserOrderVo.setAmount( arg0.getAmount() );
        appUserOrderVo.setType( arg0.getType() );
        appUserOrderVo.setWithdrawCoin( arg0.getWithdrawCoin() );
        appUserOrderVo.setCoin( arg0.getCoin() );
        appUserOrderVo.setStatus( arg0.getStatus() );
        appUserOrderVo.setPayTime( arg0.getPayTime() );
        appUserOrderVo.setCloseTime( arg0.getCloseTime() );
        appUserOrderVo.setNo( arg0.getNo() );
        appUserOrderVo.setFailDesc( arg0.getFailDesc() );
        appUserOrderVo.setCreateTime( arg0.getCreateTime() );

        return appUserOrderVo;
    }

    @Override
    public AppUserOrderVo convert(UserOrder arg0, AppUserOrderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setType( arg0.getType() );
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setCloseTime( arg0.getCloseTime() );
        arg1.setNo( arg0.getNo() );
        arg1.setFailDesc( arg0.getFailDesc() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
