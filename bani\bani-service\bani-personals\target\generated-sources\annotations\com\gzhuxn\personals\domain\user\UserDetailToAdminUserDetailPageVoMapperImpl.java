package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.admin.user.vo.AdminUserDetailPageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAdminUserDetailPageVoMapperImpl implements UserDetailToAdminUserDetailPageVoMapper {

    @Override
    public AdminUserDetailPageVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminUserDetailPageVo adminUserDetailPageVo = new AdminUserDetailPageVo();

        adminUserDetailPageVo.setUserId( arg0.getUserId() );
        adminUserDetailPageVo.setNickName( arg0.getNickName() );
        adminUserDetailPageVo.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            adminUserDetailPageVo.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        adminUserDetailPageVo.setPhoneNumber( arg0.getPhoneNumber() );
        adminUserDetailPageVo.setPid( arg0.getPid() );
        adminUserDetailPageVo.setBirthday( arg0.getBirthday() );
        adminUserDetailPageVo.setStar( arg0.getStar() );
        adminUserDetailPageVo.setAnimal( arg0.getAnimal() );
        adminUserDetailPageVo.setHeight( arg0.getHeight() );
        adminUserDetailPageVo.setWeight( arg0.getWeight() );
        adminUserDetailPageVo.setEdu( arg0.getEdu() );
        adminUserDetailPageVo.setJob( arg0.getJob() );
        adminUserDetailPageVo.setRevenue( arg0.getRevenue() );
        adminUserDetailPageVo.setWechat( arg0.getWechat() );
        adminUserDetailPageVo.setAddr( arg0.getAddr() );
        adminUserDetailPageVo.setAddrNew( arg0.getAddrNew() );
        adminUserDetailPageVo.setProgress( arg0.getProgress() );
        adminUserDetailPageVo.setAuditStatus( arg0.getAuditStatus() );
        adminUserDetailPageVo.setIsIdentity( arg0.getIsIdentity() );
        adminUserDetailPageVo.setUserLevel( arg0.getUserLevel() );
        adminUserDetailPageVo.setIsMatched( arg0.getIsMatched() );
        adminUserDetailPageVo.setStatus( arg0.getStatus() );

        return adminUserDetailPageVo;
    }

    @Override
    public AdminUserDetailPageVo convert(UserDetail arg0, AdminUserDetailPageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            arg1.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        else {
            arg1.setAvatar( null );
        }
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setIsIdentity( arg0.getIsIdentity() );
        arg1.setUserLevel( arg0.getUserLevel() );
        arg1.setIsMatched( arg0.getIsMatched() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
