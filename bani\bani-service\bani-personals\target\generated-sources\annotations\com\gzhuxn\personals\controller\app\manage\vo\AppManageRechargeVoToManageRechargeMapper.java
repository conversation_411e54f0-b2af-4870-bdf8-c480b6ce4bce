package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageRecharge;
import com.gzhuxn.personals.domain.manage.ManageRechargeToAppManageRechargeVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageRechargeToAppManageRechargeVoMapper.class},
    imports = {}
)
public interface AppManageRechargeVoToManageRechargeMapper extends BaseMapper<AppManageRechargeVo, ManageRecharge> {
}
