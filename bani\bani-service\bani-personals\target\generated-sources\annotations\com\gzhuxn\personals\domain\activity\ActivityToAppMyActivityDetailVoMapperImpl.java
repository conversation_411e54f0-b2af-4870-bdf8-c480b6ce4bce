package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.user.vo.activity.AppMyActivityDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppMyActivityDetailVoMapperImpl implements ActivityToAppMyActivityDetailVoMapper {

    @Override
    public AppMyActivityDetailVo convert(Activity source) {
        if ( source == null ) {
            return null;
        }

        AppMyActivityDetailVo appMyActivityDetailVo = new AppMyActivityDetailVo();

        appMyActivityDetailVo.setUid( source.getCreateBy() );
        appMyActivityDetailVo.setId( source.getId() );
        appMyActivityDetailVo.setName( source.getName() );
        if ( source.getBackgroundImage() != null ) {
            appMyActivityDetailVo.setBackgroundImage( String.valueOf( source.getBackgroundImage() ) );
        }
        appMyActivityDetailVo.setClassify( source.getClassify() );
        appMyActivityDetailVo.setIntroduce( source.getIntroduce() );
        appMyActivityDetailVo.setIntroduceImages( source.getIntroduceImages() );
        appMyActivityDetailVo.setLocation( source.getLocation() );
        appMyActivityDetailVo.setAddress( source.getAddress() );
        appMyActivityDetailVo.setEnrollStartTime( source.getEnrollStartTime() );
        appMyActivityDetailVo.setEnrollEndTime( source.getEnrollEndTime() );
        appMyActivityDetailVo.setStartTime( source.getStartTime() );
        appMyActivityDetailVo.setEndTime( source.getEndTime() );
        appMyActivityDetailVo.setTimeLength( source.getTimeLength() );
        appMyActivityDetailVo.setRefundTime( source.getRefundTime() );
        appMyActivityDetailVo.setLimitNum( source.getLimitNum() );
        appMyActivityDetailVo.setOriginalAmount( source.getOriginalAmount() );
        appMyActivityDetailVo.setAmount( source.getAmount() );
        appMyActivityDetailVo.setStatus( source.getStatus() );
        appMyActivityDetailVo.setAuditStatus( source.getAuditStatus() );
        appMyActivityDetailVo.setLon( source.getLon() );
        appMyActivityDetailVo.setLat( source.getLat() );
        appMyActivityDetailVo.setCreateTime( source.getCreateTime() );
        appMyActivityDetailVo.setCreateBy( source.getCreateBy() );
        appMyActivityDetailVo.setEnrollNum( source.getEnrollNum() );

        return appMyActivityDetailVo;
    }

    @Override
    public AppMyActivityDetailVo convert(Activity source, AppMyActivityDetailVo target) {
        if ( source == null ) {
            return target;
        }

        target.setUid( source.getCreateBy() );
        target.setId( source.getId() );
        target.setName( source.getName() );
        if ( source.getBackgroundImage() != null ) {
            target.setBackgroundImage( String.valueOf( source.getBackgroundImage() ) );
        }
        else {
            target.setBackgroundImage( null );
        }
        target.setClassify( source.getClassify() );
        target.setIntroduce( source.getIntroduce() );
        target.setIntroduceImages( source.getIntroduceImages() );
        target.setLocation( source.getLocation() );
        target.setAddress( source.getAddress() );
        target.setEnrollStartTime( source.getEnrollStartTime() );
        target.setEnrollEndTime( source.getEnrollEndTime() );
        target.setStartTime( source.getStartTime() );
        target.setEndTime( source.getEndTime() );
        target.setTimeLength( source.getTimeLength() );
        target.setRefundTime( source.getRefundTime() );
        target.setLimitNum( source.getLimitNum() );
        target.setOriginalAmount( source.getOriginalAmount() );
        target.setAmount( source.getAmount() );
        target.setStatus( source.getStatus() );
        target.setAuditStatus( source.getAuditStatus() );
        target.setLon( source.getLon() );
        target.setLat( source.getLat() );
        target.setCreateTime( source.getCreateTime() );
        target.setCreateBy( source.getCreateBy() );
        target.setEnrollNum( source.getEnrollNum() );

        return target;
    }
}
