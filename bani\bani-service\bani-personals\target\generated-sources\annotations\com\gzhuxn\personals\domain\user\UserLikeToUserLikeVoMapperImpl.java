package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserLikeVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLikeToUserLikeVoMapperImpl implements UserLikeToUserLikeVoMapper {

    @Override
    public UserLikeVo convert(UserLike arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLikeVo userLikeVo = new UserLikeVo();

        userLikeVo.setId( arg0.getId() );
        userLikeVo.setUserId( arg0.getUserId() );
        userLikeVo.setType( arg0.getType() );
        userLikeVo.setBusinessId( arg0.getBusinessId() );
        userLikeVo.setCreateTime( arg0.getCreateTime() );

        return userLikeVo;
    }

    @Override
    public UserLikeVo convert(UserLike arg0, UserLikeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
