{"version": 3, "file": "withdraw.js", "sources": ["pagesubs/promotion-center/withdraw/withdraw.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccHJvbW90aW9uLWNlbnRlclx3aXRoZHJhd1x3aXRoZHJhdy52dWU"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"邀请朋友\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-container\">\n\t\t\t\t<!-- 主要内容 -->\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<!-- 邀请统计卡片 -->\n\t\t\t\t\t<view class=\"invite-stats-card\">\n\t\t\t\t\t\t<view class=\"stats-header\">\n\t\t\t\t\t\t\t<text class=\"invite-count\">已邀请：{{ inviteStats.inviteCount }}人</text>\n\t\t\t\t\t\t\t<view class=\"header-actions\">\n\t\t\t\t\t\t\t\t<button class=\"invite-more-btn\" @click=\"inviteMore\">邀请更多朋友</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"stats-content\">\n\t\t\t\t\t\t\t<view class=\"stat-row\">\n\t\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label\">总收益</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-value\">{{ inviteStats.totalEarnings }}元</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label\">已提现收益</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-value\">{{ inviteStats.withdrawnEarnings }}元</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-row\">\n\t\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"stat-label\">待提现收益</text>\n\t\t\t\t\t\t\t\t\t<text class=\"stat-value\">{{ inviteStats.pendingEarnings }}元</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"stat-actions\">\n\t\t\t\t\t\t\t\t\t<button class=\"withdraw-btn\" @click=\"showWithdrawModal\">\n\t\t\t\t\t\t\t\t\t\t申请提现\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 标签页切换 -->\n\t\t\t\t\t<view class=\"tab-section\">\n\t\t\t\t\t\t<view class=\"tab-nav\">\n\t\t\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'invite' }\"\n\t\t\t\t\t\t\t\t@click=\"switchTab('invite')\">\n\t\t\t\t\t\t\t\t<text>邀请信息</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'withdraw' }\"\n\t\t\t\t\t\t\t\t@click=\"switchTab('withdraw')\">\n\t\t\t\t\t\t\t\t<text>提现记录</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 邀请信息内容 -->\n\t\t\t\t\t<view v-if=\"activeTab === 'invite'\" class=\"invite-content\">\n\t\t\t\t\t\t<!-- 朋友信息Tab选项卡 -->\n\t\t\t\t\t\t<view class=\"friend-info-section\">\n\t\t\t\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t\t\t\t<text>朋友信息</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- Tab选项卡导航 -->\n\t\t\t\t\t\t\t<view class=\"friend-tabs\">\n\t\t\t\t\t\t\t\t<view class=\"friend-tab-item\" :class=\"{ active: selectedCategory === 'all' }\"\n\t\t\t\t\t\t\t\t\t@click=\"selectCategory('all')\">\n\t\t\t\t\t\t\t\t\t<view class=\"tab-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"20\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tab-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-name\">全部朋友</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-count\">{{ friendStats.total }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<view class=\"friend-tab-item\" :class=\"{ active: selectedCategory === 'premium' }\"\n\t\t\t\t\t\t\t\t\t@click=\"selectCategory('premium')\">\n\t\t\t\t\t\t\t\t\t<view class=\"tab-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"vip-filled\" size=\"20\" color=\"#FFD700\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tab-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-name\">高级朋友</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-count\">{{ friendStats.premium }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<view class=\"friend-tab-item\" :class=\"{ active: selectedCategory === 'active' }\"\n\t\t\t\t\t\t\t\t\t@click=\"selectCategory('active')\">\n\t\t\t\t\t\t\t\t\t<view class=\"tab-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"heart-filled\" size=\"20\" color=\"#FF6B6B\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tab-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-name\">活跃朋友</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-count\">{{ friendStats.active }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<view class=\"friend-tab-item\" :class=\"{ active: selectedCategory === 'new' }\"\n\t\t\t\t\t\t\t\t\t@click=\"selectCategory('new')\">\n\t\t\t\t\t\t\t\t\t<view class=\"tab-icon\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"star-filled\" size=\"20\" color=\"#4ECDC4\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"tab-content\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-name\">新朋友</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"tab-count\">{{ friendStats.new }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 朋友列表 -->\n\t\t\t\t\t\t<z-paging ref=\"friendPaging\" v-model=\"friendList\" :fixed=\"false\" :use-page-scroll=\"false\"\n\t\t\t\t\t\t\t:data-key=\"selectedCategory\" @query=\"queryFriendList\">\n\n\t\t\t\t\t\t\t<view class=\"friend-list\">\n\t\t\t\t\t\t\t\t<view class=\"friend-item\" v-for=\"friend in friendList\" :key=\"friend.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"friend-avatar\">\n\t\t\t\t\t\t\t\t\t\t<image :src=\"friend.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"friend-info\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"friend-name\">{{ friend.name }}</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"friend-status\">{{ friend.status }} 花瓣</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"friend-level\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"level-name\">{{ friend.level }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</z-paging>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 提现记录内容 -->\n\t\t\t\t\t<view v-if=\"activeTab === 'withdraw'\" class=\"withdraw-content\">\n\t\t\t\t\t\t<z-paging ref=\"withdrawPaging\" v-model=\"withdrawList\" :fixed=\"false\" :use-page-scroll=\"false\"\n\t\t\t\t\t\t\t@query=\"queryWithdrawList\">\n\n\t\t\t\t\t\t\t<view class=\"withdraw-list\">\n\t\t\t\t\t\t\t\t<view class=\"withdraw-item\" v-for=\"record in withdrawList\" :key=\"record.id\">\n\t\t\t\t\t\t\t\t\t<view class=\"withdraw-info\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"withdraw-amount\">{{ record.amount }}元</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"withdraw-time\">{{ record.time }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"withdraw-status\" :class=\"record.status\">\n\t\t\t\t\t\t\t\t\t\t<text>{{ getStatusText(record.status) }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</z-paging>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 提现申请弹窗 -->\n\t\t\t\t<uni-popup ref=\"withdrawPopup\" type=\"center\" :mask-click=\"false\">\n\t\t\t\t\t<view class=\"withdraw-modal\">\n\t\t\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t\t\t<text class=\"modal-title\">申请提现</text>\n\t\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\" @click=\"closeWithdrawModal\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t\t\t<view class=\"amount-info\">\n\t\t\t\t\t\t\t\t<text class=\"available-amount\">可提现金额：{{ inviteStats.pendingEarnings }}元</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"withdraw-form\">\n\t\t\t\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t\t\t\t<text class=\"form-label\">提现金额</text>\n\t\t\t\t\t\t\t\t\t<input class=\"form-input\" type=\"number\" v-model=\"withdrawForm.amount\"\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"请输入提现金额\" :max=\"inviteStats.pendingEarnings\">\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t\t\t<button class=\"cancel-btn\" @click=\"closeWithdrawModal\">取消</button>\n\t\t\t\t\t\t\t<button class=\"confirm-btn\" @click=\"submitWithdraw\"\n\t\t\t\t\t\t\t\t:disabled=\"!canSubmitWithdraw\">确认提现</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</uni-popup>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, computed, reactive } from 'vue'\nimport { onLoad, onPageScroll } from '@dcloudio/uni-app'\nimport globalConfig from '@/config'\nimport { toast } from '@/utils/common'\n\n// 页面状态\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0)\nconst activeTab = ref('invite')\nconst selectedCategory = ref('all')\n\n// 邀请统计数据\nconst inviteStats = reactive({\n\tinviteCount: 1,\n\ttotalEarnings: 0,\n\twithdrawnEarnings: 0,\n\tpendingEarnings: 0\n})\n\n// 朋友统计数据\nconst friendStats = reactive({\n\ttotal: 100,\n\tpremium: 25,\n\tactive: 60,\n\tnew: 15\n})\n\n// 朋友列表数据\nconst friends = ref([\n\t{\n\t\tid: 1,\n\t\tname: '悟净',\n\t\tavatar: 'https://picsum.photos/100/100?random=1',\n\t\tstatus: '+100',\n\t\treward: 100,\n\t\tlevel: '黄金会员',\n\t\tcategory: 'new'\n\t},\n\t{\n\t\tid: 2,\n\t\tname: '小明',\n\t\tavatar: 'https://picsum.photos/100/100?random=2',\n\t\tstatus: '+200',\n\t\treward: 200,\n\t\tlevel: '钻石会员',\n\t\tcategory: 'premium'\n\t},\n\t{\n\t\tid: 3,\n\t\tname: '小红',\n\t\tavatar: 'https://picsum.photos/100/100?random=3',\n\t\tstatus: '+50',\n\t\treward: 50,\n\t\tlevel: '普通会员',\n\t\tcategory: 'active'\n\t},\n\t{\n\t\tid: 4,\n\t\tname: '张三',\n\t\tavatar: 'https://picsum.photos/100/100?random=4',\n\t\tstatus: '+150',\n\t\treward: 150,\n\t\tlevel: '白金会员',\n\t\tcategory: 'premium'\n\t},\n\t{\n\t\tid: 5,\n\t\tname: '李四',\n\t\tavatar: 'https://picsum.photos/100/100?random=5',\n\t\tstatus: '+80',\n\t\treward: 80,\n\t\tlevel: '银牌会员',\n\t\tcategory: 'active'\n\t},\n\t{\n\t\tid: 6,\n\t\tname: '王五',\n\t\tavatar: 'https://picsum.photos/100/100?random=6',\n\t\tstatus: '+120',\n\t\treward: 120,\n\t\tlevel: '黄金会员',\n\t\tcategory: 'new'\n\t},\n\t{\n\t\tid: 7,\n\t\tname: '赵六',\n\t\tavatar: 'https://picsum.photos/100/100?random=7',\n\t\tstatus: '+300',\n\t\treward: 300,\n\t\tlevel: '钻石会员',\n\t\tcategory: 'premium'\n\t},\n\t{\n\t\tid: 8,\n\t\tname: '孙七',\n\t\tavatar: 'https://picsum.photos/100/100?random=8',\n\t\tstatus: '+60',\n\t\treward: 60,\n\t\tlevel: '普通会员',\n\t\tcategory: 'active'\n\t}\n])\n\n// z-paging相关数据\nconst friendList = ref([])\nconst withdrawList = ref([])\nconst friendPaging = ref(null)\nconst withdrawPaging = ref(null)\n\n// 提现表单\nconst withdrawForm = reactive({\n\tamount: ''\n})\n\n// 弹窗引用\nconst withdrawPopup = ref(null)\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 计算导航栏文字颜色\nconst getNavTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor\n}\n\n\n\n// 是否可以提交提现\nconst canSubmitWithdraw = computed(() => {\n\tconst amount = parseFloat(withdrawForm.amount)\n\treturn amount > 0 && amount <= inviteStats.pendingEarnings\n})\n\n// 切换标签页\nconst switchTab = (tab) => {\n\tactiveTab.value = tab\n\t// 切换到提现记录tab时，触发数据加载\n\tif (tab === 'withdraw') {\n\t\tsetTimeout(() => {\n\t\t\tif (withdrawPaging.value) {\n\t\t\t\twithdrawPaging.value.reload()\n\t\t\t}\n\t\t}, 100)\n\t}\n}\n\n// 选择朋友分类\nconst selectCategory = (category) => {\n\tselectedCategory.value = category\n\t// 重新加载朋友列表数据\n\tif (friendPaging.value) {\n\t\tfriendPaging.value.reload()\n\t}\n}\n\n// 邀请更多朋友\nconst inviteMore = () => {\n\t// TODO: 实现邀请功能\n\ttoast('邀请功能开发中')\n}\n\n// 显示提现弹窗\nconst showWithdrawModal = () => {\n\tif (inviteStats.pendingEarnings <= 0) {\n\t\ttoast('暂无可提现金额')\n\t\t//return\n\t}\n\twithdrawPopup.value.open()\n}\n\n// 关闭提现弹窗\nconst closeWithdrawModal = () => {\n\twithdrawPopup.value.close()\n\twithdrawForm.amount = ''\n}\n\n// 提交提现申请\nconst submitWithdraw = () => {\n\tif (!canSubmitWithdraw.value) {\n\t\ttoast('请输入正确的提现金额')\n\t\treturn\n\t}\n\n\t// TODO: 调用提现API\n\ttoast('提现申请已提交')\n\tcloseWithdrawModal()\n}\n\n// 查询朋友列表\nconst queryFriendList = async (pageNo, pageSize) => {\n\ttry {\n\t\tconsole.log('=== 查询朋友列表开始 ===')\n\t\tconsole.log('pageNo:', pageNo, 'pageSize:', pageSize, '分类:', selectedCategory.value)\n\n\t\t// 根据分类过滤朋友数据\n\t\tlet filteredData = friends.value\n\t\tif (selectedCategory.value !== 'all') {\n\t\t\tfilteredData = friends.value.filter(friend => friend.category === selectedCategory.value)\n\t\t}\n\n\t\tconsole.log('原始朋友数据:', friends.value)\n\t\tconsole.log('过滤后的数据:', filteredData)\n\n\t\t// 模拟分页\n\t\tconst startIndex = (pageNo - 1) * pageSize\n\t\tconst endIndex = startIndex + pageSize\n\t\tconst pageData = filteredData.slice(startIndex, endIndex)\n\n\t\tconsole.log('分页数据:', pageData)\n\t\tconsole.log('=== 查询朋友列表结束 ===')\n\n\t\t// 完成分页请求\n\t\tfriendPaging.value.complete(pageData)\n\t} catch (error) {\n\t\tconsole.error('查询朋友列表出错:', error)\n\t\t// 请求失败\n\t\tfriendPaging.value.complete(false)\n\t}\n}\n\n// 查询提现记录列表\nconst queryWithdrawList = async (pageNo, pageSize) => {\n\ttry {\n\t\tconsole.log('=== 查询提现记录开始 ===')\n\t\tconsole.log('pageNo:', pageNo, 'pageSize:', pageSize)\n\n\t\t// 模拟提现记录数据\n\t\tconst mockWithdrawData = [\n\t\t\t{\n\t\t\t\tid: 1,\n\t\t\t\tamount: 100,\n\t\t\t\ttime: '2024-01-15 14:30',\n\t\t\t\tstatus: 'success'\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 2,\n\t\t\t\tamount: 50,\n\t\t\t\ttime: '2024-01-14 10:20',\n\t\t\t\tstatus: 'pending'\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 3,\n\t\t\t\tamount: 200,\n\t\t\t\ttime: '2024-01-13 16:45',\n\t\t\t\tstatus: 'failed'\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 4,\n\t\t\t\tamount: 150,\n\t\t\t\ttime: '2024-01-12 09:15',\n\t\t\t\tstatus: 'success'\n\t\t\t},\n\t\t\t{\n\t\t\t\tid: 5,\n\t\t\t\tamount: 80,\n\t\t\t\ttime: '2024-01-11 18:30',\n\t\t\t\tstatus: 'pending'\n\t\t\t}\n\t\t]\n\n\t\tconsole.log('提现记录总数据:', mockWithdrawData)\n\n\t\t// 模拟分页\n\t\tconst startIndex = (pageNo - 1) * pageSize\n\t\tconst endIndex = startIndex + pageSize\n\t\tconst pageData = mockWithdrawData.slice(startIndex, endIndex)\n\n\t\tconsole.log('提现记录分页数据:', pageData)\n\t\tconsole.log('=== 查询提现记录结束 ===')\n\n\t\t// 完成分页请求\n\t\twithdrawPaging.value.complete(pageData)\n\t} catch (error) {\n\t\tconsole.error('查询提现记录出错:', error)\n\t\t// 请求失败\n\t\twithdrawPaging.value.complete(false)\n\t}\n}\n\n// 获取状态文字\nconst getStatusText = (status) => {\n\tconst statusMap = {\n\t\tpending: '处理中',\n\t\tsuccess: '已到账',\n\t\tfailed: '失败'\n\t}\n\treturn statusMap[status] || '未知'\n}\n\nonLoad(() => {\n\t// 初始化数据\n\tconsole.log('助力计划页面加载')\n\tconsole.log('朋友数据:', friends.value)\n\tconsole.log('当前选中分类:', selectedCategory.value)\n\n\t// 确保z-paging组件能正确初始化\n\tsetTimeout(() => {\n\t\tconsole.log('friendPaging ref:', friendPaging.value)\n\t\tconsole.log('withdrawPaging ref:', withdrawPaging.value)\n\t}, 1000)\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: #f5f5f5;\n}\n\n.main-container {\n\tmin-height: calc(100vh - var(--nav-height, 88px));\n\theight: calc(100vh - var(--nav-height, 88px));\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx 120rpx 20rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n// 邀请统计卡片\n.invite-stats-card {\n\tbackground: linear-gradient(135deg, rgba($primary-color, 0.08), rgba($primary-color, 0.15));\n\tborder-radius: 16rpx;\n\tpadding: 20rpx 24rpx;\n\tmargin: 16rpx 0 20rpx;\n\tborder: 1rpx solid rgba($primary-color, 0.2);\n\tflex-shrink: 0; // 不允许压缩\n\n\t.stats-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 16rpx;\n\n\t\t.invite-count {\n\t\t\tfont-size: 30rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\n\t\t.header-actions {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: flex-end;\n\n\t\t\t.invite-more-btn {\n\t\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\t\t\tcolor: #fff;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tpadding: 10rpx 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t}\n\t}\n\n\t.stats-content {\n\t\t.stat-row {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 12rpx;\n\n\t\t\t&:last-child {\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t\t.stat-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: flex-start;\n\n\t\t\t\t.stat-label {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t\t}\n\n\t\t\t\t.stat-value {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.stat-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: flex-end;\n\n\t\t\t\t.withdraw-btn {\n\t\t\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 18rpx;\n\t\t\t\t\tpadding: 8rpx 18rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 600;\n\n\t\t\t\t\t&:disabled {\n\t\t\t\t\t\tbackground: #ccc;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 标签页\n.tab-section {\n\tmargin-bottom: 24rpx;\n\tflex-shrink: 0; // 不允许压缩\n\n\t.tab-nav {\n\t\tdisplay: flex;\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 8rpx;\n\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\n\t\t.tab-item {\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\tpadding: 16rpx 0;\n\t\t\tborder-radius: 12rpx;\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\ttext {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\n\t\t\t\ttext {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 朋友信息部分\n.friend-info-section {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tmargin-bottom: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\n\t.section-title {\n\t\tmargin-bottom: 20rpx;\n\n\t\ttext {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\n\t.friend-tabs {\n\t\tdisplay: flex;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 6rpx;\n\t\tgap: 4rpx;\n\n\t\t.friend-tab-item {\n\t\t\tflex: 1;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tpadding: 16rpx 8rpx;\n\t\t\tborder-radius: 8rpx;\n\t\t\tbackground: transparent;\n\t\t\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\t\t\tcursor: pointer;\n\t\t\tposition: relative;\n\n\t\t\t&.active {\n\t\t\t\tbackground: #fff;\n\t\t\t\ttransform: scale(1.05);\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba($primary-color, 0.15);\n\n\t\t\t\t.tab-content {\n\t\t\t\t\t.tab-name {\n\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\n\t\t\t\t\t.tab-count {\n\t\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.tab-icon {\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\ttransition: transform 0.3s ease;\n\t\t\t}\n\n\t\t\t.tab-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.tab-name {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\n\t\t\t\t.tab-count {\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.98);\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 朋友列表\n.friend-list {\n\tbackground: #fff;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\n\t.friend-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 24rpx;\n\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\n\t\t.friend-avatar {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tborder-radius: 40rpx;\n\t\t\toverflow: hidden;\n\t\t\tmargin-right: 20rpx;\n\n\t\t\timage {\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t}\n\t\t}\n\n\t\t.friend-info {\n\t\t\tflex: 1;\n\n\t\t\t.friend-name {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t}\n\n\t\t\t.friend-status {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #666;\n\t\t\t}\n\t\t}\n\n\t\t.friend-level {\n\t\t\t.level-name {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: $primary-color;\n\t\t\t\tbackground: rgba($primary-color, 0.1);\n\t\t\t\tpadding: 6rpx 12rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tborder: 1rpx solid rgba($primary-color, 0.2);\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 邀请信息内容\n.invite-content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\toverflow: hidden;\n\tmin-height: 0; // 允许flex子项收缩\n}\n\n// 提现记录内容\n.withdraw-content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\toverflow: hidden;\n\tmin-height: 0; // 允许flex子项收缩\n\n\t.withdraw-list {\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\n\t\t.withdraw-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 24rpx;\n\t\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\n\t\t\t&:last-child {\n\t\t\t\tborder-bottom: none;\n\t\t\t}\n\n\t\t\t.withdraw-info {\n\t\t\t\t.withdraw-amount {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\n\t\t\t\t.withdraw-time {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.withdraw-status {\n\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 500;\n\n\t\t\t\t&.pending {\n\t\t\t\t\tbackground: rgba(#E6A23C, 0.1);\n\t\t\t\t\tcolor: #E6A23C;\n\t\t\t\t}\n\n\t\t\t\t&.success {\n\t\t\t\t\tbackground: rgba(#67C23A, 0.1);\n\t\t\t\t\tcolor: #67C23A;\n\t\t\t\t}\n\n\t\t\t\t&.failed {\n\t\t\t\t\tbackground: rgba(#F56C6C, 0.1);\n\t\t\t\t\tcolor: #F56C6C;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 提现弹窗\n.withdraw-modal {\n\twidth: 600rpx;\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\n\t.modal-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 24rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t\t.modal-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\n\t.modal-content {\n\t\tpadding: 24rpx;\n\n\t\t.amount-info {\n\t\t\tmargin-bottom: 24rpx;\n\n\t\t\t.available-amount {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #666;\n\t\t\t}\n\t\t}\n\n\t\t.withdraw-form {\n\t\t\t.form-item {\n\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\t.form-label {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\n\t\t\t\t.form-input {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder: 2rpx solid #e0e0e0;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tpadding: 0 20rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tbox-sizing: border-box;\n\n\t\t\t\t\t&:focus {\n\t\t\t\t\t\tborder-color: $primary-color;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.modal-footer {\n\t\tdisplay: flex;\n\t\tgap: 16rpx;\n\t\tpadding: 24rpx;\n\t\tborder-top: 1rpx solid #f0f0f0;\n\n\t\t.cancel-btn,\n\t\t.confirm-btn {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 600;\n\t\t\tborder: none;\n\t\t}\n\n\t\t.cancel-btn {\n\t\t\tbackground: #f5f5f5;\n\t\t\tcolor: #666;\n\t\t}\n\n\t\t.confirm-btn {\n\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\t\tcolor: #fff;\n\n\t\t\t&:disabled {\n\t\t\t\tbackground: #ccc;\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 空状态样式\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 80rpx 40rpx;\n\n\t.empty-icon {\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tmargin-bottom: 24rpx;\n\t\topacity: 0.6;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\ttext-align: center;\n\t}\n}\n\n// z-paging组件样式\n:deep(.z-paging) {\n\tflex: 1;\n\theight: 100%;\n\tmin-height: 400rpx;\n}\n\n:deep(.z-paging-content) {\n\theight: 100%;\n\tmin-height: 400rpx;\n}\n\n// 确保z-paging容器有足够高度\n.invite-content z-paging,\n.withdraw-content z-paging {\n\tflex: 1;\n\theight: 100%;\n\tmin-height: 400rpx;\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/promotion-center/withdraw/withdraw.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "activeTab", "selectedCate<PERSON><PERSON>", "inviteStats", "reactive", "friendStats", "friends", "friendList", "withdrawList", "friendPaging", "withdrawPaging", "withdrawForm", "withdrawPopup", "onPageScroll", "e", "canSubmitWithdraw", "computed", "amount", "switchTab", "tab", "selectCategory", "category", "inviteMore", "toast", "showWithdrawModal", "closeWithdrawModal", "submitWithdraw", "queryFriendList", "pageNo", "pageSize", "uni", "filteredData", "friend", "startIndex", "endIndex", "pageData", "error", "queryWithdrawList", "mockWithdrawData", "getStatusText", "status", "onLoad", "MiniProgramPage"], "mappings": "6lBA6LA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EACAE,EAAAF,EAAA,IAAA,QAAA,EACAG,EAAAH,EAAA,IAAA,KAAA,EAGAI,EAAAC,EAAAA,SAAA,CACA,YAAA,EACA,cAAA,EACA,kBAAA,EACA,gBAAA,CACA,CAAA,EAGAC,EAAAD,EAAAA,SAAA,CACA,MAAA,IACA,QAAA,GACA,OAAA,GACA,IAAA,EACA,CAAA,EAGAE,EAAAP,EAAAA,IAAA,CACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,OACA,OAAA,IACA,MAAA,OACA,SAAA,KACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,OACA,OAAA,IACA,MAAA,OACA,SAAA,SACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,MACA,OAAA,GACA,MAAA,OACA,SAAA,QACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,OACA,OAAA,IACA,MAAA,OACA,SAAA,SACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,MACA,OAAA,GACA,MAAA,OACA,SAAA,QACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,OACA,OAAA,IACA,MAAA,OACA,SAAA,KACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,OACA,OAAA,IACA,MAAA,OACA,SAAA,SACA,EACA,CACA,GAAA,EACA,KAAA,KACA,OAAA,yCACA,OAAA,MACA,OAAA,GACA,MAAA,OACA,SAAA,QACA,CACA,CAAA,EAGAQ,EAAAR,EAAA,IAAA,EAAA,EACAS,EAAAT,EAAA,IAAA,EAAA,EACAU,EAAAV,EAAA,IAAA,IAAA,EACAW,EAAAX,EAAA,IAAA,IAAA,EAGAY,EAAAP,EAAAA,SAAA,CACA,OAAA,EACA,CAAA,EAGAQ,EAAAb,EAAA,IAAA,IAAA,EAGAc,EAAA,aAAAC,GAAA,CACAhB,EAAA,MAAAgB,EAAA,SACA,CAAA,EAgBA,MAAAC,EAAAC,EAAA,SAAA,IAAA,CACA,MAAAC,EAAA,WAAAN,EAAA,MAAA,EACA,OAAAM,EAAA,GAAAA,GAAAd,EAAA,eACA,CAAA,EAGAe,EAAAC,GAAA,CACAlB,EAAA,MAAAkB,EAEAA,IAAA,YACA,WAAA,IAAA,CACAT,EAAA,OACAA,EAAA,MAAA,OAAA,CAEA,EAAA,GAAA,CAEA,EAGAU,EAAAC,GAAA,CACAnB,EAAA,MAAAmB,EAEAZ,EAAA,OACAA,EAAA,MAAA,OAAA,CAEA,EAGAa,EAAA,IAAA,CAEAC,EAAAA,MAAA,SAAA,CACA,EAGAC,EAAA,IAAA,CACArB,EAAA,iBAAA,GACAoB,EAAAA,MAAA,SAAA,EAGAX,EAAA,MAAA,KAAA,CACA,EAGAa,EAAA,IAAA,CACAb,EAAA,MAAA,MAAA,EACAD,EAAA,OAAA,EACA,EAGAe,EAAA,IAAA,CACA,GAAA,CAAAX,EAAA,MAAA,CACAQ,EAAAA,MAAA,YAAA,EACA,MACA,CAGAA,EAAAA,MAAA,SAAA,EACAE,EAAA,CACA,EAGAE,EAAA,MAAAC,EAAAC,IAAA,CACA,GAAA,CACAC,EAAAA,MAAA,MAAA,MAAA,yDAAA,kBAAA,EACAA,EAAAA,MAAA,MAAA,MAAA,yDAAA,UAAAF,EAAA,YAAAC,EAAA,MAAA3B,EAAA,KAAA,EAGA,IAAA6B,EAAAzB,EAAA,MACAJ,EAAA,QAAA,QACA6B,EAAAzB,EAAA,MAAA,OAAA0B,GAAAA,EAAA,WAAA9B,EAAA,KAAA,GAGA4B,EAAA,MAAA,MAAA,MAAA,yDAAA,UAAAxB,EAAA,KAAA,EACAwB,EAAAA,MAAA,MAAA,MAAA,yDAAA,UAAAC,CAAA,EAGA,MAAAE,GAAAL,EAAA,GAAAC,EACAK,EAAAD,EAAAJ,EACAM,EAAAJ,EAAA,MAAAE,EAAAC,CAAA,EAEAJ,EAAAA,MAAA,MAAA,MAAA,yDAAA,QAAAK,CAAA,EACAL,EAAAA,MAAA,MAAA,MAAA,yDAAA,kBAAA,EAGArB,EAAA,MAAA,SAAA0B,CAAA,CACA,OAAAC,EAAA,CACAN,EAAAA,MAAA,MAAA,QAAA,yDAAA,YAAAM,CAAA,EAEA3B,EAAA,MAAA,SAAA,EAAA,CACA,CACA,EAGA4B,EAAA,MAAAT,EAAAC,IAAA,CACA,GAAA,CACAC,EAAAA,MAAA,MAAA,MAAA,yDAAA,kBAAA,EACAA,EAAA,MAAA,MAAA,MAAA,yDAAA,UAAAF,EAAA,YAAAC,CAAA,EAGA,MAAAS,EAAA,CACA,CACA,GAAA,EACA,OAAA,IACA,KAAA,mBACA,OAAA,SACA,EACA,CACA,GAAA,EACA,OAAA,GACA,KAAA,mBACA,OAAA,SACA,EACA,CACA,GAAA,EACA,OAAA,IACA,KAAA,mBACA,OAAA,QACA,EACA,CACA,GAAA,EACA,OAAA,IACA,KAAA,mBACA,OAAA,SACA,EACA,CACA,GAAA,EACA,OAAA,GACA,KAAA,mBACA,OAAA,SACA,CACA,EAEAR,EAAAA,MAAA,MAAA,MAAA,yDAAA,WAAAQ,CAAA,EAGA,MAAAL,GAAAL,EAAA,GAAAC,EACAK,EAAAD,EAAAJ,EACAM,EAAAG,EAAA,MAAAL,EAAAC,CAAA,EAEAJ,EAAAA,MAAA,MAAA,MAAA,yDAAA,YAAAK,CAAA,EACAL,EAAAA,MAAA,MAAA,MAAA,yDAAA,kBAAA,EAGApB,EAAA,MAAA,SAAAyB,CAAA,CACA,OAAAC,EAAA,CACAN,EAAAA,MAAA,MAAA,QAAA,yDAAA,YAAAM,CAAA,EAEA1B,EAAA,MAAA,SAAA,EAAA,CACA,CACA,EAGA6B,EAAAC,IACA,CACA,QAAA,MACA,QAAA,MACA,OAAA,IACA,GACAA,CAAA,GAAA,KAGAC,OAAAA,EAAAA,OAAA,IAAA,CAEAX,EAAAA,MAAA,MAAA,MAAA,yDAAA,UAAA,EACAA,EAAA,MAAA,MAAA,MAAA,yDAAA,QAAAxB,EAAA,KAAA,EACAwB,EAAA,MAAA,MAAA,MAAA,yDAAA,UAAA5B,EAAA,KAAA,EAGA,WAAA,IAAA,CACA4B,EAAA,MAAA,MAAA,MAAA,yDAAA,oBAAArB,EAAA,KAAA,EACAqB,EAAA,MAAA,MAAA,MAAA,yDAAA,sBAAApB,EAAA,KAAA,CACA,EAAA,GAAA,CACA,CAAA,ioDC1eA,GAAG,WAAWgC,CAAe"}