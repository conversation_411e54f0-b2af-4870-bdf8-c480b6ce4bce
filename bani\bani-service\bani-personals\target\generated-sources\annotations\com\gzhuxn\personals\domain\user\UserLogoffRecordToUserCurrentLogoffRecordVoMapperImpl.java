package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.logoff.UserCurrentLogoffRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLogoffRecordToUserCurrentLogoffRecordVoMapperImpl implements UserLogoffRecordToUserCurrentLogoffRecordVoMapper {

    @Override
    public UserCurrentLogoffRecordVo convert(UserLogoffRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserCurrentLogoffRecordVo userCurrentLogoffRecordVo = new UserCurrentLogoffRecordVo();

        userCurrentLogoffRecordVo.setId( arg0.getId() );
        userCurrentLogoffRecordVo.setUserId( arg0.getUserId() );
        userCurrentLogoffRecordVo.setLogoffTime( arg0.getLogoffTime() );

        return userCurrentLogoffRecordVo;
    }

    @Override
    public UserCurrentLogoffRecordVo convert(UserLogoffRecord arg0, UserCurrentLogoffRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setLogoffTime( arg0.getLogoffTime() );

        return arg1;
    }
}
