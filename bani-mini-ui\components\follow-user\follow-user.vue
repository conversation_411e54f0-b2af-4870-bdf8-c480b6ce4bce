<template>
	<view class="follow-user-container">
		<!-- 用户列表 -->
		<z-paging ref="paging" v-model="userList" @query="queryUserList" :auto="true" :refresher-enabled="true"
			:loading-more-enabled="true" :empty-view-text="emptyText" empty-view-img="" :data-key="currentCategory">

			<!-- 自定义刷新组件 -->
			<template #refresher="{ refresherStatus }">
				<custom-refresher :refresher-status="refresherStatus" />
			</template>

			<template #top>
				<!-- 好友分类标签 -->
				<view class="top-menu margin-split" v-if="showCategory" :style="{ paddingTop: navBarHeight + 'px' }">
					<uni-segmented-control :current="segmentedIndex" :values="segmentedValues" @clickItem="switchTab"
						styleType="text" activeColor="#696CF3">
					</uni-segmented-control>
				</view>
			</template>
			<view class="user-list-container margin-split">
				<view class="user-item" v-for="(item, index) in userList" :key="index" @click="handleUserClick(item)">
					<view class="avatar-container">
						<image class="avatar" :src="item.oppAvatar" mode="aspectFill"></image>
						<view class="online-status" v-if="item.isOnline"></view>
					</view>
					<view class="content">
						<view class="name-with-gender">
							<text class="title">{{ item.oppNickName }}</text>
							<text class="iconfont gender-icon"
								:class="item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'"
								:style="{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }"></text>
						</view>
						<!-- 用户信息行 -->
						<view class="user-info-row">
							<text class="info-text">{{ item.oppAge }} · {{ item.oppHeight }}{{
								item.oppCity ? ' · ' + item.oppCity : ''
							}}</text>
						</view>
						<!-- 关注时间行 -->
						<view class="follow-time-row">
							<text class="time-text">{{ item.createTime }}</text>
						</view>
					</view>
					<view class="action-btn" @click.stop="handleUserAction(item)">
						<text class="action-text">{{ getUserActionText(item) }}</text>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { followUserPage, toggleUserFollow } from '@/api/my/follow'
import CustomRefresher from '@/components/custom-refresher/custom-refresher.vue'

// Props
const props = defineProps({
	// 导航栏高度
	navBarHeight: {
		type: Number,
		default: 0
	},
	// 初始分类
	initialCategory: {
		type: String,
		default: 'mutual'
	},
	// 是否显示分类标签
	showCategory: {
		type: Boolean,
		default: true
	},
	// 主题色
	themeColor: {
		type: String,
		default: '#696CF3'
	}
})

// Emits
const emit = defineEmits(['userClick', 'userAction', 'categoryChange'])

// 状态管理
const currentCategory = ref(props.initialCategory)
const userList = ref([])
const paging = ref(null)

// 计数
const mutualCount = ref(0)
const followingCount = ref(0)
const followersCount = ref(0)

// uni-segmented-control相关
const segmentedIndex = ref(0)
const segmentedValues = computed(() => {
	const values = []
	// 关注
	if (followingCount.value > 0) {
		values.push(`关注(${followingCount.value})`)
	} else {
		values.push('关注')
	}

	// 粉丝
	if (followersCount.value > 0) {
		values.push(`粉丝(${followersCount.value})`)
	} else {
		values.push('粉丝')
	}

	// 互关
	if (mutualCount.value > 0) {
		values.push(`互关(${mutualCount.value})`)
	} else {
		values.push('互关')
	}

	return values
})

const emptyText = computed(() => {
	switch (currentCategory.value) {
		case 'mutual':
			return '暂无互关好友'
		case 'following':
			return '暂无关注用户'
		case 'followers':
			return '暂无粉丝'
		default:
			return '暂无数据'
	}
})

// 监听分类变化
watch(currentCategory, (newCategory) => {
	emit('categoryChange', newCategory)
	// 更新segmentedIndex
	updateSegmentedIndex()
	// 重新加载数据
	if (paging.value) {
		paging.value.reload()
	}
})

// 更新segmentedIndex
const updateSegmentedIndex = () => {
	switch (currentCategory.value) {
		case 'following':
			segmentedIndex.value = 0
			break
		case 'followers':
			segmentedIndex.value = 1
			break
		case 'mutual':
			segmentedIndex.value = 2
			break
	}
}

// 分类切换
const switchCategory = (category) => {
	if (currentCategory.value !== category) {
		currentCategory.value = category
	}
}

// uni-segmented-control点击处理
const switchTab = (e) => {
	if (segmentedIndex.value !== e.currentIndex) {
		segmentedIndex.value = e.currentIndex

		// 根据索引设置分类
		switch (e.currentIndex) {
			case 0:
				currentCategory.value = 'following'
				break
			case 1:
				currentCategory.value = 'followers'
				break
			case 2:
				currentCategory.value = 'mutual'
				break
		}
	}
}

// 获取关系类型对应的API参数
const getRelationshipType = (category) => {
	switch (category) {
		case 'following':
			return 2 // 我关注的
		case 'followers':
			return 1 // 关注我的
		case 'mutual':
			return 3 // 互关
		default:
			return 2
	}
}

// z-paging查询数据
const queryUserList = (pageNo, pageSize) => {
	const relationshipType = getRelationshipType(currentCategory.value)
	followUserPage({
		type: relationshipType,
		pageSize: pageSize,
		pageNum: pageNo
	}).then(response => {
		const formattedList = response.rows
		console.log('用户列表加载成功:', formattedList.length, '条数据')

		// 通知z-paging数据加载完成
		paging.value.complete(formattedList)
	})
}

// 处理用户点击
const handleUserClick = (user) => {
	console.log('点击用户:', user)
	emit('userClick', user)
}

// 处理用户操作
const handleUserAction = async (user) => {
	console.log('用户操作:', user)

	switch (currentCategory.value) {
		case 'mutual':
			// 跳转到打招呼页面
			uni.navigateTo({
				url: `/pagesubs/personals/greeting/greeting?userId=${user.oppUserId}&nickName=${encodeURIComponent(user.oppNickName)}&avatar=${user.oppAvatar}`,
				fail: (err) => {
					console.error('跳转打招呼页面失败:', err)
					uni.showToast({
						title: '跳转失败',
						icon: 'error'
					})
				}
			})
			break
		case 'following':
			// 已关注用户，取消关注
			await handleUnfollow(user)
			break
		case 'followers':
			// 粉丝，回关
			await handleFollow(user)
			break
		default:
			// 默认关注
			await handleFollow(user)
			break
	}

	emit('userAction', user)
}

// 关注用户
const handleFollow = async (user) => {
	console.log('关注用户:', user.oppUserId)

	const response = await toggleUserFollow(user.oppUserId, false)

	if (response.code === 1) {
		uni.showToast({
			title: '关注成功',
			icon: 'none',
			duration: 1500
		})
		// 刷新列表
		if (paging.value) {
			paging.value.reload()
		}
	} else {
		console.error('关注操作失败:', response.msg || '未知错误')
		uni.showToast({
			title: response.msg || '关注失败，请重试',
			icon: 'none',
			duration: 2000
		})
	}
}

// 取消关注用户
const handleUnfollow = (user) => {
	console.log('取消关注用户:', user.oppUserId)

	// 显示确认对话框
	uni.showModal({
		title: '确认',
		content: `确定要取消关注 ${user.oppNickName} 吗？`,
		confirmText: '取消关注',
		cancelText: '再想想',
		success: (result) => {
			if (result.confirm) {
				toggleUserFollow(user.oppUserId, true).then(response => {
					// 显示取消关注成功提示
					uni.showToast({
						title: '取消关注成功',
						icon: 'none',
						duration: 1500
					})
				})
			}
		}
	})
}

// 获取用户操作按钮文字
const getUserActionText = (user) => {
	switch (currentCategory.value) {
		case 'mutual':
			return '打招呼'
		case 'following':
			return '取消关注'
		case 'followers':
			if (user.oppIsFollowed) {
				return '打招呼';
			}
			return '回关'
		default:
			return '关注'
	}
}

// 刷新数据
const refresh = () => {
	if (paging.value) {
		paging.value.reload()
	}
}

// 暴露方法给父组件
defineExpose({
	refresh,
	switchCategory
})

// 组件挂载时初始化
onMounted(() => {
	console.log('follow-user组件mounted')
	// 初始化segmentedIndex
	updateSegmentedIndex()
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.follow-user-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.user-list-container {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	overflow: hidden;
}

.user-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid rgba($primary-color, 0.08);
	background: rgba(255, 255, 255, 0.95);

	&:active {
		background-color: rgba($primary-color, 0.05);
	}

	&:last-child {
		border-bottom: none;
	}

	.avatar-container {
		position: relative;
		margin-right: $spacing-lg;

		.avatar {
			width: 110rpx;
			height: 110rpx;
			border-radius: $radius-full;
			background: $bg-secondary;
			box-shadow: $shadow-sm;
		}

		.online-status {
			position: absolute;
			bottom: 4rpx;
			right: 4rpx;
			width: 20rpx;
			height: 20rpx;
			background: #4CAF50;
			border: 2rpx solid $bg-primary;
			border-radius: 50%;
		}
	}

	.content {
		flex: 1;
		min-width: 0;
		height: 110rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-top: -20rpx;

		.name-with-gender {
			display: flex;
			align-items: center;
			flex: 1;
			min-width: 0;

			.title {
				font-size: $title-size-md;
				font-weight: $font-weight-medium;
				color: $text-primary;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-right: 8rpx;
			}

			.gender-icon {
				font-size: 28rpx;
				flex-shrink: 0;
			}
		}

		.user-info-row {
			margin-bottom: 6rpx;

			.info-text {
				font-size: $font-size-sm;
				color: $text-secondary;
				line-height: 1.4;
			}
		}

		.follow-time-row {
			margin-bottom: 8rpx;

			.time-text {
				font-size: $font-size-xs;
				color: $text-tertiary;
				line-height: 1.4;
			}
		}

		.subtitle {
			font-size: $font-size-sm;
			color: $text-secondary;
			line-height: 1.4;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			margin-bottom: 8rpx;
		}

		.user-tags {
			display: flex;
			flex-wrap: wrap;
			gap: 8rpx;

			.tag {
				padding: 4rpx 8rpx;
				background: $bg-secondary;
				color: $text-secondary;
				font-size: $font-size-xs;
				border-radius: $radius-sm;
			}
		}
	}

	.action-btn {
		padding: 12rpx 20rpx;
		background: linear-gradient(135deg, #696CF3 0%, #5A5FE8 100%);
		color: $text-white;
		border-radius: 50rpx;
		font-size: 24rpx;
		margin-left: 20rpx;
		flex-shrink: 0;
		box-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.3);
		border: none;

		&:active {
			transform: scale(0.95);
			box-shadow: 0 2rpx 8rpx rgba(105, 108, 243, 0.4);
		}

		.action-text {
			color: $text-white;
			font-size: 24rpx;
			font-weight: 500;
		}
	}
}
</style>
