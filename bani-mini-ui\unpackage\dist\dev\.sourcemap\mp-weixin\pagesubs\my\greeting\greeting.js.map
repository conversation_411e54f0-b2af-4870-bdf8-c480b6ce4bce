{"version": 3, "file": "greeting.js", "sources": ["pagesubs/my/greeting/greeting.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcZ3JlZXRpbmdcZ3JlZXRpbmcudnVl"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"打招呼记录\" :show-back=\"true\" @heightChange=\"handleNavHeightChange\">\n\t\t<template #content>\n\t\t\t<z-paging ref=\"paging\" v-model=\"datas\" :auto=\"true\" :refresher-enabled=\"true\" :loading-more-enabled=\"true\"\n\t\t\t\t@query=\"queryList\">\n\t\t\t\t<template #top>\n\t\t\t\t\t<!-- 顶部菜单 -->\n\t\t\t\t\t<view class=\"top-menu margin-split\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t\t<uni-segmented-control :current=\"segmentedIndex\" :values=\"['想认识我的', '我想认识的']\"\n\t\t\t\t\t\t\t@clickItem=\"switchTab\" styleType=\"text\" activeColor=\"#696CF3\"></uni-segmented-control>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t\t<!-- 自定义刷新组件 -->\n\t\t\t\t<template #refresher=\"{ refresherStatus }\">\n\t\t\t\t\t<custom-refresher :refresher-status=\"refresherStatus\" />\n\t\t\t\t</template>\n\t\t\t\t<!-- 打招呼记录列表 -->\n\t\t\t\t<view class=\"greeting-list margin-split\">\n\t\t\t\t\t<view class=\"greeting-card\" v-for=\"(item, index) in datas\" :key=\"item.id\">\n\t\t\t\t\t\t<!-- 卡片内容 -->\n\t\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t\t<view class=\"user-section\">\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.oppAvatar\"></image>\n\t\t\t\t\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t\t\t\t\t<!-- 昵称与时间同一行 -->\n\t\t\t\t\t\t\t\t\t<view class=\"name-time-row\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"name-icons\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"nick-name\">{{ item.oppNickName }}</text>\n\t\t\t\t\t\t\t\t\t\t\t<!-- 性别图标 -->\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }\"></text>\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.oppIsIdentity\" class=\"verified-tag\">已实名</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ item.time }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"user-detail\">{{ item.oppAge }} · {{ item.oppHeight }} · {{ item.oppCity\n\t\t\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 打招呼内容 -->\n\t\t\t\t\t\t\t<view class=\"greeting-section\">\n\t\t\t\t\t\t\t\t<view class=\"greeting-content\">\n\t\t\t\t\t\t\t\t\t<text class=\"greeting-text\">{{ item.content || '向你打招呼' }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<!-- 状态标识 -->\n\t\t\t\t\t\t\t\t<view class=\"greeting-status\" v-if=\"item.status\">\n\t\t\t\t\t\t\t\t\t<text class=\"status-text\" :class=\"getStatusClass(item.status)\">{{ getStatusText(item.status) }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 回复内容 (仅在已回复状态下显示) -->\n\t\t\t\t\t\t\t<view class=\"reply-content\" v-if=\"item.status === 1 && item.replyContent\">\n\t\t\t\t\t\t\t\t<text class=\"reply-label\">回复：</text>\n\t\t\t\t\t\t\t\t<text class=\"reply-text\">{{ item.replyContent }}</text>\n\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t<!-- 操作按钮区域 (仅收到的打招呼显示) -->\n\t\t\t\t\t\t\t<view class=\"action-section\" v-if=\"segmentedIndex === 0 && item.status === 0\">\n\t\t\t\t\t\t\t\t<button class=\"action-btn ignore-btn\" @click=\"handleIgnore(item.id)\">忽略</button>\n\t\t\t\t\t\t\t\t<button class=\"action-btn reply-btn\" @click=\"handleReply(item.id)\">回复</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<template #empty>\n\t\t\t\t\t<view class=\"empty-state\">\n\t\t\t\t\t\t<image class=\"empty-icon\" src=\"/static/image/empty.png\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t<text class=\"empty-text\">暂无打招呼记录</text>\n\t\t\t\t\t</view>\n\t\t\t\t</template>\n\t\t\t</z-paging>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { onLoad, onPageScroll } from \"@dcloudio/uni-app\"\nimport { getReceivedGreetingPage, getSentGreetingPage, replyGreeting, ignoreGreeting } from '@/api/user/greeting'\n\n// 导航栏相关\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0) // 初始为0，等待组件传递真实高度\n\n// 页面数据\nconst segmentedIndex = ref(0)\nconst queryType = ref('received') // 'received'-收到的打招呼、'sent'-发送的打招呼\n\n// z-paging组件\nconst paging = ref(null)\nconst datas = ref([])\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\nonLoad((param) => {\n\t// 默认显示收到的打招呼\n\tif (!param.type) {\n\t\tparam.type = 'received'\n\t}\n\tconst type = param.type\n\tsegmentedIndex.value = type === 'received' ? 0 : 1 // 'received'-想认识我的(index=0), 'sent'-我想认识的(index=1)\n\tqueryType.value = type\n})\n\nfunction queryList(pageNum, pageSize) {\n\tconst apiFunction = queryType.value === 'received' ? getReceivedGreetingPage : getSentGreetingPage\n\n\tapiFunction({\n\t\tpageNum: pageNum,\n\t\tpageSize: pageSize,\n\t}).then(res => {\n\t\tpaging.value.complete(res.rows);\n\t})\n}\n\nconst switchTab = (e) => {\n\tif (segmentedIndex.value !== e.currentIndex) {\n\t\tsegmentedIndex.value = e.currentIndex\n\t\t// 0-想认识我的(queryType='received'), 1-我想认识的(queryType='sent')\n\t\tqueryType.value = segmentedIndex.value === 0 ? 'received' : 'sent'\n\t\tpaging.value.reload()\n\t}\n}\n\n// 获取状态文本\nconst getStatusText = (status) => {\n\tswitch (status) {\n\t\tcase 0:\n\t\t\treturn '待回复'\n\t\tcase 1:\n\t\t\treturn '已回复'\n\t\tcase 2:\n\t\t\treturn '已忽略'\n\t\tdefault:\n\t\t\treturn ''\n\t}\n}\n\n// 获取状态样式类\nconst getStatusClass = (status) => {\n\tswitch (status) {\n\t\tcase 0:\n\t\t\treturn 'status-pending'\n\t\tcase 1:\n\t\t\treturn 'status-replied'\n\t\tcase 2:\n\t\t\treturn 'status-ignored'\n\t\tdefault:\n\t\t\treturn ''\n\t}\n}\n\n// 处理回复\nconst handleReply = (id) => {\n\tuni.showModal({\n\t\ttitle: '回复打招呼',\n\t\teditable: true,\n\t\tplaceholderText: '请输入回复内容...',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm && res.content) {\n\t\t\t\treplyGreeting(id, res.content).then(() => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '回复成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\tpaging.value.reload()\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err.message || '回复失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n\n// 处理忽略\nconst handleIgnore = (id) => {\n\tuni.showModal({\n\t\ttitle: '确认忽略',\n\t\tcontent: '确定要忽略这条打招呼吗？',\n\t\tsuccess: (res) => {\n\t\t\tif (res.confirm) {\n\t\t\t\tignoreGreeting(id).then(() => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已忽略',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t\tpaging.value.reload()\n\t\t\t\t}).catch(err => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: err.message || '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t})\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n// z-paging组件样式\n:deep(.z-paging-content) {\n\tmin-height: calc(100vh - 200px);\n}\n\n.greeting-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 16rpx;\n}\n\n.greeting-card {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\toverflow: hidden;\n\ttransition: all 0.3s ease;\n\n\t&:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 8rpx 24rpx rgba($primary-color, 0.12);\n\t}\n\n\t.card-content {\n\t\tpadding: 24rpx;\n\n\t\t.user-section {\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\t\t\tgap: 16rpx;\n\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t.avatar {\n\t\t\t\theight: 80rpx;\n\t\t\t\twidth: 80rpx;\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba($primary-color, 0.15);\n\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.user-info {\n\t\t\t\tflex: 1;\n\t\t\t\tmin-width: 0;\n\n\t\t\t\t.name-time-row {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\n\t\t\t\t\t.name-icons {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tgap: 6rpx;\n\n\t\t\t\t\t\t.nick-name {\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\t\tletter-spacing: 0.5rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.gender-icon {\n\t\t\t\t\t\t\tmargin-left: 4rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.verified-tag {\n\t\t\t\t\t\t\tbackground: #696CF3;\n\t\t\t\t\t\t\tcolor: white;\n\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\tpadding: 2rpx 6rpx;\n\t\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tmargin-left: 4rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.time {\n\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.user-detail {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.greeting-section {\n\t\t\tbackground: rgba($primary-color, 0.05);\n\t\t\tborder-radius: 12rpx;\n\t\t\tpadding: 16rpx 20rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t.greeting-content {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.greeting-text {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.greeting-status {\n\t\t\t\tflex-shrink: 0;\n\t\t\t\tmargin-left: 16rpx;\n\n\t\t\t\t.status-text {\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tpadding: 4rpx 12rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tfont-weight: 500;\n\n\t\t\t\t\t&.status-pending {\n\t\t\t\t\t\tcolor: #FF8C00;\n\t\t\t\t\t\tbackground: rgba(255, 140, 0, 0.1);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.status-replied {\n\t\t\t\t\t\tcolor: #4CAF50;\n\t\t\t\t\t\tbackground: rgba(76, 175, 80, 0.1);\n\t\t\t\t\t}\n\n\t\t\t\t\t&.status-ignored {\n\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\tbackground: rgba(153, 153, 153, 0.1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.reply-content {\n\t\t\tmargin: 12rpx 0;\n\t\t\tpadding: 16rpx;\n\t\t\tbackground: rgba($primary-color, 0.05);\n\t\t\tborder-radius: 12rpx;\n\t\t\tborder-left: 4rpx solid $primary-color;\n\n\t\t\t.reply-label {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: $primary-color;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tmargin-right: 8rpx;\n\t\t\t}\n\n\t\t\t.reply-text {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #555;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tword-break: break-all;\n\t\t\t}\n\t\t}\n\n\t\t.action-section {\n\t\t\tdisplay: flex;\n\t\t\tgap: 16rpx;\n\t\t\tjustify-content: flex-end;\n\n\t\t\t.action-btn {\n\t\t\t\tpadding: 12rpx 24rpx;\n\t\t\t\tborder-radius: 24rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tborder: none;\n\t\t\t\tcursor: pointer;\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&.ignore-btn {\n\t\t\t\t\tbackground: rgba(153, 153, 153, 0.1);\n\t\t\t\t\tcolor: #999;\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tbackground: rgba(153, 153, 153, 0.2);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.reply-btn {\n\t\t\t\t\tbackground: $primary-color;\n\t\t\t\t\tcolor: white;\n\n\t\t\t\t\t&:hover {\n\t\t\t\t\t\tbackground: darken($primary-color, 10%);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 空状态样式\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 120rpx 40rpx;\n\n\t.empty-icon {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tmargin-bottom: 32rpx;\n\t\topacity: 0.6;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t\tfont-weight: 500;\n\t}\n}\n</style>\n\n<style>\n@import '@/static/fonts/iconfont.css';\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/greeting/greeting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "segmentedIndex", "queryType", "paging", "datas", "onPageScroll", "e", "handleNavHeightChange", "height", "onLoad", "param", "type", "queryList", "pageNum", "pageSize", "getReceivedGreetingPage", "getSentGreetingPage", "res", "switchTab", "getStatusText", "status", "getStatusClass", "handleReply", "id", "uni", "replyGreeting", "err", "handleIgnore", "ignoreGreeting", "MiniProgramPage"], "mappings": "wrBAqFA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,CAAA,EACAG,EAAAH,EAAA,IAAA,UAAA,EAGAI,EAAAJ,EAAA,IAAA,IAAA,EACAK,EAAAL,EAAA,IAAA,EAAA,EAGAM,EAAA,aAAAC,GAAA,CACAR,EAAA,MAAAQ,EAAA,SACA,CAAA,EAGA,MAAAC,EAAAC,GAAA,CACAR,EAAA,MAAAQ,CACA,EAEAC,EAAA,OAAAC,GAAA,CAEAA,EAAA,OACAA,EAAA,KAAA,YAEA,MAAAC,EAAAD,EAAA,KACAT,EAAA,MAAAU,IAAA,WAAA,EAAA,EACAT,EAAA,MAAAS,CACA,CAAA,EAEA,SAAAC,EAAAC,EAAAC,EAAA,EACAZ,EAAA,QAAA,WAAAa,EAAA,wBAAAC,EAAA,qBAEA,CACA,QAAAH,EACA,SAAAC,CACA,CAAA,EAAA,KAAAG,GAAA,CACAd,EAAA,MAAA,SAAAc,EAAA,IAAA,CACA,CAAA,CACA,CAEA,MAAAC,EAAAZ,GAAA,CACAL,EAAA,QAAAK,EAAA,eACAL,EAAA,MAAAK,EAAA,aAEAJ,EAAA,MAAAD,EAAA,QAAA,EAAA,WAAA,OACAE,EAAA,MAAA,OAAA,EAEA,EAGAgB,EAAAC,GAAA,CACA,OAAAA,EAAA,CACA,IAAA,GACA,MAAA,MACA,IAAA,GACA,MAAA,MACA,IAAA,GACA,MAAA,MACA,QACA,MAAA,EACA,CACA,EAGAC,EAAAD,GAAA,CACA,OAAAA,EAAA,CACA,IAAA,GACA,MAAA,iBACA,IAAA,GACA,MAAA,iBACA,IAAA,GACA,MAAA,iBACA,QACA,MAAA,EACA,CACA,EAGAE,EAAAC,GAAA,CACAC,EAAAA,MAAA,UAAA,CACA,MAAA,QACA,SAAA,GACA,gBAAA,aACA,QAAAP,GAAA,CACAA,EAAA,SAAAA,EAAA,SACAQ,EAAAA,cAAAF,EAAAN,EAAA,OAAA,EAAA,KAAA,IAAA,CACAO,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,KAAA,SACA,CAAA,EACArB,EAAA,MAAA,OAAA,CACA,CAAA,EAAA,MAAAuB,GAAA,CACAF,EAAAA,MAAA,UAAA,CACA,MAAAE,EAAA,SAAA,OACA,KAAA,MACA,CAAA,CACA,CAAA,CAEA,CACA,CAAA,CACA,EAGAC,EAAAJ,GAAA,CACAC,EAAAA,MAAA,UAAA,CACA,MAAA,OACA,QAAA,eACA,QAAAP,GAAA,CACAA,EAAA,SACAW,iBAAAL,CAAA,EAAA,KAAA,IAAA,CACAC,EAAAA,MAAA,UAAA,CACA,MAAA,MACA,KAAA,SACA,CAAA,EACArB,EAAA,MAAA,OAAA,CACA,CAAA,EAAA,MAAAuB,GAAA,CACAF,EAAAA,MAAA,UAAA,CACA,MAAAE,EAAA,SAAA,OACA,KAAA,MACA,CAAA,CACA,CAAA,CAEA,CACA,CAAA,CACA,yoCClNA,GAAG,WAAWG,CAAe"}