package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.greeting.AppUserGreetingCreateBoToUserGreetingMapper;
import com.gzhuxn.personals.domain.user.vo.UserGreetingVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserGreetingCreateBoToUserGreetingMapper.class,UserGreetingToAppUserGreetingVoMapper.class},
    imports = {}
)
public interface UserGreetingToUserGreetingVoMapper extends BaseMapper<UserGreeting, UserGreetingVo> {
}
