package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBoToActSafeguardItemMapper;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardItemBoToActSafeguardItemMapper.class,ActSafeguardItemToActSafeguardItemStorageVoMapper.class},
    imports = {}
)
public interface ActSafeguardItemToActSafeguardItemVoMapper extends BaseMapper<ActSafeguardItem, ActSafeguardItemVo> {
}
