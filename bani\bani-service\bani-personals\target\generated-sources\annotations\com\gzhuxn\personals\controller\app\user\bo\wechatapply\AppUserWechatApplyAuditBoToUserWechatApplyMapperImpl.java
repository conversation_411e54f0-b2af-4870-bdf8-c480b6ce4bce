package com.gzhuxn.personals.controller.app.user.bo.wechatapply;

import com.gzhuxn.personals.domain.user.UserWechatApply;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserWechatApplyAuditBoToUserWechatApplyMapperImpl implements AppUserWechatApplyAuditBoToUserWechatApplyMapper {

    @Override
    public UserWechatApply convert(AppUserWechatApplyAuditBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWechatApply userWechatApply = new UserWechatApply();

        userWechatApply.setId( arg0.getId() );
        userWechatApply.setOppositeUserId( arg0.getOppositeUserId() );
        userWechatApply.setStatus( arg0.getStatus() );

        return userWechatApply;
    }

    @Override
    public UserWechatApply convert(AppUserWechatApplyAuditBo arg0, UserWechatApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
