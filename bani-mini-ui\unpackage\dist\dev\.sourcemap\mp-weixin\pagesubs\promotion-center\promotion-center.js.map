{"version": 3, "file": "promotion-center.js", "sources": ["pagesubs/promotion-center/promotion-center.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNccHJvbW90aW9uLWNlbnRlclxwcm9tb3Rpb24tY2VudGVyLnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"推广中心\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-container\">\n\t\t\t\t<!-- 收益概览卡片 -->\n\t\t\t\t<view class=\"earnings-card\">\n\t\t\t\t\t<view class=\"earnings-header\">\n\t\t\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t\t\t<uni-icons type=\"wallet-filled\" size=\"28\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t<text class=\"header-title\">我的收益</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"header-right\" @click=\"navigateToRecords\">\n\t\t\t\t\t\t\t<text class=\"view-records\">查看明细</text>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"earnings-content\">\n\t\t\t\t\t\t<view class=\"earnings-item\">\n\t\t\t\t\t\t\t<text class=\"earnings-label\">累计收益</text>\n\t\t\t\t\t\t\t<text class=\"earnings-amount\">¥{{ earningsData.totalEarnings || '0.00' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"earnings-item\">\n\t\t\t\t\t\t\t<text class=\"earnings-label\">可提现余额</text>\n\t\t\t\t\t\t\t<text class=\"earnings-amount highlight\">¥{{ earningsData.availableAmount || '0.00' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"earnings-item\">\n\t\t\t\t\t\t\t<text class=\"earnings-label\">已提现</text>\n\t\t\t\t\t\t\t<text class=\"earnings-amount\">¥{{ earningsData.withdrawnAmount || '0.00' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"earnings-actions\">\n\t\t\t\t\t\t<button class=\"withdraw-btn\" @click=\"navigateToWithdraw\">\n\t\t\t\t\t\t\t<uni-icons type=\"wallet\" size=\"20\" color=\"white\"></uni-icons>\n\t\t\t\t\t\t\t<text>立即提现</text>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 推广数据卡片 -->\n\t\t\t\t<view class=\"promotion-card\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<uni-icons type=\"person-filled\" size=\"24\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"card-title\">推广数据</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"promotion-stats\">\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number\">{{ promotionData.inviteCount || 0 }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">邀请人数</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number\">{{ promotionData.activeCount || 0 }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">活跃用户</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<text class=\"stat-number\">{{ promotionData.todayEarnings || '0.00' }}</text>\n\t\t\t\t\t\t\t<text class=\"stat-label\">今日收益</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 推广工具 -->\n\t\t\t\t<view class=\"tools-card\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<uni-icons type=\"gear-filled\" size=\"24\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t<text class=\"card-title\">推广工具</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"tools-list\">\n\t\t\t\t\t\t<view class=\"tool-item\" @click=\"shareInviteCode\">\n\t\t\t\t\t\t\t<view class=\"tool-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"share\" size=\"24\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"tool-content\">\n\t\t\t\t\t\t\t\t<text class=\"tool-title\">分享邀请码</text>\n\t\t\t\t\t\t\t\t<text class=\"tool-desc\">分享您的专属邀请码给好友</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"tool-item\" @click=\"generatePoster\">\n\t\t\t\t\t\t\t<view class=\"tool-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"image\" size=\"24\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"tool-content\">\n\t\t\t\t\t\t\t\t<text class=\"tool-title\">生成推广海报</text>\n\t\t\t\t\t\t\t\t<text class=\"tool-desc\">生成专属推广海报分享</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 底部导航 -->\n\t\t\t\t<nav-tabbar tab-index=\"1\"></nav-tabbar>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { onLoad, onShow } from '@dcloudio/uni-app'\nimport { getUserBalance } from '@/api/my/withdraw'\nimport { toast } from '@/utils/common'\n\n// 收益数据\nconst earningsData = ref({\n\ttotalEarnings: 0,\n\tavailableAmount: 0,\n\twithdrawnAmount: 0\n})\n\n// 推广数据\nconst promotionData = ref({\n\tinviteCount: 0,\n\tactiveCount: 0,\n\ttodayEarnings: 0\n})\n\n// 页面加载\nonLoad(() => {\n\tloadEarningsData()\n\tloadPromotionData()\n})\n\n// 页面显示时刷新数据\nonShow(() => {\n\tloadEarningsData()\n})\n\n// 加载收益数据\nconst loadEarningsData = async () => {\n\ttry {\n\t\tconst res = await getUserBalance()\n\t\tearningsData.value = res.data\n\t} catch (error) {\n\t\tconsole.error('获取收益数据失败:', error)\n\t}\n}\n\n// 加载推广数据\nconst loadPromotionData = async () => {\n\t// TODO: 实现推广数据API调用\n\t// 这里先使用模拟数据\n\tpromotionData.value = {\n\t\tinviteCount: 15,\n\t\tactiveCount: 8,\n\t\ttodayEarnings: 12.50\n\t}\n}\n\n// 跳转到提现页面\nconst navigateToWithdraw = () => {\n\tif (earningsData.value.availableAmount <= 0) {\n\t\ttoast('暂无可提现余额')\n\t\treturn\n\t}\n\tuni.navigateTo({\n\t\turl: '/pagesubs/promotion-center/withdraw-apply'\n\t})\n}\n\n// 跳转到收益明细\nconst navigateToRecords = () => {\n\t// TODO: 实现收益明细页面\n\ttoast('功能开发中')\n}\n\n// 分享邀请码\nconst shareInviteCode = () => {\n\t// TODO: 实现分享邀请码功能\n\ttoast('功能开发中')\n}\n\n// 生成推广海报\nconst generatePoster = () => {\n\t// TODO: 实现生成推广海报功能\n\ttoast('功能开发中')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page-container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f5f5;\n\tpadding-bottom: 120rpx; // 为底部导航留出空间\n}\n\n.earnings-card {\n\tmargin: 20rpx;\n\tpadding: 40rpx;\n\tbackground: linear-gradient(135deg, #696CF3 0%, #9B59B6 100%);\n\tborder-radius: 20rpx;\n\tcolor: white;\n\n\t.earnings-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\n\t\t.header-left {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.header-title {\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t}\n\n\t\t.header-right {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\topacity: 0.8;\n\n\t\t\t.view-records {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tmargin-right: 5rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.earnings-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 40rpx;\n\n\t\t.earnings-item {\n\t\t\ttext-align: center;\n\n\t\t\t.earnings-label {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\topacity: 0.8;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t}\n\n\t\t\t.earnings-amount {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\n\t\t\t\t&.highlight {\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: #FFE066;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.earnings-actions {\n\t\ttext-align: center;\n\n\t\t.withdraw-btn {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 60rpx;\n\t\t\tbackground-color: rgba(255, 255, 255, 0.2);\n\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\n\t\t\tborder-radius: 50rpx;\n\t\t\tcolor: white;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\n\t\t\ttext {\n\t\t\t\tmargin-left: 10rpx;\n\t\t\t}\n\n\t\t\t&:active {\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.1);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.promotion-card,\n.tools-card {\n\tmargin: 20rpx;\n\tpadding: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 20rpx;\n\n\t.card-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\n\t\t.card-title {\n\t\t\tmargin-left: 10rpx;\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n}\n\n.promotion-stats {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\n\t.stat-item {\n\t\ttext-align: center;\n\n\t\t.stat-number {\n\t\t\tdisplay: block;\n\t\t\tfont-size: 40rpx;\n\t\t\tfont-weight: 700;\n\t\t\tcolor: #696CF3;\n\t\t\tmargin-bottom: 10rpx;\n\t\t}\n\n\t\t.stat-label {\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #666;\n\t\t}\n\t}\n}\n\n.tools-list {\n\t.tool-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 30rpx 0;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\n\t\t.tool-icon {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tbackground-color: #f8f9ff;\n\t\t\tborder-radius: 40rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-right: 20rpx;\n\t\t}\n\n\t\t.tool-content {\n\t\t\tflex: 1;\n\n\t\t\t.tool-title {\n\t\t\t\tdisplay: block;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #333;\n\t\t\t\tmargin-bottom: 5rpx;\n\t\t\t}\n\n\t\t\t.tool-desc {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/promotion-center/promotion-center.vue'\nwx.createPage(MiniProgramPage)"], "names": ["earningsData", "ref", "promotionData", "onLoad", "loadEarningsData", "loadPromotionData", "onShow", "res", "getUserBalance", "error", "uni", "navigateToWithdraw", "toast", "navigateToRecords", "shareInviteCode", "generatePoster", "MiniProgramPage"], "mappings": "8fA6GA,MAAMA,EAAeC,EAAAA,IAAI,CACxB,cAAe,EACf,gBAAiB,EACjB,gBAAiB,CAClB,CAAC,EAGKC,EAAgBD,EAAAA,IAAI,CACzB,YAAa,EACb,YAAa,EACb,cAAe,CAChB,CAAC,EAGDE,EAAAA,OAAO,IAAM,CACZC,EAAkB,EAClBC,EAAmB,CACpB,CAAC,EAGDC,EAAAA,OAAO,IAAM,CACZF,EAAkB,CACnB,CAAC,EAGD,MAAMA,EAAmB,SAAY,CACpC,GAAI,CACH,MAAMG,EAAM,MAAMC,iBAAgB,EAClCR,EAAa,MAAQO,EAAI,IACzB,OAAQE,EAAO,CACfC,EAAAA,4EAAc,YAAaD,CAAK,CAChC,CACF,EAGMJ,EAAoB,SAAY,CAGrCH,EAAc,MAAQ,CACrB,YAAa,GACb,YAAa,EACb,cAAe,IACf,CACF,EAGMS,EAAqB,IAAM,CAChC,GAAIX,EAAa,MAAM,iBAAmB,EAAG,CAC5CY,EAAAA,MAAM,SAAS,EACf,MACA,CACDF,EAAAA,MAAI,WAAW,CACd,IAAK,2CACP,CAAE,CACF,EAGMG,EAAoB,IAAM,CAE/BD,EAAAA,MAAM,OAAO,CACd,EAGME,EAAkB,IAAM,CAE7BF,EAAAA,MAAM,OAAO,CACd,EAGMG,EAAiB,IAAM,CAE5BH,EAAAA,MAAM,OAAO,CACd,4zBCpLA,GAAG,WAAWI,CAAe"}