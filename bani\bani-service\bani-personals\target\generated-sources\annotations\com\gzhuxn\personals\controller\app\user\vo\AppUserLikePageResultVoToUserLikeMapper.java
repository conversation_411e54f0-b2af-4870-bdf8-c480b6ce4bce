package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserLike;
import com.gzhuxn.personals.domain.user.UserLikeToAppUserLikePageResultVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserLikeToAppUserLikePageResultVoMapper.class},
    imports = {}
)
public interface AppUserLikePageResultVoToUserLikeMapper extends BaseMapper<AppUserLikePageResultVo, UserLike> {
}
