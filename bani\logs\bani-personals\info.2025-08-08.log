2025-08-08 07:24:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 07:24:12 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 3580 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 07:24:12 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 07:24:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 07:24:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 07:24:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 07:24:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 07:24:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 07:24:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 07:24:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@45fa7f1a
2025-08-08 07:24:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-08 07:24:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-08 07:24:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-08 07:24:28 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-08 07:24:28 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-08 07:24:28 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-08 07:24:29 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-08 07:24:29 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-08 07:24:34 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-08 07:24:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-08 07:24:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-08 07:24:37 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-08 07:24:37 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-08 07:24:37 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-08 07:24:40 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 30.202 seconds (process running for 31.334)
2025-08-08 07:24:42 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-08 07:24:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-08 07:24:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-08 07:24:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-08 07:24:42 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-08 07:24:42 [RMI TCP Connection(10)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.15.Final
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.g.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus destroy listeners...
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-08 09:11:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-08 09:12:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 09:12:16 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 25064 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 09:12:16 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 09:12:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 09:12:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 09:12:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 09:12:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 09:12:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 09:12:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 09:12:43 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@12f21add
2025-08-08 09:12:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-08 09:12:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-08 09:12:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-08 09:12:48 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-08 09:12:48 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-08 09:12:49 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-08 09:12:49 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-08 09:12:50 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-08 09:12:57 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-08 09:13:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-08 09:13:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-08 09:13:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-08 09:13:07 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-08 09:13:08 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-08 09:13:14 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 62.908 seconds (process running for 69.351)
2025-08-08 09:13:16 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-08 09:13:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-08 09:13:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-08 09:13:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-08 09:13:16 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-08 09:21:55 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-08 16:39:25 [eventbus-connectionWatchdog-pool-1] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus destroy listeners...
2025-08-08 19:53:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 19:53:47 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 23324 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 19:53:47 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 19:53:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 19:53:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 19:53:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 19:54:15 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 19:54:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 19:54:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 19:57:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 19:57:47 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 11736 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 19:57:47 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 19:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 19:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 19:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 19:57:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 19:57:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 19:57:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 19:57:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@185d151b
2025-08-08 19:57:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-08 19:57:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-08 19:57:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-08 19:58:04 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-08 19:58:04 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-08 19:58:04 [redisson-netty-1-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-08 19:58:05 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-08 19:58:05 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-08 19:58:12 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-08 19:58:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-08 19:58:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-08 19:58:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-08 19:58:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-08 19:58:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-08 19:58:19 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 35.887 seconds (process running for 38.524)
2025-08-08 19:58:21 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-08 19:58:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-08 19:58:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-08 19:58:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-08 19:58:21 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-08 19:59:23 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-08 20:57:01 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.15.Final
2025-08-08 20:57:01 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-08 20:57:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 20:57:07 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 9104 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 20:57:07 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 20:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 20:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 20:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 20:57:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 20:57:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 20:57:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 20:57:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@249a7ccc
2025-08-08 20:57:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-08 20:57:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-08 20:57:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-08 20:57:19 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-08 20:57:19 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-08 20:57:19 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-08 20:57:20 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-08 20:57:20 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-08 20:57:24 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-08 20:57:27 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-08 20:57:27 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-08 20:57:27 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-08 20:57:27 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-08 20:57:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-08 20:57:31 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 25.878 seconds (process running for 27.314)
2025-08-08 20:57:32 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-08 20:57:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-08 20:57:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-08 20:57:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-08 20:57:32 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-08 20:57:33 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.15.Final
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  c.g.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus destroy listeners...
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-08-08 21:10:39 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-08-08 21:11:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-08 21:11:07 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 27756 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-08 21:11:07 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-08 21:11:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-08 21:11:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-08 21:11:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-08 21:11:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-08 21:11:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-08 21:11:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-08 21:11:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@156329de
2025-08-08 21:11:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-08 21:11:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-08 21:11:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-08 21:11:25 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-08 21:11:25 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-08 21:11:26 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-08 21:11:26 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-08 21:11:26 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-08 21:11:32 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-08 21:11:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-08 21:11:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-08 21:11:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-08 21:11:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-08 21:11:37 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-08 21:11:40 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 36.919 seconds (process running for 42.594)
2025-08-08 21:11:42 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-08 21:11:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-08 21:11:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-08 21:11:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-08 21:11:42 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-08 21:12:03 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
