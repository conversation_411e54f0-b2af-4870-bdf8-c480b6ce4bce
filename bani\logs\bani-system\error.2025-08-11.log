2025-08-11 00:14:26 [Thread-28] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.2.14, current host: **********, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:502)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/java.io.InputStream.read(InputStream.java:220)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
