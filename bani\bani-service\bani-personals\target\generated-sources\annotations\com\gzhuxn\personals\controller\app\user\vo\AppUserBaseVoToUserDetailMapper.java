package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserDetail;
import com.gzhuxn.personals.domain.user.UserDetailToAppUserBaseVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserDetailToAppUserBaseVoMapper.class},
    imports = {}
)
public interface AppUserBaseVoToUserDetailMapper extends BaseMapper<AppUserBaseVo, UserDetail> {
}
