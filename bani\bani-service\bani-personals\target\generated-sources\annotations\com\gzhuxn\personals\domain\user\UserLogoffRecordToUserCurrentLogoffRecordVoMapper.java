package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.logoff.UserCurrentLogoffRecordVo;
import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBoToUserLogoffRecordMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserLogoffRecordBoToUserLogoffRecordMapper.class,UserLogoffRecordToUserLogoffRecordVoMapper.class},
    imports = {}
)
public interface UserLogoffRecordToUserCurrentLogoffRecordVoMapper extends BaseMapper<UserLogoffRecord, UserCurrentLogoffRecordVo> {
}
