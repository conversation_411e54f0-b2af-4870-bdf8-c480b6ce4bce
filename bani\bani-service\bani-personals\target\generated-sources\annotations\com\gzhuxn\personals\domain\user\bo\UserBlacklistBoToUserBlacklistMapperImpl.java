package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserBlacklist;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBlacklistBoToUserBlacklistMapperImpl implements UserBlacklistBoToUserBlacklistMapper {

    @Override
    public UserBlacklist convert(UserBlacklistBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserBlacklist userBlacklist = new UserBlacklist();

        userBlacklist.setSearchValue( arg0.getSearchValue() );
        userBlacklist.setCreateBy( arg0.getCreateBy() );
        userBlacklist.setCreateTime( arg0.getCreateTime() );
        userBlacklist.setUpdateBy( arg0.getUpdateBy() );
        userBlacklist.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userBlacklist.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userBlacklist.setCreateDept( arg0.getCreateDept() );
        userBlacklist.setId( arg0.getId() );
        userBlacklist.setUserId( arg0.getUserId() );
        userBlacklist.setOppositeUserId( arg0.getOppositeUserId() );

        return userBlacklist;
    }

    @Override
    public UserBlacklist convert(UserBlacklistBo arg0, UserBlacklist arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
