package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.admin.audit.vo.AdminContentAuditUserCommentVo;
import com.gzhuxn.personals.controller.app.user.bo.comment.AppUserCommentCreateBoToUserCommentMapper;
import com.gzhuxn.personals.domain.user.bo.UserCommentBoToUserCommentMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserCommentCreateBoToUserCommentMapper.class,UserCommentBoToUserCommentMapper.class,UserCommentToUserCommentVoMapper.class,UserCommentToAppUserCommentRootVoMapper.class,UserCommentToAppUserCommentItemVoMapper.class},
    imports = {}
)
public interface UserCommentToAdminContentAuditUserCommentVoMapper extends BaseMapper<UserComment, AdminContentAuditUserCommentVo> {
}
