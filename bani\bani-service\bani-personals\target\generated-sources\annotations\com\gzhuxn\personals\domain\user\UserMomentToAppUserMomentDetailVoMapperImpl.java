package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.moment.AppUserMomentDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToAppUserMomentDetailVoMapperImpl implements UserMomentToAppUserMomentDetailVoMapper {

    @Override
    public AppUserMomentDetailVo convert(UserMoment source) {
        if ( source == null ) {
            return null;
        }

        AppUserMomentDetailVo appUserMomentDetailVo = new AppUserMomentDetailVo();

        appUserMomentDetailVo.setTime( source.getCreateTime() );
        appUserMomentDetailVo.setId( source.getId() );
        appUserMomentDetailVo.setContent( source.getContent() );
        appUserMomentDetailVo.setImages( source.getImages() );
        appUserMomentDetailVo.setLocation( source.getLocation() );
        appUserMomentDetailVo.setPv( source.getPv() );
        appUserMomentDetailVo.setLv( source.getLv() );
        appUserMomentDetailVo.setCv( source.getCv() );

        return appUserMomentDetailVo;
    }

    @Override
    public AppUserMomentDetailVo convert(UserMoment source, AppUserMomentDetailVo target) {
        if ( source == null ) {
            return target;
        }

        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setContent( source.getContent() );
        target.setImages( source.getImages() );
        target.setLocation( source.getLocation() );
        target.setPv( source.getPv() );
        target.setLv( source.getLv() );
        target.setCv( source.getCv() );

        return target;
    }
}
