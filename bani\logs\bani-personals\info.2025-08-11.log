2025-08-11 04:41:45 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] start to publish all metadata., dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteMessageService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=publishAll,publishMessage, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369152}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteLocationMapService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteLocationMapService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=geocoderLocation, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369746}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteContentAuditService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteContentAuditService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=checkIdentity,checkImage,checkText, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839370141}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteFileService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByIds,selectMapByIds,selectSmallUrlByIds,selectUrlByIds,upload, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368659}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDictService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDictService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectDictDataByType,selectLabelByType,selectLabelByTypes, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369344}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDeptService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDeptService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectDeptNameByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839367379}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteClientService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteClientService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=queryByClientId, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369946}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteLogService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteLogService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839370345}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteUserService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteUserService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,isUserLoggedOff,logoffUser,recordLoginInfo,registerUserInfo,selectById,selectEmailById,selectListByIds,selectMpOpenIdsByIds,selectNicknameById,selectNicknameByIds,selectPhoneNumberById,selectUserIdsByRoleIds,selectUserNameById,softDeleteUser,updateMpSubscribe,updateUserAvatar,updateUserInfo,updateUserStatus, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368740}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.resource.api.RemoteFileService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByIds,selectMapByIds,selectSmallUrlByIds,selectUrlByIds,upload, logger=slf4j, check=false, qos.enable=false, timeout=3000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, sticky=false, mock=true, validation=jvalidationNew, timestamp=1754839421893}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDataScopeService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDataScopeService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=getDeptAndChild,getRoleCustom, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839368940}, dubbo version: 3.2.14, current host: **********
2025-08-11 04:41:45 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='bani-personals', serviceInterface='com.gzhuxn.system.api.RemoteDistrictService', version='', group='', side='consumer'}; definition: {metadata-type=remote, application=bani-personals, interface=com.gzhuxn.system.api.RemoteDistrictService, side=consumer, release=3.2.14, dubbo=2.0.2, pid=33732, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=**********, methods=selectByCode,selectByNameAndParent,selectCodeByNameAndParent,selectNameByCode,selectNameMapByCodes, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1754839369548}, dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](bani-personals), dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [bani-system], dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [GRPC-UNSUBSCRIBE] service:bani-system, group:DUBBO_GROUP, cluster: 
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x61dabb14, L:/**********:64869 - R:/**********:20880], dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /**********:64869 -> /**********:20880 is disconnected., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](bani-personals) to null, dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](bani-personals) is stopping., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:09 [SpringApplicationShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.14, current host: **********
2025-08-11 07:57:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-11 07:57:22 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 26860 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-11 07:57:22 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:22 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-11 07:57:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 07:57:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 07:57:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 07:57:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@77bd9821
2025-08-11 07:57:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 07:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 07:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 07:57:39 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-11 07:57:39 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-11 07:57:40 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-11 07:57:41 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-11 07:57:41 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-11 07:57:47 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 07:57:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-11 07:57:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 07:57:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 07:57:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 07:57:51 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-11 07:57:55 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 36.274 seconds (process running for 41.636)
2025-08-11 07:57:57 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-11 07:57:57 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-11 07:58:10 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 08:23:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-08-11 08:23:24 [main] INFO  c.g.p.BaniPersonalsApplication - Starting BaniPersonalsApplication using Java 21.0.3 with PID 31284 (E:\bani\code\bani\bani-service\bani-personals\target\classes started by likavn in E:\bani\code\bani)
2025-08-11 08:23:24 [main] INFO  c.g.p.BaniPersonalsApplication - The following 1 profile is active: "dev"
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=bani-personals.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-08-11 08:23:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-11 08:23:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-11 08:23:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-11 08:23:33 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@c0d114b
2025-08-11 08:23:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-11 08:23:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-11 08:23:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-11 08:23:38 [main] INFO  c.g.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-08-11 08:23:38 [main] INFO  org.redisson.Version - Redisson 3.34.1
2025-08-11 08:23:39 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***************/***************:8762
2025-08-11 08:23:40 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***************/***************:8762
2025-08-11 08:23:40 [main] INFO  c.g.l.e.c.EventBusAutoConfiguration - Eventbus Initializing... redis
2025-08-11 08:23:47 [main] INFO  c.g.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-11 08:23:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.15.Final
2025-08-11 08:23:52 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-11 08:23:52 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-11 08:23:52 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-11 08:23:52 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP bani-personals **********:9211 register finished
2025-08-11 08:23:56 [main] INFO  c.g.p.BaniPersonalsApplication - Started BaniPersonalsApplication in 34.272 seconds (process running for 35.302)
2025-08-11 08:23:57 [main] INFO  c.g.l.e.c.base.MsgListenerContainer - Eventbus register listeners success
2025-08-11 08:23:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-08-11 08:23:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-08-11 08:23:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=bani-personals.yml, group=DEFAULT_GROUP
2025-08-11 08:23:58 [main] INFO  c.g.p.BaniPersonalsApplication - 交友服务模块启动成功...
2025-08-11 08:23:58 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-11 11:11:59 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 11:25:37 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 12:05:57 [eventbus-msg-pool-1] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
2025-08-11 23:10:53 [eventbus-msg-pool-2] INFO  c.g.p.event.follow.FollowConsumer - 关注事件消费者收到消息：FollowProducer.FollowEvent(type=FOLLOW, follow=UserFollow(type=1, businessId=1938886425951789058))
