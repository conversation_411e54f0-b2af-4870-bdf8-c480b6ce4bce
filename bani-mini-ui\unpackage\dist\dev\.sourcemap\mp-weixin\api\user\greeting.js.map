{"version": 3, "file": "greeting.js", "sources": ["api/user/greeting.js"], "sourcesContent": ["/**\r\n * 用户打招呼相关接口\r\n */\r\n\r\nimport request from '@/utils/request'\r\n\r\n/**\r\n * 发送打招呼\r\n * @param {Object} data - 打招呼数据\r\n * @param {number} data.oppositeUserId - 对方用户ID\r\n * @param {string} data.content - 打招呼内容\r\n * @returns {Promise} 返回发送结果\r\n */\r\nexport function sendUserGreeting(data) {\r\n\treturn request({\r\n\t\turl: '/personals/user/greeting/send',\r\n\t\tmethod: 'POST',\r\n\t\tdata: data\r\n\t})\r\n}\r\n\r\n/**\r\n * 查询收到的打招呼列表\r\n * @param {Object} params - 查询参数\r\n * @param {number} params.pageSize - 分页大小\r\n * @param {number} params.pageNum - 当前页数\r\n * @returns {Promise} 返回打招呼列表\r\n */\r\nexport function getReceivedGreetingPage(params = {}) {\r\n\treturn request({\r\n\t\turl: '/personals/user/greeting/received/page',\r\n\t\tmethod: 'GET',\r\n\t\tparams: {\r\n\t\t\tpageSize: params.pageSize || 10,\r\n\t\t\tpageNum: params.pageNum || 1\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 查询发送的打招呼列表\r\n * @param {Object} params - 查询参数\r\n * @param {number} params.pageSize - 分页大小\r\n * @param {number} params.pageNum - 当前页数\r\n * @returns {Promise} 返回打招呼列表\r\n */\r\nexport function getSentGreetingPage(params = {}) {\r\n\treturn request({\r\n\t\turl: '/personals/user/greeting/sent/page',\r\n\t\tmethod: 'GET',\r\n\t\tparams: {\r\n\t\t\tpageSize: params.pageSize || 10,\r\n\t\t\tpageNum: params.pageNum || 1\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 检查是否已经向对方打过招呼\r\n * @param {number} oppositeUserId - 对方用户ID\r\n * @returns {Promise} 返回检查结果\r\n */\r\nexport function checkUserGreeted(oppositeUserId) {\r\n\treturn request({\r\n\t\turl: '/personals/user/greeting/check',\r\n\t\tmethod: 'GET',\r\n\t\tparams: {\r\n\t\t\toppositeUserId\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 回复打招呼\r\n * @param {number} id - 打招呼记录ID\r\n * @param {string} content - 回复内容\r\n * @returns {Promise} 返回回复结果\r\n */\r\nexport function replyGreeting(id, content) {\r\n\treturn request({\r\n\t\turl: `/personals/user/greeting/reply/${id}`,\r\n\t\tmethod: 'POST',\r\n\t\tparams: {\r\n\t\t\tcontent\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 忽略打招呼\r\n * @param {number} id - 打招呼记录ID\r\n * @returns {Promise} 返回忽略结果\r\n */\r\nexport function ignoreGreeting(id) {\r\n\treturn request({\r\n\t\turl: `/personals/user/greeting/ignore/${id}`,\r\n\t\tmethod: 'POST'\r\n\t})\r\n}\r\n"], "names": ["getReceivedGreetingPage", "params", "request", "getSentGreetingPage", "replyGreeting", "id", "content", "ignoreGreeting"], "mappings": "uDA4BO,SAASA,EAAwBC,EAAS,GAAI,CACpD,OAAOC,UAAQ,CACd,IAAK,yCACL,OAAQ,MACR,OAAQ,CACP,SAAUD,EAAO,UAAY,GAC7B,QAASA,EAAO,SAAW,CAC3B,CACH,CAAE,CACF,CASO,SAASE,EAAoBF,EAAS,GAAI,CAChD,OAAOC,UAAQ,CACd,IAAK,qCACL,OAAQ,MACR,OAAQ,CACP,SAAUD,EAAO,UAAY,GAC7B,QAASA,EAAO,SAAW,CAC3B,CACH,CAAE,CACF,CAuBO,SAASG,EAAcC,EAAIC,EAAS,CAC1C,OAAOJ,UAAQ,CACd,IAAK,kCAAkCG,CAAE,GACzC,OAAQ,OACR,OAAQ,CACP,QAAAC,CACA,CACH,CAAE,CACF,CAOO,SAASC,EAAeF,EAAI,CAClC,OAAOH,UAAQ,CACd,IAAK,mCAAmCG,CAAE,GAC1C,OAAQ,MACV,CAAE,CACF"}