{"version": 3, "file": "greeting.js", "sources": ["api/user/greeting.js"], "sourcesContent": ["/**\n * 用户打招呼相关接口\n */\n\nimport request from '@/utils/request'\n\n/**\n * 发送打招呼\n * @param {Object} data - 打招呼数据\n * @param {number} data.oppositeUserId - 对方用户ID\n * @param {string} data.content - 打招呼内容\n * @returns {Promise} 返回发送结果\n */\nexport function sendUserGreeting(data) {\n\treturn request({\n\t\turl: '/personals/user/greeting/send',\n\t\tmethod: 'POST',\n\t\tdata: data\n\t})\n}\n\n/**\n * 查询收到的打招呼列表\n * @param {Object} params - 查询参数\n * @param {number} params.pageSize - 分页大小\n * @param {number} params.pageNum - 当前页数\n * @returns {Promise} 返回打招呼列表\n */\nexport function getReceivedGreetingPage(params = {}) {\n\treturn request({\n\t\turl: '/personals/user/greeting/received/page',\n\t\tmethod: 'GET',\n\t\tparams: {\n\t\t\tpageSize: params.pageSize || 10,\n\t\t\tpageNum: params.pageNum || 1\n\t\t}\n\t})\n}\n\n/**\n * 查询发送的打招呼列表\n * @param {Object} params - 查询参数\n * @param {number} params.pageSize - 分页大小\n * @param {number} params.pageNum - 当前页数\n * @returns {Promise} 返回打招呼列表\n */\nexport function getSentGreetingPage(params = {}) {\n\treturn request({\n\t\turl: '/personals/user/greeting/sent/page',\n\t\tmethod: 'GET',\n\t\tparams: {\n\t\t\tpageSize: params.pageSize || 10,\n\t\t\tpageNum: params.pageNum || 1\n\t\t}\n\t})\n}\n\n/**\n * 检查是否已经向对方打过招呼\n * @param {number} oppositeUserId - 对方用户ID\n * @returns {Promise} 返回检查结果\n */\nexport function checkUserGreeted(oppositeUserId) {\n\treturn request({\n\t\turl: '/personals/user/greeting/check',\n\t\tmethod: 'GET',\n\t\tparams: {\n\t\t\toppositeUserId\n\t\t}\n\t})\n}\n\n/**\n * 回复打招呼\n * @param {number} id - 打招呼记录ID\n * @param {string} content - 回复内容\n * @returns {Promise} 返回回复结果\n */\nexport function replyGreeting(id, content) {\n\treturn request({\n\t\turl: `/personals/user/greeting/reply/${id}`,\n\t\tmethod: 'POST',\n\t\tparams: {\n\t\t\tcontent\n\t\t}\n\t})\n}\n\n/**\n * 忽略打招呼\n * @param {number} id - 打招呼记录ID\n * @returns {Promise} 返回忽略结果\n */\nexport function ignoreGreeting(id) {\n\treturn request({\n\t\turl: `/personals/user/greeting/ignore/${id}`,\n\t\tmethod: 'POST'\n\t})\n}\n"], "names": ["sendUserGreeting", "data", "request", "checkUserGreeted", "oppositeUserId"], "mappings": "uDAaO,SAASA,EAAiBC,EAAM,CACtC,OAAOC,UAAQ,CACd,IAAK,gCACL,OAAQ,OACR,KAAMD,CACR,CAAE,CACF,CA2CO,SAASE,EAAiBC,EAAgB,CAChD,OAAOF,UAAQ,CACd,IAAK,iCACL,OAAQ,MACR,OAAQ,CACP,eAAAE,CACA,CACH,CAAE,CACF"}