package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageGiftVoToManageGiftMapper;
import com.gzhuxn.personals.domain.manage.bo.ManageGiftBoToManageGiftMapper;
import com.gzhuxn.personals.domain.manage.vo.ManageGiftVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageGiftBoToManageGiftMapper.class,AppManageGiftVoToManageGiftMapper.class,ManageGiftToAppManageGiftVoMapper.class,ManageGiftToAdminManageGiftVoMapper.class},
    imports = {}
)
public interface ManageGiftToManageGiftVoMapper extends BaseMapper<ManageGift, ManageGiftVo> {
}
