package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.recommend.vo.activity.AppBuddyActivityVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppBuddyActivityVoMapperImpl implements ActivityToAppBuddyActivityVoMapper {

    @Override
    public AppBuddyActivityVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppBuddyActivityVo appBuddyActivityVo = new AppBuddyActivityVo();

        appBuddyActivityVo.setId( arg0.getId() );
        appBuddyActivityVo.setName( arg0.getName() );
        appBuddyActivityVo.setClassify( arg0.getClassify() );
        appBuddyActivityVo.setAddress( arg0.getAddress() );
        appBuddyActivityVo.setStartTime( arg0.getStartTime() );
        appBuddyActivityVo.setAmount( arg0.getAmount() );
        appBuddyActivityVo.setStatus( arg0.getStatus() );
        if ( arg0.getBackgroundImage() != null ) {
            appBuddyActivityVo.setBackgroundImage( String.valueOf( arg0.getBackgroundImage() ) );
        }
        appBuddyActivityVo.setIntroduce( arg0.getIntroduce() );
        appBuddyActivityVo.setLimitNum( arg0.getLimitNum() );
        appBuddyActivityVo.setEnrollNum( arg0.getEnrollNum() );

        return appBuddyActivityVo;
    }

    @Override
    public AppBuddyActivityVo convert(Activity arg0, AppBuddyActivityVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setClassify( arg0.getClassify() );
        arg1.setAddress( arg0.getAddress() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg0.getBackgroundImage() != null ) {
            arg1.setBackgroundImage( String.valueOf( arg0.getBackgroundImage() ) );
        }
        else {
            arg1.setBackgroundImage( null );
        }
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setLimitNum( arg0.getLimitNum() );
        arg1.setEnrollNum( arg0.getEnrollNum() );

        return arg1;
    }
}
