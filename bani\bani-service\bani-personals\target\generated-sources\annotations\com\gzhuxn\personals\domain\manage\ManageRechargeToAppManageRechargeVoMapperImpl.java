package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageRechargeVo;
import java.math.BigDecimal;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageRechargeToAppManageRechargeVoMapperImpl implements ManageRechargeToAppManageRechargeVoMapper {

    @Override
    public AppManageRechargeVo convert(ManageRecharge arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppManageRechargeVo appManageRechargeVo = new AppManageRechargeVo();

        appManageRechargeVo.setId( arg0.getId() );
        appManageRechargeVo.setOriginalAmount( arg0.getOriginalAmount() );
        appManageRechargeVo.setAmount( arg0.getAmount() );
        if ( arg0.getDiscountRate() != null ) {
            appManageRechargeVo.setDiscountRate( BigDecimal.valueOf( arg0.getDiscountRate() ) );
        }
        appManageRechargeVo.setDiscountType( arg0.getDiscountType() );
        appManageRechargeVo.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        appManageRechargeVo.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        appManageRechargeVo.setWithdrawCoin( arg0.getWithdrawCoin() );
        appManageRechargeVo.setCoin( arg0.getCoin() );

        return appManageRechargeVo;
    }

    @Override
    public AppManageRechargeVo convert(ManageRecharge arg0, AppManageRechargeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        if ( arg0.getDiscountRate() != null ) {
            arg1.setDiscountRate( BigDecimal.valueOf( arg0.getDiscountRate() ) );
        }
        else {
            arg1.setDiscountRate( null );
        }
        arg1.setDiscountType( arg0.getDiscountType() );
        arg1.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        arg1.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
