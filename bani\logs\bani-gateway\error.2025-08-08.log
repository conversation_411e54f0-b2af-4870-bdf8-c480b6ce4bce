2025-08-08 19:53:08 [reactor-http-nio-6] ERROR c.gzhuxn.gateway.filter.AuthFilter - AuthFilter.error
org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@325677708 [redisClient=[addr=redis://101.200.242.239:8762], channel=[id: 0xc954e772, L:/192.168.0.108:53034 ! R:101.200.242.239/101.200.242.239:8762], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJtaW5pOjE5NTM0NzQ1ODA0NjMyMDY0MDIiLCJyblN0ciI6Ink1N05oSTVPeVFTZWxzTXV2bGVHY09NbUJpN0hua3ZqIiwiQ2xpZW50LUlkIjoiNTIwMDEiLCJ1c2VySWQiOjE5NTM0NzQ1ODA0NjMyMDY0MDIsInVzZXJOYW1lIjoi5b6u5L-h55So5oi3In0.A9LwLWVgy5At7KemAdjtBolw6fxV0nftB_2nyLhLBDY] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:364)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:194)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-08 19:54:12 [reactor-http-nio-6] ERROR c.g.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/app-api/auth/login,异常信息:Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1209431345 [redisClient=[addr=redis://101.200.242.239:8762], channel=[id: 0x40c43ba0, L:/192.168.0.108:49157 ! R:101.200.242.239/101.200.242.239:8762], currentCommand=null, usage=1], command: (GET), params: [Authorization:var:same-token] after 3 retry attempts
2025-08-08 19:55:15 [reactor-http-nio-7] ERROR c.gzhuxn.gateway.filter.AuthFilter - AuthFilter.error
org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@325677708 [redisClient=[addr=redis://101.200.242.239:8762], channel=[id: 0xc954e772, L:/192.168.0.108:53034 ! R:101.200.242.239/101.200.242.239:8762], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI5MlY3ZEkxNnVVVktxVWhQS2ZDTm1jZVZYZ2s0Y1dSTyIsIkNsaWVudC1JZCI6ImU1Y2Q3ZTQ4OTFiZjk1ZDFkMTkyMDZjZTI0YTdiMzJlIiwidGVuYW50SWQiOiIxIiwidXNlcklkIjoxLCJ1c2VyTmFtZSI6ImFkbWluIiwiZGVwdElkIjoxMDAsImRlcHROYW1lIjoi5Ly05L2g56eR5oqAIiwiZGVwdENhdGVnb3J5IjoiIn0.BSucDnMbmk9sAgRfgpb4l7YSdF4_oMGfthZ9_jvof1A] after 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:364)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:194)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-08-08 22:30:26 [reactor-http-nio-12] ERROR c.g.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
