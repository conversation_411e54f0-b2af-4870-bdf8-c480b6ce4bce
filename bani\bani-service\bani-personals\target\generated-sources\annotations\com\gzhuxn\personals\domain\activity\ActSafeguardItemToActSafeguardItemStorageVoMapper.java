package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.bo.ActSafeguardItemBoToActSafeguardItemMapper;
import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemStorageVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActSafeguardItemBoToActSafeguardItemMapper.class,ActSafeguardItemToActSafeguardItemVoMapper.class},
    imports = {}
)
public interface ActSafeguardItemToActSafeguardItemStorageVoMapper extends BaseMapper<ActSafeguardItem, ActSafeguardItemStorageVo> {
}
