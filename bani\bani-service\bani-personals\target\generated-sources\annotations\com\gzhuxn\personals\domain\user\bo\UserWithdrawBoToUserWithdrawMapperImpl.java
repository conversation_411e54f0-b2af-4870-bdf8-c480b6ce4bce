package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserWithdraw;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserWithdrawBoToUserWithdrawMapperImpl implements UserWithdrawBoToUserWithdrawMapper {

    @Override
    public UserWithdraw convert(UserWithdrawBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWithdraw userWithdraw = new UserWithdraw();

        userWithdraw.setSearchValue( arg0.getSearchValue() );
        userWithdraw.setCreateBy( arg0.getCreateBy() );
        userWithdraw.setCreateTime( arg0.getCreateTime() );
        userWithdraw.setUpdateBy( arg0.getUpdateBy() );
        userWithdraw.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userWithdraw.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userWithdraw.setCreateDept( arg0.getCreateDept() );
        userWithdraw.setId( arg0.getId() );
        userWithdraw.setUserId( arg0.getUserId() );
        userWithdraw.setAmount( arg0.getAmount() );
        userWithdraw.setWithdrawQrCodeImage( arg0.getWithdrawQrCodeImage() );
        userWithdraw.setAuditStatus( arg0.getAuditStatus() );
        userWithdraw.setAuditTime( arg0.getAuditTime() );
        userWithdraw.setAuditUserId( arg0.getAuditUserId() );
        userWithdraw.setRemitStatus( arg0.getRemitStatus() );
        userWithdraw.setRemitTime( arg0.getRemitTime() );

        return userWithdraw;
    }

    @Override
    public UserWithdraw convert(UserWithdrawBo arg0, UserWithdraw arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setWithdrawQrCodeImage( arg0.getWithdrawQrCodeImage() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setAuditTime( arg0.getAuditTime() );
        arg1.setAuditUserId( arg0.getAuditUserId() );
        arg1.setRemitStatus( arg0.getRemitStatus() );
        arg1.setRemitTime( arg0.getRemitTime() );

        return arg1;
    }
}
