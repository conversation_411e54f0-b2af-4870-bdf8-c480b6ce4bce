package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserActivityRecordBoToUserActivityRecordMapper;
import com.gzhuxn.personals.domain.user.vo.UserActivityRecordVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserActivityRecordBoToUserActivityRecordMapper.class},
    imports = {}
)
public interface UserActivityRecordToUserActivityRecordVoMapper extends BaseMapper<UserActivityRecord, UserActivityRecordVo> {
}
