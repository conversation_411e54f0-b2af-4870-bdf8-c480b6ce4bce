package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.blacklist.AppUserBlacklistVoToUserBlacklistMapper;
import com.gzhuxn.personals.domain.user.bo.UserBlacklistBoToUserBlacklistMapper;
import com.gzhuxn.personals.domain.user.vo.UserBlacklistVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserBlacklistBoToUserBlacklistMapper.class,AppUserBlacklistVoToUserBlacklistMapper.class,UserBlacklistToAppUserBlacklistVoMapper.class},
    imports = {}
)
public interface UserBlacklistToUserBlacklistVoMapper extends BaseMapper<UserBlacklist, UserBlacklistVo> {
}
