package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.wechatapply.AppUserWechatApplyAuditBoToUserWechatApplyMapper;
import com.gzhuxn.personals.controller.app.user.bo.wechatapply.AppUserWechatApplyCreateBoToUserWechatApplyMapper;
import com.gzhuxn.personals.domain.user.bo.UserWechatApplyBoToUserWechatApplyMapper;
import com.gzhuxn.personals.domain.user.vo.UserWechatApplyVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserWechatApplyBoToUserWechatApplyMapper.class,AppUserWechatApplyAuditBoToUserWechatApplyMapper.class,AppUserWechatApplyCreateBoToUserWechatApplyMapper.class,UserWechatApplyToAppUserWechatApplyVoMapper.class},
    imports = {}
)
public interface UserWechatApplyToUserWechatApplyVoMapper extends BaseMapper<UserWechatApply, UserWechatApplyVo> {
}
