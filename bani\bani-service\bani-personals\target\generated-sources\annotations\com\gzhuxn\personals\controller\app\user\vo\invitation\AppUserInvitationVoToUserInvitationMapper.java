package com.gzhuxn.personals.controller.app.user.vo.invitation;

import com.gzhuxn.personals.domain.user.UserInvitation;
import com.gzhuxn.personals.domain.user.UserInvitationToAppUserInvitationVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserInvitationToAppUserInvitationVoMapper.class},
    imports = {}
)
public interface AppUserInvitationVoToUserInvitationMapper extends BaseMapper<AppUserInvitationVo, UserInvitation> {
}
