package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserAuthPayRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAuthPayRecordBoToUserAuthPayRecordMapperImpl implements UserAuthPayRecordBoToUserAuthPayRecordMapper {

    @Override
    public UserAuthPayRecord convert(UserAuthPayRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAuthPayRecord userAuthPayRecord = new UserAuthPayRecord();

        userAuthPayRecord.setSearchValue( arg0.getSearchValue() );
        userAuthPayRecord.setCreateBy( arg0.getCreateBy() );
        userAuthPayRecord.setCreateTime( arg0.getCreateTime() );
        userAuthPayRecord.setUpdateBy( arg0.getUpdateBy() );
        userAuthPayRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userAuthPayRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userAuthPayRecord.setCreateDept( arg0.getCreateDept() );
        userAuthPayRecord.setId( arg0.getId() );
        userAuthPayRecord.setUserId( arg0.getUserId() );
        userAuthPayRecord.setOrderId( arg0.getOrderId() );
        userAuthPayRecord.setOriginalAmount( arg0.getOriginalAmount() );
        userAuthPayRecord.setAmount( arg0.getAmount() );
        userAuthPayRecord.setCoin( arg0.getCoin() );
        userAuthPayRecord.setPayStatus( arg0.getPayStatus() );
        userAuthPayRecord.setAuthApplyId( arg0.getAuthApplyId() );
        userAuthPayRecord.setAuthType( arg0.getAuthType() );
        userAuthPayRecord.setUseStatus( arg0.getUseStatus() );

        return userAuthPayRecord;
    }

    @Override
    public UserAuthPayRecord convert(UserAuthPayRecordBo arg0, UserAuthPayRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayStatus( arg0.getPayStatus() );
        arg1.setAuthApplyId( arg0.getAuthApplyId() );
        arg1.setAuthType( arg0.getAuthType() );
        arg1.setUseStatus( arg0.getUseStatus() );

        return arg1;
    }
}
