<template>
	<scroll-nav-page :title="pageTitle" :show-back="true">
		<template #content>
			<!-- 内容区域 -->
			<view class="content-wrapper">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
			</view>

			<!-- 内容显示 -->
			<view v-else class="up-content">
				<rich-text :nodes="content"></rich-text>
			</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { toast } from '@/utils/common'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import { getAgreementByKey } from '@/api/content/agreement'

// 导航栏高度
const navBarHeight = ref(0)
// 页面标题
const pageTitle = ref('')
// 页面内容
const content = ref('')
// 加载状态
const loading = ref(true)
// 加载文字
const loadingText = ref({
	contentdown: '正在加载协议内容...',
	contentrefresh: '正在加载协议内容...',
	contentnomore: '加载完成'
})

// 页面加载生命周期
onLoad((option) => {
	// 根据传入的 key 参数确定页面标题和内容
	if (!option.key) {
		toast('不存在的页面Key')
		uni.navigateBack({
			delta: 1
		})
		return
	}
	pageTitle.value = ''
	loading.value = true

	// 获取协议内容
	getAgreementByKey(option.key).then(res => {
		if (res && res.data && res.data.content) {
			content.value = res.data.content
			pageTitle.value = res.data.name
		} else {
			content.value = getDefaultContent(option.key)
		}
		loading.value = false
	}).catch(err => {
		// 显示默认内容
		content.value = getDefaultContent(option.key)
		loading.value = false
	})
})

// 获取默认内容（当接口失败时）
const getDefaultContent = (key) => {
	const defaultContents = {
		userAgreement: `
			<h2>用户协议</h2>
			<p>欢迎使用伴你有约！</p>
			<p>本协议是您与伴你有约之间关于使用伴你有约服务的法律协议。</p>
			<h3>1. 服务条款</h3>
			<p>用户在使用本服务时，应当遵守相关法律法规...</p>
		`,
		privacyAgreement: `
			<h2>隐私政策</h2>
			<p>我们非常重视您的隐私保护。</p>
			<p>本隐私政策说明了我们如何收集、使用和保护您的个人信息。</p>
			<h3>1. 信息收集</h3>
			<p>我们可能收集以下类型的信息...</p>
		`,
		serviceAgreement: `
			<h2>服务协议</h2>
			<p>本服务协议规定了服务的具体条款和条件。</p>
		`,
		communityRules: `
			<h2>社区规范</h2>
			<p>为了维护良好的社区环境，请遵守以下规范。</p>
		`
	}

	return defaultContents[key] || '<p>内容加载中...</p>'
}

</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			rgba(105, 108, 243, 0.08) 0%,
			rgba(105, 108, 243, 0.05) 30%,
			rgba(105, 108, 243, 0.02) 60%,
			rgba(255, 255, 255, 1) 100%);
}

.content-wrapper {
	margin-top: 20rpx;
	padding: 20rpx;
	box-sizing: border-box;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 400rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
}

.up-content {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	font-size: 30rpx;
	line-height: 1.8;
	color: #333;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1px solid rgba(255, 255, 255, 0.2);

	// 富文本内容样式优化
	:deep(p) {
		margin-bottom: 24rpx;
		text-align: justify;
	}

	:deep(h1),
	:deep(h2),
	:deep(h3) {
		color: $primary-color;
		font-weight: 600;
		margin: 40rpx 0 20rpx 0;
	}

	:deep(h1) {
		font-size: 36rpx;
	}

	:deep(h2) {
		font-size: 34rpx;
	}

	:deep(h3) {
		font-size: 32rpx;
	}

	:deep(ul),
	:deep(ol) {
		padding-left: 40rpx;
		margin-bottom: 24rpx;
	}

	:deep(li) {
		margin-bottom: 12rpx;
		line-height: 1.6;
	}

	:deep(strong) {
		color: $primary-color;
		font-weight: 600;
	}

	:deep(a) {
		color: $primary-color;
		text-decoration: underline;
	}
}

// 响应式设计
@media screen and (max-width: 750rpx) {
	.up-content {
		padding: 30rpx 24rpx;
		font-size: 28rpx;

		:deep(h1) {
			font-size: 34rpx;
		}

		:deep(h2) {
			font-size: 32rpx;
		}

		:deep(h3) {
			font-size: 30rpx;
		}
	}
}
</style>
