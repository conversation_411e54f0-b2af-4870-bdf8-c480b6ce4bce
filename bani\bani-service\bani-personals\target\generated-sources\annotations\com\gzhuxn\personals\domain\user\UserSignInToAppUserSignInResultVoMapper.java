package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.signin.AppUserSignInResultVo;
import com.gzhuxn.personals.domain.user.bo.UserSignInBoToUserSignInMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserSignInBoToUserSignInMapper.class,UserSignInToUserSignInVoMapper.class},
    imports = {}
)
public interface UserSignInToAppUserSignInResultVoMapper extends BaseMapper<UserSignIn, AppUserSignInResultVo> {
}
