"use strict";const e=require("../../common/vendor.js"),U=require("../../config.js"),C=require("../../store/index.js"),c=require("../../api/recommend/recommend.js"),M=require("../../utils/common.js");if(!Array){const m=e.resolveComponent("custom-refresher"),d=e.resolveComponent("uni-icons"),s=e.resolveComponent("z-paging"),f=e.resolveComponent("scroll-nav-page"),u=e.resolveComponent("mp-subscribe");(m+d+s+f+u)()}const H=()=>"../../components/custom-refresher/custom-refresher.js",V=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",F=()=>"../../uni_modules/z-paging/components/z-paging/z-paging.js",R=()=>"../../components/scroll-nav-page/scroll-nav-page.js",I=()=>"../../components/mp-subscribe/mp-subscribe.js";Math||(H+V+B+F+R+I)();const B=()=>"./components/empty-state.js",N={__name:"personals",setup(m){var y,x;const d=e.ref(C.$store.isUserShort()?[{label:"附近",value:"nearby"}]:[{label:"同城",value:"local"},{label:"附近",value:"nearby"}]),s=e.ref(C.$store.isUserShort()?"nearby":"local"),f=e.ref(0),u=e.ref(0),p=e.ref([]),v=e.ref({}),g=e.ref({ageMin:null,ageMax:null,heightMin:null,heightMax:null,education:null,location:null}),_=((x=(y=U.globalConfig)==null?void 0:y.theme)==null?void 0:x.primaryColor)||"#696CF3",T=e.computed(()=>p.value.filter((o,r)=>r%2===0)),z=e.computed(()=>p.value.filter((o,r)=>r%2===1)),t=e.ref(null),w=o=>{o!==s.value&&(s.value=o,t.value&&t.value.reload())},P=o=>{f.value=o.scrollTop||0},$=o=>{u.value=o},j=()=>{e.index.navigateTo({url:"/pages/club/club"})},A=()=>{e.index.navigateTo({url:"/pages/activity/activity"})},k=()=>{e.index.navigateTo({url:"/pages/dating/dating"})},D=()=>{e.index.navigateTo({url:"/pages/moment/moment"})},L=()=>{const o=r=>{b(r),e.index.$off("filterApplied",o)};e.index.$on("filterApplied",o),e.index.navigateTo({url:"/pagesubs/personals/filter",animationType:"slide-in-right",events:{filterApplied:r=>{b(r),e.index.$off("filterApplied",o)}}})},b=o=>{g.value={...g.value,...o},t.value&&t.value.reload(),e.index.showToast({title:"筛选条件已应用",icon:"success"})},S=(o,r)=>{if(s.value==="local"){const a=c.buildSameCityParams(o,r,g.value);c.getSameCityUsers(a).then(n=>{const l=n.rows.map(i=>c.transformUserData(i));t.value.complete(l)})}else s.value==="nearby"&&M.getLocation().then(a=>{v.value=a;const n=c.buildNearbyParams(o,r,v.value.longitude,v.value.latitude,g.value);c.getNearbyUsers(n).then(l=>{const i=l.rows.map(E=>c.transformUserData(E));t.value.complete(i)})})},h=o=>{e.index.navigateTo({url:`/pagesubs/personals/profile?userId=${o.id}`})},q=()=>{t.value&&t.value.reload()};return e.onShow(()=>{e.index.__f__("log","at pages/personals/personals.vue:347","推荐页面显示，重新加载数据"),q()}),e.onUnmounted(()=>{e.index.$off("filterApplied")}),(o,r)=>({a:u.value+"px",b:e.w(({refresherStatus:a},n,l)=>({a:"b1e09953-2-"+l+",b1e09953-1",b:e.p({"refresher-status":a}),c:l,d:n}),{name:"refresher",path:"b",vueId:"b1e09953-1,b1e09953-0"}),c:e.p({type:"home",size:"32",color:"#fff"}),d:e.o(j),e:e.p({type:"search",size:"32",color:"#fff"}),f:e.o(A),g:e.p({type:"heart",size:"32",color:"#fff"}),h:e.o(k),i:e.p({type:"chat",size:"32",color:"#fff"}),j:e.o(D),k:e.f(d.value,(a,n,l)=>({a:e.t(a.label),b:s.value===a.value?e.unref(_):"#666",c:a.value,d:s.value===a.value?1:"",e:e.o(i=>w(a.value),a.value)})),l:e.p({type:"tune",size:"20",color:e.unref(_)}),m:e.o(L),n:e.f(T.value,(a,n,l)=>e.e({a:a.avatar,b:a.imageHeight+"rpx",c:e.t(a.distance),d:a.distance&&s.value==="nearby",e:e.t(a.nickname),f:e.n(a.gender==="male"?"bani-xingbie-nan":"bani-xingbie-nv"),g:a.gender==="male"?"#4A90E2":"#E91E63",h:a.isVerified},a.isVerified?{}:{},{i:e.t(a.currentCity),j:e.t(a.hometown),k:e.t(a.age),l:e.t(a.height),m:e.t(a.occupation),n:a.uid||n,o:e.o(i=>h(a),a.uid||n)})),o:e.f(z.value,(a,n,l)=>e.e({a:a.avatar,b:a.imageHeight+"rpx",c:e.t(a.distance),d:a.distance&&s.value==="nearby",e:e.t(a.nickname),f:e.n(a.gender==="male"?"bani-xingbie-nan":"bani-xingbie-nv"),g:a.gender==="male"?"#4A90E2":"#E91E63",h:a.isVerified},a.isVerified?{}:{},{i:e.t(a.currentCity),j:e.t(a.age),k:e.t(a.height),l:e.t(a.occupation),m:a.id||n,n:e.o(i=>h(a),a.id||n)})),p:e.p({"current-tab":s.value}),q:e.sr(t,"b1e09953-1,b1e09953-0",{k:"pagePaging"}),r:e.o((a,n)=>S(a,n)),s:e.o(a=>p.value=a),t:e.p({"auto-clean-list-when-reload":!1,"auto-scroll-to-top-when-reload":!1,auto:!0,"default-page-size":10,modelValue:p.value}),v:e.o(P),w:e.o($),x:e.p({enableScrollGradient:!1}),y:e.p({navBarHeight:u.value})})}},G=e._export_sfc(N,[["__scopeId","data-v-b1e09953"]]);wx.createPage(G);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/personals/personals.js.map
