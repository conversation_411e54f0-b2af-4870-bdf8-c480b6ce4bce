package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.domain.message.MsgContent;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface MsgContentBoToMsgContentMapper extends BaseMapper<MsgContentBo, MsgContent> {
}
