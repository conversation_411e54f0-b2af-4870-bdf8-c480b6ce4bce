package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.domain.message.MsgContentUser;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgContentUserBoToMsgContentUserMapperImpl implements MsgContentUserBoToMsgContentUserMapper {

    @Override
    public MsgContentUser convert(MsgContentUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContentUser msgContentUser = new MsgContentUser();

        msgContentUser.setSearchValue( arg0.getSearchValue() );
        msgContentUser.setCreateBy( arg0.getCreateBy() );
        msgContentUser.setCreateTime( arg0.getCreateTime() );
        msgContentUser.setUpdateBy( arg0.getUpdateBy() );
        msgContentUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            msgContentUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        msgContentUser.setCreateDept( arg0.getCreateDept() );
        msgContentUser.setUserId( arg0.getUserId() );
        msgContentUser.setId( arg0.getId() );
        msgContentUser.setGroupId( arg0.getGroupId() );
        msgContentUser.setContentId( arg0.getContentId() );
        msgContentUser.setSendUserId( arg0.getSendUserId() );
        msgContentUser.setType( arg0.getType() );
        msgContentUser.setSubType( arg0.getSubType() );

        return msgContentUser;
    }

    @Override
    public MsgContentUser convert(MsgContentUserBo arg0, MsgContentUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setContentId( arg0.getContentId() );
        arg1.setSendUserId( arg0.getSendUserId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );

        return arg1;
    }
}
