package com.gzhuxn.personals.controller.app.order.bo;

import com.gzhuxn.personals.domain.order.UserOrder;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppCreateOrderBoToUserOrderMapper extends BaseMapper<AppCreateOrderBo, UserOrder> {
}
