{"version": 3, "file": "scroll-nav-page.js", "sources": ["components/scroll-nav-page/scroll-nav-page.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovYmFuaS9jb2RlL2JhbmktbWluaS11aS9jb21wb25lbnRzL3Njcm9sbC1uYXYtcGFnZS9zY3JvbGwtbmF2LXBhZ2UudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"scroll-nav-page\">\r\n\t\t<!-- 滚动渐变导航栏 -->\r\n\t\t<view class=\"scroll-nav-bar\" :style=\"{ backgroundColor: currentBgColor, color: currentTextColor }\">\r\n\t\t\t<!-- 状态栏占位 -->\r\n\t\t\t<view v-if=\"statusBar\" class=\"status-bar\"\r\n\t\t\t\t:style=\"{ height: statusBarHeight + 'px', backgroundColor: currentBgColor }\"></view>\r\n\r\n\t\t\t<!-- 导航栏内容 -->\r\n\t\t\t<view class=\"nav-content\" :style=\"{ height: navHeight + 'px', backgroundColor: currentBgColor }\">\r\n\t\t\t\t<!-- 左侧区域 -->\r\n\t\t\t\t<view class=\"nav-left\">\r\n\t\t\t\t\t<!-- 返回按钮 -->\r\n\t\t\t\t\t<view v-if=\"showBack\" class=\"back-button\" @click=\"handleBack\">\r\n\t\t\t\t\t\t<uni-icons :type=\"currentBackIcon\" size=\"20\" :color=\"currentTextColor\" />\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 左侧插槽内容 -->\r\n\t\t\t\t\t<view class=\"left-slot\" @click=\"handleClickLeft\">\r\n\t\t\t\t\t\t<slot name=\"nav-left\">\r\n\t\t\t\t\t\t\t<!-- 默认左侧内容 -->\r\n\t\t\t\t\t\t\t<view v-if=\"leftIcon || leftText\" class=\"default-left\">\r\n\t\t\t\t\t\t\t\t<uni-icons v-if=\"leftIcon\" :type=\"leftIcon\" size=\"20\" :color=\"currentTextColor\" />\r\n\t\t\t\t\t\t\t\t<text v-if=\"leftText\" class=\"left-text\" :style=\"{ color: currentTextColor }\">{{ leftText\r\n\t\t\t\t\t\t\t\t\t}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</slot>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 中间区域 -->\r\n\t\t\t\t<view class=\"nav-center\">\r\n\t\t\t\t\t<slot name=\"nav-center\">\r\n\t\t\t\t\t\t<!-- 默认中间内容 -->\r\n\t\t\t\t\t\t<text v-if=\"title\" class=\"nav-title\" :style=\"{ color: currentTextColor }\">{{ title }}</text>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 右侧区域 -->\r\n\t\t\t\t<view class=\"nav-right\" @click=\"handleClickRight\">\r\n\t\t\t\t\t<slot name=\"nav-right\">\r\n\t\t\t\t\t\t<!-- 默认右侧内容 -->\r\n\t\t\t\t\t\t<view v-if=\"rightIcon || rightText\" class=\"default-right\">\r\n\t\t\t\t\t\t\t<text v-if=\"rightText\" class=\"right-text\" :style=\"{ color: currentTextColor }\">{{ rightText\r\n\t\t\t\t\t\t\t\t}}</text>\r\n\t\t\t\t\t\t\t<uni-icons v-if=\"rightIcon\" :type=\"rightIcon\" size=\"20\" :color=\"currentTextColor\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 边框 -->\r\n\t\t\t<view v-if=\"border\" class=\"nav-border\"></view>\r\n\r\n\t\t\t<!-- 阴影 -->\r\n\t\t\t<view v-if=\"shadow\" class=\"nav-shadow\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 内容区域插槽 -->\r\n\t\t<view class=\"content-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t<slot name=\"content\">\r\n\t\t\t\t<!-- 默认内容区域 -->\r\n\t\t\t\t<view class=\"default-content\">\r\n\t\t\t\t\t<text>请在content插槽中添加页面内容</text>\r\n\t\t\t\t</view>\r\n\t\t\t</slot>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, watch } from 'vue'\r\nimport { onPageScroll } from '@dcloudio/uni-app'\r\nimport globalConfig from '@/config'\r\n\r\n// 定义 props\r\nconst props = defineProps({\r\n\t// 页面标题\r\n\ttitle: {\r\n\t\ttype: String,\r\n\t\tdefault: ''\r\n\t},\r\n\t// 是否显示返回按钮\r\n\tshowBack: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: false\r\n\t},\r\n\t// 左侧图标\r\n\tleftIcon: {\r\n\t\ttype: String,\r\n\t\tdefault: ''\r\n\t},\r\n\t// 左侧文字\r\n\tleftText: {\r\n\t\ttype: String,\r\n\t\tdefault: ''\r\n\t},\r\n\t// 右侧图标\r\n\trightIcon: {\r\n\t\ttype: String,\r\n\t\tdefault: ''\r\n\t},\r\n\t// 右侧文字\r\n\trightText: {\r\n\t\ttype: String,\r\n\t\tdefault: ''\r\n\t},\r\n\t// 是否显示边框\r\n\tborder: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: false\r\n\t},\r\n\t// 是否固定\r\n\tfixed: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: true\r\n\t},\r\n\t// 是否包含状态栏\r\n\tstatusBar: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: true\r\n\t},\r\n\t// 是否显示阴影\r\n\tshadow: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: false\r\n\t},\r\n\t// 导航栏高度\r\n\theight: {\r\n\t\ttype: [String, Number],\r\n\t\tdefault: 44\r\n\t},\r\n\t// 初始背景色（主题色）\r\n\tinitialBgColor: {\r\n\t\ttype: String,\r\n\t\tdefault: globalConfig?.theme?.primaryColor || '#696CF3'\r\n\t},\r\n\t// 滚动后的背景色（白色）\r\n\tscrolledBgColor: {\r\n\t\ttype: String,\r\n\t\tdefault: '#ffffff'\r\n\t},\r\n\t// 初始文字颜色\r\n\tinitialTextColor: {\r\n\t\ttype: String,\r\n\t\tdefault: '#ffffff'\r\n\t},\r\n\t// 滚动后的文字颜色\r\n\tscrolledTextColor: {\r\n\t\ttype: String,\r\n\t\tdefault: '#333333'\r\n\t},\r\n\t// 滚动距离阈值\r\n\tscrollThreshold: {\r\n\t\ttype: Number,\r\n\t\tdefault: 100\r\n\t},\r\n\t// 渐变分割点\r\n\tfadeTransitionPoint: {\r\n\t\ttype: Number,\r\n\t\tdefault: 0.6,\r\n\t\tvalidator: (value) => value >= 0 && value <= 1\r\n\t},\r\n\t// 是否启用滚动渐变\r\n\tenableScrollGradient: {\r\n\t\ttype: Boolean,\r\n\t\tdefault: true\r\n\t}\r\n})\r\n\r\n// 定义 emits\r\nconst emit = defineEmits(['clickLeft', 'clickRight', 'back', 'scroll', 'heightChange'])\r\n\r\n// 页面状态\r\nconst pageScrollTop = ref(0)\r\nconst navBarHeight = ref(0)\r\n\r\n// 当前背景色和文字颜色\r\nconst currentBgColor = ref(props.initialBgColor)\r\nconst currentTextColor = ref(props.initialTextColor)\r\n\r\n// 状态栏高度\r\nconst statusBarHeight = ref(0)\r\n\r\n// 当前返回按钮图标\r\nconst currentBackIcon = computed(() => {\r\n\tif (!props.showBack) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// 检查是否有上一页\r\n\tconst pages = getCurrentPages()\r\n\tif (pages.length <= 1) {\r\n\t\t// 没有上一页，显示首页图标\r\n\t\treturn 'home'\r\n\t} else {\r\n\t\t// 有上一页，显示返回图标\r\n\t\treturn 'left'\r\n\t}\r\n})\r\n\r\n// 导航栏高度\r\nconst navHeight = computed(() => {\r\n\treturn typeof props.height === 'number' ? props.height : parseInt(props.height)\r\n})\r\n\r\n// 总高度（状态栏 + 导航栏）\r\nconst totalHeight = computed(() => {\r\n\treturn statusBarHeight.value + navHeight.value\r\n})\r\n\r\n// 更新导航栏颜色\r\nconst updateNavColors = (scrollTop) => {\r\n\tif (!props.enableScrollGradient) return\r\n\r\n\t// 根据滚动距离计算透明度，实现渐变效果\r\n\tconst opacity = Math.min(scrollTop / props.scrollThreshold, 1)\r\n\r\n\t// 优化的渐变过程：先透明再变白\r\n\tif (opacity === 0) {\r\n\t\t// 完全透明状态\r\n\t\tcurrentBgColor.value = props.initialBgColor\r\n\t\tcurrentTextColor.value = props.initialTextColor\r\n\t} else if (opacity === 1) {\r\n\t\t// 完全不透明状态\r\n\t\tcurrentBgColor.value = props.scrolledBgColor\r\n\t\tcurrentTextColor.value = props.scrolledTextColor\r\n\t} else {\r\n\t\t// 渐变过程：分为两个阶段\r\n\t\tconst transitionPoint = props.fadeTransitionPoint\r\n\r\n\t\tif (opacity <= transitionPoint) {\r\n\t\t\t// 第一阶段：从原色逐渐变透明\r\n\t\t\tconst fadeProgress = opacity / transitionPoint // 0 到 1\r\n\t\t\tconst fadeOpacity = 1 - fadeProgress // 从1变到0（完全透明）\r\n\t\t\tconst initialColor = hexToRgb(props.initialBgColor)\r\n\r\n\t\t\tif (initialColor) {\r\n\t\t\t\tcurrentBgColor.value = `rgba(${initialColor.r}, ${initialColor.g}, ${initialColor.b}, ${fadeOpacity})`\r\n\t\t\t\tcurrentTextColor.value = props.initialTextColor\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// 第二阶段：从透明的白色变成不透明的白色\r\n\t\t\tconst whiteProgress = (opacity - transitionPoint) / (1 - transitionPoint) // 0 到 1\r\n\t\t\tconst scrolledColor = hexToRgb(props.scrolledBgColor)\r\n\r\n\t\t\tif (scrolledColor) {\r\n\t\t\t\t// 背景色：白色从透明变到不透明\r\n\t\t\t\tcurrentBgColor.value = `rgba(${scrolledColor.r}, ${scrolledColor.g}, ${scrolledColor.b}, ${whiteProgress})`\r\n\r\n\t\t\t\t// 文字颜色切换：在第二阶段的30%处切换\r\n\t\t\t\tconst textSwitchPoint = transitionPoint + (1 - transitionPoint) * 0.3\r\n\t\t\t\tcurrentTextColor.value = opacity > textSwitchPoint ? props.scrolledTextColor : props.initialTextColor\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 将十六进制颜色转换为 RGB\r\nconst hexToRgb = (hex) => {\r\n\t// 移除 # 符号\r\n\thex = hex.replace('#', '')\r\n\r\n\t// 如果是3位十六进制，转换为6位\r\n\tif (hex.length === 3) {\r\n\t\thex = hex.split('').map(char => char + char).join('')\r\n\t}\r\n\r\n\tconst result = /^([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\r\n\treturn result ? {\r\n\t\tr: parseInt(result[1], 16),\r\n\t\tg: parseInt(result[2], 16),\r\n\t\tb: parseInt(result[3], 16)\r\n\t} : null\r\n}\r\n\r\n// 页面滚动处理函数\r\nconst handlePageScroll = (e) => {\r\n\t// uni-app的onPageScroll事件对象结构是 { scrollTop: number }\r\n\tconst scrollTop = e.scrollTop || 0\r\n\tpageScrollTop.value = scrollTop\r\n\tupdateNavColors(scrollTop)\r\n\temit('scroll', { scrollTop })\r\n}\r\n\r\n// 页面滚动监听\r\nonPageScroll(handlePageScroll)\r\n\r\n// 获取状态栏高度\r\nconst getStatusBarHeight = () => {\r\n\ttry {\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\tconst systemInfo = uni.getWindowInfo()\r\n\t\tstatusBarHeight.value = systemInfo.statusBarHeight || 0\r\n\t\t// #endif\r\n\t\t// #ifndef MP-WEIXIN\r\n\t\tconst sysInfo = uni.getSystemInfoSync()\r\n\t\tstatusBarHeight.value = sysInfo.statusBarHeight || 0\r\n\t\t// #endif\r\n\t} catch (e) {\r\n\t\tstatusBarHeight.value = 0\r\n\t}\r\n}\r\n\r\n// 事件处理\r\nconst handleClickLeft = () => {\r\n\temit('clickLeft')\r\n}\r\n\r\nconst handleClickRight = () => {\r\n\temit('clickRight')\r\n}\r\n\r\nconst handleBack = () => {\r\n\temit('back')\r\n\r\n\t// 检查是否有上一页\r\n\tconst pages = getCurrentPages()\r\n\tif (pages.length <= 1) {\r\n\t\t// 没有上一页，跳转到首页\r\n\t\tuni.reLaunch({\r\n\t\t\turl: globalConfig.homePagePath\r\n\t\t})\r\n\t} else {\r\n\t\t// 有上一页，正常返回\r\n\t\tuni.navigateBack()\r\n\t}\r\n}\r\n\r\n// 监听总高度变化\r\nwatch(totalHeight, (newHeight) => {\r\n\tnavBarHeight.value = newHeight\r\n\temit('heightChange', newHeight)\r\n}, { immediate: true })\r\n\r\n// 初始化\r\nonMounted(() => {\r\n\tgetStatusBarHeight()\r\n\tupdateNavColors(0)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 引入uni.scss变量\r\n@import '@/uni.scss';\r\n\r\n.scroll-nav-page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f9fa;\r\n}\r\n\r\n.scroll-nav-bar {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tz-index: 999;\r\n\twidth: 100%;\r\n\t/* 移除过渡动画，让渐变效果更流畅 */\r\n}\r\n\r\n.status-bar {\r\n\twidth: 100%;\r\n}\r\n\r\n.nav-content {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 0 16rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.nav-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-start;\r\n\tmin-width: 120rpx;\r\n\theight: 100%;\r\n\r\n\t.back-button {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t\tborder-radius: 50%;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tcursor: pointer;\r\n\r\n\t\t&:hover {\r\n\t\t\tbackground: rgba(0, 0, 0, 0.05);\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t\tbackground: rgba(0, 0, 0, 0.1);\r\n\t\t}\r\n\t}\r\n\r\n\t.left-slot {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n}\r\n\r\n.nav-center {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight: 100%;\r\n\tpadding: 0 20rpx;\r\n}\r\n\r\n.nav-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: flex-end;\r\n\tmin-width: 120rpx;\r\n\theight: 100%;\r\n}\r\n\r\n.default-left,\r\n.default-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.nav-title {\r\n\tfont-size: $title-size-md;\r\n\tfont-weight: 600;\r\n\ttext-align: center;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tmax-width: 100%;\r\n}\r\n\r\n.left-text,\r\n.right-text {\r\n\tfont-size: $font-size-md;\r\n}\r\n\r\n.nav-border {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 1rpx;\r\n\tbackground-color: rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.nav-shadow {\r\n\tposition: absolute;\r\n\tbottom: -10rpx;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 10rpx;\r\n\tbackground: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), transparent);\r\n}\r\n\r\n.content-container {\r\n\tmin-height: calc(100vh - 88rpx);\r\n}\r\n\r\n.default-content {\r\n\tpadding: 40rpx;\r\n\ttext-align: center;\r\n\tcolor: #999;\r\n\tfont-size: $font-size-md;\r\n}\r\n</style>\r\n", "import Component from 'E:/bani/code/bani-mini-ui/components/scroll-nav-page/scroll-nav-page.vue'\nwx.createComponent(Component)"], "names": ["props", "__props", "emit", "__emit", "pageScrollTop", "ref", "navBarHeight", "currentBgColor", "currentTextColor", "statusBarHeight", "currentBackIcon", "computed", "navHeight", "totalHeight", "updateNavColors", "scrollTop", "opacity", "transitionPoint", "fadeOpacity", "initialColor", "hexToRgb", "whiteProgress", "scrolledColor", "textSwitchPoint", "hex", "char", "result", "handlePageScroll", "e", "onPageScroll", "getStatusBarHeight", "systemInfo", "uni", "handleClickLeft", "handleClickRight", "handleBack", "globalConfig", "watch", "newHeight", "onMounted", "Component"], "mappings": "6nCA4EA,MAAMA,EAAQC,EA+FRC,EAAOC,EAGPC,EAAgBC,EAAG,IAAC,CAAC,EACrBC,EAAeD,EAAG,IAAC,CAAC,EAGpBE,EAAiBF,EAAAA,IAAIL,EAAM,cAAc,EACzCQ,EAAmBH,EAAAA,IAAIL,EAAM,gBAAgB,EAG7CS,EAAkBJ,EAAG,IAAC,CAAC,EAGvBK,EAAkBC,EAAQ,SAAC,IAC3BX,EAAM,SAKG,gBAAiB,EACrB,QAAU,EAEZ,OAGA,OAVP,MAYD,EAGKY,EAAYD,EAAQ,SAAC,IACnB,OAAOX,EAAM,QAAW,SAAWA,EAAM,OAAS,SAASA,EAAM,MAAM,CAC9E,EAGKa,EAAcF,EAAQ,SAAC,IACrBF,EAAgB,MAAQG,EAAU,KACzC,EAGKE,EAAmBC,GAAc,CACtC,GAAI,CAACf,EAAM,qBAAsB,OAGjC,MAAMgB,EAAU,KAAK,IAAID,EAAYf,EAAM,gBAAiB,CAAC,EAG7D,GAAIgB,IAAY,EAEfT,EAAe,MAAQP,EAAM,eAC7BQ,EAAiB,MAAQR,EAAM,yBACrBgB,IAAY,EAEtBT,EAAe,MAAQP,EAAM,gBAC7BQ,EAAiB,MAAQR,EAAM,sBACzB,CAEN,MAAMiB,EAAkBjB,EAAM,oBAE9B,GAAIgB,GAAWC,EAAiB,CAG/B,MAAMC,EAAc,EADCF,EAAUC,EAEzBE,EAAeC,EAASpB,EAAM,cAAc,EAE9CmB,IACHZ,EAAe,MAAQ,QAAQY,EAAa,CAAC,KAAKA,EAAa,CAAC,KAAKA,EAAa,CAAC,KAAKD,CAAW,IACnGV,EAAiB,MAAQR,EAAM,iBAEnC,KAAS,CAEN,MAAMqB,GAAiBL,EAAUC,IAAoB,EAAIA,GACnDK,EAAgBF,EAASpB,EAAM,eAAe,EAEpD,GAAIsB,EAAe,CAElBf,EAAe,MAAQ,QAAQe,EAAc,CAAC,KAAKA,EAAc,CAAC,KAAKA,EAAc,CAAC,KAAKD,CAAa,IAGxG,MAAME,EAAkBN,GAAmB,EAAIA,GAAmB,GAClET,EAAiB,MAAQQ,EAAUO,EAAkBvB,EAAM,kBAAoBA,EAAM,gBACrF,CACD,CACD,CACF,EAGMoB,EAAYI,GAAQ,CAEzBA,EAAMA,EAAI,QAAQ,IAAK,EAAE,EAGrBA,EAAI,SAAW,IAClBA,EAAMA,EAAI,MAAM,EAAE,EAAE,IAAIC,GAAQA,EAAOA,CAAI,EAAE,KAAK,EAAE,GAGrD,MAAMC,EAAS,0CAA0C,KAAKF,CAAG,EACjE,OAAOE,EAAS,CACf,EAAG,SAASA,EAAO,CAAC,EAAG,EAAE,EACzB,EAAG,SAASA,EAAO,CAAC,EAAG,EAAE,EACzB,EAAG,SAASA,EAAO,CAAC,EAAG,EAAE,CAC3B,EAAK,IACL,EAGMC,EAAoBC,GAAM,CAE/B,MAAMb,EAAYa,EAAE,WAAa,EACjCxB,EAAc,MAAQW,EACtBD,EAAgBC,CAAS,EACzBb,EAAK,SAAU,CAAE,UAAAa,EAAW,CAC7B,EAGAc,EAAY,aAACF,CAAgB,EAG7B,MAAMG,EAAqB,IAAM,CAChC,GAAI,CAEH,MAAMC,EAAaC,EAAG,MAAC,cAAe,EACtCvB,EAAgB,MAAQsB,EAAW,iBAAmB,CAMtD,MAAW,CACXtB,EAAgB,MAAQ,CACxB,CACF,EAGMwB,EAAkB,IAAM,CAC7B/B,EAAK,WAAW,CACjB,EAEMgC,EAAmB,IAAM,CAC9BhC,EAAK,YAAY,CAClB,EAEMiC,EAAa,IAAM,CACxBjC,EAAK,MAAM,EAGG,gBAAiB,EACrB,QAAU,EAEnB8B,EAAAA,MAAI,SAAS,CACZ,IAAKI,EAAY,aAAC,YACrB,CAAG,EAGDJ,EAAAA,MAAI,aAAc,CAEpB,EAGAK,OAAAA,EAAAA,MAAMxB,EAAcyB,GAAc,CACjChC,EAAa,MAAQgC,EACrBpC,EAAK,eAAgBoC,CAAS,CAC/B,EAAG,CAAE,UAAW,GAAM,EAGtBC,EAAAA,UAAU,IAAM,CACfT,EAAoB,EACpBhB,EAAgB,CAAC,CAClB,CAAC,myBClVD,GAAG,gBAAgB0B,CAAS"}