package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserLogoffRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserLogoffRecordBoToUserLogoffRecordMapperImpl implements UserLogoffRecordBoToUserLogoffRecordMapper {

    @Override
    public UserLogoffRecord convert(UserLogoffRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserLogoffRecord userLogoffRecord = new UserLogoffRecord();

        userLogoffRecord.setSearchValue( arg0.getSearchValue() );
        userLogoffRecord.setCreateBy( arg0.getCreateBy() );
        userLogoffRecord.setCreateTime( arg0.getCreateTime() );
        userLogoffRecord.setUpdateBy( arg0.getUpdateBy() );
        userLogoffRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userLogoffRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userLogoffRecord.setCreateDept( arg0.getCreateDept() );
        userLogoffRecord.setId( arg0.getId() );
        userLogoffRecord.setUserId( arg0.getUserId() );
        userLogoffRecord.setStatus( arg0.getStatus() );
        userLogoffRecord.setLogoffTime( arg0.getLogoffTime() );
        userLogoffRecord.setSucceedTime( arg0.getSucceedTime() );

        return userLogoffRecord;
    }

    @Override
    public UserLogoffRecord convert(UserLogoffRecordBo arg0, UserLogoffRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setLogoffTime( arg0.getLogoffTime() );
        arg1.setSucceedTime( arg0.getSucceedTime() );

        return arg1;
    }
}
