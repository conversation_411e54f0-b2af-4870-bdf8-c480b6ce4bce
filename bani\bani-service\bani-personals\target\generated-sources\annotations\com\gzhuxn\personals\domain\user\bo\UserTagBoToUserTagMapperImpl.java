package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserTag;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserTagBoToUserTagMapperImpl implements UserTagBoToUserTagMapper {

    @Override
    public UserTag convert(UserTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserTag userTag = new UserTag();

        userTag.setId( arg0.getId() );
        userTag.setNamespace( arg0.getNamespace() );
        userTag.setTagKey( arg0.getTagKey() );
        userTag.setTagVal( arg0.getTagVal() );
        userTag.setTagValName( arg0.getTagValName() );

        return userTag;
    }

    @Override
    public UserTag convert(UserTagBo arg0, UserTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setNamespace( arg0.getNamespace() );
        arg1.setTagKey( arg0.getTagKey() );
        arg1.setTagVal( arg0.getTagVal() );
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
