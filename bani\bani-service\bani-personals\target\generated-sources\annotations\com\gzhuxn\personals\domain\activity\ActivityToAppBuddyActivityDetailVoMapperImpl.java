package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.recommend.vo.activity.AppBuddyActivityDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppBuddyActivityDetailVoMapperImpl implements ActivityToAppBuddyActivityDetailVoMapper {

    @Override
    public AppBuddyActivityDetailVo convert(Activity source) {
        if ( source == null ) {
            return null;
        }

        AppBuddyActivityDetailVo appBuddyActivityDetailVo = new AppBuddyActivityDetailVo();

        appBuddyActivityDetailVo.setTime( source.getCreateTime() );
        appBuddyActivityDetailVo.setId( source.getId() );
        appBuddyActivityDetailVo.setName( source.getName() );
        appBuddyActivityDetailVo.setClassify( source.getClassify() );
        appBuddyActivityDetailVo.setAddress( source.getAddress() );
        appBuddyActivityDetailVo.setStartTime( source.getStartTime() );
        appBuddyActivityDetailVo.setEndTime( source.getEndTime() );
        appBuddyActivityDetailVo.setEnrollStartTime( source.getEnrollStartTime() );
        appBuddyActivityDetailVo.setIntroduce( source.getIntroduce() );
        appBuddyActivityDetailVo.setBackgroundImage( source.getBackgroundImage() );
        appBuddyActivityDetailVo.setIntroduceImages( source.getIntroduceImages() );
        appBuddyActivityDetailVo.setAmount( source.getAmount() );
        appBuddyActivityDetailVo.setStatus( source.getStatus() );
        appBuddyActivityDetailVo.setLimitNum( source.getLimitNum() );
        appBuddyActivityDetailVo.setEnrollNum( source.getEnrollNum() );
        appBuddyActivityDetailVo.setTimeLength( source.getTimeLength() );
        appBuddyActivityDetailVo.setLon( source.getLon() );
        appBuddyActivityDetailVo.setLat( source.getLat() );

        return appBuddyActivityDetailVo;
    }

    @Override
    public AppBuddyActivityDetailVo convert(Activity source, AppBuddyActivityDetailVo target) {
        if ( source == null ) {
            return target;
        }

        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setName( source.getName() );
        target.setClassify( source.getClassify() );
        target.setAddress( source.getAddress() );
        target.setStartTime( source.getStartTime() );
        target.setEndTime( source.getEndTime() );
        target.setEnrollStartTime( source.getEnrollStartTime() );
        target.setIntroduce( source.getIntroduce() );
        target.setBackgroundImage( source.getBackgroundImage() );
        target.setIntroduceImages( source.getIntroduceImages() );
        target.setAmount( source.getAmount() );
        target.setStatus( source.getStatus() );
        target.setLimitNum( source.getLimitNum() );
        target.setEnrollNum( source.getEnrollNum() );
        target.setTimeLength( source.getTimeLength() );
        target.setLon( source.getLon() );
        target.setLat( source.getLat() );

        return target;
    }
}
