"use strict";/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/function Ct(e,t){const n=new Set(e.split(","));return t?o=>n.has(o.toLowerCase()):o=>n.has(o)}const J=Object.freeze({}),ur=Object.freeze([]),Z=()=>{},Wu=()=>!1,zi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),lr=e=>e.startsWith("onUpdate:"),V=Object.assign,Xn=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},zu=Object.prototype.hasOwnProperty,L=(e,t)=>zu.call(e,t),R=Array.isArray,Xe=e=>ye(e)==="[object Map]",Ji=e=>ye(e)==="[object Set]",O=e=>typeof e=="function",W=e=>typeof e=="string",$t=e=>typeof e=="symbol",B=e=>e!==null&&typeof e=="object",en=e=>(B(e)||O(e))&&O(e.then)&&O(e.catch),Gi=Object.prototype.toString,ye=e=>Gi.call(e),Qn=e=>ye(e).slice(8,-1),ee=e=>ye(e)==="[object Object]",xs=e=>W(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Yi=Ct(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ju=Ct("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),Zn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Gu=/-(\w)/g,xe=Zn(e=>e.replace(Gu,(t,n)=>n?n.toUpperCase():"")),Yu=/\B([A-Z])/g,ot=Zn(e=>e.replace(Yu,"-$1").toLowerCase()),st=Zn(e=>e.charAt(0).toUpperCase()+e.slice(1)),ze=Zn(e=>e?`on${st(e)}`:""),Be=(e,t)=>!Object.is(e,t),xn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Xu=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Qu=e=>{const t=parseFloat(e);return isNaN(t)?e:t};function Xi(e){if(R(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=W(o)?nl(o):Xi(o);if(s)for(const r in s)t[r]=s[r]}return t}else if(W(e)||B(e))return e}const Zu=/;(?![^(]*\))/g,el=/:([^]+)/,tl=/\/\*[^]*?\*\//g;function nl(e){const t={};return e.replace(tl,"").split(Zu).forEach(n=>{if(n){const o=n.split(el);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function Qi(e){let t="";if(W(e))t=e;else if(R(e))for(let n=0;n<e.length;n++){const o=Qi(e[n]);o&&(t+=o+" ")}else if(B(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ol=e=>W(e)?e:e==null?"":R(e)||B(e)&&(e.toString===Gi||!O(e.toString))?JSON.stringify(e,Zi,2):String(e),Zi=(e,t)=>t&&t.__v_isRef?Zi(e,t.value):Xe(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,s],r)=>(n[po(o,r)+" =>"]=s,n),{})}:Ji(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>po(n))}:$t(t)?po(t):B(t)&&!R(t)&&!ee(t)?String(t):t,po=(e,t="")=>{var n;return $t(e)?`Symbol(${(n=e.description)!=null?n:t})`:e},sl=e=>e!==null&&typeof e=="object",rl=["{","}"];class il{constructor(){this._caches=Object.create(null)}interpolate(t,n,o=rl){if(!n)return[t];let s=this._caches[t];return s||(s=ul(t,o),this._caches[t]=s),ll(s,n)}}const al=/^(?:\d)+/,cl=/^(?:\w)+/;function ul(e,[t,n]){const o=[];let s=0,r="";for(;s<e.length;){let i=e[s++];if(i===t){r&&o.push({type:"text",value:r}),r="";let a="";for(i=e[s++];i!==void 0&&i!==n;)a+=i,i=e[s++];const c=i===n,u=al.test(a)?"list":c&&cl.test(a)?"named":"unknown";o.push({value:a,type:u})}else r+=i}return r&&o.push({type:"text",value:r}),o}function ll(e,t){const n=[];let o=0;const s=Array.isArray(t)?"list":sl(t)?"named":"unknown";if(s==="unknown")return n;for(;o<e.length;){const r=e[o];switch(r.type){case"text":n.push(r.value);break;case"list":n.push(t[parseInt(r.value,10)]);break;case"named":s==="named"?n.push(t[r.value]):console.warn(`Type of token '${r.type}' and format of value '${s}' don't match!`);break;case"unknown":console.warn("Detect 'unknown' type of token!");break}o++}return n}const ho="zh-Hans",fr="zh-Hant",ve="en",fl="fr",dl="es",pl=Object.prototype.hasOwnProperty,dr=(e,t)=>pl.call(e,t),hl=new il;function gl(e,t){return!!t.find(n=>e.indexOf(n)!==-1)}function ml(e,t){return t.find(n=>e.indexOf(n)===0)}function Cn(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),e==="chinese")return ho;if(e.indexOf("zh")===0)return e.indexOf("-hans")>-1?ho:e.indexOf("-hant")>-1||gl(e,["-tw","-hk","-mo","-cht"])?fr:ho;let n=[ve,fl,dl];t&&Object.keys(t).length>0&&(n=Object.keys(t));const o=ml(e,n);if(o)return o}class yl{constructor({locale:t,fallbackLocale:n,messages:o,watcher:s,formater:r}){this.locale=ve,this.fallbackLocale=ve,this.message={},this.messages={},this.watchers=[],n&&(this.fallbackLocale=n),this.formater=r||hl,this.messages=o||{},this.setLocale(t||ve),s&&this.watchLocale(s)}setLocale(t){const n=this.locale;this.locale=Cn(t,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],n!==this.locale&&this.watchers.forEach(o=>{o(this.locale,n)})}getLocale(){return this.locale}watchLocale(t){const n=this.watchers.push(t)-1;return()=>{this.watchers.splice(n,1)}}add(t,n,o=!0){const s=this.messages[t];s?o?Object.assign(s,n):Object.keys(n).forEach(r=>{dr(s,r)||(s[r]=n[r])}):this.messages[t]=n}f(t,n,o){return this.formater.interpolate(t,n,o).join("")}t(t,n,o){let s=this.message;return typeof n=="string"?(n=Cn(n,this.messages),n&&(s=this.messages[n])):o=n,dr(s,t)?this.formater.interpolate(s[t],o).join(""):(console.warn(`Cannot translate the value of keypath ${t}. Use the value of keypath as default.`),t)}}function _l(e,t){e.$watchLocale?e.$watchLocale(n=>{t.setLocale(n)}):e.$watch(()=>e.$locale,n=>{t.setLocale(n)})}function bl(){return typeof N<"u"&&N.getLocale?N.getLocale():typeof global<"u"&&global.getLocale?global.getLocale():ve}function vl(e,t={},n,o){if(typeof e!="string"){const i=[t,e];e=i[0],t=i[1]}typeof e!="string"&&(e=bl()),typeof n!="string"&&(n=typeof __uniConfig<"u"&&__uniConfig.fallbackLocale||ve);const s=new yl({locale:e,fallbackLocale:n,messages:t,watcher:o});let r=(i,a)=>{if(typeof getApp!="function")r=function(c,u){return s.t(c,u)};else{let c=!1;r=function(u,f){const l=getApp().$vm;return l&&(l.$locale,c||(c=!0,_l(l,s))),s.t(u,f)}}return r(i,a)};return{i18n:s,f(i,a,c){return s.f(i,a,c)},t(i,a){return r(i,a)},add(i,a,c=!0){return s.add(i,a,c)},watch(i){return s.watchLocale(i)},getLocale(){return s.getLocale()},setLocale(i){return s.setLocale(i)}}}const wl="d",tn="onShow",nn="onHide",Ps="onLaunch",rt="onError",ea="onThemeChange",ta="onPageNotFound",na="onUnhandledRejection",Sl="onExit",kt="onLoad",Is="onReady",As="onUnload",oa="onInit",sa="onSaveExitState",ra="onResize",ia="onBackPress",Ts="onPageScroll",Es="onTabItemTap",Os="onReachBottom",Cs="onPullDownRefresh",$s="onShareTimeline",aa="onShareChat",Rs="onAddToFavorites",Ls="onShareAppMessage",ca="onNavigationBarButtonTap",ua="onNavigationBarSearchInputClicked",la="onNavigationBarSearchInputChanged",fa="onNavigationBarSearchInputConfirmed",da="onNavigationBarSearchInputFocusChanged",xl="virtualHostStyle",Pl="virtualHostClass",Il="virtualHostHidden",jo="virtualHostId";function Al(e){return e.indexOf("/")===0}function Tl(e){return Al(e)?e:"/"+e}const El=(e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n};function pa(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function Ns(e,t){if(!W(t))return;t=t.replace(/\[(\d+)\]/g,".$1");const n=t.split(".");let o=n[0];return e||(e={}),n.length===1?e[o]:Ns(e[o],n.slice(1).join("."))}function ha(e){let t={};return ee(e)&&Object.keys(e).sort().forEach(n=>{const o=n;t[o]=e[o]}),Object.keys(t)?t:e}const Ol=/:/g;function Cl(e){return xe(e.replace(Ol,"-"))}const $l=encodeURIComponent;function kl(e,t=$l){const n=e?Object.keys(e).map(o=>{let s=e[o];return typeof s===void 0||s===null?s="":ee(s)&&(s=JSON.stringify(s)),t(o)+"="+t(s)}).filter(o=>o.length>0).join("&"):null;return n?`?${n}`:""}const Rl=[oa,kt,tn,nn,As,ia,Ts,Es,Os,Cs,$s,Ls,aa,Rs,sa,ca,ua,la,fa,da];function Ll(e){return Rl.indexOf(e)>-1}const ga=[tn,nn,Ps,rt,ea,ta,na,Sl,oa,kt,Is,As,ra,ia,Ts,Es,Os,Cs,$s,Rs,Ls,aa,sa,ca,ua,la,fa,da],Do={onPageScroll:1,onShareAppMessage:2,onShareTimeline:4};function ma(e,t,n=!0){return n&&!O(t)?!1:ga.indexOf(e)>-1?!0:e.indexOf("on")===0}let Mo;const ya=[];function Nl(e){if(Mo)return e(Mo);ya.push(e)}function jl(e){Mo=e,ya.forEach(t=>t(e))}const Dl=pa((e,t)=>t(e)),_a=function(){};_a.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function s(){o.off(e,s),t.apply(n,arguments)}return s._=t,this.on(e,s,n)},emit:function(e){var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,s=n.length;for(o;o<s;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],s=[];if(o&&t){for(var r=o.length-1;r>=0;r--)if(o[r].fn===t||o[r].fn._===t||o[r]._id===t){o.splice(r,1);break}s=o}return s.length?n[e]=s:delete n[e],this}};var Ml=_a;/**
* @dcloudio/uni-mp-vue v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Pe(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let le;class ba{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=le,!t&&le&&(this.index=(le.scopes||(le.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=le;try{return le=this,t()}finally{le=n}}else Pe("cannot run an inactive effect scope.")}on(){le=this}off(){le=this.parent}stop(t){if(this._active){let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.scopes)for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function va(e){return new ba(e)}function Ul(e,t=le){t&&t.active&&t.effects.push(e)}function wa(){return le}function Hl(e){le?le.cleanups.push(e):Pe("onScopeDispose() is called when there is no active effect scope to be associated with.")}let Qe;class js{constructor(t,n,o,s){this.fn=t,this.trigger=n,this.scheduler=o,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,Ul(this,s)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,ut();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(Fl(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),lt()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=Ue,n=Qe;try{return Ue=!0,Qe=this,this._runnings++,pr(this),this.fn()}finally{hr(this),this._runnings--,Qe=n,Ue=t}}stop(){var t;this.active&&(pr(this),hr(this),(t=this.onStop)==null||t.call(this),this.active=!1)}}function Fl(e){return e.value}function pr(e){e._trackId++,e._depsLength=0}function hr(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Sa(e.deps[t],e);e.deps.length=e._depsLength}}function Sa(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let Ue=!0,Uo=0;const xa=[];function ut(){xa.push(Ue),Ue=!1}function lt(){const e=xa.pop();Ue=e===void 0?!0:e}function Ds(){Uo++}function Ms(){for(Uo--;!Uo&&Ho.length;)Ho.shift()()}function Pa(e,t,n){var o;if(t.get(e)!==e._trackId){t.set(e,e._trackId);const s=e.deps[e._depsLength];s!==t?(s&&Sa(s,e),e.deps[e._depsLength++]=t):e._depsLength++,(o=e.onTrack)==null||o.call(e,V({effect:e},n))}}const Ho=[];function Ia(e,t,n){var o;Ds();for(const s of e.keys()){let r;s._dirtyLevel<t&&(r??(r=e.get(s)===s._trackId))&&(s._shouldSchedule||(s._shouldSchedule=s._dirtyLevel===0),s._dirtyLevel=t),s._shouldSchedule&&(r??(r=e.get(s)===s._trackId))&&((o=s.onTrigger)==null||o.call(s,V({effect:s},n)),s.trigger(),(!s._runnings||s.allowRecurse)&&s._dirtyLevel!==2&&(s._shouldSchedule=!1,s.scheduler&&Ho.push(s.scheduler)))}Ms()}const Aa=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},$n=new WeakMap,Ze=Symbol("iterate"),Fo=Symbol("Map key iterate");function ne(e,t,n){if(Ue&&Qe){let o=$n.get(e);o||$n.set(e,o=new Map);let s=o.get(n);s||o.set(n,s=Aa(()=>o.delete(n))),Pa(Qe,s,{target:e,type:t,key:n})}}function Te(e,t,n,o,s,r){const i=$n.get(e);if(!i)return;let a=[];if(t==="clear")a=[...i.values()];else if(n==="length"&&R(e)){const c=Number(o);i.forEach((u,f)=>{(f==="length"||!$t(f)&&f>=c)&&a.push(u)})}else switch(n!==void 0&&a.push(i.get(n)),t){case"add":R(e)?xs(n)&&a.push(i.get("length")):(a.push(i.get(Ze)),Xe(e)&&a.push(i.get(Fo)));break;case"delete":R(e)||(a.push(i.get(Ze)),Xe(e)&&a.push(i.get(Fo)));break;case"set":Xe(e)&&a.push(i.get(Ze));break}Ds();for(const c of a)c&&Ia(c,4,{target:e,type:t,key:n,newValue:o,oldValue:s,oldTarget:r});Ms()}function ql(e,t){var n;return(n=$n.get(e))==null?void 0:n.get(t)}const Bl=Ct("__proto__,__v_isRef,__isVue"),Ta=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter($t)),gr=Vl();function Vl(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const o=j(this);for(let r=0,i=this.length;r<i;r++)ne(o,"get",r+"");const s=o[t](...n);return s===-1||s===!1?o[t](...n.map(j)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){ut(),Ds();const o=j(this)[t].apply(this,n);return Ms(),lt(),o}}),e}function Kl(e){const t=j(this);return ne(t,"has",e),t.hasOwnProperty(e)}class Ea{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){const s=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return r;if(n==="__v_raw")return o===(s?r?Na:La:r?Ra:ka).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=R(t);if(!s){if(i&&L(gr,n))return Reflect.get(gr,n,o);if(n==="hasOwnProperty")return Kl}const a=Reflect.get(t,n,o);return($t(n)?Ta.has(n):Bl(n))||(s||ne(t,"get",n),r)?a:z(a)?i&&xs(n)?a:a.value:B(a)?s?ja(a):on(a):a}}class Oa extends Ea{constructor(t=!1){super(!1,t)}set(t,n,o,s){let r=t[n];if(!this._isShallow){const c=St(r);if(!kn(o)&&!St(o)&&(r=j(r),o=j(o)),!R(t)&&z(r)&&!z(o))return c?!1:(r.value=o,!0)}const i=R(t)&&xs(n)?Number(n)<t.length:L(t,n),a=Reflect.set(t,n,o,s);return t===j(s)&&(i?Be(o,r)&&Te(t,"set",n,o,r):Te(t,"add",n,o)),a}deleteProperty(t,n){const o=L(t,n),s=t[n],r=Reflect.deleteProperty(t,n);return r&&o&&Te(t,"delete",n,void 0,s),r}has(t,n){const o=Reflect.has(t,n);return(!$t(n)||!Ta.has(n))&&ne(t,"has",n),o}ownKeys(t){return ne(t,"iterate",R(t)?"length":Ze),Reflect.ownKeys(t)}}class Ca extends Ea{constructor(t=!1){super(!0,t)}set(t,n){return Pe(`Set operation on key "${String(n)}" failed: target is readonly.`,t),!0}deleteProperty(t,n){return Pe(`Delete operation on key "${String(n)}" failed: target is readonly.`,t),!0}}const Wl=new Oa,zl=new Ca,Jl=new Oa(!0),Gl=new Ca(!0),Us=e=>e,eo=e=>Reflect.getPrototypeOf(e);function fn(e,t,n=!1,o=!1){e=e.__v_raw;const s=j(e),r=j(t);n||(Be(t,r)&&ne(s,"get",t),ne(s,"get",r));const{has:i}=eo(s),a=o?Us:n?Hs:Vt;if(i.call(s,t))return a(e.get(t));if(i.call(s,r))return a(e.get(r));e!==s&&e.get(t)}function dn(e,t=!1){const n=this.__v_raw,o=j(n),s=j(e);return t||(Be(e,s)&&ne(o,"has",e),ne(o,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function pn(e,t=!1){return e=e.__v_raw,!t&&ne(j(e),"iterate",Ze),Reflect.get(e,"size",e)}function mr(e){e=j(e);const t=j(this);return eo(t).has.call(t,e)||(t.add(e),Te(t,"add",e,e)),this}function yr(e,t){t=j(t);const n=j(this),{has:o,get:s}=eo(n);let r=o.call(n,e);r?$a(n,o,e):(e=j(e),r=o.call(n,e));const i=s.call(n,e);return n.set(e,t),r?Be(t,i)&&Te(n,"set",e,t,i):Te(n,"add",e,t),this}function _r(e){const t=j(this),{has:n,get:o}=eo(t);let s=n.call(t,e);s?$a(t,n,e):(e=j(e),s=n.call(t,e));const r=o?o.call(t,e):void 0,i=t.delete(e);return s&&Te(t,"delete",e,void 0,r),i}function br(){const e=j(this),t=e.size!==0,n=Xe(e)?new Map(e):new Set(e),o=e.clear();return t&&Te(e,"clear",void 0,void 0,n),o}function hn(e,t){return function(o,s){const r=this,i=r.__v_raw,a=j(i),c=t?Us:e?Hs:Vt;return!e&&ne(a,"iterate",Ze),i.forEach((u,f)=>o.call(s,c(u),c(f),r))}}function gn(e,t,n){return function(...o){const s=this.__v_raw,r=j(s),i=Xe(r),a=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,u=s[e](...o),f=n?Us:t?Hs:Vt;return!t&&ne(r,"iterate",c?Fo:Ze),{next(){const{value:l,done:p}=u.next();return p?{value:l,done:p}:{value:a?[f(l[0]),f(l[1])]:f(l),done:p}},[Symbol.iterator](){return this}}}}function Re(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";Pe(`${st(e)} operation ${n}failed: target is readonly.`,j(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function Yl(){const e={get(r){return fn(this,r)},get size(){return pn(this)},has:dn,add:mr,set:yr,delete:_r,clear:br,forEach:hn(!1,!1)},t={get(r){return fn(this,r,!1,!0)},get size(){return pn(this)},has:dn,add:mr,set:yr,delete:_r,clear:br,forEach:hn(!1,!0)},n={get(r){return fn(this,r,!0)},get size(){return pn(this,!0)},has(r){return dn.call(this,r,!0)},add:Re("add"),set:Re("set"),delete:Re("delete"),clear:Re("clear"),forEach:hn(!0,!1)},o={get(r){return fn(this,r,!0,!0)},get size(){return pn(this,!0)},has(r){return dn.call(this,r,!0)},add:Re("add"),set:Re("set"),delete:Re("delete"),clear:Re("clear"),forEach:hn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=gn(r,!1,!1),n[r]=gn(r,!0,!1),t[r]=gn(r,!1,!0),o[r]=gn(r,!0,!0)}),[e,n,t,o]}const[Xl,Ql,Zl,ef]=Yl();function to(e,t){const n=t?e?ef:Zl:e?Ql:Xl;return(o,s,r)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?o:Reflect.get(L(n,s)&&s in o?n:o,s,r)}const tf={get:to(!1,!1)},nf={get:to(!1,!0)},of={get:to(!0,!1)},sf={get:to(!0,!0)};function $a(e,t,n){const o=j(n);if(o!==n&&t.call(e,o)){const s=Qn(e);Pe(`Reactive ${s} contains both the raw and reactive versions of the same object${s==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const ka=new WeakMap,Ra=new WeakMap,La=new WeakMap,Na=new WeakMap;function rf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function af(e){return e.__v_skip||!Object.isExtensible(e)?0:rf(Qn(e))}function on(e){return St(e)?e:no(e,!1,Wl,tf,ka)}function cf(e){return no(e,!1,Jl,nf,Ra)}function ja(e){return no(e,!0,zl,of,La)}function gt(e){return no(e,!0,Gl,sf,Na)}function no(e,t,n,o,s){if(!B(e))return Pe(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=s.get(e);if(r)return r;const i=af(e);if(i===0)return e;const a=new Proxy(e,i===2?o:n);return s.set(e,a),a}function we(e){return St(e)?we(e.__v_raw):!!(e&&e.__v_isReactive)}function St(e){return!!(e&&e.__v_isReadonly)}function kn(e){return!!(e&&e.__v_isShallow)}function Da(e){return we(e)||St(e)}function j(e){const t=e&&e.__v_raw;return t?j(t):e}function Ae(e){return Object.isExtensible(e)&&Xu(e,"__v_skip",!0),e}const Vt=e=>B(e)?on(e):e,Hs=e=>B(e)?ja(e):e,uf="Computed is still dirty after getter evaluation, likely because a computed is mutating its own dependency in its getter. State mutations in computed getters should be avoided.  Check the docs for more details: https://vuejs.org/guide/essentials/computed.html#getters-should-be-side-effect-free";class Ma{constructor(t,n,o,s){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new js(()=>t(this._value),()=>Pn(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=o}get value(){const t=j(this);return(!t._cacheable||t.effect.dirty)&&Be(t._value,t._value=t.effect.run())&&Pn(t,4),Ua(t),t.effect._dirtyLevel>=2&&(this._warnRecursive&&Pe(uf,`

getter: `,this.getter),Pn(t,2)),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function lf(e,t,n=!1){let o,s;const r=O(e);r?(o=e,s=()=>{Pe("Write operation failed: computed value is readonly")}):(o=e.get,s=e.set);const i=new Ma(o,s,r||!s,n);return t&&!n&&(i.effect.onTrack=t.onTrack,i.effect.onTrigger=t.onTrigger),i}function Ua(e){var t;Ue&&Qe&&(e=j(e),Pa(Qe,(t=e.dep)!=null?t:e.dep=Aa(()=>e.dep=void 0,e instanceof Ma?e:void 0),{target:e,type:"get",key:"value"}))}function Pn(e,t=4,n){e=j(e);const o=e.dep;o&&Ia(o,t,{target:e,type:"set",key:"value",newValue:n})}function z(e){return!!(e&&e.__v_isRef===!0)}function Rt(e){return ff(e,!1)}function ff(e,t){return z(e)?e:new df(e,t)}class df{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:j(t),this._value=n?t:Vt(t)}get value(){return Ua(this),this._value}set value(t){const n=this.__v_isShallow||kn(t)||St(t);t=n?t:j(t),Be(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Vt(t),Pn(this,4,t))}}function Fs(e){return z(e)?e.value:e}const pf={get:(e,t,n)=>Fs(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const s=e[t];return z(s)&&!z(n)?(s.value=n,!0):Reflect.set(e,t,n,o)}};function Ha(e){return we(e)?e:new Proxy(e,pf)}function vr(e){Da(e)||Pe("toRefs() expects a reactive object but received a plain one.");const t=R(e)?new Array(e.length):{};for(const n in e)t[n]=Fa(e,n);return t}class hf{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return ql(j(this._object),this._key)}}class gf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function In(e,t,n){return z(e)?e:O(e)?new gf(e):B(e)&&arguments.length>1?Fa(e,t,n):Rt(e)}function Fa(e,t,n){const o=e[t];return z(o)?o:new hf(e,t,n)}const et=[];function qs(e){et.push(e)}function Bs(){et.pop()}function k(e,...t){ut();const n=et.length?et[et.length-1].component:null,o=n&&n.appContext.config.warnHandler,s=mf();if(o)Ee(o,n,11,[e+t.map(r=>{var i,a;return(a=(i=r.toString)==null?void 0:i.call(r))!=null?a:JSON.stringify(r)}).join(""),n&&n.proxy,s.map(({vnode:r})=>`at <${io(n,r.type)}>`).join(`
`),s]);else{const r=[`[Vue warn]: ${e}`,...t];s.length&&r.push(`
`,...yf(s)),console.warn(...r)}lt()}function mf(){let e=et[et.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const o=e.component&&e.component.parent;e=o&&o.vnode}return t}function yf(e){const t=[];return e.forEach((n,o)=>{t.push(...o===0?[]:[`
`],..._f(n))}),t}function _f({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",o=e.component?e.component.parent==null:!1,s=` at <${io(e.component,e.type,o)}`,r=">"+n;return e.props?[s,...bf(e.props),r]:[s+r]}function bf(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(o=>{t.push(...Ba(o,e[o]))}),n.length>3&&t.push(" ..."),t}function Ba(e,t,n){return W(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:z(t)?(t=Ba(e,j(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):O(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=j(t),n?t:[`${e}=`,t])}const Vs={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush. This is likely a Vue internals bug. Please open an issue at https://github.com/vuejs/core ."};function Ee(e,t,n,o){try{return o?e(...o):e()}catch(s){oo(s,t,n)}}function Ve(e,t,n,o){if(O(e)){const r=Ee(e,t,n,o);return r&&en(r)&&r.catch(i=>{oo(i,t,n)}),r}const s=[];for(let r=0;r<e.length;r++)s.push(Ve(e[r],t,n,o));return s}function oo(e,t,n,o=!0){const s=t?t.vnode:null;if(t){let r=t.parent;const i=t.proxy,a=Vs[n]||n;for(;r;){const u=r.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,i,a)===!1)return}r=r.parent}const c=t.appContext.config.errorHandler;if(c){Ee(c,null,10,[e,i,a]);return}}Va(e,n,s,o)}function Va(e,t,n,o=!0){{const s=Vs[t]||t;n&&qs(n),k(`Unhandled error${s?` during execution of ${s}`:""}`),n&&Bs(),console.error(e)}}let Kt=!1,qo=!1;const Q=[];let ge=0;const _t=[];let Ie=null,je=0;const Ka=Promise.resolve();let Ks=null;const vf=100;function Rn(e){const t=Ks||Ka;return e?t.then(this?e.bind(this):e):t}function wf(e){let t=ge+1,n=Q.length;for(;t<n;){const o=t+n>>>1,s=Q[o],r=Wt(s);r<e||r===e&&s.pre?t=o+1:n=o}return t}function Ln(e){(!Q.length||!Q.includes(e,Kt&&e.allowRecurse?ge+1:ge))&&(e.id==null?Q.push(e):Q.splice(wf(e.id),0,e),Wa())}function Wa(){!Kt&&!qo&&(qo=!0,Ks=Ka.then(Ga))}function Sf(e){return Q.indexOf(e)>-1}function xf(e){const t=Q.indexOf(e);t>ge&&Q.splice(t,1)}function za(e){R(e)?_t.push(...e):(!Ie||!Ie.includes(e,e.allowRecurse?je+1:je))&&_t.push(e),Wa()}function Ja(e,t,n=Kt?ge+1:0){for(t=t||new Map;n<Q.length;n++){const o=Q[n];if(o&&o.pre){if(Ws(t,o))continue;Q.splice(n,1),n--,o()}}}function Pf(e){if(_t.length){const t=[...new Set(_t)].sort((n,o)=>Wt(n)-Wt(o));if(_t.length=0,Ie){Ie.push(...t);return}for(Ie=t,e=e||new Map,je=0;je<Ie.length;je++)Ws(e,Ie[je])||Ie[je]();Ie=null,je=0}}const Wt=e=>e.id==null?1/0:e.id,If=(e,t)=>{const n=Wt(e)-Wt(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ga(e){qo=!1,Kt=!0,e=e||new Map,Q.sort(If);const t=n=>Ws(e,n);try{for(ge=0;ge<Q.length;ge++){const n=Q[ge];if(n&&n.active!==!1){if(t(n))continue;Ee(n,null,14)}}}finally{ge=0,Q.length=0,Pf(e),Kt=!1,Ks=null,(Q.length||_t.length)&&Ga(e)}}function Ws(e,t){if(!e.has(t))e.set(t,1);else{const n=e.get(t);if(n>vf){const o=t.ownerInstance,s=o&&Qs(o.type);return oo(`Maximum recursive updates exceeded${s?` in component <${s}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}else e.set(t,n+1)}}let me,Mt=[],Bo=!1;function so(e,...t){me?me.emit(e,...t):Bo||Mt.push({event:e,args:t})}function Ya(e,t){var n,o;me=e,me?(me.enabled=!0,Mt.forEach(({event:s,args:r})=>me.emit(s,...r)),Mt=[]):typeof window<"u"&&window.HTMLElement&&!((o=(n=window.navigator)==null?void 0:n.userAgent)!=null&&o.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(r=>{Ya(r,t)}),setTimeout(()=>{me||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,Bo=!0,Mt=[])},3e3)):(Bo=!0,Mt=[])}function Af(e,t){so("app:init",e,t,{Fragment:hd,Text:gd,Comment:md,Static:yd})}const Tf=zs("component:added"),Ef=zs("component:updated"),Of=zs("component:removed"),Cf=e=>{me&&typeof me.cleanupBuffer=="function"&&!me.cleanupBuffer(e)&&Of(e)};/*! #__NO_SIDE_EFFECTS__ */function zs(e){return t=>{so(e,t.appContext.app,t.uid,t.uid===0?void 0:t.parent?t.parent.uid:0,t)}}const $f=Xa("perf:start"),kf=Xa("perf:end");function Xa(e){return(t,n,o)=>{so(e,t.appContext.app,t.uid,t,n,o)}}function Rf(e,t,n){so("component:emit",e.appContext.app,e,t,n)}function Lf(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||J;{const{emitsOptions:f,propsOptions:[l]}=e;if(f)if(!(t in f))(!l||!(ze(t)in l))&&k(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${ze(t)}" prop.`);else{const p=f[t];O(p)&&(p(...n)||k(`Invalid event arguments: event validation failed for event "${t}".`))}}let s=n;const r=t.startsWith("update:"),i=r&&t.slice(7);if(i&&i in o){const f=`${i==="modelValue"?"model":i}Modifiers`,{number:l,trim:p}=o[f]||J;p&&(s=n.map(g=>W(g)?g.trim():g)),l&&(s=n.map(Qu))}Rf(e,t,s);{const f=t.toLowerCase();f!==t&&o[ze(f)]&&k(`Event "${f}" is emitted in component ${io(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${ot(t)}" instead of "${t}".`)}let a,c=o[a=ze(t)]||o[a=ze(xe(t))];!c&&r&&(c=o[a=ze(ot(t))]),c&&Ve(c,e,6,s);const u=o[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ve(u,e,6,s)}}function Qa(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(s!==void 0)return s;const r=e.emits;let i={},a=!1;if(!O(e)){const c=u=>{const f=Qa(u,t,!0);f&&(a=!0,V(i,f))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!r&&!a?(B(e)&&o.set(e,null),null):(R(r)?r.forEach(c=>i[c]=null):V(i,r),B(e)&&o.set(e,i),i)}function Za(e,t){return!e||!zi(t)?!1:(t=t.slice(2).replace(/Once$/,""),L(e,t[0].toLowerCase()+t.slice(1))||L(e,ot(t))||L(e,t))}let Ke=null;function Nn(e){const t=Ke;return Ke=e,e&&e.type.__scopeId,t}const Vo="components";function Nf(e,t){return jf(Vo,e,!0,t)||e}function jf(e,t,n=!0,o=!1){const s=Ke||G;if(s){const r=s.type;if(e===Vo){const a=Qs(r,!1);if(a&&(a===t||a===xe(t)||a===st(xe(t))))return r}const i=wr(s[e]||r[e],t)||wr(s.appContext[e],t);if(!i&&o)return r;if(n&&!i){const a=e===Vo?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";k(`Failed to resolve ${e.slice(0,-1)}: ${t}${a}`)}return i}else k(`resolve${st(e.slice(0,-1))} can only be used in render() or setup().`)}function wr(e,t){return e&&(e[t]||e[xe(t)]||e[st(xe(t))])}const mn={};function Ht(e,t,n){return O(t)||k("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),ec(e,t,n)}function ec(e,t,{immediate:n,deep:o,flush:s,once:r,onTrack:i,onTrigger:a}=J){if(t&&r){const w=t;t=(...A)=>{w(...A),x()}}o!==void 0&&typeof o=="number"&&k('watch() "deep" option with number value will be used as watch depth in future versions. Please use a boolean instead to avoid potential breakage.'),t||(n!==void 0&&k('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),o!==void 0&&k('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),r!==void 0&&k('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=w=>{k("Invalid watch source: ",w,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},u=G,f=w=>o===!0?w:mt(w,o===!1?1:void 0);let l,p=!1,g=!1;if(z(e)?(l=()=>e.value,p=kn(e)):we(e)?(l=()=>f(e),p=!0):R(e)?(g=!0,p=e.some(w=>we(w)||kn(w)),l=()=>e.map(w=>{if(z(w))return w.value;if(we(w))return f(w);if(O(w))return Ee(w,u,2);c(w)})):O(e)?t?l=()=>Ee(e,u,2):l=()=>(y&&y(),Ve(e,u,3,[h])):(l=Z,c(e)),t&&o){const w=l;l=()=>mt(w())}let y,h=w=>{y=_.onStop=()=>{Ee(w,u,4),y=_.onStop=void 0}},d=g?new Array(e.length).fill(mn):mn;const m=()=>{if(!(!_.active||!_.dirty))if(t){const w=_.run();(o||p||(g?w.some((A,D)=>Be(A,d[D])):Be(w,d)))&&(y&&y(),Ve(t,u,3,[w,d===mn?void 0:g&&d[0]===mn?[]:d,h]),d=w)}else _.run()};m.allowRecurse=!!t;let b;s==="sync"?b=m:s==="post"?b=()=>$r(m,u&&u.suspense):(m.pre=!0,u&&(m.id=u.uid),b=()=>Ln(m));const _=new js(l,Z,b),v=wa(),x=()=>{_.stop(),v&&Xn(v.effects,_)};return _.onTrack=i,_.onTrigger=a,t?n?m():d=_.run():s==="post"?$r(_.run.bind(_),u&&u.suspense):_.run(),x}function Df(e,t,n){const o=this.proxy,s=W(e)?e.includes(".")?tc(o,e):()=>o[e]:e.bind(o,o);let r;O(t)?r=t:(r=t.handler,n=t);const i=sn(this),a=ec(s,r.bind(o),n);return i(),a}function tc(e,t){const n=t.split(".");return()=>{let o=e;for(let s=0;s<n.length&&o;s++)o=o[n[s]];return o}}function mt(e,t,n=0,o){if(!B(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if(o=o||new Set,o.has(e))return e;if(o.add(e),z(e))mt(e.value,t,n,o);else if(R(e))for(let s=0;s<e.length;s++)mt(e[s],t,n,o);else if(Ji(e)||Xe(e))e.forEach(s=>{mt(s,t,n,o)});else if(ee(e))for(const s in e)mt(e[s],t,n,o);return e}function nc(e){Ju(e)&&k("Do not use built-in directive ids as custom directive id: "+e)}function oc(){return{app:null,config:{isNativeTag:Wu,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Mf=0;function Uf(e,t){return function(o,s=null){O(o)||(o=V({},o)),s!=null&&!B(s)&&(k("root props passed to app.mount() must be an object."),s=null);const r=oc(),i=new WeakSet,a=r.app={_uid:Mf++,_component:o,_props:s,_container:null,_context:r,_instance:null,version:hc,get config(){return r.config},set config(c){k("app.config cannot be replaced. Modify individual options instead.")},use(c,...u){return i.has(c)?k("Plugin has already been applied to target app."):c&&O(c.install)?(i.add(c),c.install(a,...u)):O(c)?(i.add(c),c(a,...u)):k('A plugin must either be a function or an object with an "install" function.'),a},mixin(c){return r.mixins.includes(c)?k("Mixin has already been applied to target app"+(c.name?`: ${c.name}`:"")):r.mixins.push(c),a},component(c,u){return Xo(c,r.config),u?(r.components[c]&&k(`Component "${c}" has already been registered in target app.`),r.components[c]=u,a):r.components[c]},directive(c,u){return nc(c),u?(r.directives[c]&&k(`Directive "${c}" has already been registered in target app.`),r.directives[c]=u,a):r.directives[c]},mount(){},unmount(){},provide(c,u){return c in r.provides&&k(`App already provides property with key "${String(c)}". It will be overwritten with the new value.`),r.provides[c]=u,a},runWithContext(c){const u=bt;bt=a;try{return c()}finally{bt=u}}};return a}}let bt=null;function Hf(e,t){if(!G)k("provide() can only be used inside setup().");else{let n=G.provides;const o=G.parent&&G.parent.provides;o===n&&(n=G.provides=Object.create(o)),n[e]=t,G.type.mpType==="app"&&G.appContext.app.provide(e,t)}}function Ft(e,t,n=!1){const o=G||Ke;if(o||bt){const s=o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:bt._context.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&O(t)?t.call(o&&o.proxy):t;k(`injection "${String(e)}" not found.`)}else k("inject() can only be used inside setup() or functional components.")}function sc(){return!!(G||Ke||bt)}const Ff=e=>e.type.__isKeepAlive;function qf(e,t){rc(e,"a",t)}function Bf(e,t){rc(e,"da",t)}function rc(e,t,n=G){const o=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(We(t,o,n),n){let s=n.parent;for(;s&&s.parent;)Ff(s.parent.vnode)&&Vf(o,t,n,s),s=s.parent}}function Vf(e,t,n,o){const s=We(t,e,o,!0);Ys(()=>{Xn(o[t],s)},n)}function We(e,t,n=G,o=!1){if(n){Ll(e)&&(n=n.root);const s=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{if(n.isUnmounted)return;ut();const a=sn(n),c=Ve(t,n,e,i);return a(),lt(),c});return o?s.unshift(r):s.push(r),r}else{const s=ze((Vs[e]||e.replace(/^on/,"")).replace(/ hook$/,""));k(`${s} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup().`)}}const Oe=e=>(t,n=G)=>(!ro||e==="sp")&&We(e,(...o)=>t(...o),n),Kf=Oe("bm"),Js=Oe("m"),Wf=Oe("bu"),zf=Oe("u"),Gs=Oe("bum"),Ys=Oe("um"),Jf=Oe("sp"),Gf=Oe("rtg"),Yf=Oe("rtc");function Xf(e,t=G){We("ec",e,t)}const Ko=e=>e?dc(e)?rn(e)||e.proxy:Ko(e.parent):null,zt=V(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>gt(e.props),$attrs:e=>gt(e.attrs),$slots:e=>gt(e.slots),$refs:e=>gt(e.refs),$parent:e=>Ko(e.parent),$root:e=>Ko(e.root),$emit:e=>e.emit,$options:e=>cc(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Ln(e.update)}),$watch:e=>Df.bind(e)}),Xs=e=>e==="_"||e==="$",go=(e,t)=>e!==J&&!e.__isScriptSetup&&L(e,t),ic={get({_:e},t){const{ctx:n,setupState:o,data:s,props:r,accessCache:i,type:a,appContext:c}=e;if(t==="__isVue")return!0;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return r[t]}else{if(go(o,t))return i[t]=1,o[t];if(s!==J&&L(s,t))return i[t]=2,s[t];if((u=e.propsOptions[0])&&L(u,t))return i[t]=3,r[t];if(n!==J&&L(n,t))return i[t]=4,n[t];Wo&&(i[t]=0)}}const f=zt[t];let l,p;if(f)return(t==="$attrs"||t==="$slots")&&ne(e,"get",t),f(e);if((l=a.__cssModules)&&(l=l[t]))return l;if(n!==J&&L(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,L(p,t))return p[t];Ke&&(!W(t)||t.indexOf("__v")!==0)&&(s!==J&&Xs(t[0])&&L(s,t)?k(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===Ke&&k(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,n){const{data:o,setupState:s,ctx:r}=e;return go(s,t)?(s[t]=n,!0):s.__isScriptSetup&&L(s,t)?(k(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):o!==J&&L(o,t)?(o[t]=n,!0):L(e.props,t)?(k(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?(k(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):(t in e.appContext.config.globalProperties?Object.defineProperty(r,t,{enumerable:!0,configurable:!0,value:n}):r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:r}},i){let a;return!!n[i]||e!==J&&L(e,i)||go(t,i)||(a=r[0])&&L(a,i)||L(o,i)||L(zt,i)||L(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:L(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};ic.ownKeys=e=>(k("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e));function Qf(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(zt).forEach(n=>{Object.defineProperty(t,n,{configurable:!0,enumerable:!1,get:()=>zt[n](e),set:Z})}),t}function Zf(e){const{ctx:t,propsOptions:[n]}=e;n&&Object.keys(n).forEach(o=>{Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>e.props[o],set:Z})})}function ed(e){const{ctx:t,setupState:n}=e;Object.keys(j(n)).forEach(o=>{if(!n.__isScriptSetup){if(Xs(o[0])){k(`setup() return property ${JSON.stringify(o)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>n[o],set:Z})}})}function Sr(e){return R(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function td(){const e=Object.create(null);return(t,n)=>{e[n]?k(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}let Wo=!0;function nd(e){const t=cc(e),n=e.proxy,o=e.ctx;Wo=!1,t.beforeCreate&&xr(t.beforeCreate,e,"bc");const{data:s,computed:r,methods:i,watch:a,provide:c,inject:u,created:f,beforeMount:l,mounted:p,beforeUpdate:g,updated:y,activated:h,deactivated:d,beforeDestroy:m,beforeUnmount:b,destroyed:_,unmounted:v,render:x,renderTracked:w,renderTriggered:A,errorCaptured:D,serverPrefetch:q,expose:$,inheritAttrs:E,components:H,directives:K,filters:ue}=t,oe=td();{const[M]=e.propsOptions;if(M)for(const U in M)oe("Props",U)}function ke(){u&&od(u,o,oe)}if(ke(),i)for(const M in i){const U=i[M];O(U)?(Object.defineProperty(o,M,{value:U.bind(n),configurable:!0,enumerable:!0,writable:!0}),oe("Methods",M)):k(`Method "${M}" has type "${typeof U}" in the component definition. Did you reference the function correctly?`)}if(s){O(s)||k("The data option must be a function. Plain object usage is no longer supported.");const M=s.call(n,n);if(en(M)&&k("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!B(M))k("data() should return an object.");else{e.data=on(M);for(const U in M)oe("Data",U),Xs(U[0])||Object.defineProperty(o,U,{configurable:!0,enumerable:!0,get:()=>M[U],set:Z})}}if(Wo=!0,r)for(const M in r){const U=r[M],S=O(U)?U.bind(n,n):O(U.get)?U.get.bind(n,n):Z;S===Z&&k(`Computed property "${M}" has no getter.`);const T=!O(U)&&O(U.set)?U.set.bind(n):()=>{k(`Write operation failed: computed property "${M}" is readonly.`)},P=ao({get:S,set:T});Object.defineProperty(o,M,{enumerable:!0,configurable:!0,get:()=>P.value,set:I=>P.value=I}),oe("Computed",M)}if(a)for(const M in a)ac(a[M],o,n,M);function fe(){if(c){const M=O(c)?c.call(n):c;Reflect.ownKeys(M).forEach(U=>{Hf(U,M[U])})}}fe(),f&&xr(f,e,"c");function F(M,U){R(U)?U.forEach(S=>M(S.bind(n))):U&&M(U.bind(n))}if(F(Kf,l),F(Js,p),F(Wf,g),F(zf,y),F(qf,h),F(Bf,d),F(Xf,D),F(Yf,w),F(Gf,A),F(Gs,b),F(Ys,v),F(Jf,q),R($))if($.length){const M=e.exposed||(e.exposed={});$.forEach(U=>{Object.defineProperty(M,U,{get:()=>n[U],set:S=>n[U]=S})})}else e.exposed||(e.exposed={});x&&e.render===Z&&(e.render=x),E!=null&&(e.inheritAttrs=E),H&&(e.components=H),K&&(e.directives=K),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function od(e,t,n=Z){R(e)&&(e=zo(e));for(const o in e){const s=e[o];let r;B(s)?"default"in s?r=Ft(s.from||o,s.default,!0):r=Ft(s.from||o):r=Ft(s),z(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[o]=r,n("Inject",o)}}function xr(e,t,n){Ve(R(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function ac(e,t,n,o){const s=o.includes(".")?tc(n,o):()=>n[o];if(W(e)){const r=t[e];O(r)?Ht(s,r):k(`Invalid watch handler specified by key "${e}"`,r)}else if(O(e))Ht(s,e.bind(n));else if(B(e))if(R(e))e.forEach(r=>ac(r,t,n,o));else{const r=O(e.handler)?e.handler.bind(n):t[e.handler];O(r)?Ht(s,r,e):k(`Invalid watch handler specified by key "${e.handler}"`,r)}else k(`Invalid watch option: "${o}"`,e)}function cc(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,a=r.get(t);let c;return a?c=a:!s.length&&!n&&!o?c=t:(c={},s.length&&s.forEach(u=>jn(c,u,i,!0)),jn(c,t,i)),B(t)&&r.set(t,c),c}function jn(e,t,n,o=!1){const{mixins:s,extends:r}=t;r&&jn(e,r,n,!0),s&&s.forEach(i=>jn(e,i,n,!0));for(const i in t)if(o&&i==="expose")k('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const a=sd[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const sd={data:Pr,props:Ir,emits:Ir,methods:Ut,computed:Ut,beforeCreate:se,created:se,beforeMount:se,mounted:se,beforeUpdate:se,updated:se,beforeDestroy:se,beforeUnmount:se,destroyed:se,unmounted:se,activated:se,deactivated:se,errorCaptured:se,serverPrefetch:se,components:Ut,directives:Ut,watch:id,provide:Pr,inject:rd};function Pr(e,t){return t?e?function(){return V(O(e)?e.call(this,this):e,O(t)?t.call(this,this):t)}:t:e}function rd(e,t){return Ut(zo(e),zo(t))}function zo(e){if(R(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function se(e,t){return e?[...new Set([].concat(e,t))]:t}function Ut(e,t){return e?V(Object.create(null),e,t):t}function Ir(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:V(Object.create(null),Sr(e),Sr(t??{})):t}function id(e,t){if(!e)return t;if(!t)return e;const n=V(Object.create(null),e);for(const o in t)n[o]=se(e[o],t[o]);return n}function ad(e,t,n,o=!1){const s={},r={};e.propsDefaults=Object.create(null),uc(e,t,s,r);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);fc(t||{},s,e),n?e.props=o?s:cf(s):e.type.props?e.props=s:e.props=r,e.attrs=r}function cd(e,t,n,o){const{props:s,attrs:r,vnode:{patchFlag:i}}=e,a=j(s),[c]=e.propsOptions;let u=!1;if(!void 0&&(o||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let l=0;l<f.length;l++){let p=f[l];if(Za(e.emitsOptions,p))continue;const g=t[p];if(c)if(L(r,p))g!==r[p]&&(r[p]=g,u=!0);else{const y=xe(p);s[y]=Jo(c,a,y,g,e,!1)}else g!==r[p]&&(r[p]=g,u=!0)}}}else{uc(e,t,s,r)&&(u=!0);let f;for(const l in a)(!t||!L(t,l)&&((f=ot(l))===l||!L(t,f)))&&(c?n&&(n[l]!==void 0||n[f]!==void 0)&&(s[l]=Jo(c,a,l,void 0,e,!0)):delete s[l]);if(r!==a)for(const l in r)(!t||!L(t,l))&&(delete r[l],u=!0)}u&&Te(e,"set","$attrs"),fc(t||{},s,e)}function uc(e,t,n,o){const[s,r]=e.propsOptions;let i=!1,a;if(t)for(let c in t){if(Yi(c))continue;const u=t[c];let f;s&&L(s,f=xe(c))?!r||!r.includes(f)?n[f]=u:(a||(a={}))[f]=u:Za(e.emitsOptions,c)||(!(c in o)||u!==o[c])&&(o[c]=u,i=!0)}if(r){const c=j(n),u=a||J;for(let f=0;f<r.length;f++){const l=r[f];n[l]=Jo(s,c,l,u[l],e,!L(u,l))}}return i}function Jo(e,t,n,o,s,r){const i=e[n];if(i!=null){const a=L(i,"default");if(a&&o===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&O(c)){const{propsDefaults:u}=s;if(n in u)o=u[n];else{const f=sn(s);o=u[n]=c.call(null,t),f()}}else o=c}i[0]&&(r&&!a?o=!1:i[1]&&(o===""||o===ot(n))&&(o=!0))}return o}function lc(e,t,n=!1){const o=t.propsCache,s=o.get(e);if(s)return s;const r=e.props,i={},a=[];let c=!1;if(!O(e)){const f=l=>{c=!0;const[p,g]=lc(l,t,!0);V(i,p),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!r&&!c)return B(e)&&o.set(e,ur),ur;if(R(r))for(let f=0;f<r.length;f++){W(r[f])||k("props must be strings when using array syntax.",r[f]);const l=xe(r[f]);Ar(l)&&(i[l]=J)}else if(r){B(r)||k("invalid props options",r);for(const f in r){const l=xe(f);if(Ar(l)){const p=r[f],g=i[l]=R(p)||O(p)?{type:p}:V({},p);if(g){const y=Er(Boolean,g.type),h=Er(String,g.type);g[0]=y>-1,g[1]=h<0||y<h,(y>-1||L(g,"default"))&&a.push(l)}}}}const u=[i,a];return B(e)&&o.set(e,u),u}function Ar(e){return e[0]!=="$"&&!Yi(e)?!0:(k(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Go(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function Tr(e,t){return Go(e)===Go(t)}function Er(e,t){return R(t)?t.findIndex(n=>Tr(n,e)):O(t)&&Tr(t,e)?0:-1}function fc(e,t,n){const o=j(t),s=n.propsOptions[0];for(const r in s){let i=s[r];i!=null&&ud(r,o[r],i,gt(o),!L(e,r)&&!L(e,ot(r)))}}function ud(e,t,n,o,s){const{type:r,required:i,validator:a,skipCheck:c}=n;if(i&&s){k('Missing required prop: "'+e+'"');return}if(!(t==null&&!i)){if(r!=null&&r!==!0&&!c){let u=!1;const f=R(r)?r:[r],l=[];for(let p=0;p<f.length&&!u;p++){const{valid:g,expectedType:y}=fd(t,f[p]);l.push(y||""),u=g}if(!u){k(dd(e,t,l));return}}a&&!a(t,o)&&k('Invalid prop: custom validator check failed for prop "'+e+'".')}}const ld=Ct("String,Number,Boolean,Function,Symbol,BigInt");function fd(e,t){let n;const o=Go(t);if(ld(o)){const s=typeof e;n=s===o.toLowerCase(),!n&&s==="object"&&(n=e instanceof t)}else o==="Object"?n=B(e):o==="Array"?n=R(e):o==="null"?n=e===null:n=e instanceof t;return{valid:n,expectedType:o}}function dd(e,t,n){if(n.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let o=`Invalid prop: type check failed for prop "${e}". Expected ${n.map(st).join(" | ")}`;const s=n[0],r=Qn(t),i=Or(t,s),a=Or(t,r);return n.length===1&&Cr(s)&&!pd(s,r)&&(o+=` with value ${i}`),o+=`, got ${r} `,Cr(r)&&(o+=`with value ${a}.`),o}function Or(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function Cr(e){return["string","number","boolean"].some(n=>e.toLowerCase()===n)}function pd(...e){return e.some(t=>t.toLowerCase()==="boolean")}let Nt,De;function Dn(e,t){e.appContext.config.performance&&Un()&&De.mark(`vue-${t}-${e.uid}`),$f(e,t,Un()?De.now():Date.now())}function Mn(e,t){if(e.appContext.config.performance&&Un()){const n=`vue-${t}-${e.uid}`,o=n+":end";De.mark(o),De.measure(`<${io(e,e.type)}> ${t}`,n,o),De.clearMarks(n),De.clearMarks(o)}kf(e,t,Un()?De.now():Date.now())}function Un(){return Nt!==void 0||(typeof window<"u"&&window.performance?(Nt=!0,De=window.performance):Nt=!1),Nt}const $r=za,hd=Symbol.for("v-fgt"),gd=Symbol.for("v-txt"),md=Symbol.for("v-cmt"),yd=Symbol.for("v-stc");function _d(e){return e?e.__v_isVNode===!0:!1}const bd="__vInternal";function vd(e){return e?Da(e)||bd in e?V({},e):e:null}const wd=oc();let Sd=0;function xd(e,t,n){const o=e.type,s=(t?t.appContext:e.appContext)||wd,r={uid:Sd++,vnode:e,type:o,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new ba(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:lc(o,s),emitsOptions:Qa(o,s),emit:null,emitted:null,propsDefaults:J,inheritAttrs:o.inheritAttrs,ctx:J,data:J,props:J,attrs:J,slots:J,refs:J,setupState:J,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{},$eA:{}};return r.ctx=Qf(r),r.root=t?t.root:r,r.emit=Lf.bind(null,r),e.ce&&e.ce(r),r}let G=null;const Ce=()=>G||Ke;let Hn,Yo;Hn=e=>{G=e},Yo=e=>{ro=e};const sn=e=>{const t=G;return Hn(e),e.scope.on(),()=>{e.scope.off(),Hn(t)}},kr=()=>{G&&G.scope.off(),Hn(null)},Pd=Ct("slot,component");function Xo(e,{isNativeTag:t}){(Pd(e)||t(e))&&k("Do not use built-in or reserved HTML elements as component id: "+e)}function dc(e){return e.vnode.shapeFlag&4}let ro=!1;function Id(e,t=!1){t&&Yo(t);const{props:n}=e.vnode,o=dc(e);ad(e,n,o,t);const s=o?Ad(e,t):void 0;return t&&Yo(!1),s}function Ad(e,t){const n=e.type;{if(n.name&&Xo(n.name,e.appContext.config),n.components){const s=Object.keys(n.components);for(let r=0;r<s.length;r++)Xo(s[r],e.appContext.config)}if(n.directives){const s=Object.keys(n.directives);for(let r=0;r<s.length;r++)nc(s[r])}n.compilerOptions&&Od()&&k('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=Ae(new Proxy(e.ctx,ic)),Zf(e);const{setup:o}=n;if(o){const s=e.setupContext=o.length>1?kd(e):null,r=sn(e);ut();const i=Ee(o,e,0,[gt(e.props),s]);lt(),r(),en(i)?(i.then(kr,kr),k("setup() returned a Promise, but the version of Vue you are using does not support it yet.")):Td(e,i,t)}else pc(e,t)}function Td(e,t,n){O(t)?e.render=t:B(t)?(_d(t)&&k("setup() should not return VNodes directly - return a render function instead."),e.devtoolsRawSetupState=t,e.setupState=Ha(t),ed(e)):t!==void 0&&k(`setup() should return an object. Received: ${t===null?"null":typeof t}`),pc(e,n)}let Ed;const Od=()=>!Ed;function pc(e,t,n){const o=e.type;e.render||(e.render=o.render||Z);{const s=sn(e);ut();try{nd(e)}finally{lt(),s()}}!o.render&&e.render===Z&&!t&&(o.template?k('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):k("Component is missing template or render function."))}function Cd(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return ne(e,"get","$attrs"),t[n]},set(){return k("setupContext.attrs is readonly."),!1},deleteProperty(){return k("setupContext.attrs is readonly."),!1}}))}function $d(e){return e.slotsProxy||(e.slotsProxy=new Proxy(e.slots,{get(t,n){return ne(e,"get","$slots"),t[n]}}))}function kd(e){return Object.freeze({get attrs(){return Cd(e)},get slots(){return $d(e)},get emit(){return(n,...o)=>e.emit(n,...o)},expose:n=>{if(e.exposed&&k("expose() should be called only once per setup()."),n!=null){let o=typeof n;o==="object"&&(R(n)?o="array":z(n)&&(o="ref")),o!=="object"&&k(`expose() should be passed a plain object, received ${o}.`)}e.exposed=n||{}}})}function rn(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Ha(Ae(e.exposed)),{get(t,n){return n in t?t[n]:e.proxy[n]},has(t,n){return n in t||n in zt}}))}const Rd=/(?:^|[-_])(\w)/g,Ld=e=>e.replace(Rd,t=>t.toUpperCase()).replace(/[-_]/g,"");function Qs(e,t=!0){return O(e)?e.displayName||e.name:e.name||t&&e.__name}function io(e,t,n=!1){let o=Qs(t);if(!o&&t.__file){const s=t.__file.match(/([^/\\]+)\.\w+$/);s&&(o=s[1])}if(!o&&e&&e.parent){const s=r=>{for(const i in r)if(r[i]===t)return i};o=s(e.components||e.parent.type.components)||s(e.appContext.components)}return o?Ld(o):n?"App":"Anonymous"}const ao=(e,t)=>{const n=lf(e,t,ro);{const o=Ce();o&&o.appContext.config.warnRecursiveComputed&&(n._warnRecursive=!0)}return n},hc="3.4.21",co=k;function Fn(e){return Fs(e)}const Je="[object Array]",Ge="[object Object]";function gc(e,t){const n={};return Qo(e,t),An(e,t,"",n),n}function Qo(e,t){if(e=Fn(e),e===t)return;const n=ye(e),o=ye(t);if(n==Ge&&o==Ge)for(let s in t){const r=e[s];r===void 0?e[s]=null:Qo(r,t[s])}else n==Je&&o==Je&&e.length>=t.length&&t.forEach((s,r)=>{Qo(e[r],s)})}function An(e,t,n,o){if(e=Fn(e),e===t)return;const s=ye(e),r=ye(t);if(s==Ge)if(r!=Ge||Object.keys(e).length<Object.keys(t).length)Le(o,n,e);else for(let i in e){const a=Fn(e[i]),c=t[i],u=ye(a),f=ye(c);if(u!=Je&&u!=Ge)a!=c&&Le(o,(n==""?"":n+".")+i,a);else if(u==Je)f!=Je||a.length<c.length?Le(o,(n==""?"":n+".")+i,a):a.forEach((l,p)=>{An(l,c[p],(n==""?"":n+".")+i+"["+p+"]",o)});else if(u==Ge)if(f!=Ge||Object.keys(a).length<Object.keys(c).length)Le(o,(n==""?"":n+".")+i,a);else for(let l in a)An(a[l],c[l],(n==""?"":n+".")+i+"."+l,o)}else s==Je?r!=Je||e.length<t.length?Le(o,n,e):e.forEach((i,a)=>{An(i,t[a],n+"["+a+"]",o)}):Le(o,n,e)}function Le(e,t,n){e[t]=n}function Nd(e){return Q.includes(e.update)}function Rr(e){const n=e.ctx.__next_tick_callbacks;if(n&&n.length){const o=n.slice(0);n.length=0;for(let s=0;s<o.length;s++)o[s]()}}function Zo(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!Nd(e))return Rn(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push(()=>{t?Ee(t.bind(e.proxy),e,14):o&&o(e.proxy)}),new Promise(s=>{o=s})}function es(e,t){e=Fn(e);const n=typeof e;if(n==="object"&&e!==null){let o=t.get(e);if(typeof o<"u")return o;if(R(e)){const s=e.length;o=new Array(s),t.set(e,o);for(let r=0;r<s;r++)o[r]=es(e[r],t)}else{o={},t.set(e,o);for(const s in e)L(e,s)&&(o[s]=es(e[s],t))}return o}if(n!=="symbol")return e}function Dd(e){return es(e,typeof WeakMap<"u"?new WeakMap:new Map)}function Md(e,t){const n=e.data,o=Object.create(null);return t.forEach(s=>{o[s]=n[s]}),o}function Lr(e,t,n){if(!t)return;t=Dd(t),t.$eS=e.$eS||{},t.$eA=e.$eA||{};const o=e.ctx,s=o.mpType;if(s==="page"||s==="component"){t.r0=1;const r=o.$scope,i=Object.keys(t),a=gc(t,n||Md(r,i));Object.keys(a).length?(o.__next_tick_pending=!0,r.setData(a,()=>{o.__next_tick_pending=!1,Rr(e)}),Ja()):Rr(e)}}function Ud(e){e.globalProperties.$nextTick=function(n){return Zo(this.$,n)}}function Hd(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const s=Object.keys(o);if(s.length){const r=t.ctx;r.$computedKeys||(r.$computedKeys=[]),r.$computedKeys.push(...s)}}delete t.ctx.$onApplyOptions}function mc(e,t=!1){const{setupState:n,$templateRefs:o,$templateUniElementRefs:s,ctx:{$scope:r,$mpPlatform:i}}=e;if(i==="mp-alipay"||!r||!o&&!s)return;if(t){o&&o.forEach(f=>jt(f,null,n)),s&&s.forEach(f=>jt(f,null,n));return}const a=i==="mp-baidu"||i==="mp-toutiao",c=f=>{if(f.length===0)return[];const l=(r.selectAllComponents(".r")||[]).concat(r.selectAllComponents(".r-i-f")||[]);return f.filter(p=>{const g=qd(l,p.i);return a&&g===null?!0:(jt(p,g,n),!1)})},u=()=>{if(o){const f=c(o);f.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},()=>{c(f)})}};s&&s.length&&Zo(e,()=>{s.forEach(f=>{R(f.v)?f.v.forEach(l=>{jt(f,l,n)}):jt(f,f.v,n)})}),r._$setRef?r._$setRef(u):Zo(e,u)}function Fd(e){return B(e)&&Ae(e),e}function qd(e,t){const n=e.find(o=>o&&(o.properties||o.props).uI===t);if(n){const o=n.$vm;return o?rn(o.$)||o:Fd(n)}return null}function jt({r:e,f:t},n,o){if(O(e))e(n,{});else{const s=W(e),r=z(e);if(s||r)if(t){if(!r)return;R(e.value)||(e.value=[]);const i=e.value;if(i.indexOf(n)===-1){if(i.push(n),!n)return;n.$&&Gs(()=>Xn(i,n),n.$)}}else s?L(o,e)&&(o[e]=n):z(e)?e.value=n:Nr(e);else Nr(e)}}function Nr(e){co("Invalid template ref type:",e,`(${typeof e})`)}const ts=za;function jr(e,t){const n=e.component=xd(e,t.parentComponent,null);return n.ctx.$onApplyOptions=Hd,n.ctx.$children=[],t.mpType==="app"&&(n.render=Z),t.onBeforeSetup&&t.onBeforeSetup(n,t),qs(e),Dn(n,"mount"),Dn(n,"init"),Id(n),Mn(n,"init"),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(rn(n)||n.proxy),Wd(n),Bs(),Mn(n,"mount"),n.proxy}const Bd=e=>{let t;for(const n in e)(n==="class"||n==="style"||zi(n))&&((t||(t={}))[n]=e[n]);return t};function Dr(e){const{type:t,vnode:n,proxy:o,withProxy:s,props:r,propsOptions:[i],slots:a,attrs:c,emit:u,render:f,renderCache:l,data:p,setupState:g,ctx:y,uid:h,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:d}}}},inheritAttrs:m}=e;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,d(h),e.__counter=e.__counter===0?1:0;let b;const _=Nn(e);try{if(n.shapeFlag&4){Mr(m,r,i,c);const v=s||o;b=f.call(v,v,l,r,g,p,y)}else{Mr(m,r,i,t.props?c:Bd(c));const v=t;b=v.length>1?v(r,{attrs:c,slots:a,emit:u}):v(r,null)}}catch(v){oo(v,e,1),b=!1}return mc(e),Nn(_),b}function Mr(e,t,n,o){if(t&&o&&e!==!1){const s=Object.keys(o).filter(r=>r!=="class"&&r!=="style");if(!s.length)return;n&&s.some(lr)?s.forEach(r=>{(!lr(r)||!(r.slice(9)in n))&&(t[r]=o[r])}):s.forEach(r=>t[r]=o[r])}}const Vd=e=>{ut(),Ja(),lt()};function Kd(){const e=this.$scopedSlotsData;if(!e||e.length===0)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach(({path:s,index:r,data:i})=>{const a=Ns(n,s),c=W(r)?`${s}.${r}`:`${s}[${r}]`;if(typeof a>"u"||typeof a[r]>"u")o[c]=i;else{const u=gc(i,a[r]);Object.keys(u).forEach(f=>{o[c+"."+f]=u[f]})}}),e.length=0,Object.keys(o).length&&t.setData(o)}function mo({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Wd(e){const t=Kd.bind(e);e.$updateScopedSlots=()=>Rn(()=>Ln(t));const n=()=>{if(!e.isMounted)Gs(()=>{mc(e,!0)},e),Dn(e,"patch"),Lr(e,Dr(e)),Mn(e,"patch"),Tf(e);else{const{next:r,bu:i,u:a}=e;qs(r||e.vnode),mo(e,!1),Vd(),i&&xn(i),mo(e,!0),Dn(e,"patch"),Lr(e,Dr(e)),Mn(e,"patch"),a&&ts(a),Ef(e),Bs()}},o=e.effect=new js(n,Z,()=>Ln(s),e.scope),s=e.update=()=>{o.dirty&&o.run()};s.id=e.uid,mo(e,!0),o.onTrack=e.rtc?r=>xn(e.rtc,r):void 0,o.onTrigger=e.rtg?r=>xn(e.rtg,r):void 0,s.ownerInstance=e,s()}function zd(e){const{bum:t,scope:n,update:o,um:s}=e;t&&xn(t);{const r=e.parent;if(r){const i=r.ctx.$children,a=rn(e)||e.proxy,c=i.indexOf(a);c>-1&&i.splice(c,1)}}n.stop(),o&&(o.active=!1),s&&ts(s),ts(()=>{e.isUnmounted=!0}),Cf(e)}const Jd=Uf();function Gd(){if(typeof window<"u")return window;if(typeof globalThis<"u")return globalThis;if(typeof global<"u")return global;if(typeof my<"u")return my}function Yd(e,t=null){const n=Gd();n.__VUE__=!0,Ya(n.__VUE_DEVTOOLS_GLOBAL_HOOK__,n);const o=Jd(e,t),s=o._context;Ud(s.config);const r=c=>(c.appContext=s,c.shapeFlag=6,c),i=function(u,f){return jr(r(u),f)},a=function(u){return u&&zd(u.$)};return o.mount=function(){e.render=Z;const u=jr(r({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return o._instance=u.$,Af(o,hc),u.$app=o,u.$createComponent=i,u.$destroyComponent=a,s.$appInstance=u,u},o.unmount=function(){co("Cannot unmount an app.")},o}function Xd(e){const t=Ce();if(!t){co("useCssVars is called without current active component instance.");return}Qd(t,e)}function Qd(e,t){e.ctx.__cssVars=()=>{const n=t(e.proxy),o={};for(const s in n)o[`--${s}`]=n[s];return o}}function Ur(e,t,n,o){O(t)&&We(e,t.bind(n),o)}function Zd(e,t,n){const o=e.mpType||n.$mpType;!o||o==="component"||Object.keys(e).forEach(s=>{if(ma(s,e[s],!1)){const r=e[s];R(r)?r.forEach(i=>Ur(s,i,n,t)):Ur(s,r,n,t)}})}function ep(e,t,n){Zd(e,t,n)}function tp(e,t,n){return e[t]=n}function np(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function op(e){const t=e.config.errorHandler;return function(o,s,r){t&&t(o,s,r);const i=e._instance;if(!i||!i.proxy)throw o;i[rt]?i.proxy.$callHook(rt,o):Va(o,r,s?s.$.vnode:null,!1)}}function sp(e,t){return e?[...new Set([].concat(e,t))]:t}function rp(e){ga.forEach(t=>{e[t]=sp})}let ns;const yn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ip=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;typeof atob!="function"?ns=function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ip.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");e+="==".slice(2-(e.length&3));for(var t,n="",o,s,r=0;r<e.length;)t=yn.indexOf(e.charAt(r++))<<18|yn.indexOf(e.charAt(r++))<<12|(o=yn.indexOf(e.charAt(r++)))<<6|(s=yn.indexOf(e.charAt(r++))),n+=o===64?String.fromCharCode(t>>16&255):s===64?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,t&255);return n}:ns=atob;function ap(e){return decodeURIComponent(ns(e).split("").map(function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)}).join(""))}function yo(){const e=N.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||t.length!==3)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse(ap(t[1]))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}return n.tokenExpired=n.exp*1e3,delete n.exp,delete n.iat,n}function cp(e){e.uniIDHasRole=function(t){const{role:n}=yo();return n.indexOf(t)>-1},e.uniIDHasPermission=function(t){const{permission:n}=yo();return this.uniIDHasRole("admin")||n.indexOf(t)>-1},e.uniIDTokenValid=function(){const{tokenExpired:t}=yo();return t>Date.now()}}function up(e){const t=e.config;t.errorHandler=Dl(e,op),rp(t.optionMergeStrategies);const n=t.globalProperties;cp(n),n.$set=tp,n.$applyOptions=ep,n.$callMethod=np,N.invokeCreateVueAppHook(e)}const Jt=Object.create(null);function lp(e){const{uid:t,__counter:n}=Ce(),o=(Jt[t]||(Jt[t]=[])).push(vd(e))-1;return t+","+o+","+n}function yc(e){delete Jt[e]}function Zs(e){if(!e)return;const[t,n]=e.split(",");if(Jt[t])return Jt[t][parseInt(n)]}var fp={install(e){up(e),e.config.globalProperties.pruneComponentPropsCache=yc;const t=e.mount;e.mount=function(o){const s=t.call(e,o),r=dp();return r?r(s):typeof createMiniProgramApp<"u"&&createMiniProgramApp(s),s}}};function dp(){const e="createApp";if(typeof global<"u"&&typeof global[e]<"u")return global[e];if(typeof my<"u")return my[e]}function pp(e){return W(e)?e:hp(Xi(e))}function hp(e){let t="";if(!e||W(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:ot(n)}:${e[n]};`;return t}function gp(e,t){const n=Ce(),o=n.ctx,s=typeof t<"u"&&(o.$mpPlatform==="mp-weixin"||o.$mpPlatform==="mp-qq"||o.$mpPlatform==="mp-xhs")&&(W(t)||typeof t=="number")?"_"+t:"",r="e"+n.$ei+++s,i=o.$scope;if(!e)return delete i[r],r;const a=i[r];return a?a.value=e:i[r]=mp(e,n),r}function mp(e,t){const n=o=>{_p(o);let s=[o];t&&t.ctx.$getTriggerEventDetail&&typeof o.detail=="number"&&(o.detail=t.ctx.$getTriggerEventDetail(o.detail)),o.detail&&o.detail.__args__&&(s=o.detail.__args__);const r=n.value,i=()=>Ve(bp(o,r),t,5,s),a=o.target,c=a&&a.dataset?String(a.dataset.eventsync)==="true":!1;if(yp.includes(o.type)&&!c)setTimeout(i);else{const u=i();return o.type==="input"&&(R(u)||en(u))?void 0:u}};return n.value=e,n}const yp=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function _p(e,t){e.type&&e.target&&(e.preventDefault=Z,e.stopPropagation=Z,e.stopImmediatePropagation=Z,L(e,"detail")||(e.detail={}),L(e,"markerId")&&(e.detail=typeof e.detail=="object"?e.detail:{},e.detail.markerId=e.markerId),ee(e.detail)&&L(e.detail,"checked")&&!L(e.detail,"value")&&(e.detail.value=e.detail.checked),ee(e.detail)&&(e.target=V({},e.target,e.detail)))}function bp(e,t){if(R(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map(o=>s=>!s._stopped&&o(s))}else return t}function vp(e,t){let n;if(R(e)||W(e)){n=new Array(e.length);for(let o=0,s=e.length;o<s;o++)n[o]=t(e[o],o,o)}else if(typeof e=="number"){if(!Number.isInteger(e))return co(`The v-for range expect an integer value but got ${e}.`),[];n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(B(e))if(e[Symbol.iterator])n=Array.from(e,(o,s)=>t(o,s,s));else{const o=Object.keys(e);n=new Array(o.length);for(let s=0,r=o.length;s<r;s++){const i=o[s];n[s]=t(e[i],i,s)}}else n=[];return n}function _c(e,t={},n){const o=Ce(),{parent:s,isMounted:r,ctx:{$scope:i}}=o,a=(i.properties||i.props).uI;if(!a)return;if(!s&&!r){Js(()=>{_c(e,t,n)},o);return}const c=wp(a,o);c&&c(e,t,n)}function wp(e,t){let n=t.parent;for(;n;){const o=n.$ssi;if(o&&o[e])return o[e];n=n.parent}}function Sp(e,{name:t,path:n,vueId:o}){const s=Ce();e.path=n;const r=s.$ssi||(s.$ssi={}),i=r[o]||(r[o]=xp(s));return i.slots[t]?i.slots[t].fn=e:i.slots[t]={fn:e},Ns(s.ctx.$scope.data,n)}function xp(e){const t=(n,o,s)=>{const r=t.slots[n];if(!r)return;const i=typeof s<"u";s=s||0;const a=Nn(e),c=r.fn(o,n+(i?"-"+s:""),s),u=r.fn.path;Nn(a),(e.$scopedSlotsData||(e.$scopedSlotsData=[])).push({path:u,index:s,data:c}),e.$updateScopedSlots()};return t.slots={},t}function Pp(e,t,n={}){const{$templateRefs:o}=Ce();o.push({i:t,r:e,k:n.k,f:n.f})}const Ip=(e,t)=>gp(e,t),Ap=(e,t)=>vp(e,t),Tp=(e,t,n)=>_c(e,t,n),Ep=(e,t)=>Sp(e,t),Op=e=>pp(e),Cp=(e,...t)=>V(e,...t),$p=e=>Qi(e),kp=e=>ol(e),Rp=e=>lp(e),Lp=(e,t,n)=>Pp(e,t,n);function Np(e,t=null){return e&&(e.mpType="app"),Yd(e,t).use(fp)}const jp=Np;function Dp(){var e;let t="";{const n=((e=wx.getAppBaseInfo)===null||e===void 0?void 0:e.call(wx))||wx.getSystemInfoSync(),o=n&&n.language?n.language:ve;t=Cn(o)||ve}return t}function Mp(e,t){console.warn(`${e}: ${t}`)}function Hr(e,t,n,o){o||(o=Mp);for(const s in n){const r=Hp(s,t[s],n[s],!L(t,s));W(r)&&o(e,r)}}function Up(e,t,n,o){if(!n)return;if(!R(n))return Hr(e,t[0]||Object.create(null),n,o);const s=n.length,r=t.length;for(let i=0;i<s;i++){const a=n[i],c=Object.create(null);r>i&&(c[a.name]=t[i]),Hr(e,c,{[a.name]:a},o)}}function Hp(e,t,n,o){ee(n)||(n={type:n});const{type:s,required:r,validator:i}=n;if(r&&o)return'Missing required args: "'+e+'"';if(!(t==null&&!r)){if(s!=null){let a=!1;const c=R(s)?s:[s],u=[];for(let f=0;f<c.length&&!a;f++){const{valid:l,expectedType:p}=qp(t,c[f]);u.push(p||""),a=l}if(!a)return Bp(e,t,u)}if(i)return i(t)}}const Fp=Ct("String,Number,Boolean,Function,Symbol");function qp(e,t){let n;const o=Vp(t);if(Fp(o)){const s=typeof e;n=s===o.toLowerCase(),!n&&s==="object"&&(n=e instanceof t)}else o==="Object"?n=B(e):o==="Array"?n=R(e):n=e instanceof t;return{valid:n,expectedType:o}}function Bp(e,t,n){let o=`Invalid args: type check failed for args "${e}". Expected ${n.map(st).join(", ")}`;const s=n[0],r=Qn(t),i=Fr(t,s),a=Fr(t,r);return n.length===1&&qr(s)&&!Kp(s,r)&&(o+=` with value ${i}`),o+=`, got ${r} `,qr(r)&&(o+=`with value ${a}.`),o}function Vp(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:""}function Fr(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function qr(e){return["string","number","boolean"].some(n=>e.toLowerCase()===n)}function Kp(...e){return e.some(t=>t.toLowerCase()==="boolean")}function Wp(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let zp=1;const os={};function Jp(e,t,n,o=!1){return os[e]={name:t,keepAlive:o,callback:n},e}function bc(e,t,n){if(typeof e=="number"){const o=os[e];if(o)return o.keepAlive||delete os[e],o.callback(t,n)}return t}const Gp="success",Yp="fail",Xp="complete";function Qp(e){const t={};for(const n in e){const o=e[n];O(o)&&(t[n]=Wp(o),delete e[n])}return t}function Zp(e,t){return!e||e.indexOf(":fail")===-1?t+":ok":t+e.substring(e.indexOf(":fail"))}function eh(e,t={},{beforeAll:n,beforeSuccess:o}={}){ee(t)||(t={});const{success:s,fail:r,complete:i}=Qp(t),a=O(s),c=O(r),u=O(i),f=zp++;return Jp(f,e,l=>{l=l||{},l.errMsg=Zp(l.errMsg,e),O(n)&&n(l),l.errMsg===e+":ok"?(O(o)&&o(l,t),a&&s(l)):c&&r(l),u&&i(l)}),f}const th="success",nh="fail",oh="complete",xt={},Pt={};function sh(e,t){return function(n){return e(n,t)||n}}function vc(e,t,n){let o=!1;for(let s=0;s<e.length;s++){const r=e[s];if(o)o=Promise.resolve(sh(r,n));else{const i=r(t,n);if(en(i)&&(o=Promise.resolve(i)),i===!1)return{then(){},catch(){}}}}return o||{then(s){return s(t)},catch(){}}}function Br(e,t={}){return[th,nh,oh].forEach(n=>{const o=e[n];if(!R(o))return;const s=t[n];t[n]=function(i){vc(o,i,t).then(a=>O(s)&&s(a)||a)}}),t}function qn(e,t){const n=[];R(xt.returnValue)&&n.push(...xt.returnValue);const o=Pt[e];return o&&R(o.returnValue)&&n.push(...o.returnValue),n.forEach(s=>{t=s(t)||t}),t}function Vr(e){const t=Object.create(null);Object.keys(xt).forEach(o=>{o!=="returnValue"&&(t[o]=xt[o].slice())});const n=Pt[e];return n&&Object.keys(n).forEach(o=>{o!=="returnValue"&&(t[o]=(t[o]||[]).concat(n[o]))}),t}function Bn(e,t,n,o){const s=Vr(e);return s&&Object.keys(s).length?R(s.invoke)?vc(s.invoke,n).then(i=>t(Br(Vr(e),i),...o)):t(Br(s,n),...o):t(n,...o)}function rh(e){return!!(ee(e)&&[Gp,Yp,Xp].find(t=>O(e[t])))}function ih(e,t){return(n={},...o)=>rh(n)?qn(e,Bn(e,t,n,o)):qn(e,new Promise((s,r)=>{Bn(e,t,V(n,{success:s,fail:r}),o)}))}function ah(e,t){e[0]}function ch(e,t,n){const o={errMsg:t+":ok"};return bc(e,V(n||{},o))}function Kr(e,t,n,o={}){const s=t+":fail";let r="";n?n.indexOf(s)===0?r=n:r=s+" "+n:r=s,delete o.errCode;let i=V({errMsg:r},o);return bc(e,i)}function wc(e,t,n,o){Up(e,t,n);const s=ah(t);if(s)return s}function uh(e){return!e||W(e)?e:e.stack?((typeof globalThis>"u"||!globalThis.harmonyChannel)&&console.error(e.message+`
`+e.stack),e.message):e}function lh(e,t,n,o){return s=>{const r=eh(e,s,o),i=wc(e,[s],n);return i?Kr(r,e,i):t(s,{resolve:a=>ch(r,e,a),reject:(a,c)=>Kr(r,e,uh(a),c)})}}function fh(e,t,n,o){return(...s)=>{const r=wc(e,s,n);if(r)throw new Error(r);return t.apply(null,s)}}function dh(e,t,n,o){return lh(e,t,n,o)}function ft(e,t,n,o){return fh(e,t,n)}function ph(e,t,n,o){return ih(e,dh(e,t,n,o))}const hh="upx2px",gh=[{name:"upx",type:[Number,String],required:!0}],mh=1e-4,yh=750;let Sc=!1,ss=0,xc=0;function _h(){var e,t;let n,o,s;{const r=((e=wx.getWindowInfo)===null||e===void 0?void 0:e.call(wx))||wx.getSystemInfoSync(),i=((t=wx.getDeviceInfo)===null||t===void 0?void 0:t.call(wx))||wx.getSystemInfoSync();n=r.windowWidth,o=r.pixelRatio,s=i.platform}ss=n,xc=o,Sc=s==="ios"}const Wr=ft(hh,(e,t)=>{if(ss===0&&_h(),e=Number(e),e===0)return 0;let n=t||ss,o=e/yh*n;return o<0&&(o=-o),o=Math.floor(o+mh),o===0&&(xc===1||!Sc?o=1:o=.5),e<0?-o:o},gh);function bh(e,t,...n){t&&n.push(t),console[e].apply(console,n)}const vh="addInterceptor",wh="removeInterceptor",Pc=[{name:"method",type:[String,Object],required:!0}],Sh=Pc;function zr(e,t){Object.keys(t).forEach(n=>{O(t[n])&&(e[n]=xh(e[n],t[n]))})}function Jr(e,t){!e||!t||Object.keys(t).forEach(n=>{const o=e[n],s=t[n];R(o)&&O(s)&&Xn(o,s)})}function xh(e,t){const n=t?e?e.concat(t):R(t)?t:[t]:e;return n&&Ph(n)}function Ph(e){const t=[];for(let n=0;n<e.length;n++)t.indexOf(e[n])===-1&&t.push(e[n]);return t}const Ih=ft(vh,(e,t)=>{W(e)&&ee(t)?zr(Pt[e]||(Pt[e]={}),t):ee(e)&&zr(xt,e)},Pc),Ah=ft(wh,(e,t)=>{W(e)?ee(t)?Jr(Pt[e],t):delete Pt[e]:ee(e)&&Jr(xt,e)},Sh),Th={},Eh="$on",Ic=[{name:"event",type:String,required:!0},{name:"callback",type:Function,required:!0}],Oh="$once",Ch=Ic,$h="$off",kh=[{name:"event",type:[String,Array]},{name:"callback",type:[Function,Number]}],Rh="$emit",Lh=[{name:"event",type:String,required:!0}];class Nh{constructor(){this.$emitter=new Ml}on(t,n){return this.$emitter.on(t,n)}once(t,n){return this.$emitter.once(t,n)}off(t,n){if(!t){this.$emitter.e={};return}this.$emitter.off(t,n)}emit(t,...n){this.$emitter.emit(t,...n)}}const It=new Nh,jh=ft(Eh,(e,t)=>(It.on(e,t),()=>It.off(e,t)),Ic),Dh=ft(Oh,(e,t)=>(It.once(e,t),()=>It.off(e,t)),Ch),Mh=ft($h,(e,t)=>{R(e)||(e=e?[e]:[]),e.forEach(n=>{It.off(n,t)})},kh),Uh=ft(Rh,(e,...t)=>{It.emit(e,...t)},Lh);let qt,rs,is;function Gr(e){try{return JSON.parse(e)}catch{}return e}function Hh(e){if(e.type==="enabled")is=!0;else if(e.type==="clientId")qt=e.cid,rs=e.errMsg,Ac(qt,e.errMsg);else if(e.type==="pushMsg"){const t={type:"receive",data:Gr(e.message)};for(let n=0;n<He.length;n++){const o=He[n];if(o(t),t.stopped)break}}else e.type==="click"&&He.forEach(t=>{t({type:"click",data:Gr(e.message)})})}const as=[];function Ac(e,t){as.forEach(n=>{n(e,t)}),as.length=0}const Fh="getPushClientId",qh=ph(Fh,(e,{resolve:t,reject:n})=>{Promise.resolve().then(()=>{typeof is>"u"&&(is=!1,qt="",rs="uniPush is not enabled"),as.push((o,s)=>{o?t({cid:o}):n(s)}),typeof qt<"u"&&Ac(qt,rs)})}),He=[],Bh=e=>{He.indexOf(e)===-1&&He.push(e)},Vh=e=>{if(!e)He.length=0;else{const t=He.indexOf(e);t>-1&&He.splice(t,1)}},Kh=/^\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,Wh=/^create|Manager$/,zh=["createBLEConnection"],Jh=["request","downloadFile","uploadFile","connectSocket"],Gh=["createBLEConnection"],Yh=/^on|^off/;function Tn(e){return Wh.test(e)&&zh.indexOf(e)===-1}function Tc(e){return Kh.test(e)&&Gh.indexOf(e)===-1}function Xh(e){return Yh.test(e)&&e!=="onPush"}function Yr(e){return Jh.indexOf(e)!==-1}function Qh(e){return!(Tn(e)||Tc(e)||Xh(e))}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then(n=>t.resolve(e&&e()).then(()=>n),n=>t.resolve(e&&e()).then(()=>{throw n}))});function _o(e,t){return!Qh(e)||!O(t)?t:function(o={},...s){return O(o.success)||O(o.fail)||O(o.complete)?qn(e,Bn(e,t,o,s)):qn(e,new Promise((r,i)=>{Bn(e,t,V({},o,{success:r,fail:i}),s)}))}}const Zh=["success","fail","cancel","complete"];function eg(e){function t(s,r,i){return function(a){return r(o(s,a,i))}}function n(s,r,i={},a={},c=!1){if(ee(r)){const u=c===!0?r:{};O(i)&&(i=i(r,u)||{});for(const f in r)if(L(i,f)){let l=i[f];O(l)&&(l=l(r[f],r,u)),l?W(l)?u[l]=r[f]:ee(l)&&(u[l.name?l.name:f]=l.value):console.warn(`微信小程序 ${s} 暂不支持 ${f}`)}else if(Zh.indexOf(f)!==-1){const l=r[f];O(l)&&(u[f]=t(s,l,a))}else!c&&!L(u,f)&&(u[f]=r[f]);return u}else O(r)&&(O(i)&&i(r,{}),r=t(s,r,a));return r}function o(s,r,i,a=!1){return O(e.returnValue)&&(r=e.returnValue(s,r)),n(s,r,i,{},a||!1)}return function(r,i){const a=L(e,r);if(!a&&typeof wx[r]!="function")return i;const c=a||O(e.returnValue)||Tn(r)||Yr(r),u=a||O(i);if(!a&&!i)return function(){console.error(`微信小程序 暂不支持${r}`)};if(!c||!u)return i;const f=e[r];return function(l,p){let g=f||{};O(f)&&(g=f(l)),l=n(r,l,g.args,g.returnValue);const y=[l];typeof p<"u"&&y.push(p);const h=wx[g.name||r].apply(wx,y);return(Tn(r)||Yr(r))&&h&&!h.__v_skip&&(h.__v_skip=!0),Tc(r)?o(r,h,g.returnValue,Tn(r)):h}}}const Vn=()=>{const e=O(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:Dp()},tg=e=>{const t=O(getApp)&&getApp();return t&&t.$vm.$locale!==e?(t.$vm.$locale=e,cs.forEach(o=>o({locale:e})),!0):!1},cs=[],ng=e=>{cs.indexOf(e)===-1&&cs.push(e)};typeof global<"u"&&(global.getLocale=Vn);const Xr="__DC_STAT_UUID";let pt;function Ec(e=wx){return function(n,o){pt=pt||e.getStorageSync(Xr),pt||(pt=Date.now()+""+Math.floor(Math.random()*1e7),wx.setStorage({key:Xr,data:pt})),o.deviceId=pt}}function Oc(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function Cc(e,t){let n="",o="";switch(n=e.split(" ")[0]||t,o=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows";break}return{osName:n,osVersion:o}}function og(e,t){const{brand:n="",model:o="",system:s="",language:r="",theme:i,version:a,platform:c,fontSizeSetting:u,SDKVersion:f,pixelRatio:l,deviceOrientation:p}=e,{osName:g,osVersion:y}=Cc(s,c);let h=a,d=$c(e,o),m=kc(n),b=Lc(e),_=p,v=l,x=f;const w=(r||"").replace(/_/g,"-"),A={appId:"__UNI__A07BE01",appName:"bani",appVersion:"1.0.0",appVersionCode:"100",appLanguage:Rc(w),uniCompileVersion:"4.66",uniCompilerVersion:"4.66",uniRuntimeVersion:"4.66",uniPlatform:"mp-weixin",deviceBrand:m,deviceModel:o,deviceType:d,devicePixelRatio:v,deviceOrientation:_,osName:g,osVersion:y,hostTheme:i,hostVersion:h,hostLanguage:w,hostName:b,hostSDKVersion:x,hostFontSizeSetting:u,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};V(t,A)}function $c(e,t){let n=e.deviceType||"phone";{const o={ipad:"pad",windows:"pc",mac:"pc"},s=Object.keys(o),r=t.toLocaleLowerCase();for(let i=0;i<s.length;i++){const a=s[i];if(r.indexOf(a)!==-1){n=o[a];break}}}return n}function kc(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function Rc(e){return Vn?Vn():e}function Lc(e){let n=e.hostName||"WeChat";return e.environment?n=e.environment:e.host&&e.host.env&&(n=e.host.env),n}const Nc={returnValue:(e,t)=>{Oc(e,t),Ec()(e,t),og(e,t)}},sg=Nc,rg={},ig={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!R(o))return;const s=o.length;if(s)return n<0?n=0:n>=s&&(n=s-1),n>0?(t.current=o[n],t.urls=o.filter((r,i)=>i<n?r!==o[n]:!0)):t.current=o[0],{indicator:!1,loop:!1}}},ag={args(e,t){t.alertText=e.title}},cg={returnValue:(e,t)=>{const{brand:n,model:o,system:s="",platform:r=""}=e;let i=$c(e,o),a=kc(n);Ec()(e,t);const{osName:c,osVersion:u}=Cc(s,r);t=ha(V(t,{deviceType:i,deviceBrand:a,deviceModel:o,osName:c,osVersion:u}))}},ug={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:s,theme:r}=e;let i=Lc(e),a=(o||"").replace(/_/g,"-");const c={hostVersion:n,hostLanguage:a,hostName:i,hostSDKVersion:s,hostTheme:r,appId:"__UNI__A07BE01",appName:"bani",appVersion:"1.0.0",appVersionCode:"100",appLanguage:Rc(a),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.66",uniCompilerVersion:"4.66",uniRuntimeVersion:"4.66"};V(t,c)}},lg={returnValue:(e,t)=>{Oc(e,t),t=ha(V(t,{windowTop:0,windowBottom:0}))}},fg={returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",n===!0?t.locationAccuracy="reduced":n===!1&&(t.locationAccuracy="full")}},dg={args(e){const t=getApp({allowDefault:!0})||{};t.$vm?We(rt,e,t.$vm.$):(wx.$onErrorHandlers||(wx.$onErrorHandlers=[]),wx.$onErrorHandlers.push(e))}},pg={args(e){const t=getApp({allowDefault:!0})||{};if(t.$vm){if(e.__weh){const n=t.$vm.$[rt];if(n){const o=n.indexOf(e.__weh);o>-1&&n.splice(o,1)}}}else{if(!wx.$onErrorHandlers)return;const n=wx.$onErrorHandlers.findIndex(o=>o===e);n!==-1&&wx.$onErrorHandlers.splice(n,1)}}},jc={args(){if(wx.__uni_console__){if(wx.__uni_console_warned__)return;wx.__uni_console_warned__=!0,console.warn("开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)")}}},hg=jc,Qr={$on:jh,$off:Mh,$once:Dh,$emit:Uh,upx2px:Wr,rpx2px:Wr,interceptors:Th,addInterceptor:Ih,removeInterceptor:Ah,onCreateVueApp:Nl,invokeCreateVueAppHook:jl,getLocale:Vn,setLocale:tg,onLocaleChange:ng,getPushClientId:qh,onPushMessage:Bh,offPushMessage:Vh,invokePushCallback:Hh,__f__:bh};function gg(e,t,n=wx){const o=eg(t),s={get(r,i){return L(r,i)?r[i]:L(e,i)?_o(i,e[i]):L(Qr,i)?_o(i,Qr[i]):_o(i,o(i,n[i]))}};return new Proxy({},s)}function mg(e){return function({service:n,success:o,fail:s,complete:r}){let i;e[n]?(i={errMsg:"getProvider:ok",service:n,provider:e[n]},O(o)&&o(i)):(i={errMsg:"getProvider:fail:服务["+n+"]不存在"},O(s)&&s(i)),O(r)&&r(i)}}const yg=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],_g=["lanDebug","router","worklet"],Zr=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function bg(e){return Zr&&Zr.scene===1154&&_g.includes(e)?!1:yg.indexOf(e)>-1||typeof wx[e]=="function"}function Dc(){const e={};for(const t in wx)bg(t)&&(e[t]=wx[t]);return typeof globalThis<"u"&&typeof requireMiniProgram>"u"&&(globalThis.wx=e),e}const vg=["__route__","__wxExparserNodeId__","__wxWebviewId__"],wg=mg({oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]});function Sg(e){const t=Object.create(null);return vg.forEach(n=>{t[n]=e[n]}),t}function xg(){const e=re.createSelectorQuery(),t=e.in;return e.in=function(o){return o.$scope?t.call(this,o.$scope):t.call(this,Sg(o))},e}const re=Dc();re.canIUse("getAppBaseInfo")||(re.getAppBaseInfo=re.getSystemInfoSync);re.canIUse("getWindowInfo")||(re.getWindowInfo=re.getSystemInfoSync);re.canIUse("getDeviceInfo")||(re.getDeviceInfo=re.getSystemInfoSync);let Kn=re.getAppBaseInfo&&re.getAppBaseInfo();Kn||(Kn=re.getSystemInfoSync());const ei=Kn?Kn.host:null,Pg=ei&&ei.env==="SAAASDK"?re.miniapp.shareVideoMessage:re.shareVideoMessage;var Ig=Object.freeze({__proto__:null,createSelectorQuery:xg,getProvider:wg,shareVideoMessage:Pg});const Ag={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var Tg=Object.freeze({__proto__:null,compressImage:Ag,getAppAuthorizeSetting:fg,getAppBaseInfo:ug,getDeviceInfo:cg,getSystemInfo:Nc,getSystemInfoSync:sg,getWindowInfo:lg,offError:pg,onError:dg,onSocketMessage:hg,onSocketOpen:jc,previewImage:ig,redirectTo:rg,showActionSheet:ag});const vt=Dc();var N=gg(Ig,Tg,vt);function Eg(e,t,n){return e==""||t==""||n==""?Promise.resolve(null):e.split(",").reduce((o,s)=>o.then(r=>r!=null?Promise.resolve(r):Cg(s,t,n)),Promise.resolve(null))}const Og=500;function Cg(e,t,n){return new Promise((o,s)=>{const r=N.connectSocket({url:`ws://${e}:${t}/${n}`,multiple:!0,fail(){o(null)}}),i=setTimeout(()=>{r.close({code:1006,reason:"connect timeout"}),o(null)},Og);r.onOpen(a=>{clearTimeout(i),o(r)}),r.onClose(a=>{clearTimeout(i),o(null)}),r.onError(a=>{clearTimeout(i),o(null)})})}const us=["log","warn","error","info","debug"],Me=us.reduce((e,t)=>(e[t]=console[t].bind(console),e),{});let ls=null;const En=new Set,Mc={};function Wn(e){if(ls==null){e.forEach(n=>{En.add(n)});return}const t=e.map(n=>{if(typeof n=="string")return n;const o=n&&"promise"in n&&"reason"in n,s=o?"UnhandledPromiseRejection: ":"";if(o&&(n=n.reason),n instanceof Error&&n.stack)return n.message&&!n.stack.includes(n.message)?`${s}${n.message}
${n.stack}`:`${s}${n.stack}`;if(typeof n=="object"&&n!==null)try{return s+JSON.stringify(n)}catch(r){return s+String(r)}return s+String(n)}).filter(Boolean);t.length>0&&ls(JSON.stringify(Object.assign({type:"error",data:t},Mc)))}function $g(e,t={}){if(ls=e,Object.assign(Mc,t),e!=null&&En.size>0){const n=Array.from(En);En.clear(),Wn(n)}}function ti(){function e(t){try{if(typeof PromiseRejectionEvent<"u"&&t instanceof PromiseRejectionEvent&&t.reason instanceof Error&&t.reason.message&&t.reason.message.includes("Cannot create property 'errMsg' on string 'taskId"))return;Me.error(t),Wn([t])}catch(n){Me.error(n)}}return typeof N.onError=="function"&&N.onError(e),typeof N.onUnhandledRejection=="function"&&N.onUnhandledRejection(e),function(){typeof N.offError=="function"&&N.offError(e),typeof N.offUnhandledRejection=="function"&&N.offUnhandledRejection(e)}}function ni(e,t){try{return{type:e,args:kg(t)}}catch{}return{type:e,args:[]}}function kg(e){return e.map(t=>At(t))}function At(e,t=0){if(t>=7)return{type:"object",value:"[Maximum depth reached]"};switch(typeof e){case"string":return Mg(e);case"number":return jg(e);case"boolean":return Ng(e);case"object":try{return Hg(e,t)}catch{return{type:"object",value:{properties:[]}}}case"undefined":return Lg();case"function":return Rg(e);case"symbol":return Ug(e);case"bigint":return Dg(e)}}function Rg(e){return{type:"function",value:`function ${e.name}() {}`}}function Lg(){return{type:"undefined"}}function Ng(e){return{type:"boolean",value:String(e)}}function jg(e){return{type:"number",value:String(e)}}function Dg(e){return{type:"bigint",value:String(e)}}function Mg(e){return{type:"string",value:e}}function Ug(e){return{type:"symbol",value:e.description}}function Hg(e,t){if(e===null)return{type:"null"};{if(qg(e))return Bg(e,t);if(Uc(e))return Vg(e,t);if(Kg(e))return Wg(e,t);if(zg(e))return Jg(e,t)}if(Array.isArray(e))return{type:"object",subType:"array",value:{properties:e.map((s,r)=>Gg(s,r,t+1))}};if(e instanceof Set)return{type:"object",subType:"set",className:"Set",description:`Set(${e.size})`,value:{entries:Array.from(e).map(s=>Yg(s,t+1))}};if(e instanceof Map)return{type:"object",subType:"map",className:"Map",description:`Map(${e.size})`,value:{entries:Array.from(e.entries()).map(s=>Xg(s,t+1))}};if(e instanceof Promise)return{type:"object",subType:"promise",value:{properties:[]}};if(e instanceof RegExp)return{type:"object",subType:"regexp",value:String(e),className:"Regexp"};if(e instanceof Date)return{type:"object",subType:"date",value:String(e),className:"Date"};if(e instanceof Error)return{type:"object",subType:"error",value:e.message||String(e),className:e.name||"Error"};let n;{const s=e.constructor;s&&s.get$UTSMetadata$&&(n=s.get$UTSMetadata$().name)}let o=Object.entries(e);return Fg(e)&&(o=o.filter(([s])=>s!=="modifier"&&s!=="nodeContent")),{type:"object",className:n,value:{properties:o.map(s=>an(s[0],s[1],t+1))}}}function Fg(e){return e.modifier&&e.modifier._attribute&&e.nodeContent}function qg(e){return e.$&&Uc(e.$)}function Uc(e){return e.type&&e.uid!=null&&e.appContext}function Bg(e,t){return{type:"object",className:"ComponentPublicInstance",value:{properties:Object.entries(e.$.type).map(([n,o])=>an(n,o,t+1))}}}function Vg(e,t){return{type:"object",className:"ComponentInternalInstance",value:{properties:Object.entries(e.type).map(([n,o])=>an(n,o,t+1))}}}function Kg(e){return e.style&&e.tagName!=null&&e.nodeName!=null}function Wg(e,t){return{type:"object",value:{properties:Object.entries(e).filter(([n])=>["id","tagName","nodeName","dataset","offsetTop","offsetLeft","style"].includes(n)).map(([n,o])=>an(n,o,t+1))}}}function zg(e){return typeof e.getPropertyValue=="function"&&typeof e.setProperty=="function"&&e.$styles}function Jg(e,t){return{type:"object",value:{properties:Object.entries(e.$styles).map(([n,o])=>an(n,o,t+1))}}}function an(e,t,n){const o=At(t,n);return o.name=e,o}function Gg(e,t,n){const o=At(e,n);return o.name=`${t}`,o}function Yg(e,t){return{value:At(e,t)}}function Xg(e,t){return{key:At(e[0],t),value:At(e[1],t)}}let fs=null;const On=[],Hc={},oi="---BEGIN:EXCEPTION---",Qg="---END:EXCEPTION---";function ds(e){if(fs==null){On.push(...e);return}fs(JSON.stringify(Object.assign({type:"console",data:e},Hc)))}function Zg(e,t={}){if(fs=e,Object.assign(Hc,t),e!=null&&On.length>0){const n=On.slice();On.length=0,ds(n)}}const em=/^\s*at\s+[\w/./-]+:\d+$/;function si(){function e(t){return function(...n){const o=[...n];if(o.length){const s=o[o.length-1];typeof s=="string"&&em.test(s)&&o.pop()}if(Me[t](...o),t==="error"&&n.length===1){const s=n[0];if(typeof s=="string"&&s.startsWith(oi)){const r=oi.length,i=s.length-Qg.length;Wn([s.slice(r,i)]);return}else if(s instanceof Error){Wn([s]);return}}ds([ni(t,n)])}}if(tm())return us.forEach(t=>{console[t]=e(t)}),function(){us.forEach(n=>{console[n]=Me[n]})};if(typeof N<"u"&&N.__f__){const t=N.__f__;if(t)return N.__f__=function(...n){const[o,s,...r]=n;t(o,"",...r),ds([ni(o,[...r,s])])},function(){N.__f__=t}}return function(){}}function tm(){const e=console.log,t=Symbol();try{console.log=t}catch{return!1}const n=console.log===t;return console.log=e,n}function nm(){const e="192.168.0.108,127.0.0.1,172.31.0.1",t="8090",n="mp-weixin_5B38bI",o=typeof swan<"u";let s=o?()=>{}:ti(),r=o?()=>{}:si();return Promise.resolve().then(()=>(o&&(s=ti(),r=si()),Eg(e,t,n).then(i=>i?(om(),i.onClose(()=>{Me.error(_n("开发模式下日志通道 socket 连接关闭，请在 HBuilderX 中重新运行。")),s(),r()}),Zg(a=>{i.send({data:a})}),$g(a=>{i.send({data:a})}),!0):(s(),r(),Me.error(_n("开发模式下日志通道建立 socket 连接失败。")),Me.error(_n("小程序平台，请勾选不校验合法域名配置。")),Me.error(_n("如果是运行到真机，请确认手机与电脑处于同一网络。")),!1))))}const ri="‌";function _n(e){return`${ri}${e}${ri}`}function om(){typeof vt<"u"?vt.__uni_console__=!0:typeof my<"u"?my.__uni_console__=!0:typeof tt<"u"?tt.__uni_console__=!0:typeof swan<"u"?swan.__uni_console__=!0:typeof qq<"u"?qq.__uni_console__=!0:typeof ks<"u"?ks.__uni_console__=!0:typeof jd<"u"?jd.__uni_console__=!0:typeof xhs<"u"?xhs.__uni_console__=!0:typeof has<"u"?has.__uni_console__=!0:typeof qa<"u"&&(qa.__uni_console__=!0)}nm();const sm=(e,t)=>{const n=e.__vccOpts||e;for(const[o,s]of t)n[o]=s;return n};function rm(e,t){if(!e)return;const n=e.split(","),o=n.length;o===1?t._$vueId=n[0]:o===2&&(t._$vueId=n[0],t._$vuePid=n[1])}const im=["externalClasses"];function am(e,t){im.forEach(n=>{L(t,n)&&(e[n]=t[n])})}const cm=/_(.*)_worklet_factory_/;function um(e,t){t&&Object.keys(t).forEach(n=>{const o=n.match(cm);if(o){const s=o[1];e[n]=t[n],e[s]=t[s]}})}function lm(e,t){R(t)&&t.forEach(n=>{e[n]=function(o){return this.$vm[n](o)}})}function fm(e,t,n){e.selectAllComponents(t).forEach(s=>{const r=s.properties.uR;n[r]=s.$vm||s})}function dm(e,t){Object.defineProperty(e,"refs",{get(){const n={};return fm(t,".r",n),t.selectAllComponents(".r-i-f").forEach(s=>{const r=s.properties.uR;r&&(n[r]||(n[r]=[]),n[r].push(s.$vm||s))}),n}})}function Fc(e,t){const n=e.$children;for(let s=n.length-1;s>=0;s--){const r=n[s];if(r.$scope._$vueId===t)return r}let o;for(let s=n.length-1;s>=0;s--)if(o=Fc(n[s],t),o)return o}function pm(){var e;let t="";{const n=((e=wx.getAppBaseInfo)===null||e===void 0?void 0:e.call(wx))||wx.getSystemInfoSync(),o=n&&n.language?n.language:ve;t=Cn(o)||ve}return t}const hm=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function gm(e,t){return function(o,...s){const r=t.$scope;if(r&&o){const i={__args__:s};r.triggerEvent(o,i)}return e.apply(this,[o,...s])}}function qc(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,Object.defineProperties(n,{[jo]:{get(){const o=this.$scope.data[jo];return o===void 0?"":o}}}),n.$mp={},n._self={},e.slots={},R(t.slots)&&t.slots.length&&(t.slots.forEach(o=>{e.slots[o]=!0}),e.slots[wl]&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=_m,n.$callHook=Bc,e.emit=gm(e.emit,n)}function mm(e,t){qc(e,t);const n=e.ctx;hm.forEach(o=>{n[o]=function(...s){const r=n.$scope;if(r&&r[o])return r[o].apply(r,s)}})}function ym(e,t,n){const o=e.ctx;n.forEach(s=>{L(t,s)&&(e[s]=o[s]=t[s])})}function _m(e){const t=this.$[e];return!!(t&&t.length)}function Bc(e,t){e==="mounted"&&(Bc.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&El(n,t)}const bm=[kt,tn,nn,As,ra,Es,Os,Cs,Rs];function ps(e,t=new Set){if(e){Object.keys(e).forEach(n=>{ma(n,e[n])&&t.add(n)});{const{extends:n,mixins:o}=e;o&&o.forEach(s=>ps(s,t)),n&&ps(n,t)}}return t}function er(e,t,n){n.indexOf(t)===-1&&!L(e,t)&&(e[t]=function(o){return this.$vm&&this.$vm.$callHook(t,o)})}const Vc=[Is];function tr(e,t,n=Vc){t.forEach(o=>er(e,o,n))}function Kc(e,t,n=Vc){ps(t).forEach(o=>er(e,o,n))}function vm(e,t){if(!t)return;Object.keys(Do).forEach(o=>{t&Do[o]&&er(e,o,[])})}const wm=pa(()=>{const e=[],t=O(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(R(n)){const o=Object.keys(Do);n.forEach(s=>{o.forEach(r=>{L(s,r)&&!e.includes(r)&&e.push(r)})})}}return e});function Sm(e){tr(e,wm())}const xm=[tn,nn,rt,ea,ta,na];function nr(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(i){this.$vm=e;const a=n.ctx;this.$vm&&a.$scope&&a.$callHook||(qc(n,{mpType:"app",mpInstance:this,slots:[]}),a.globalData=this.globalData,e.$callHook(Ps,i))}},s=wx.$onErrorHandlers;s&&(s.forEach(i=>{We(rt,i,n)}),s.length=0),Am(e);const r=e.$.type;tr(o,xm),Kc(o,r);{const i=r.methods;i&&V(o,i)}return o}function Pm(e){return function(n){return App(nr(n))}}function Im(e){return function(n){const o=nr(n),s=O(getApp)&&getApp({allowDefault:!0});if(!s)return;n.$.ctx.$scope=s;const r=s.globalData;r&&Object.keys(o.globalData).forEach(i=>{L(r,i)||(r[i]=o.globalData[i])}),Object.keys(o).forEach(i=>{L(s,i)||(s[i]=o[i])}),Wc(o,n)}}function Wc(e,t){if(O(e.onLaunch)){const n=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(n)}O(e.onShow)&&wx.onAppShow&&wx.onAppShow(n=>{t.$callHook("onShow",n)}),O(e.onHide)&&wx.onAppHide&&wx.onAppHide(n=>{t.$callHook("onHide",n)})}function Am(e){const t=Rt(pm());Object.defineProperty(e,"$locale",{get(){return t.value},set(n){t.value=n}})}const zc=["eO","uR","uRIF","uI","uT","uP","uS"];function Tm(e,t=!1){const n={};if(!t){let o=function(s){const r=Object.create(null);s&&s.forEach(i=>{r[i]=!0}),this.setData({$slots:r})};zc.forEach(s=>{n[s]={type:null,value:""}}),n.uS={type:null,value:[]},n.uS.observer=o}return e.behaviors&&e.behaviors.includes("wx://form-field")&&((!e.properties||!e.properties.name)&&(n.name={type:null,value:""}),(!e.properties||!e.properties.value)&&(n.value={type:null,value:""})),n}function Em(e){const t={};return e&&e.virtualHost&&(t[xl]={type:null,value:""},t[Pl]={type:null,value:""},t[Il]={type:null,value:""},t[jo]={type:null,value:""}),t}function Jc(e){e.properties||(e.properties={}),V(e.properties,Tm(e),Em(e.options))}const Om=[String,Number,Boolean,Object,Array,null];function Cm(e,t){return R(e)&&e.length===1?e[0]:e}function ii(e,t){const n=Cm(e);return Om.indexOf(n)!==-1?n:null}function $m({properties:e},t){R(t)?t.forEach(n=>{e[n]={type:String,value:""}}):ee(t)&&Object.keys(t).forEach(n=>{const o=t[n];if(ee(o)){let s=o.default;O(s)&&(s=s());const r=o.type;o.type=ii(r),e[n]={type:o.type,value:s}}else e[n]={type:ii(o)}})}function km(e,t){return(t?Rm(e):Zs(e.uP))||{}}function Rm(e){const t={};return ee(e)&&Object.keys(e).forEach(n=>{zc.indexOf(n)===-1&&(t[n]=e[n])}),t}function Lm(e){const t=e.$options;R(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})},{immediate:!0})}function Nm(e){return{}}function Gc(e){const t=function(){const o=this.properties.uP;o&&(this.$vm?Dm(o,this.$vm.$):this.properties.uT==="m"&&jm(o,this))};e.observers||(e.observers={}),e.observers.uP=t}function jm(e,t){const n=t.properties,o=Zs(e)||{};Yc(n,o,!1)&&t.setData(o)}function Dm(e,t){const n=j(t.props),o=Zs(e)||{};Yc(n,o)&&(cd(t,o,n,!1),Sf(t.update)&&xf(t.update),t.update())}function Yc(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let s=0;s<o.length;s++){const r=o[s];if(t[r]!==e[r])return!0}return!1}function Mm(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return R(t)&&t.forEach(s=>{o.push(s.replace("uni://","wx://")),s==="uni://form-field"&&(R(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))}),o}function Um(e,t){e.data=Nm(),e.behaviors=Mm(t)}function Xc(e,{parse:t,mocks:n,isPage:o,isPageInProject:s,initRelation:r,handleLink:i,initLifetimes:a}){e=e.default||e;const c={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};R(e.mixins)&&e.mixins.forEach(f=>{B(f.options)&&V(c,f.options)}),e.options&&V(c,e.options);const u={options:c,lifetimes:a({mocks:n,isPage:o,initRelation:r,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(f){this.$vm&&this.$vm.$callHook("onPageResize",f)}},methods:{__l:i}};return Um(u,e),Jc(u),Gc(u),am(u,e),lm(u.methods,e.wxsCallMethods),um(u.methods,e.methods),t&&t(u,{handleLink:i}),u}function Hm(e){return function(n){return Component(Xc(n,e))}}let bo,vo;function Qc(){return getApp().$vm}function Fm(e,t){bo||(bo=Qc().$createComponent);const n=bo(e,t);return rn(n.$)||n}function qm(e){return vo||(vo=Qc().$destroyComponent),vo(e)}function Bm(e,t){const{parse:n,mocks:o,isPage:s,initRelation:r,handleLink:i,initLifetimes:a}=t,c=Xc(e,{mocks:o,isPage:s,isPageInProject:!0,initRelation:r,handleLink:i,initLifetimes:a});$m(c,(e.default||e).props);const u=c.methods;return u.onLoad=function(f){return this.options=f,this.$page={fullPath:Tl(this.route+kl(f))},this.$vm&&this.$vm.$callHook(kt,f)},tr(u,bm),Kc(u,e),vm(u,e.__runtimeHooks),Sm(u),n&&n(c,{handleLink:i}),c}function Vm(e){return function(n){return Component(Bm(n,e))}}function Km(e){return function(n){Wc(nr(n),n)}}const Wm=Page,zm=Component;function ai(e){const t=e.triggerEvent,n=function(o,...s){return t.apply(e,[Cl(o),...s])};try{e.triggerEvent=n}catch{e._triggerEvent=n}}function Zc(e,t,n){const o=t[e];o?t[e]=function(...s){return ai(this),o.apply(this,s)}:t[e]=function(){ai(this)}}Page=function(e){return Zc(kt,e),Wm(e)};Component=function(e){return Zc("created",e),e.properties&&e.properties.uP||(Jc(e),Gc(e)),zm(e)};function Jm({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let s=this.properties;rm(s.uI,this);const r={vuePid:this._$vuePid};n(this,r);const i=this,a=t(i);let c=s;this.$vm=Fm({type:o,props:km(c,a)},{mpType:a?"page":"component",mpInstance:i,slots:s.uS||{},parentComponent:r.parent&&r.parent.$,onBeforeSetup(u,f){dm(u,i),ym(u,i,e),mm(u,f)}}),a||Lm(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook(Is))},detached(){this.$vm&&(yc(this.$vm.$.uid),qm(this.$vm))}}}const Gm=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function Ym(e){return!!e.route}function Xm(e,t){e.triggerEvent("__l",t)}function Qm(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=Fc(this.$vm,n)),o||(o=this.$vm),t.parent=o}var eu=Object.freeze({__proto__:null,handleLink:Qm,initLifetimes:Jm,initRelation:Xm,isPage:Ym,mocks:Gm});const Zm=Pm(),ey=Vm(eu),ty=Hm(eu),ny=Km(),oy=Im();wx.createApp=global.createApp=Zm,wx.createPage=ey,wx.createComponent=ty,wx.createPluginApp=global.createPluginApp=ny,wx.createSubpackageApp=global.createSubpackageApp=oy;const dt=e=>(t,n=Ce())=>{!ro&&We(e,t,n)},sy=dt(tn),ry=dt(nn),iy=dt(Ps),ay=dt(kt),cy=dt(Ts),uy=dt($s),ly=dt(Ls);var wo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},tu={exports:{}};(function(e,t){(function(n,o){e.exports=o()})(wo,function(){var n=typeof wo<"u"?wo:self;if(typeof n.TextEncoder<"u"&&typeof n.TextDecoder<"u")return{TextEncoder:n.TextEncoder,TextDecoder:n.TextDecoder};var o=["utf8","utf-8","unicode-1-1-utf-8"],s=function(i){if(o.indexOf(i)<0&&typeof i<"u"&&i!==null)throw new RangeError("Invalid encoding type. Only utf-8 is supported");this.encoding="utf-8",this.encode=function(a){if(typeof a!="string")throw new TypeError("passed argument must be of type string");var c=unescape(encodeURIComponent(a)),u=new Uint8Array(c.length);return c.split("").forEach(function(f,l){u[l]=f.charCodeAt(0)}),u}},r=function(i,a){if(o.indexOf(i)<0&&typeof i<"u"&&i!==null)throw new RangeError("Invalid encoding type. Only utf-8 is supported");if(this.encoding="utf-8",this.ignoreBOM=!1,this.fatal=typeof a<"u"&&"fatal"in a?a.fatal:!1,typeof this.fatal!="boolean")throw new TypeError("fatal flag must be boolean");this.decode=function(c,u){if(typeof c>"u")return"";var f=typeof u<"u"&&"stream"in u?u.stream:!1;if(typeof f!="boolean")throw new TypeError("stream option must be boolean");if(ArrayBuffer.isView(c)){var l=new Uint8Array(c.buffer,c.byteOffset,c.byteLength),p=new Array(l.length);return l.forEach(function(g,y){p[y]=String.fromCharCode(g)}),decodeURIComponent(escape(p.join("")))}else throw new TypeError("passed argument must be an array buffer view")}};return{TextEncoder:s,TextDecoder:r}})})(tu);var fy=tu.exports,dy=!1;function bn(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}function So(e,t){if(Array.isArray(e)){e.splice(t,1);return}delete e[t]}/*!
 * pinia v2.1.7
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */let zn;const it=e=>zn=e,py=()=>sc()&&Ft(uo)||zn,uo=Symbol("pinia");function at(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var wt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(wt||(wt={}));const lo=typeof window<"u",Jn=lo,ci=[],ui=e=>"🍍 "+e;function hy(e,t){ci.includes(ui(t.$id))||ci.push(ui(t.$id))}function li(e,t,n){const o=t.reduce((s,r)=>(s[r]=j(e)[r],s),{});for(const s in o)e[s]=function(){const r=n?new Proxy(e,{get(...a){return Reflect.get(...a)},set(...a){return Reflect.set(...a)}}):e;return o[s].apply(r,arguments)}}function gy({app:e,store:t,options:n}){if(t.$id.startsWith("__hot:"))return;t._isOptionsAPI=!!n.state,li(t,Object.keys(n.actions),t._isOptionsAPI);const o=t._hotUpdate;j(t)._hotUpdate=function(s){o.apply(this,arguments),li(t,Object.keys(s._hmrPayload.actions),!!t._isOptionsAPI)},hy(e,t)}function nu(){const e=va(!0),t=e.run(()=>Rt({}));let n=[],o=[];const s=Ae({install(r){it(s),s._a=r,r.provide(uo,s),r.config.globalProperties.$pinia=s,o.forEach(i=>n.push(i)),o=[]},use(r){return!this._a&&!dy?o.push(r):n.push(r),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return Jn&&typeof Proxy<"u"&&s.use(gy),s}const yy=e=>typeof e=="function"&&typeof e.$id=="string";function ou(e,t){for(const n in t){const o=t[n];if(!(n in e))continue;const s=e[n];at(s)&&at(o)&&!z(o)&&!we(o)?e[n]=ou(s,o):e[n]=o}return e}function _y(e,t){return n=>{const o=t.data.pinia||e._pinia;if(o){t.data.pinia=o;for(const s in n){const r=n[s];if(yy(r)&&o._s.has(r.$id)){const i=r.$id;if(i!==e.$id)return console.warn(`The id of the store changed from "${e.$id}" to "${i}". Reloading.`),t.invalidate();const a=o._s.get(i);if(!a){console.log("[Pinia]: skipping hmr because store doesn't exist yet");return}r(o,a)}}}}}const by=()=>{};function fi(e,t,n,o=by){e.push(t);const s=()=>{const r=e.indexOf(t);r>-1&&(e.splice(r,1),o())};return!n&&wa()&&Hl(s),s}function ht(e,...t){e.slice().forEach(n=>{n(...t)})}const vy=e=>e();function hs(e,t){e instanceof Map&&t instanceof Map&&t.forEach((n,o)=>e.set(o,n)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const o=t[n],s=e[n];at(s)&&at(o)&&e.hasOwnProperty(n)&&!z(o)&&!we(o)?e[n]=hs(s,o):e[n]=o}return e}const su=Symbol("pinia:skipHydration");function wy(e){return Object.defineProperty(e,su,{})}function Sy(e){return!at(e)||!e.hasOwnProperty(su)}const{assign:de}=Object;function di(e){return!!(z(e)&&e.effect)}function pi(e,t,n,o){const{state:s,actions:r,getters:i}=t,a=n.state.value[e];let c;function u(){!a&&!o&&(n.state.value[e]=s?s():{});const f=vr(o?Rt(s?s():{}).value:n.state.value[e]);return de(f,r,Object.keys(i||{}).reduce((l,p)=>(p in f&&console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with "${p}" in store "${e}".`),l[p]=Ae(ao(()=>{it(n);const g=n._s.get(e);return i[p].call(g,g)})),l),{}))}return c=gs(e,u,t,n,o,!0),c}function gs(e,t,n={},o,s,r){let i;const a=de({actions:{}},n);if(!o._e.active)throw new Error("Pinia destroyed");const c={deep:!0};c.onTrigger=$=>{u?g=$:u==!1&&!A._hotUpdating&&(Array.isArray(g)?g.push($):console.error("🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug."))};let u,f,l=[],p=[],g;const y=o.state.value[e];!r&&!y&&!s&&(o.state.value[e]={});const h=Rt({});let d;function m($){let E;u=f=!1,g=[],typeof $=="function"?($(o.state.value[e]),E={type:wt.patchFunction,storeId:e,events:g}):(hs(o.state.value[e],$),E={type:wt.patchObject,payload:$,storeId:e,events:g});const H=d=Symbol();Rn().then(()=>{d===H&&(u=!0)}),f=!0,ht(l,E,o.state.value[e])}const b=r?function(){const{state:E}=n,H=E?E():{};this.$patch(K=>{de(K,H)})}:()=>{throw new Error(`🍍: Store "${e}" is built using the setup syntax and does not implement $reset().`)};function _(){i.stop(),l=[],p=[],o._s.delete(e)}function v($,E){return function(){it(o);const H=Array.from(arguments),K=[],ue=[];function oe(F){K.push(F)}function ke(F){ue.push(F)}ht(p,{args:H,name:$,store:A,after:oe,onError:ke});let fe;try{fe=E.apply(this&&this.$id===e?this:A,H)}catch(F){throw ht(ue,F),F}return fe instanceof Promise?fe.then(F=>(ht(K,F),F)).catch(F=>(ht(ue,F),Promise.reject(F))):(ht(K,fe),fe)}}const x=Ae({actions:{},getters:{},state:[],hotState:h}),w={_p:o,$id:e,$onAction:fi.bind(null,p),$patch:m,$reset:b,$subscribe($,E={}){const H=fi(l,$,E.detached,()=>K()),K=i.run(()=>Ht(()=>o.state.value[e],ue=>{(E.flush==="sync"?f:u)&&$({storeId:e,type:wt.direct,events:g},ue)},de({},c,E)));return H},$dispose:_},A=on(de({_hmrPayload:x,_customProperties:Ae(new Set)},w));o._s.set(e,A);const q=(o._a&&o._a.runWithContext||vy)(()=>o._e.run(()=>(i=va()).run(t)));for(const $ in q){const E=q[$];if(z(E)&&!di(E)||we(E))s?bn(h.value,$,In(q,$)):r||(y&&Sy(E)&&(z(E)?E.value=y[$]:hs(E,y[$])),o.state.value[e][$]=E),x.state.push($);else if(typeof E=="function"){const H=s?E:v($,E);q[$]=H,x.actions[$]=E,a.actions[$]=E}else di(E)&&(x.getters[$]=r?n.getters[$]:E,lo&&(q._getters||(q._getters=Ae([]))).push($))}if(de(A,q),de(j(A),q),Object.defineProperty(A,"$state",{get:()=>s?h.value:o.state.value[e],set:$=>{if(s)throw new Error("cannot set hotState");m(E=>{de(E,$)})}}),A._hotUpdate=Ae($=>{A._hotUpdating=!0,$._hmrPayload.state.forEach(E=>{if(E in A.$state){const H=$.$state[E],K=A.$state[E];typeof H=="object"&&at(H)&&at(K)?ou(H,K):$.$state[E]=K}bn(A,E,In($.$state,E))}),Object.keys(A.$state).forEach(E=>{E in $.$state||So(A,E)}),u=!1,f=!1,o.state.value[e]=In($._hmrPayload,"hotState"),f=!0,Rn().then(()=>{u=!0});for(const E in $._hmrPayload.actions){const H=$[E];bn(A,E,v(E,H))}for(const E in $._hmrPayload.getters){const H=$._hmrPayload.getters[E],K=r?ao(()=>(it(o),H.call(A,A))):H;bn(A,E,K)}Object.keys(A._hmrPayload.getters).forEach(E=>{E in $._hmrPayload.getters||So(A,E)}),Object.keys(A._hmrPayload.actions).forEach(E=>{E in $._hmrPayload.actions||So(A,E)}),A._hmrPayload=$._hmrPayload,A._getters=$._getters,A._hotUpdating=!1}),Jn){const $={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach(E=>{Object.defineProperty(A,E,de({value:A[E]},$))})}return o._p.forEach($=>{if(Jn){const E=i.run(()=>$({store:A,app:o._a,pinia:o,options:a}));Object.keys(E||{}).forEach(H=>A._customProperties.add(H)),de(A,E)}else de(A,i.run(()=>$({store:A,app:o._a,pinia:o,options:a})))}),A.$state&&typeof A.$state=="object"&&typeof A.$state.constructor=="function"&&!A.$state.constructor.toString().includes("[native code]")&&console.warn(`[🍍]: The "state" must be a plain object. It cannot be
	state: () => new MyClass()
Found in store "${A.$id}".`),y&&r&&n.hydrate&&n.hydrate(A.$state,y),u=!0,f=!0,A}function ru(e,t,n){let o,s;const r=typeof t=="function";if(typeof e=="string")o=e,s=r?n:t;else if(s=e,o=e.id,typeof o!="string")throw new Error('[🍍]: "defineStore()" must be passed a store id as its first argument.');function i(a,c){const u=sc();if(a=a||(u?Ft(uo,null):null),a&&it(a),!zn)throw new Error(`[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?
See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.
This will fail in production.`);a=zn,a._s.has(o)||(r?gs(o,t,s,a):pi(o,s,a),i._pinia=a);const f=a._s.get(o);if(c){const l="__hot:"+o,p=r?gs(l,t,s,a,!0):pi(l,de({},s),a,!0);c._hotUpdate(p),delete a.state.value[l],a._s.delete(l)}if(lo){const l=Ce();if(l&&l.proxy&&!c){const p=l.proxy,g="_pStores"in p?p._pStores:p._pStores={};g[o]=f}}return f}return i.$id=o,i}let iu="Store";function xy(e){iu=e}function Py(...e){return Array.isArray(e[0])&&(console.warn(`[🍍]: Directly pass all stores to "mapStores()" without putting them in an array:
Replace
	mapStores([useAuthStore, useCartStore])
with
	mapStores(useAuthStore, useCartStore)
This will fail in production if not fixed.`),e=e[0]),e.reduce((t,n)=>(t[n.$id+iu]=function(){return n(this.$pinia)},t),{})}function au(e,t){return Array.isArray(t)?t.reduce((n,o)=>(n[o]=function(){return e(this.$pinia)[o]},n),{}):Object.keys(t).reduce((n,o)=>(n[o]=function(){const s=e(this.$pinia),r=t[o];return typeof r=="function"?r.call(this,s):s[r]},n),{})}const Iy=au;function Ay(e,t){return Array.isArray(t)?t.reduce((n,o)=>(n[o]=function(...s){return e(this.$pinia)[o](...s)},n),{}):Object.keys(t).reduce((n,o)=>(n[o]=function(...s){return e(this.$pinia)[t[o]](...s)},n),{})}function Ty(e,t){return Array.isArray(t)?t.reduce((n,o)=>(n[o]={get(){return e(this.$pinia)[o]},set(s){return e(this.$pinia)[o]=s}},n),{}):Object.keys(t).reduce((n,o)=>(n[o]={get(){return e(this.$pinia)[t[o]]},set(s){return e(this.$pinia)[t[o]]=s}},n),{})}function Ey(e){{e=j(e);const t={};for(const n in e){const o=e[n];(z(o)||we(o))&&(t[n]=In(e,n))}return t}}const Oy=function(e){e.mixin({beforeCreate(){const t=this.$options;if(t.pinia){const n=t.pinia;if(!this._provided){const o={};Object.defineProperty(this,"_provided",{get:()=>o,set:s=>Object.assign(o,s)})}this._provided[uo]=n,this.$pinia||(this.$pinia=n),n._a=this,lo&&it(n),Jn&&(n._a,void 0)}else!this.$pinia&&t.parent&&t.parent.$pinia&&(this.$pinia=t.parent.$pinia)},destroyed(){delete this._pStores}})},Cy=Object.freeze(Object.defineProperty({__proto__:null,get MutationType(){return wt},PiniaVuePlugin:Oy,acceptHMRUpdate:_y,createPinia:nu,defineStore:ru,getActivePinia:py,mapActions:Ay,mapGetters:Iy,mapState:au,mapStores:Py,mapWritableState:Ty,setActivePinia:it,setMapStoreSuffix:xy,skipHydrate:wy,storeToRefs:Ey},Symbol.toStringTag,{value:"Module"})),$y=[{path:"pages/index/index",style:{navigationStyle:"custom"}},{path:"pages/auth/auth",style:{navigationStyle:"custom"}},{path:"pages/personals/personals",style:{navigationBarTitleText:"伴你有约",navigationStyle:"custom"}},{path:"pages/activity/activity",style:{navigationStyle:"custom"}},{path:"pages/moment/moment",style:{navigationBarTitleText:"发现",navigationStyle:"custom"}},{path:"pages/message/message",style:{navigationBarTitleText:"消息",navigationStyle:"custom"}},{path:"pages/my/my",style:{navigationStyle:"custom"}}],ky=[{root:"pagesubs/activity",pages:[{path:"detail"},{path:"buddy/detail"},{path:"buddy/add",style:{navigationBarTitleText:"发布搭子活动",navigationStyle:"custom"}}]},{root:"pagesubs/message",pages:[{path:"follow",style:{navigationBarTitleText:"新关注的",navigationStyle:"custom"}},{path:"interaction",style:{navigationBarTitleText:"互动消息",navigationStyle:"custom"}},{path:"system",style:{navigationBarTitleText:"系统消息",navigationStyle:"custom"}}]},{root:"pagesubs/personals",pages:[{path:"profile",style:{navigationBarTitleText:"个人主页",navigationStyle:"custom"}},{path:"filter",style:{navigationBarTitleText:"刷选条件",navigationStyle:"custom"}},{path:"gift/gift",style:{navigationBarTitleText:"礼物",navigationStyle:"custom"}},{path:"greeting/greeting",style:{navigationBarTitleText:"打招呼",navigationStyle:"custom"}}]},{root:"pagesubs/my",pages:[{path:"album/album"},{path:"profile/profileEdit"},{path:"profile/baseEdit"},{path:"profile/base/fullBaseEdit",style:{navigationStyle:"custom"}},{path:"profile/avatarEdit"},{path:"profile/about/aboutEdit",style:{navigationStyle:"custom"}},{path:"profile/matched/matchedEdit",style:{navigationStyle:"custom"}},{path:"gift/gift"},{path:"greeting/greeting",style:{navigationStyle:"custom"}},{path:"follow/follow"},{path:"browse/browse"},{path:"coin/coin"},{path:"coin/coin-detail"},{path:"feedback/feedback"},{path:"question/question"},{path:"moment/moment"},{path:"moment/edit",style:{navigationStyle:"custom"}},{path:"activity/activity"},{path:"activity/detail"},{path:"upgrade/upgrade"},{path:"auth/auth",style:{navigationStyle:"custom"}},{path:"auth/identity",style:{navigationStyle:"custom"}},{path:"auth/education",style:{navigationStyle:"custom"}},{path:"auth/work",style:{navigationStyle:"custom"}},{path:"auth/house",style:{navigationStyle:"custom"}},{path:"auth/car",style:{navigationStyle:"custom"}},{path:"setting/setting",style:{navigationStyle:"custom"}},{path:"setting/recommend"},{path:"setting/privacy"},{path:"setting/msRemind"},{path:"content/agreement/agreement",style:{navigationStyle:"custom"}},{path:"content/help/help",style:{navigationStyle:"custom"}},{path:"report/report",style:{navigationBarTitleText:"举报",navigationStyle:"custom"}},{path:"order/order-list",style:{navigationBarTitleText:"我的订单",navigationStyle:"custom"}},{path:"order/order-detail",style:{navigationBarTitleText:"订单详情",navigationStyle:"custom"}}]},{root:"pagesubs/promotion-center",pages:[{path:"promotion-center",style:{navigationBarTitleText:"推广中心"}},{path:"withdraw/withdraw",style:{navigationBarTitleText:"助力计划"}},{path:"webview/webview"}]},{root:"pagesubs/moment",pages:[{path:"add",style:{navigationBarTitleText:"写动态",navigationStyle:"custom"}},{path:"detail",style:{navigationBarTitleText:"动态详情",navigationStyle:"custom"}},{path:"tag/list",style:{navigationBarTitleText:"更多话题",navigationStyle:"custom"}},{path:"tag/detail",style:{navigationBarTitleText:"话题详情",navigationStyle:"custom"}}]}],Ry={autoscan:!0},Ly={navigationBarTextStyle:"black",navigationBarBackgroundColor:"#FFFFFF",navigationStyle:"custom"},Ny={color:"#989898",selectedColor:"#696CF3",borderStyle:"black",backgroundColor:"#ffffff",fontSize:"18px",height:"70px",list:[{pagePath:"pages/personals/personals",text:"推荐",iconPath:"static/image/tabbar/house.png",selectedIconPath:"static/image/tabbar/house-fill.png"},{pagePath:"pages/activity/activity",text:"活动",iconPath:"static/image/tabbar/activity.png",selectedIconPath:"static/image/tabbar/activity-fill.png"},{pagePath:"pages/moment/moment",text:"发现",iconPath:"static/image/tabbar/discover.png",selectedIconPath:"static/image/tabbar/discover-fill.png"},{pagePath:"pages/message/message",text:"消息",iconPath:"static/image/tabbar/message.png",selectedIconPath:"static/image/tabbar/message-fill.png"},{pagePath:"pages/my/my",text:"我的",iconPath:"static/image/tabbar/my.png",selectedIconPath:"static/image/tabbar/my-fill.png"}]},jy={},cu={pages:$y,subPackages:ky,easycom:Ry,globalStyle:Ly,tabBar:Ny,uniIdRouter:jy};var Dy=[];function My(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Se(e,t,n){return e(n={path:t,exports:{},require:function(o,s){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(s==null&&n.path)}},n.exports),n.exports}var Uy=Se(function(e,t){var n;e.exports=(n=n||function(o,s){var r=Object.create||function(){function d(){}return function(m){var b;return d.prototype=m,b=new d,d.prototype=null,b}}(),i={},a=i.lib={},c=a.Base={extend:function(d){var m=r(this);return d&&m.mixIn(d),m.hasOwnProperty("init")&&this.init!==m.init||(m.init=function(){m.$super.init.apply(this,arguments)}),m.init.prototype=m,m.$super=this,m},create:function(){var d=this.extend();return d.init.apply(d,arguments),d},init:function(){},mixIn:function(d){for(var m in d)d.hasOwnProperty(m)&&(this[m]=d[m]);d.hasOwnProperty("toString")&&(this.toString=d.toString)},clone:function(){return this.init.prototype.extend(this)}},u=a.WordArray=c.extend({init:function(d,m){d=this.words=d||[],this.sigBytes=m!=s?m:4*d.length},toString:function(d){return(d||l).stringify(this)},concat:function(d){var m=this.words,b=d.words,_=this.sigBytes,v=d.sigBytes;if(this.clamp(),_%4)for(var x=0;x<v;x++){var w=b[x>>>2]>>>24-x%4*8&255;m[_+x>>>2]|=w<<24-(_+x)%4*8}else for(x=0;x<v;x+=4)m[_+x>>>2]=b[x>>>2];return this.sigBytes+=v,this},clamp:function(){var d=this.words,m=this.sigBytes;d[m>>>2]&=4294967295<<32-m%4*8,d.length=o.ceil(m/4)},clone:function(){var d=c.clone.call(this);return d.words=this.words.slice(0),d},random:function(d){for(var m,b=[],_=function(w){w=w;var A=987654321,D=4294967295;return function(){var q=((A=36969*(65535&A)+(A>>16)&D)<<16)+(w=18e3*(65535&w)+(w>>16)&D)&D;return q/=4294967296,(q+=.5)*(o.random()>.5?1:-1)}},v=0;v<d;v+=4){var x=_(4294967296*(m||o.random()));m=987654071*x(),b.push(4294967296*x()|0)}return new u.init(b,d)}}),f=i.enc={},l=f.Hex={stringify:function(d){for(var m=d.words,b=d.sigBytes,_=[],v=0;v<b;v++){var x=m[v>>>2]>>>24-v%4*8&255;_.push((x>>>4).toString(16)),_.push((15&x).toString(16))}return _.join("")},parse:function(d){for(var m=d.length,b=[],_=0;_<m;_+=2)b[_>>>3]|=parseInt(d.substr(_,2),16)<<24-_%8*4;return new u.init(b,m/2)}},p=f.Latin1={stringify:function(d){for(var m=d.words,b=d.sigBytes,_=[],v=0;v<b;v++){var x=m[v>>>2]>>>24-v%4*8&255;_.push(String.fromCharCode(x))}return _.join("")},parse:function(d){for(var m=d.length,b=[],_=0;_<m;_++)b[_>>>2]|=(255&d.charCodeAt(_))<<24-_%4*8;return new u.init(b,m)}},g=f.Utf8={stringify:function(d){try{return decodeURIComponent(escape(p.stringify(d)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(d){return p.parse(unescape(encodeURIComponent(d)))}},y=a.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new u.init,this._nDataBytes=0},_append:function(d){typeof d=="string"&&(d=g.parse(d)),this._data.concat(d),this._nDataBytes+=d.sigBytes},_process:function(d){var m=this._data,b=m.words,_=m.sigBytes,v=this.blockSize,x=_/(4*v),w=(x=d?o.ceil(x):o.max((0|x)-this._minBufferSize,0))*v,A=o.min(4*w,_);if(w){for(var D=0;D<w;D+=v)this._doProcessBlock(b,D);var q=b.splice(0,w);m.sigBytes-=A}return new u.init(q,A)},clone:function(){var d=c.clone.call(this);return d._data=this._data.clone(),d},_minBufferSize:0});a.Hasher=y.extend({cfg:c.extend(),init:function(d){this.cfg=this.cfg.extend(d),this.reset()},reset:function(){y.reset.call(this),this._doReset()},update:function(d){return this._append(d),this._process(),this},finalize:function(d){return d&&this._append(d),this._doFinalize()},blockSize:16,_createHelper:function(d){return function(m,b){return new d.init(b).finalize(m)}},_createHmacHelper:function(d){return function(m,b){return new h.HMAC.init(d,b).finalize(m)}}});var h=i.algo={};return i}(Math),n)}),Fe=Uy,Hy=(Se(function(e,t){var n;e.exports=(n=Fe,function(o){var s=n,r=s.lib,i=r.WordArray,a=r.Hasher,c=s.algo,u=[];(function(){for(var h=0;h<64;h++)u[h]=4294967296*o.abs(o.sin(h+1))|0})();var f=c.MD5=a.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(h,d){for(var m=0;m<16;m++){var b=d+m,_=h[b];h[b]=16711935&(_<<8|_>>>24)|4278255360&(_<<24|_>>>8)}var v=this._hash.words,x=h[d+0],w=h[d+1],A=h[d+2],D=h[d+3],q=h[d+4],$=h[d+5],E=h[d+6],H=h[d+7],K=h[d+8],ue=h[d+9],oe=h[d+10],ke=h[d+11],fe=h[d+12],F=h[d+13],M=h[d+14],U=h[d+15],S=v[0],T=v[1],P=v[2],I=v[3];S=l(S,T,P,I,x,7,u[0]),I=l(I,S,T,P,w,12,u[1]),P=l(P,I,S,T,A,17,u[2]),T=l(T,P,I,S,D,22,u[3]),S=l(S,T,P,I,q,7,u[4]),I=l(I,S,T,P,$,12,u[5]),P=l(P,I,S,T,E,17,u[6]),T=l(T,P,I,S,H,22,u[7]),S=l(S,T,P,I,K,7,u[8]),I=l(I,S,T,P,ue,12,u[9]),P=l(P,I,S,T,oe,17,u[10]),T=l(T,P,I,S,ke,22,u[11]),S=l(S,T,P,I,fe,7,u[12]),I=l(I,S,T,P,F,12,u[13]),P=l(P,I,S,T,M,17,u[14]),S=p(S,T=l(T,P,I,S,U,22,u[15]),P,I,w,5,u[16]),I=p(I,S,T,P,E,9,u[17]),P=p(P,I,S,T,ke,14,u[18]),T=p(T,P,I,S,x,20,u[19]),S=p(S,T,P,I,$,5,u[20]),I=p(I,S,T,P,oe,9,u[21]),P=p(P,I,S,T,U,14,u[22]),T=p(T,P,I,S,q,20,u[23]),S=p(S,T,P,I,ue,5,u[24]),I=p(I,S,T,P,M,9,u[25]),P=p(P,I,S,T,D,14,u[26]),T=p(T,P,I,S,K,20,u[27]),S=p(S,T,P,I,F,5,u[28]),I=p(I,S,T,P,A,9,u[29]),P=p(P,I,S,T,H,14,u[30]),S=g(S,T=p(T,P,I,S,fe,20,u[31]),P,I,$,4,u[32]),I=g(I,S,T,P,K,11,u[33]),P=g(P,I,S,T,ke,16,u[34]),T=g(T,P,I,S,M,23,u[35]),S=g(S,T,P,I,w,4,u[36]),I=g(I,S,T,P,q,11,u[37]),P=g(P,I,S,T,H,16,u[38]),T=g(T,P,I,S,oe,23,u[39]),S=g(S,T,P,I,F,4,u[40]),I=g(I,S,T,P,x,11,u[41]),P=g(P,I,S,T,D,16,u[42]),T=g(T,P,I,S,E,23,u[43]),S=g(S,T,P,I,ue,4,u[44]),I=g(I,S,T,P,fe,11,u[45]),P=g(P,I,S,T,U,16,u[46]),S=y(S,T=g(T,P,I,S,A,23,u[47]),P,I,x,6,u[48]),I=y(I,S,T,P,H,10,u[49]),P=y(P,I,S,T,M,15,u[50]),T=y(T,P,I,S,$,21,u[51]),S=y(S,T,P,I,fe,6,u[52]),I=y(I,S,T,P,D,10,u[53]),P=y(P,I,S,T,oe,15,u[54]),T=y(T,P,I,S,w,21,u[55]),S=y(S,T,P,I,K,6,u[56]),I=y(I,S,T,P,U,10,u[57]),P=y(P,I,S,T,E,15,u[58]),T=y(T,P,I,S,F,21,u[59]),S=y(S,T,P,I,q,6,u[60]),I=y(I,S,T,P,ke,10,u[61]),P=y(P,I,S,T,A,15,u[62]),T=y(T,P,I,S,ue,21,u[63]),v[0]=v[0]+S|0,v[1]=v[1]+T|0,v[2]=v[2]+P|0,v[3]=v[3]+I|0},_doFinalize:function(){var h=this._data,d=h.words,m=8*this._nDataBytes,b=8*h.sigBytes;d[b>>>5]|=128<<24-b%32;var _=o.floor(m/4294967296),v=m;d[15+(b+64>>>9<<4)]=16711935&(_<<8|_>>>24)|4278255360&(_<<24|_>>>8),d[14+(b+64>>>9<<4)]=16711935&(v<<8|v>>>24)|4278255360&(v<<24|v>>>8),h.sigBytes=4*(d.length+1),this._process();for(var x=this._hash,w=x.words,A=0;A<4;A++){var D=w[A];w[A]=16711935&(D<<8|D>>>24)|4278255360&(D<<24|D>>>8)}return x},clone:function(){var h=a.clone.call(this);return h._hash=this._hash.clone(),h}});function l(h,d,m,b,_,v,x){var w=h+(d&m|~d&b)+_+x;return(w<<v|w>>>32-v)+d}function p(h,d,m,b,_,v,x){var w=h+(d&b|m&~b)+_+x;return(w<<v|w>>>32-v)+d}function g(h,d,m,b,_,v,x){var w=h+(d^m^b)+_+x;return(w<<v|w>>>32-v)+d}function y(h,d,m,b,_,v,x){var w=h+(m^(d|~b))+_+x;return(w<<v|w>>>32-v)+d}s.MD5=a._createHelper(f),s.HmacMD5=a._createHmacHelper(f)}(Math),n.MD5)}),Se(function(e,t){var n;e.exports=(n=Fe,void function(){var o=n,s=o.lib.Base,r=o.enc.Utf8;o.algo.HMAC=s.extend({init:function(i,a){i=this._hasher=new i.init,typeof a=="string"&&(a=r.parse(a));var c=i.blockSize,u=4*c;a.sigBytes>u&&(a=i.finalize(a)),a.clamp();for(var f=this._oKey=a.clone(),l=this._iKey=a.clone(),p=f.words,g=l.words,y=0;y<c;y++)p[y]^=1549556828,g[y]^=909522486;f.sigBytes=l.sigBytes=u,this.reset()},reset:function(){var i=this._hasher;i.reset(),i.update(this._iKey)},update:function(i){return this._hasher.update(i),this},finalize:function(i){var a=this._hasher,c=a.finalize(i);return a.reset(),a.finalize(this._oKey.clone().concat(c))}})}())}),Se(function(e,t){e.exports=Fe.HmacMD5})),Fy=Se(function(e,t){e.exports=Fe.enc.Utf8}),qy=Se(function(e,t){var n;e.exports=(n=Fe,function(){var o=n,s=o.lib.WordArray;function r(i,a,c){for(var u=[],f=0,l=0;l<a;l++)if(l%4){var p=c[i.charCodeAt(l-1)]<<l%4*2,g=c[i.charCodeAt(l)]>>>6-l%4*2;u[f>>>2]|=(p|g)<<24-f%4*8,f++}return s.create(u,f)}o.enc.Base64={stringify:function(i){var a=i.words,c=i.sigBytes,u=this._map;i.clamp();for(var f=[],l=0;l<c;l+=3)for(var p=(a[l>>>2]>>>24-l%4*8&255)<<16|(a[l+1>>>2]>>>24-(l+1)%4*8&255)<<8|a[l+2>>>2]>>>24-(l+2)%4*8&255,g=0;g<4&&l+.75*g<c;g++)f.push(u.charAt(p>>>6*(3-g)&63));var y=u.charAt(64);if(y)for(;f.length%4;)f.push(y);return f.join("")},parse:function(i){var a=i.length,c=this._map,u=this._reverseMap;if(!u){u=this._reverseMap=[];for(var f=0;f<c.length;f++)u[c.charCodeAt(f)]=f}var l=c.charAt(64);if(l){var p=i.indexOf(l);p!==-1&&(a=p)}return r(i,a,u)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)});const hi="FUNCTION",By="OBJECT",Vy="CLIENT_DB",gi="pending",Ky="fulfilled",mi="rejected";function Gt(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function uu(e){return Gt(e)==="object"}function lu(e){return typeof e=="function"}function Wy(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}const yi="REJECTED",fu="NOT_PENDING";class or{constructor({createPromise:t,retryRule:n=yi}={}){this.createPromise=t,this.status=null,this.promise=null,this.retryRule=n}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case yi:return this.status===mi;case fu:return this.status!==gi}}exec(){return this.needRetry?(this.status=gi,this.promise=this.createPromise().then(t=>(this.status=Ky,Promise.resolve(t)),t=>(this.status=mi,Promise.reject(t))),this.promise):this.promise}}function sr(e){return e&&typeof e=="string"?JSON.parse(e):e}const zy=!0,Jy="mp-weixin",Gy=sr(Dy),ct=Jy,Yy=sr(""),du=sr("[]")||[];let Xy="";try{Xy="__UNI__A07BE01"}catch{}let xo={};function Tt(e,t={}){var n,o;return n=xo,o=e,Object.prototype.hasOwnProperty.call(n,o)||(xo[e]=t),xo[e]}const pu=["invoke","success","fail","complete"],_e=Tt("_globalUniCloudInterceptor");function hu(e,t){_e[e]||(_e[e]={}),uu(t)&&Object.keys(t).forEach(n=>{pu.indexOf(n)>-1&&function(o,s,r){let i=_e[o][s];i||(i=_e[o][s]=[]),i.indexOf(r)===-1&&lu(r)&&i.push(r)}(e,n,t[n])})}function Qy(e,t){_e[e]||(_e[e]={}),uu(t)?Object.keys(t).forEach(n=>{pu.indexOf(n)>-1&&function(o,s,r){const i=_e[o][s];if(!i)return;const a=i.indexOf(r);a>-1&&i.splice(a,1)}(e,n,t[n])}):delete _e[e]}function ie(e,t){return e&&e.length!==0?e.reduce((n,o)=>n.then(()=>o(t)),Promise.resolve()):Promise.resolve()}function ae(e,t){return _e[e]&&_e[e][t]||[]}function Zy(e){hu("callObject",e)}const Po=Tt("_globalUniCloudListener"),be="response",Et="needLogin",Gn="refreshToken",_i="clientdb",vn="cloudfunction",bi="cloudobject";function cn(e){return Po[e]||(Po[e]=[]),Po[e]}function Io(e,t){const n=cn(e);n.includes(t)||n.push(t)}function Ao(e,t){const n=cn(e),o=n.indexOf(t);o!==-1&&n.splice(o,1)}function pe(e,t){const n=cn(e);for(let o=0;o<n.length;o++)(0,n[o])(t)}let To,Eo=!1;function gu(){return To||(To=new Promise(e=>{Eo&&e(),function t(){if(typeof getCurrentPages=="function"){const n=getCurrentPages();n&&n[0]&&(Eo=!0,e())}Eo||setTimeout(()=>{t()},30)}()}),To)}function mu(e){const t={};for(const n in e){const o=e[n];lu(o)&&(t[n]=Wy(o))}return t}class C extends Error{constructor(t){super(t.message),this.errMsg=t.message||t.errMsg||"unknown system error",this.code=this.errCode=t.code||t.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=t.subject||t.errSubject,this.cause=t.cause,this.requestId=t.requestId}toJson(t=0){if(!(t>=10))return t++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(t):this.cause}}}var Y={request:e=>N.request(e),uploadFile:e=>N.uploadFile(e),setStorageSync:(e,t)=>N.setStorageSync(e,t),getStorageSync:e=>N.getStorageSync(e),removeStorageSync:e=>N.removeStorageSync(e),clearStorageSync:()=>N.clearStorageSync(),connectSocket:e=>N.connectSocket(e)};function yu(e){return e&&yu(e.__v_raw)||e}function Yt(){return{token:Y.getStorageSync("uni_id_token")||Y.getStorageSync("uniIdToken"),tokenExpired:Y.getStorageSync("uni_id_token_expired")}}function _u({token:e,tokenExpired:t}={}){e&&Y.setStorageSync("uni_id_token",e),t&&Y.setStorageSync("uni_id_token_expired",t)}let Oo,wn;function bu(){return Oo||(Oo=N.getSystemInfoSync()),Oo}function e_(){let e,t;try{if(N.getLaunchOptionsSync){if(N.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:o}=N.getLaunchOptionsSync();e=o,t=n}}catch{}return{channel:e,scene:t}}let ms={};function ys(){const e=N.getLocale&&N.getLocale()||"en";if(wn)return{...ms,...wn,locale:e,LOCALE:e};const t=bu(),{deviceId:n,osName:o,uniPlatform:s,appId:r}=t,i=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const a in t)Object.hasOwnProperty.call(t,a)&&i.indexOf(a)===-1&&delete t[a];return wn={PLATFORM:s,OS:o,APPID:r,DEVICEID:n,...e_(),...t},{...ms,...wn,locale:e,LOCALE:e}}var yt={sign:function(e,t){let n="";return Object.keys(e).sort().forEach(function(o){e[o]&&(n=n+"&"+o+"="+e[o])}),n=n.slice(1),Hy(n,t).toString()},wrappedRequest:function(e,t){return new Promise((n,o)=>{t(Object.assign(e,{complete(s){s||(s={});const r=s.data&&s.data.header&&s.data.header["x-serverless-request-id"]||s.header&&s.header["request-id"];if(!s.statusCode||s.statusCode>=400){const a=s.data&&s.data.error&&s.data.error.code||"SYS_ERR",c=s.data&&s.data.error&&s.data.error.message||s.errMsg||"request:fail";return o(new C({code:a,message:c,requestId:r}))}const i=s.data;if(i.error)return o(new C({code:i.error.code,message:i.error.message,requestId:r}));i.result=i.data,i.requestId=r,delete i.data,n(i)}}))})},toBase64:function(e){return qy.stringify(Fy.parse(e))}},t_=class{constructor(e){["spaceId","clientSecret"].forEach(t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)}),this.config=Object.assign({},{endpoint:e.spaceId.indexOf("mp-")===0?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=Y,this._getAccessTokenPromiseHub=new or({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then(t=>{if(!t.result||!t.result.accessToken)throw new C({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(t.result.accessToken)}),retryRule:fu})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return yt.wrappedRequest(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then(()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch(n=>new Promise((o,s)=>{!n||n.code!=="GATEWAY_INVALID_TOKEN"&&n.code!=="InvalidParameter.InvalidToken"?s(n):o()}).then(()=>this.getAccessToken()).then(()=>{const o=this.rebuildRequest(e);return this.request(o,!0)})):this.getAccessToken().then(()=>{const n=this.rebuildRequest(e);return this.request(n,!0)}))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=yt.sign(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),o={"Content-Type":"application/json"};return t!=="auth"&&(n.token=this.accessToken,o["x-basement-token"]=this.accessToken),o["x-serverless-sign"]=yt.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:o}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:o,fileType:s,onUploadProgress:r}){return new Promise((i,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:o,fileType:s,header:{"X-OSS-server-side-encrpytion":"AES256"},success(u){u&&u.statusCode<400?i(u):a(new C({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(u){a(new C({code:u.code||"UPLOAD_FAILED",message:u.message||u.errMsg||"文件上传失败"}))}});typeof r=="function"&&c&&typeof c.onProgressUpdate=="function"&&c.onProgressUpdate(u=>{r({loaded:u.totalBytesSent,total:u.totalBytesExpectedToSend})})})}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:o=!1,onUploadProgress:s,config:r}){if(Gt(t)!=="string")throw new C({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new C({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new C({code:"INVALID_PARAM",message:"cloudPath不合法"});const i=r&&r.envType||this.config.envType;if(o&&(t[0]!=="/"&&(t="/"+t),t.indexOf("\\")>-1))throw new C({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:i,filename:o?t.split("/").pop():t,fileId:o?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:u,accessKeyId:f,signature:l,host:p,ossPath:g,id:y,policy:h,ossCallbackUrl:d}=a,m={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:f,Signature:l,host:p,id:y,key:g,policy:h,success_action_status:200};if(u&&(m["x-oss-security-token"]=u),d){const _=JSON.stringify({callbackUrl:d,callbackBody:JSON.stringify({fileId:y,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});m.callback=yt.toBase64(_)}const b={url:"https://"+a.host,formData:m,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},b,{onUploadProgress:s})),d)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:y})).success)return{success:!0,filePath:e,fileID:c};throw new C({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise((t,n)=>{Array.isArray(e)&&e.length!==0||n(new C({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map(o=>({fileID:o,tempFileURL:o}))})})}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||e.length===0)throw new C({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map(n=>n.split("?")[0]).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}},n_={init(e){const t=new t_(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const o_=typeof location<"u"&&location.protocol==="http:"?"http:":"https:";var vi;(function(e){e.local="local",e.none="none",e.session="session"})(vi||(vi={}));var s_=function(){},r_=Se(function(e,t){var n;e.exports=(n=Fe,function(o){var s=n,r=s.lib,i=r.WordArray,a=r.Hasher,c=s.algo,u=[],f=[];(function(){function g(m){for(var b=o.sqrt(m),_=2;_<=b;_++)if(!(m%_))return!1;return!0}function y(m){return 4294967296*(m-(0|m))|0}for(var h=2,d=0;d<64;)g(h)&&(d<8&&(u[d]=y(o.pow(h,.5))),f[d]=y(o.pow(h,1/3)),d++),h++})();var l=[],p=c.SHA256=a.extend({_doReset:function(){this._hash=new i.init(u.slice(0))},_doProcessBlock:function(g,y){for(var h=this._hash.words,d=h[0],m=h[1],b=h[2],_=h[3],v=h[4],x=h[5],w=h[6],A=h[7],D=0;D<64;D++){if(D<16)l[D]=0|g[y+D];else{var q=l[D-15],$=(q<<25|q>>>7)^(q<<14|q>>>18)^q>>>3,E=l[D-2],H=(E<<15|E>>>17)^(E<<13|E>>>19)^E>>>10;l[D]=$+l[D-7]+H+l[D-16]}var K=d&m^d&b^m&b,ue=(d<<30|d>>>2)^(d<<19|d>>>13)^(d<<10|d>>>22),oe=A+((v<<26|v>>>6)^(v<<21|v>>>11)^(v<<7|v>>>25))+(v&x^~v&w)+f[D]+l[D];A=w,w=x,x=v,v=_+oe|0,_=b,b=m,m=d,d=oe+(ue+K)|0}h[0]=h[0]+d|0,h[1]=h[1]+m|0,h[2]=h[2]+b|0,h[3]=h[3]+_|0,h[4]=h[4]+v|0,h[5]=h[5]+x|0,h[6]=h[6]+w|0,h[7]=h[7]+A|0},_doFinalize:function(){var g=this._data,y=g.words,h=8*this._nDataBytes,d=8*g.sigBytes;return y[d>>>5]|=128<<24-d%32,y[14+(d+64>>>9<<4)]=o.floor(h/4294967296),y[15+(d+64>>>9<<4)]=h,g.sigBytes=4*y.length,this._process(),this._hash},clone:function(){var g=a.clone.call(this);return g._hash=this._hash.clone(),g}});s.SHA256=a._createHelper(p),s.HmacSHA256=a._createHmacHelper(p)}(Math),n.SHA256)}),_s=r_,vu=Se(function(e,t){e.exports=Fe.HmacSHA256});const un=()=>{let e;if(!Promise){e=()=>{},e.promise={};const n=()=>{throw new C({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:n}),Object.defineProperty(e.promise,"catch",{get:n}),e}const t=new Promise((n,o)=>{e=(s,r)=>s?o(s):n(r)});return e.promise=t,e};function i_(e){return e===void 0}function a_(e){return Object.prototype.toString.call(e)==="[object Null]"}function wi(e=""){return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Si(e=32){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=t.length;let o="";for(let s=0;s<e;s++)o+=t.charAt(Math.floor(Math.random()*n));return o}var xi;function c_(e){const t=(n=e,Object.prototype.toString.call(n)==="[object Array]"?e:[e]);var n;for(const o of t){const{isMatch:s,genAdapter:r,runtime:i}=o;if(s())return{adapter:r(),runtime:i}}}(function(e){e.WEB="web",e.WX_MP="wx_mp"})(xi||(xi={}));const te={adapter:null,runtime:void 0},u_=["anonymousUuidKey"];class Co extends s_{constructor(){super(),te.adapter.root.tcbObject||(te.adapter.root.tcbObject={})}setItem(t,n){te.adapter.root.tcbObject[t]=n}getItem(t){return te.adapter.root.tcbObject[t]}removeItem(t){delete te.adapter.root.tcbObject[t]}clear(){delete te.adapter.root.tcbObject}}function Pi(e,t){switch(e){case"local":return t.localStorage||new Co;case"none":return new Co;default:return t.sessionStorage||new Co}}class Ii{constructor(t){if(!this._storage){this._persistence=te.adapter.primaryStorage||t.persistence,this._storage=Pi(this._persistence,te.adapter);const n=`access_token_${t.env}`,o=`access_token_expire_${t.env}`,s=`refresh_token_${t.env}`,r=`anonymous_uuid_${t.env}`,i=`login_type_${t.env}`,a="device_id",c=`token_type_${t.env}`,u=`user_info_${t.env}`;this.keys={accessTokenKey:n,accessTokenExpireKey:o,refreshTokenKey:s,anonymousUuidKey:r,loginTypeKey:i,userInfoKey:u,deviceIdKey:a,tokenTypeKey:c}}}updatePersistence(t){if(t===this._persistence)return;const n=this._persistence==="local";this._persistence=t;const o=Pi(t,te.adapter);for(const s in this.keys){const r=this.keys[s];if(n&&u_.includes(s))continue;const i=this._storage.getItem(r);i_(i)||a_(i)||(o.setItem(r,i),this._storage.removeItem(r))}this._storage=o}setStore(t,n,o){if(!this._storage)return;const s={version:o||"localCachev1",content:n},r=JSON.stringify(s);try{this._storage.setItem(t,r)}catch(i){throw i}}getStore(t,n){try{if(!this._storage)return}catch{return""}n=n||"localCachev1";const o=this._storage.getItem(t);return o&&o.indexOf(n)>=0?JSON.parse(o).content:""}removeStore(t){this._storage.removeItem(t)}}const wu={},Su={};function Lt(e){return wu[e]}class xu{constructor(t,n){this.data=n||null,this.name=t}}class l_ extends xu{constructor(t,n){super("error",{error:t,data:n}),this.error=t}}const rr=new class{constructor(){this._listeners={}}on(e,t){return function(n,o,s){s[n]=s[n]||[],s[n].push(o)}(e,t,this._listeners),this}off(e,t){return function(n,o,s){if(s&&s[n]){const r=s[n].indexOf(o);r!==-1&&s[n].splice(r,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof l_)return console.error(e.error),this;const n=typeof e=="string"?new xu(e,t||{}):e,o=n.name;if(this._listens(o)){n.target=this;const s=this._listeners[o]?[...this._listeners[o]]:[];for(const r of s)r.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function Ne(e,t){rr.on(e,t)}function ce(e,t={}){rr.fire(e,t)}function f_(e,t){rr.off(e,t)}const Ot="loginStateChanged",Pu="loginStateExpire",qe="loginTypeChanged",bs="anonymousConverted",Iu="refreshAccessToken";var X;(function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"})(X||(X={}));class d_{constructor(){this._fnPromiseMap=new Map}async run(t,n){let o=this._fnPromiseMap.get(t);return o||(o=new Promise(async(s,r)=>{try{await this._runIdlePromise();const i=n();s(await i)}catch(i){r(i)}finally{this._fnPromiseMap.delete(t)}}),this._fnPromiseMap.set(t,o)),o}_runIdlePromise(){return Promise.resolve()}}class p_{constructor(t){this._singlePromise=new d_,this._cache=Lt(t.env),this._baseURL=`https://${t.env}.ap-shanghai.tcb-api.tencentcloudapi.com`,this._reqClass=new te.adapter.reqClass({timeout:t.timeout,timeoutMsg:`请求在${t.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]})}_getDeviceId(){if(this._deviceID)return this._deviceID;const{deviceIdKey:t}=this._cache.keys;let n=this._cache.getStore(t);return typeof n=="string"&&n.length>=16&&n.length<=48||(n=Si(),this._cache.setStore(t,n)),this._deviceID=n,n}async _request(t,n,o={}){const s={"x-request-id":Si(),"x-device-id":this._getDeviceId()};if(o.withAccessToken){const{tokenTypeKey:r}=this._cache.keys,i=await this.getAccessToken(),a=this._cache.getStore(r);s.authorization=`${a} ${i}`}return this._reqClass[o.method==="get"?"get":"post"]({url:`${this._baseURL}${t}`,data:n,headers:s})}async _fetchAccessToken(){const{loginTypeKey:t,accessTokenKey:n,accessTokenExpireKey:o,tokenTypeKey:s}=this._cache.keys,r=this._cache.getStore(t);if(r&&r!==X.ANONYMOUS)throw new C({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});const i=await this._singlePromise.run("fetchAccessToken",async()=>(await this._request("/auth/v1/signin/anonymously",{},{method:"post"})).data),{access_token:a,expires_in:c,token_type:u}=i;return this._cache.setStore(s,u),this._cache.setStore(n,a),this._cache.setStore(o,Date.now()+1e3*c),a}isAccessTokenExpired(t,n){let o=!0;return t&&n&&(o=n<Date.now()),o}async getAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,o=this._cache.getStore(t),s=this._cache.getStore(n);return this.isAccessTokenExpired(o,s)?this._fetchAccessToken():o}async refreshAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:n,loginTypeKey:o}=this._cache.keys;return this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(o,X.ANONYMOUS),this.getAccessToken()}async getUserInfo(){return this._singlePromise.run("getUserInfo",async()=>(await this._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"})).data)}}const Ai=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],h_={"X-SDK-Version":"1.3.5"};function $o(e,t,n){const o=e[t];e[t]=function(s){const r={},i={};n.forEach(c=>{const{data:u,headers:f}=c.call(e,s);Object.assign(r,u),Object.assign(i,f)});const a=s.data;return a&&(()=>{var c;if(c=a,Object.prototype.toString.call(c)!=="[object FormData]")s.data={...a,...r};else for(const u in r)a.append(u,r[u])})(),s.headers={...s.headers||{},...i},o.call(e,s)}}function ko(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...h_,"x-seqid":e}}}class g_{constructor(t={}){var n;this.config=t,this._reqClass=new te.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Lt(this.config.env),this._localCache=(n=this.config.env,Su[n]),this.oauth=new p_(this.config),$o(this._reqClass,"post",[ko]),$o(this._reqClass,"upload",[ko]),$o(this._reqClass,"download",[ko])}async post(t){return await this._reqClass.post(t)}async upload(t){return await this._reqClass.upload(t)}async download(t){return await this._reqClass.download(t)}async refreshAccessToken(){let t,n;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{t=await this._refreshAccessTokenPromise}catch(o){n=o}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,n)throw n;return t}async _refreshAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o,loginTypeKey:s,anonymousUuidKey:r}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n);let i=this._cache.getStore(o);if(!i)throw new C({message:"未登录CloudBase"});const a={refresh_token:i},c=await this.request("auth.fetchAccessTokenWithRefreshToken",a);if(c.data.code){const{code:u}=c.data;if(u==="SIGN_PARAM_INVALID"||u==="REFRESH_TOKEN_EXPIRED"||u==="INVALID_REFRESH_TOKEN"){if(this._cache.getStore(s)===X.ANONYMOUS&&u==="INVALID_REFRESH_TOKEN"){const f=this._cache.getStore(r),l=this._cache.getStore(o),p=await this.send("auth.signInAnonymously",{anonymous_uuid:f,refresh_token:l});return this.setRefreshToken(p.refresh_token),this._refreshAccessToken()}ce(Pu),this._cache.removeStore(o)}throw new C({code:c.data.code,message:`刷新access token失败：${c.data.code}`})}if(c.data.access_token)return ce(Iu),this._cache.setStore(t,c.data.access_token),this._cache.setStore(n,c.data.access_token_expire+Date.now()),{accessToken:c.data.access_token,accessTokenExpire:c.data.access_token_expire};c.data.refresh_token&&(this._cache.removeStore(o),this._cache.setStore(o,c.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:o}=this._cache.keys;if(!this._cache.getStore(o))throw new C({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(t),r=this._cache.getStore(n),i=!0;return this._shouldRefreshAccessTokenHook&&!await this._shouldRefreshAccessTokenHook(s,r)&&(i=!1),(!s||!r||r<Date.now())&&i?this.refreshAccessToken():{accessToken:s,accessTokenExpire:r}}async request(t,n,o){const s=`x-tcb-trace_${this.config.env}`;let r="application/x-www-form-urlencoded";const i={action:t,env:this.config.env,dataVersion:"2019-08-16",...n};let a;if(Ai.indexOf(t)===-1&&(this._cache.keys,i.access_token=await this.oauth.getAccessToken()),t==="storage.uploadFile"){a=new FormData;for(let m in a)a.hasOwnProperty(m)&&a[m]!==void 0&&a.append(m,i[m]);r="multipart/form-data"}else{r="application/json",a={};for(let m in i)i[m]!==void 0&&(a[m]=i[m])}let c={headers:{"content-type":r}};o&&o.timeout&&(c.timeout=o.timeout),o&&o.onUploadProgress&&(c.onUploadProgress=o.onUploadProgress);const u=this._localCache.getStore(s);u&&(c.headers["X-TCB-Trace"]=u);const{parse:f,inQuery:l,search:p}=n;let g={env:this.config.env};f&&(g.parse=!0),l&&(g={...l,...g});let y=function(m,b,_={}){const v=/\?/.test(b);let x="";for(let w in _)x===""?!v&&(b+="?"):x+="&",x+=`${w}=${encodeURIComponent(_[w])}`;return/^http(s)?\:\/\//.test(b+=x)?b:`${m}${b}`}(o_,"//tcb-api.tencentcloudapi.com/web",g);p&&(y+=p);const h=await this.post({url:y,data:a,...c}),d=h.header&&h.header["x-tcb-trace"];if(d&&this._localCache.setStore(s,d),Number(h.status)!==200&&Number(h.statusCode)!==200||!h.data)throw new C({code:"NETWORK_ERROR",message:"network request error"});return h}async send(t,n={},o={}){const s=await this.request(t,n,{...o,onUploadProgress:n.onUploadProgress});if((s.data.code==="ACCESS_TOKEN_DISABLED"||s.data.code==="ACCESS_TOKEN_EXPIRED")&&Ai.indexOf(t)===-1){await this.oauth.refreshAccessToken();const r=await this.request(t,n,{...o,onUploadProgress:n.onUploadProgress});if(r.data.code)throw new C({code:r.data.code,message:wi(r.data.message)});return r.data}if(s.data.code)throw new C({code:s.data.code,message:wi(s.data.message)});return s.data}setRefreshToken(t){const{accessTokenKey:n,accessTokenExpireKey:o,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(n),this._cache.removeStore(o),this._cache.setStore(s,t)}}const Au={};function $e(e){return Au[e]}class fo{constructor(t){this.config=t,this._cache=Lt(t.env),this._request=$e(t.env)}setRefreshToken(t){const{accessTokenKey:n,accessTokenExpireKey:o,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(n),this._cache.removeStore(o),this._cache.setStore(s,t)}setAccessToken(t,n){const{accessTokenKey:o,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(o,t),this._cache.setStore(s,n)}async refreshUserInfo(){const{data:t}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(t),t}setLocalUserInfo(t){const{userInfoKey:n}=this._cache.keys;this._cache.setStore(n,t)}}class m_{constructor(t){if(!t)throw new C({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=Lt(this._envId),this._request=$e(this._envId),this.setUserInfo()}linkWithTicket(t){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:t})}linkWithRedirect(t){t.signInWithRedirect()}updatePassword(t,n){return this._request.send("auth.updatePassword",{oldPassword:n,newPassword:t})}updateEmail(t){return this._request.send("auth.updateEmail",{newEmail:t})}updateUsername(t){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:t})}async getLinkedUidList(){const{data:t}=await this._request.send("auth.getLinkedUidList",{});let n=!1;const{users:o}=t;return o.forEach(s=>{s.wxOpenId&&s.wxPublicId&&(n=!0)}),{users:o,hasPrimaryUid:n}}setPrimaryUid(t){return this._request.send("auth.setPrimaryUid",{uid:t})}unlink(t){return this._request.send("auth.unlink",{platform:t})}async update(t){const{nickName:n,gender:o,avatarUrl:s,province:r,country:i,city:a}=t,{data:c}=await this._request.send("auth.updateUserInfo",{nickName:n,gender:o,avatarUrl:s,province:r,country:i,city:a});this.setLocalUserInfo(c)}async refresh(){const t=await this._request.oauth.getUserInfo();return this.setLocalUserInfo(t),t}setUserInfo(){const{userInfoKey:t}=this._cache.keys,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach(o=>{this[o]=n[o]}),this.location={country:n.country,province:n.province,city:n.city}}setLocalUserInfo(t){const{userInfoKey:n}=this._cache.keys;this._cache.setStore(n,t),this.setUserInfo()}}class ln{constructor(t){if(!t)throw new C({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Lt(t);const{refreshTokenKey:n,accessTokenKey:o,accessTokenExpireKey:s}=this._cache.keys,r=this._cache.getStore(n),i=this._cache.getStore(o),a=this._cache.getStore(s);this.credential={refreshToken:r,accessToken:i,accessTokenExpire:a},this.user=new m_(t)}get isAnonymousAuth(){return this.loginType===X.ANONYMOUS}get isCustomAuth(){return this.loginType===X.CUSTOM}get isWeixinAuth(){return this.loginType===X.WECHAT||this.loginType===X.WECHAT_OPEN||this.loginType===X.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class Ro extends fo{async signIn(){this._cache.updatePersistence("local"),await this._request.oauth.getAccessToken(),ce(Ot),ce(qe,{env:this.config.env,loginType:X.ANONYMOUS,persistence:"local"});const t=new ln(this.config.env);return await t.user.refresh(),t}async linkAndRetrieveDataWithTicket(t){const{anonymousUuidKey:n,refreshTokenKey:o}=this._cache.keys,s=this._cache.getStore(n),r=this._cache.getStore(o),i=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:r,ticket:t});if(i.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),ce(bs,{env:this.config.env}),ce(qe,{loginType:X.CUSTOM,persistence:"local"}),{credential:{refreshToken:i.refresh_token}};throw new C({message:"匿名转化失败"})}_setAnonymousUUID(t){const{anonymousUuidKey:n,loginTypeKey:o}=this._cache.keys;this._cache.removeStore(n),this._cache.setStore(n,t),this._cache.setStore(o,X.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class Ti extends fo{async signIn(t){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:n}=this._cache.keys,o=await this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});if(o.refresh_token)return this.setRefreshToken(o.refresh_token),await this._request.refreshAccessToken(),ce(Ot),ce(qe,{env:this.config.env,loginType:X.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new ln(this.config.env);throw new C({message:"自定义登录失败"})}}class Ei extends fo{async signIn(t,n){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:o}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(o)||""}),{refresh_token:r,access_token:i,access_token_expire:a}=s;if(r)return this.setRefreshToken(r),i&&a?this.setAccessToken(i,a):await this._request.refreshAccessToken(),await this.refreshUserInfo(),ce(Ot),ce(qe,{env:this.config.env,loginType:X.EMAIL,persistence:this.config.persistence}),new ln(this.config.env);throw s.code?new C({code:s.code,message:`邮箱登录失败: ${s.message}`}):new C({message:"邮箱登录失败"})}async activate(t){return this._request.send("auth.activateEndUserMail",{token:t})}async resetPasswordWithToken(t,n){return this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n})}}class Oi extends fo{async signIn(t,n){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"username must be a string"});typeof n!="string"&&(n="",console.warn("password is empty"));const{refreshTokenKey:o}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:X.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(o)||""}),{refresh_token:r,access_token_expire:i,access_token:a}=s;if(r)return this.setRefreshToken(r),a&&i?this.setAccessToken(a,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),ce(Ot),ce(qe,{env:this.config.env,loginType:X.USERNAME,persistence:this.config.persistence}),new ln(this.config.env);throw s.code?new C({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new C({message:"用户名密码登录失败"})}}class y_{constructor(t){this.config=t,this._cache=Lt(t.env),this._request=$e(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),Ne(qe,this._onLoginTypeChanged)}get currentUser(){const t=this.hasLoginState();return t&&t.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new Ro(this.config)}customAuthProvider(){return new Ti(this.config)}emailAuthProvider(){return new Ei(this.config)}usernameAuthProvider(){return new Oi(this.config)}async signInAnonymously(){return new Ro(this.config).signIn()}async signInWithEmailAndPassword(t,n){return new Ei(this.config).signIn(t,n)}signInWithUsernameAndPassword(t,n){return new Oi(this.config).signIn(t,n)}async linkAndRetrieveDataWithTicket(t){return this._anonymousAuthProvider||(this._anonymousAuthProvider=new Ro(this.config)),Ne(bs,this._onAnonymousConverted),await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t)}async signOut(){if(this.loginType===X.ANONYMOUS)throw new C({message:"匿名用户不支持登出操作"});const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:o}=this._cache.keys,s=this._cache.getStore(t);if(!s)return;const r=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(t),this._cache.removeStore(n),this._cache.removeStore(o),ce(Ot),ce(qe,{env:this.config.env,loginType:X.NULL,persistence:this.config.persistence}),r}async signUpWithEmailAndPassword(t,n){return this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n})}async sendPasswordResetEmail(t){return this._request.send("auth.sendPasswordResetEmail",{email:t})}onLoginStateChanged(t){Ne(Ot,()=>{const o=this.hasLoginState();t.call(this,o)});const n=this.hasLoginState();t.call(this,n)}onLoginStateExpired(t){Ne(Pu,t.bind(this))}onAccessTokenRefreshed(t){Ne(Iu,t.bind(this))}onAnonymousConverted(t){Ne(bs,t.bind(this))}onLoginTypeChanged(t){Ne(qe,()=>{const n=this.hasLoginState();t.call(this,n)})}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,o=this._cache.getStore(t),s=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(o,s)?null:new ln(this.config.env)}async isUsernameRegistered(t){if(typeof t!="string")throw new C({code:"PARAM_ERROR",message:"username must be a string"});const{data:n}=await this._request.send("auth.isUsernameRegistered",{username:t});return n&&n.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(t){return new Ti(this.config).signIn(t)}shouldRefreshAccessToken(t){this._request._shouldRefreshAccessTokenHook=t.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then(t=>t.code?t:{...t.data,requestId:t.seqId})}getAuthHeader(){const{refreshTokenKey:t,accessTokenKey:n}=this._cache.keys,o=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+o}}_onAnonymousConverted(t){const{env:n}=t.data;n===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(t){const{loginType:n,persistence:o,env:s}=t.data;s===this.config.env&&(this._cache.updatePersistence(o),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}const __=function(e,t){t=t||un();const n=$e(this.config.env),{cloudPath:o,filePath:s,onUploadProgress:r,fileType:i="image"}=e;return n.send("storage.getUploadMetadata",{path:o}).then(a=>{const{data:{url:c,authorization:u,token:f,fileId:l,cosFileId:p},requestId:g}=a,y={key:o,signature:u,"x-cos-meta-fileid":p,success_action_status:"201","x-cos-security-token":f};n.upload({url:c,data:y,file:s,name:o,fileType:i,onUploadProgress:r}).then(h=>{h.statusCode===201?t(null,{fileID:l,requestId:g}):t(new C({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${h.data}`}))}).catch(h=>{t(h)})}).catch(a=>{t(a)}),t.promise},b_=function(e,t){t=t||un();const n=$e(this.config.env),{cloudPath:o}=e;return n.send("storage.getUploadMetadata",{path:o}).then(s=>{t(null,s)}).catch(s=>{t(s)}),t.promise},v_=function({fileList:e},t){if(t=t||un(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let o of e)if(!o||typeof o!="string")return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return $e(this.config.env).send("storage.batchDeleteFile",n).then(o=>{o.code?t(null,o):t(null,{fileList:o.data.delete_list,requestId:o.requestId})}).catch(o=>{t(o)}),t.promise},Tu=function({fileList:e},t){t=t||un(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let s of e)typeof s=="object"?(s.hasOwnProperty("fileID")&&s.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:s.fileID,max_age:s.maxAge})):typeof s=="string"?n.push({fileid:s}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const o={file_list:n};return $e(this.config.env).send("storage.batchGetDownloadUrl",o).then(s=>{s.code?t(null,s):t(null,{fileList:s.data.download_list,requestId:s.requestId})}).catch(s=>{t(s)}),t.promise},w_=async function({fileID:e},t){const n=(await Tu.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if(n.code!=="SUCCESS")return t?t(n):new Promise(r=>{r(n)});const o=$e(this.config.env);let s=n.download_url;if(s=encodeURI(s),!t)return o.download({url:s});t(await o.download({url:s}))},S_=function({name:e,data:t,query:n,parse:o,search:s,timeout:r},i){const a=i||un();let c;try{c=t?JSON.stringify(t):""}catch(f){return Promise.reject(f)}if(!e)return Promise.reject(new C({code:"PARAM_ERROR",message:"函数名不能为空"}));const u={inQuery:n,parse:o,search:s,function_name:e,request_data:c};return $e(this.config.env).send("functions.invokeFunction",u,{timeout:r}).then(f=>{if(f.code)a(null,f);else{let l=f.data.response_data;if(o)a(null,{result:l,requestId:f.requestId});else try{l=JSON.parse(f.data.response_data),a(null,{result:l,requestId:f.requestId})}catch{a(new C({message:"response data must be json"}))}}return a.promise}).catch(f=>{a(f)}),a.promise},Ci={timeout:15e3,persistence:"session"},$i={};class ir{constructor(t){this.config=t||this.config,this.authObj=void 0}init(t){switch(te.adapter||(this.requestClient=new te.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:`请求在${(t.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...Ci,...t},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new ir(this.config)}auth({persistence:t}={}){if(this.authObj)return this.authObj;const n=t||te.adapter.primaryStorage||Ci.persistence;var o;return n!==this.config.persistence&&(this.config.persistence=n),function(s){const{env:r}=s;wu[r]=new Ii(s),Su[r]=new Ii({...s,persistence:"local"})}(this.config),o=this.config,Au[o.env]=new g_(o),this.authObj=new y_(this.config),this.authObj}on(t,n){return Ne.apply(this,[t,n])}off(t,n){return f_.apply(this,[t,n])}callFunction(t,n){return S_.apply(this,[t,n])}deleteFile(t,n){return v_.apply(this,[t,n])}getTempFileURL(t,n){return Tu.apply(this,[t,n])}downloadFile(t,n){return w_.apply(this,[t,n])}uploadFile(t,n){return __.apply(this,[t,n])}getUploadMetadata(t,n){return b_.apply(this,[t,n])}registerExtension(t){$i[t.name]=t}async invokeExtension(t,n){const o=$i[t];if(!o)throw new C({message:`扩展${t} 必须先注册`});return await o.invoke(n,this)}useAdapters(t){const{adapter:n,runtime:o}=c_(t)||{};n&&(te.adapter=n),o&&(te.runtime=o)}}var Eu=new ir;function Lo(e,t,n){n===void 0&&(n={});var o=/\?/.test(t),s="";for(var r in n)s===""?!o&&(t+="?"):s+="&",s+=r+"="+encodeURIComponent(n[r]);return/^http(s)?:\/\//.test(t+=s)?t:""+e+t}class x_{get(t){const{url:n,data:o,headers:s,timeout:r}=t;return new Promise((i,a)=>{Y.request({url:Lo("https:",n),data:o,method:"GET",header:s,timeout:r,success(c){i(c)},fail(c){a(c)}})})}post(t){const{url:n,data:o,headers:s,timeout:r}=t;return new Promise((i,a)=>{Y.request({url:Lo("https:",n),data:o,method:"POST",header:s,timeout:r,success(c){i(c)},fail(c){a(c)}})})}upload(t){return new Promise((n,o)=>{const{url:s,file:r,data:i,headers:a,fileType:c}=t,u=Y.uploadFile({url:Lo("https:",s),name:"file",formData:Object.assign({},i),filePath:r,fileType:c,header:a,success(f){const l={statusCode:f.statusCode,data:f.data||{}};f.statusCode===200&&i.success_action_status&&(l.statusCode=parseInt(i.success_action_status,10)),n(l)},fail(f){o(new Error(f.errMsg||"uploadFile:fail"))}});typeof t.onUploadProgress=="function"&&u&&typeof u.onProgressUpdate=="function"&&u.onProgressUpdate(f=>{t.onUploadProgress({loaded:f.totalBytesSent,total:f.totalBytesExpectedToSend})})})}}const P_={setItem(e,t){Y.setStorageSync(e,t)},getItem:e=>Y.getStorageSync(e),removeItem(e){Y.removeStorageSync(e)},clear(){Y.clearStorageSync()}};var I_={genAdapter:function(){return{root:{},reqClass:x_,localStorage:P_,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Eu.useAdapters(I_);const ar=Eu,A_=ar.init;ar.init=function(e){e.env=e.spaceId;const t=A_.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(o){const s=n.call(this,o);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach(r=>{var i;s[r]=(i=s[r],function(a){a=a||{};const{success:c,fail:u,complete:f}=mu(a);if(!(c||u||f))return i.call(this,a);i.call(this,a).then(l=>{c&&c(l),f&&f(l)},l=>{u&&u(l),f&&f(l)})}).bind(s)}),s},t.customAuth=t.auth,t};var ki=ar;async function T_(e,t){const n=`http://${e}:${t}/system/ping`;try{const s=await(o={url:n,timeout:500},new Promise((r,i)=>{Y.request({...o,success(a){r(a)},fail(a){i(a)}})}));return!(!s.data||s.data.code!==0)}catch{return!1}var o}async function Ou(e,t){let n;for(let o=0;o<e.length;o++){const s=e[o];if(await T_(s,t)){n=s;break}}return{address:n,port:t}}const E_={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var O_=class{constructor(e){if(["spaceId","clientSecret"].forEach(t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)}),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=Y}async request(e,t=!0){const n=t;return e=n?await this.setupLocalRequest(e):this.setupRequest(e),Promise.resolve().then(()=>n?this.requestLocal(e):yt.wrappedRequest(e,this.adapter.request))}requestLocal(e){return new Promise((t,n)=>{this.adapter.request(Object.assign(e,{complete(o){if(o||(o={}),!o.statusCode||o.statusCode>=400){const s=o.data&&o.data.code||"SYS_ERR",r=o.data&&o.data.message||"request:fail";return n(new C({code:s,message:r}))}t({success:!0,result:o.data})}}))})}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=yt.sign(t,this.config.clientSecret);const o=ys();n["x-client-info"]=encodeURIComponent(JSON.stringify(o));const{token:s}=Yt();return n["x-client-token"]=s,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=ys(),{token:n}=Yt(),o=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:s,servePort:r}=this.__dev__&&this.__dev__.debugInfo||{},{address:i}=await Ou(s,r);return{url:`http://${i}:${r}/${E_[e.method]}`,method:"POST",data:o,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:o}){if(!t)throw new C({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let s;return this.getUploadFileOptions({cloudPath:t}).then(r=>{const{url:i,formData:a,name:c}=r.result;return s=r.result.fileUrl,new Promise((u,f)=>{const l=this.adapter.uploadFile({url:i,formData:a,name:c,filePath:e,fileType:n,success(p){p&&p.statusCode<400?u(p):f(new C({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(p){f(new C({code:p.code||"UPLOAD_FAILED",message:p.message||p.errMsg||"文件上传失败"}))}});typeof o=="function"&&l&&typeof l.onProgressUpdate=="function"&&l.onProgressUpdate(p=>{o({loaded:p.totalBytesSent,total:p.totalBytesExpectedToSend})})})}).then(()=>this.reportUploadFile({cloudPath:t})).then(r=>new Promise((i,a)=>{r.success?i({success:!0,filePath:e,fileID:s}):a(new C({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then(n=>{if(n.success)return n.result;throw new C({code:"DELETE_FILE_FAILED",message:"删除文件失败"})})}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||e.length===0)throw new C({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then(o=>{if(o.success)return{fileList:o.result.fileList.map(s=>({fileID:s.fileID,tempFileURL:s.tempFileURL}))};throw new C({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})})}},Ri={init(e){const t=new O_(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Bt=Se(function(e,t){e.exports=Fe.enc.Hex});function Cu(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return(e==="x"?t:3&t|8).toString(16)})}function $u(e="",t={}){const{data:n,functionName:o,method:s,headers:r,signHeaderKeys:i=[],config:a}=t,c=String(Date.now()),u=Cu(),f=Object.assign({},r,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":o,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":u,"x-alipay-callid":u,"x-trace-id":u}),l=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(i),[p="",g=""]=e.split("?")||[],y=function(h){const d=h.signedHeaders.join(";"),m=h.signedHeaders.map(A=>`${A.toLowerCase()}:${h.headers[A]}
`).join(""),b=_s(h.body).toString(Bt),_=`${h.method.toUpperCase()}
${h.path}
${h.query}
${m}
${d}
${b}
`,v=_s(_).toString(Bt),x=`HMAC-SHA256
${h.timestamp}
${v}
`,w=vu(x,h.secretKey).toString(Bt);return`HMAC-SHA256 Credential=${h.secretId}, SignedHeaders=${d}, Signature=${w}`}({path:p,query:g,method:s,headers:f,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:l.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},f,{Authorization:y})}}function ku({url:e,data:t,method:n="POST",headers:o={},timeout:s}){return new Promise((r,i)=>{Y.request({url:e,method:n,data:typeof t=="object"?JSON.stringify(t):t,header:o,dataType:"json",timeout:s,complete:(a={})=>{const c=o["x-trace-id"]||"";if(!a.statusCode||a.statusCode>=400){const{message:u,errMsg:f,trace_id:l}=a.data||{};return i(new C({code:"SYS_ERR",message:u||f||"request:fail",requestId:l||c}))}r({status:a.statusCode,data:a.data,headers:a.header,requestId:c})}})})}function Li(e,t){const{path:n,data:o,method:s="GET"}=e,{url:r,headers:i}=$u(n,{functionName:"",data:o,method:s,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return ku({url:r,data:o,method:s,headers:i}).then(a=>{const c=a.data||{};if(!c.success)throw new C({code:a.errCode,message:a.errMsg,requestId:a.requestId});return c.data||{}}).catch(a=>{throw new C({code:a.errCode,message:a.errMsg,requestId:a.requestId})})}function C_(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new C({code:"INVALID_PARAM",message:"fileID不合法"});const o=t.substring(0,n),s=t.substring(n+1);return o!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),s}function $_(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class k_{constructor(t){this.config=t}signedURL(t,n={}){const o=`/ws/function/${t}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),r=Object.assign({},n,{accessKeyId:this.config.accessKey,signatureNonce:Cu(),timestamp:""+Date.now()}),i=[o,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map(function(f){return r[f]?"".concat(f,"=").concat(r[f]):null}).filter(Boolean).join("&"),`host:${s}`].join(`
`),a=["HMAC-SHA256",_s(i).toString(Bt)].join(`
`),c=vu(a,this.config.secretKey).toString(Bt),u=Object.keys(r).map(f=>`${f}=${encodeURIComponent(r[f])}`).join("&");return`${this.config.wsEndpoint}${o}?${u}&signature=${c}`}}var R_=class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach(t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)}),e.endpoint){if(typeof e.endpoint!="string")throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new k_(this.config)}callFunction(e){return function(t,n){const{name:o,data:s,async:r=!1,timeout:i}=t,a="POST",c={"x-to-function-name":o};r&&(c["x-function-invoke-type"]="async");const{url:u,headers:f}=$u("/functions/invokeFunction",{functionName:o,data:s,method:a,headers:c,signHeaderKeys:["x-to-function-name"],config:n});return ku({url:u,data:s,method:a,headers:f,timeout:i}).then(l=>{let p=0;if(r){const g=l.data||{};p=g.errCode==="200"?0:g.errCode,l.data=g.data||{},l.errMsg=g.errMsg}if(p!==0)throw new C({code:p,message:l.errMsg,requestId:l.requestId});return{errCode:p,success:p===0,requestId:l.requestId,result:l.data}}).catch(l=>{throw new C({code:l.errCode,message:l.errMsg,requestId:l.requestId})})}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:o,onUploadProgress:s}){return new Promise((r,i)=>{const a=Y.uploadFile({url:e,filePath:t,fileType:n,formData:o,name:"file",success(c){c&&c.statusCode<400?r(c):i(new C({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(c){i(new C({code:c.code||"UPLOAD_FAILED",message:c.message||c.errMsg||"文件上传失败"}))}});typeof s=="function"&&a&&typeof a.onProgressUpdate=="function"&&a.onProgressUpdate(c=>{s({loaded:c.totalBytesSent,total:c.totalBytesExpectedToSend})})})}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:o}){if(Gt(t)!=="string")throw new C({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new C({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new C({code:"INVALID_PARAM",message:"cloudPath不合法"});const s=await Li({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:r,upload_url:i,form_data:a}=s,c=a&&a.reduce((u,f)=>(u[f.key]=f.value,u),{});return this.uploadFileToOSS({url:i,filePath:e,fileType:n,formData:c,onUploadProgress:o}).then(()=>({fileID:r}))}async getTempFileURL({fileList:e}){return new Promise((t,n)=>{(!e||e.length<0)&&t({code:"INVALID_PARAM",message:"fileList不能为空数组"}),e.length>50&&t({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});const o=[];for(const s of e){let r;Gt(s)!=="string"&&t({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{r=C_.call(this,s)}catch(i){console.warn(i.errCode,i.errMsg),r=s}o.push({file_id:r,expire:600})}Li({path:"/?download_url",data:{file_list:o},method:"POST"},this.config).then(s=>{const{file_list:r=[]}=s;t({fileList:r.map(i=>({fileID:$_.call(this,i.file_id),tempFileURL:i.download_url}))})}).catch(s=>n(s))})}async connectWebSocket(e){const{name:t,query:n}=e;return Y.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}},L_={init:e=>{e.provider="alipay";const t=new R_(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Ru({data:e}){let t;t=ys();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:o}=Yt();o&&(n.uniIdToken=o)}return n}async function Ni(e={}){await this.__dev__.initLocalNetwork();const{localAddress:t,localPort:n}=this.__dev__,o={aliyun:"aliyun",tencent:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],s=this.config.spaceId,r=`http://${t}:${n}/system/check-function`,i=`http://${t}:${n}/cloudfunctions/${e.name}`;return new Promise((a,c)=>{Y.request({method:"POST",url:r,data:{name:e.name,platform:ct,provider:o,spaceId:s},timeout:3e3,success(u){a(u)},fail(){a({data:{code:"NETWORK_ERROR",message:"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下，自动切换为已部署的云函数。"}})}})}).then(({data:a}={})=>{const{code:c,message:u}=a||{};return{code:c===0?0:c||"SYS_ERR",message:u||"SYS_ERR"}}).then(({code:a,message:c})=>{if(a!==0){switch(a){case"MODULE_ENCRYPTED":console.error(`此云函数（${e.name}）依赖加密公共模块不可本地调试，自动切换为云端已部署的云函数`);break;case"FUNCTION_ENCRYPTED":console.error(`此云函数（${e.name}）已加密不可本地调试，自动切换为云端已部署的云函数`);break;case"ACTION_ENCRYPTED":console.error(c||"需要访问加密的uni-clientDB-action，自动切换为云端环境");break;case"NETWORK_ERROR":console.error(c||"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下");break;case"SWITCH_TO_CLOUD":break;default:{const u=`检测本地调试服务出现错误：${c}，请检查网络环境或重启客户端再试`;throw console.error(u),new Error(u)}}return this._callCloudFunction(e)}return new Promise((u,f)=>{const l=Ru.call(this,{data:e.data});Y.request({method:"POST",url:i,data:{provider:o,platform:ct,param:l},timeout:e.timeout,success:({statusCode:p,data:g}={})=>!p||p>=400?f(new C({code:g.code||"SYS_ERR",message:g.message||"request:fail"})):u({result:g}),fail(p){f(new C({code:p.code||p.errCode||"SYS_ERR",message:p.message||p.errMsg||"request:fail"}))}})})})}const N_=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var Lu=/[\\^$.*+?()[\]{}|]/g,j_=RegExp(Lu.source);function ji(e,t,n){return e.replace(new RegExp((o=t)&&j_.test(o)?o.replace(Lu,"\\$&"):o,"g"),n);var o}const D_="request",M_="response",U_="both",H_={code:2e4,message:"System error"},F_={code:20101,message:"Invalid client"};function Nu(e){const{errSubject:t,subject:n,errCode:o,errMsg:s,code:r,message:i,cause:a}=e||{};return new C({subject:t||n||"uni-secure-network",code:o||r||H_.code,message:s||i,cause:a})}let vs;function Di({secretType:e}={}){return e===D_||e===M_||e===U_}function Mi({name:e,data:t={}}={}){return ct==="app"}function q_({provider:e,spaceId:t,functionName:n}={}){const{appId:o,uniPlatform:s,osName:r}=bu();let i=s;s==="app"&&(i=r);const a=function({provider:l,spaceId:p}={}){const g=Gy;if(!g)return{};l=function(h){return h==="tencent"?"tcb":h}(l);const y=g.find(h=>h.provider===l&&h.spaceId===p);return y&&y.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},u=Object.keys(c);if(u.length===0)return!0;const f=function(l,p){let g,y,h;for(let d=0;d<l.length;d++){const m=l[d];m!==p?m!=="*"?m.split(",").map(b=>b.trim()).indexOf(p)>-1&&(y=m):h=m:g=m}return g||y||h}(u,n);if(!f)return!1;if((c[f]||[]).find((l={})=>l.appId===o&&(l.platform||"").toLowerCase()===i.toLowerCase()))return!0;throw console.error(`此应用[appId: ${o}, platform: ${i}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),Nu(F_)}function Ui({functionName:e,result:t,logPvd:n}){if(this.__dev__.debugLog&&t&&t.requestId){const o=JSON.stringify({spaceId:this.config.spaceId,functionName:e,requestId:t.requestId});console.log(`[${n}-request]${o}[/${n}-request]`)}}function B_(e){const t=e.callFunction,n=function(o){const s=o.name;o.data=Ru.call(e,{data:o.data});const r={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],i=Di(o),a=Mi(o),c=i||a;return t.call(this,o).then(u=>(u.errCode=0,!c&&Ui.call(this,{functionName:s,result:u,logPvd:r}),Promise.resolve(u)),u=>(!c&&Ui.call(this,{functionName:s,result:u,logPvd:r}),u&&u.message&&(u.message=function({message:f="",extraInfo:l={},formatter:p=[]}={}){for(let g=0;g<p.length;g++){const{rule:y,content:h,mode:d}=p[g],m=f.match(y);if(!m)continue;let b=h;for(let _=1;_<m.length;_++)b=ji(b,`{$${_}}`,m[_]);for(const _ in l)b=ji(b,`{${_}}`,l[_]);return d==="replace"?b:f+b}return f}({message:`[${o.name}]: ${u.message}`,formatter:N_,extraInfo:{functionName:s}})),Promise.reject(u)))};e.callFunction=function(o){const{provider:s,spaceId:r}=e.config,i=o.name;let a,c;return o.data=o.data||{},e.__dev__.debugInfo&&!e.__dev__.debugInfo.forceRemote&&du?(e._callCloudFunction||(e._callCloudFunction=n,e._callLocalFunction=Ni),a=Ni):a=n,a=a.bind(e),Mi(o)||(function({name:u,data:f={}}){return u==="uni-id-co"&&f.method==="secureNetworkHandshakeByWeixin"}(o)?c=a.call(e,o):Di(o)?c=new vs({secretType:o.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(o):q_({provider:s,spaceId:r,functionName:i})?c=new vs({secretType:o.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(o):c=a(o)),Object.defineProperty(c,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),c.then(u=>(typeof UTSJSONObject<"u"&&typeof UTS<"u"&&(u.result=UTS.JSON.parse(JSON.stringify(u.result))),u))}}vs=class{constructor(){throw Nu({message:`Platform ${ct} is not enabled, please check whether secure network module is enabled in your manifest.json`})}};const ju=Symbol("CLIENT_DB_INTERNAL");function Yn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=ju,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(n,o,s){if(o==="_uniClient")return null;if(typeof o=="symbol")return n[o];if(o in n||typeof o!="string"){const r=n[o];return typeof r=="function"?r.bind(n):r}return t.get(n,o,s)}})}function Hi(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const o=e[t].indexOf(n);o!==-1&&e[t].splice(o,1)}}}const V_=["db.Geo","db.command","command.aggregate"];function Du(e,t){return V_.indexOf(`${e}.${t}`)>-1}function nt(e){switch(Gt(e=yu(e))){case"array":return e.map(t=>nt(t));case"object":return e._internalType===ju||Object.keys(e).forEach(t=>{e[t]=nt(e[t])}),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function Dt(e){return e&&e.content&&e.content.$method}class K_{constructor(t,n,o){this.content=t,this.prevStage=n||null,this.udb=null,this._database=o}toJSON(){let t=this;const n=[t.content];for(;t.prevStage;)t=t.prevStage,n.push(t.content);return{$db:n.reverse().map(o=>({$method:o.$method,$param:nt(o.$param)}))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const t=this.toJSON().$db.find(n=>n.$method==="action");return t&&t.$param&&t.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter(t=>t.$method!=="action")}}get isAggregate(){let t=this;for(;t;){const n=Dt(t),o=Dt(t.prevStage);if(n==="aggregate"&&o==="collection"||n==="pipeline")return!0;t=t.prevStage}return!1}get isCommand(){let t=this;for(;t;){if(Dt(t)==="command")return!0;t=t.prevStage}return!1}get isAggregateCommand(){let t=this;for(;t;){const n=Dt(t),o=Dt(t.prevStage);if(n==="aggregate"&&o==="command")return!0;t=t.prevStage}return!1}getNextStageFn(t){const n=this;return function(){return Xt({$method:t,$param:nt(Array.from(arguments))},n,n._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(t,n){const o=this.getAction(),s=this.getCommand();if(s.$db.push({$method:t,$param:nt(n)}),zy){const r=s.$db.find(a=>a.$method==="collection"),i=r&&r.$param;i&&i.length===1&&typeof r.$param[0]=="string"&&r.$param[0].indexOf(",")>-1&&console.warn(`检测到使用JQL语法联表查询时，未使用getTemp先过滤主表数据，在主表数据量大的情况下可能会查询缓慢。
- 如何优化请参考此文档：https://uniapp.dcloud.net.cn/uniCloud/jql?id=lookup-with-temp 
- 如果主表数据量很小请忽略此信息，项目发行时不会出现此提示。`)}return this._database._callCloudFunction({action:o,command:s})}}function Xt(e,t,n){return Yn(new K_(e,t,n),{get(o,s){let r="db";return o&&o.content&&(r=o.content.$method),Du(r,s)?Xt({$method:s},o,n):function(){return Xt({$method:s,$param:nt(Array.from(arguments))},o,n)}}})}function No({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map(n=>({$method:n})),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function Fi(e,t={}){return Yn(new e(t),{get:(n,o)=>Du("db",o)?Xt({$method:o},null,n):function(){return Xt({$method:o,$param:nt(Array.from(arguments))},null,n)}})}class qi extends class{constructor({uniClient:t={},isJQL:n=!1}={}){this._uniClient=t,this._authCallBacks={},this._dbCallBacks={},t._isDefault&&(this._dbCallBacks=Tt("_globalUniCloudDatabaseCallback")),n||(this.auth=Hi(this._authCallBacks)),this._isJQL=n,Object.assign(this,Hi(this._dbCallBacks)),this.env=Yn({},{get:(o,s)=>({$env:s})}),this.Geo=Yn({},{get:(o,s)=>No({path:["Geo"],method:s})}),this.serverDate=No({path:[],method:"serverDate"}),this.RegExp=No({path:[],method:"RegExp"})}getCloudEnv(t){if(typeof t!="string"||!t.trim())throw new Error("getCloudEnv参数错误");return{$env:t.replace("$cloudEnv_","")}}_callback(t,n){const o=this._dbCallBacks;o[t]&&o[t].forEach(s=>{s(...n)})}_callbackAuth(t,n){const o=this._authCallBacks;o[t]&&o[t].forEach(s=>{s(...n)})}multiSend(){const t=Array.from(arguments),n=t.map(o=>{const s=o.getAction(),r=o.getCommand();if(r.$db[r.$db.length-1].$method!=="getTemp")throw new Error("multiSend只支持子命令内使用getTemp");return{action:s,command:r}});return this._callCloudFunction({multiCommand:n,queryList:t})}}{_parseResult(t){return this._isJQL?t.result:t}_callCloudFunction({action:t,command:n,multiCommand:o,queryList:s}){function r(l,p){if(o&&s)for(let g=0;g<s.length;g++){const y=s[g];y.udb&&typeof y.udb.setResult=="function"&&(p?y.udb.setResult(p):y.udb.setResult(l.result.dataList[g]))}}const i=this,a=this._isJQL?"databaseForJQL":"database";function c(l){return i._callback("error",[l]),ie(ae(a,"fail"),l).then(()=>ie(ae(a,"complete"),l)).then(()=>(r(null,l),pe(be,{type:_i,content:l}),Promise.reject(l)))}const u=ie(ae(a,"invoke")),f=this._uniClient;return u.then(()=>f.callFunction({name:"DCloud-clientDB",type:Vy,data:{action:t,command:n,multiCommand:o}})).then(l=>{const{code:p,message:g,token:y,tokenExpired:h,systemInfo:d=[]}=l.result;if(d)for(let b=0;b<d.length;b++){const{level:_,message:v,detail:x}=d[b],w=console[_]||console.log;let A="[System Info]"+v;x&&(A=`${A}
详细信息：${x}`),w(A)}if(p)return c(new C({code:p,message:g,requestId:l.requestId}));l.result.errCode=l.result.errCode||l.result.code,l.result.errMsg=l.result.errMsg||l.result.message,y&&h&&(_u({token:y,tokenExpired:h}),this._callbackAuth("refreshToken",[{token:y,tokenExpired:h}]),this._callback("refreshToken",[{token:y,tokenExpired:h}]),pe(Gn,{token:y,tokenExpired:h}));const m=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let b=0;b<m.length;b++){const{prop:_,tips:v}=m[b];if(_ in l.result){const x=l.result[_];Object.defineProperty(l.result,_,{get:()=>(console.warn(v),x)})}}return function(b){return ie(ae(a,"success"),b).then(()=>ie(ae(a,"complete"),b)).then(()=>{r(b,null);const _=i._parseResult(b);return pe(be,{type:_i,content:_}),Promise.resolve(_)})}(l)},l=>(/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(l.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),c(new C({code:l.code||"SYSTEM_ERROR",message:l.message,requestId:l.requestId}))))}}const Ye="token无效，跳转登录页面",Mu="token过期，跳转登录页面",W_={TOKEN_INVALID_TOKEN_EXPIRED:Mu,TOKEN_INVALID_INVALID_CLIENTID:Ye,TOKEN_INVALID:Ye,TOKEN_INVALID_WRONG_TOKEN:Ye,TOKEN_INVALID_ANONYMOUS_USER:Ye},ws={"uni-id-token-expired":Mu,"uni-id-check-token-failed":Ye,"uni-id-token-not-exist":Ye,"uni-id-check-device-feature-failed":Ye};function Bi(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function Vi(e=[],t=""){const n=[],o=[];return e.forEach(s=>{s.needLogin===!0?n.push(Bi(t,s.path)):s.needLogin===!1&&o.push(Bi(t,s.path))}),{needLoginPage:n,notNeedLoginPage:o}}function Qt(e){return e.split("?")[0].replace(/^\//,"")}function cr(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t&&(t.charAt(0)!=="/"&&(t="/"+t),t)}(function(){const e=getCurrentPages();return e[e.length-1]}())}function Uu(){return Qt(cr())}function z_(e="",t={}){if(!e||!(t&&t.list&&t.list.length))return!1;const n=t.list,o=Qt(e);return n.some(s=>s.pagePath===o)}const J_=!!cu.uniIdRouter,{loginPage:Zt,routerNeedLogin:G_,resToLogin:Y_,needLoginPage:Hu,notNeedLoginPage:X_,loginPageInTabBar:Q_}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:o={}}=cu){const{loginPage:s,needLogin:r=[],resToLogin:i=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=Vi(e),{needLoginPage:u,notNeedLoginPage:f}=function(l=[]){const p=[],g=[];return l.forEach(y=>{const{root:h,pages:d=[]}=y,{needLoginPage:m,notNeedLoginPage:b}=Vi(d,h);p.push(...m),g.push(...b)}),{needLoginPage:p,notNeedLoginPage:g}}(t);return{loginPage:s,routerNeedLogin:r,resToLogin:i,needLoginPage:[...a,...u],notNeedLoginPage:[...c,...f],loginPageInTabBar:z_(s,o)}}();if(Hu.indexOf(Zt)>-1)throw new Error(`Login page [${Zt}] should not be "needLogin", please check your pages.json`);function Fu(e){const t=Uu();if(e.charAt(0)==="/")return e;const[n,o]=e.split("?"),s=n.replace(/^\//,"").split("/"),r=t.split("/");r.pop();for(let i=0;i<s.length;i++){const a=s[i];a===".."?r.pop():a!=="."&&r.push(a)}return r[0]===""&&r.shift(),"/"+r.join("/")+(o?"?"+o:"")}function Z_(e){const t=Qt(Fu(e));return!(X_.indexOf(t)>-1)&&(Hu.indexOf(t)>-1||G_.some(n=>function(o,s){return new RegExp(s).test(o)}(e,n)))}function qu({redirect:e}){const t=Qt(e),n=Qt(Zt);return Uu()!==n&&t!==n}function Ss({api:e,redirect:t}={}){if(!t||!qu({redirect:t}))return;const n=function(s,r){return s.charAt(0)!=="/"&&(s="/"+s),r?s.indexOf("?")>-1?s+`&uniIdRedirectUrl=${encodeURIComponent(r)}`:s+`?uniIdRedirectUrl=${encodeURIComponent(r)}`:s}(Zt,t);Q_?e!=="navigateTo"&&e!=="redirectTo"||(e="switchTab"):e==="switchTab"&&(e="navigateTo");const o={navigateTo:N.navigateTo,redirectTo:N.redirectTo,switchTab:N.switchTab,reLaunch:N.reLaunch};setTimeout(()=>{o[e]({url:n})},0)}function Ki({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:o,tokenExpired:s}=Yt();let r;if(o){if(s<Date.now()){const i="uni-id-token-expired";r={errCode:i,errMsg:ws[i]}}}else{const i="uni-id-check-token-failed";r={errCode:i,errMsg:ws[i]}}return r}();if(Z_(e)&&n){if(n.uniIdRedirectUrl=e,cn(Et).length>0)return setTimeout(()=>{pe(Et,n)},0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function eb(){(function(){const t=cr(),{abortLoginPageJump:n,autoToLoginPage:o}=Ki({url:t});n||o&&Ss({api:"redirectTo",redirect:t})})();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];N.addInterceptor(n,{invoke(o){const{abortLoginPageJump:s,autoToLoginPage:r}=Ki({url:o.url});return s?o:r?(Ss({api:n,redirect:Fu(o.url)}),!1):o}})}}function tb(){this.onResponse(e=>{const{type:t,content:n}=e;let o=!1;switch(t){case"cloudobject":o=function(s){if(typeof s!="object")return!1;const{errCode:r}=s||{};return r in ws}(n);break;case"clientdb":o=function(s){if(typeof s!="object")return!1;const{errCode:r}=s||{};return r in W_}(n)}o&&function(s={}){const r=cn(Et);gu().then(()=>{const i=cr();if(i&&qu({redirect:i}))return r.length>0?pe(Et,Object.assign({uniIdRedirectUrl:i},s)):void(Zt&&Ss({api:"navigateTo",redirect:i}))})}(n)})}function nb(e){(function(t){t.onResponse=function(n){Io(be,n)},t.offResponse=function(n){Ao(be,n)}})(e),function(t){t.onNeedLogin=function(n){Io(Et,n)},t.offNeedLogin=function(n){Ao(Et,n)},J_&&(Tt("_globalUniCloudStatus").needLoginInit||(Tt("_globalUniCloudStatus").needLoginInit=!0,gu().then(()=>{eb.call(t)}),Y_&&tb.call(t)))}(e),function(t){t.onRefreshToken=function(n){Io(Gn,n)},t.offRefreshToken=function(n){Ao(Gn,n)}}(e)}let Bu;const Sn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ob=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function sb(){const e=Yt().token||"",t=e.split(".");if(!e||t.length!==3)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(Bu(o).split("").map(function(s){return"%"+("00"+s.charCodeAt(0).toString(16)).slice(-2)}).join(""))))}catch(s){throw new Error("获取当前用户信息出错，详细错误信息为："+s.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}Bu=typeof atob!="function"?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!ob.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,s="",r=0;r<e.length;)t=Sn.indexOf(e.charAt(r++))<<18|Sn.indexOf(e.charAt(r++))<<12|(n=Sn.indexOf(e.charAt(r++)))<<6|(o=Sn.indexOf(e.charAt(r++))),s+=n===64?String.fromCharCode(t>>16&255):o===64?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return s}:atob;var rb=Se(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",o="chooseAndUploadFile:fail";function s(i,a){return i.tempFiles.forEach((c,u)=>{c.name||(c.name=c.path.substring(c.path.lastIndexOf("/")+1)),a&&(c.fileType=a),c.cloudPath=Date.now()+"_"+u+c.name.substring(c.name.lastIndexOf("."))}),i.tempFilePaths||(i.tempFilePaths=i.tempFiles.map(c=>c.path)),i}function r(i,a,{onChooseFile:c,onUploadProgress:u}){return a.then(f=>{if(c){const l=c(f);if(l!==void 0)return Promise.resolve(l).then(p=>p===void 0?f:p)}return f}).then(f=>f===!1?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(l,p,g=5,y){(p=Object.assign({},p)).errMsg=n;const h=p.tempFiles,d=h.length;let m=0;return new Promise(b=>{for(;m<g;)_();function _(){const v=m++;if(v>=d)return void(!h.find(w=>!w.url&&!w.errMsg)&&b(p));const x=h[v];l.uploadFile({provider:x.provider,filePath:x.path,cloudPath:x.cloudPath,fileType:x.fileType,cloudPathAsRealPath:x.cloudPathAsRealPath,onUploadProgress(w){w.index=v,w.tempFile=x,w.tempFilePath=x.path,y&&y(w)}}).then(w=>{x.url=w.fileID,v<d&&_()}).catch(w=>{x.errMsg=w.errMsg||w.message,v<d&&_()})}})}(i,f,5,u))}t.initChooseAndUploadFile=function(i){return function(a={type:"all"}){return a.type==="image"?r(i,function(c){const{count:u,sizeType:f,sourceType:l=["album","camera"],extension:p}=c;return new Promise((g,y)=>{N.chooseImage({count:u,sizeType:f,sourceType:l,extension:p,success(h){g(s(h,"image"))},fail(h){y({errMsg:h.errMsg.replace("chooseImage:fail",o)})}})})}(a),a):a.type==="video"?r(i,function(c){const{camera:u,compressed:f,maxDuration:l,sourceType:p=["album","camera"],extension:g}=c;return new Promise((y,h)=>{N.chooseVideo({camera:u,compressed:f,maxDuration:l,sourceType:p,extension:g,success(d){const{tempFilePath:m,duration:b,size:_,height:v,width:x}=d;y(s({errMsg:"chooseVideo:ok",tempFilePaths:[m],tempFiles:[{name:d.tempFile&&d.tempFile.name||"",path:m,size:_,type:d.tempFile&&d.tempFile.type||"",width:x,height:v,duration:b,fileType:"video",cloudPath:""}]},"video"))},fail(d){h({errMsg:d.errMsg.replace("chooseVideo:fail",o)})}})})}(a),a):r(i,function(c){const{count:u,extension:f}=c;return new Promise((l,p)=>{let g=N.chooseFile;if(typeof vt<"u"&&typeof vt.chooseMessageFile=="function"&&(g=vt.chooseMessageFile),typeof g!="function")return p({errMsg:o+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});g({type:"all",count:u,extension:f,success(y){l(s(y))},fail(y){p({errMsg:y.errMsg.replace("chooseFile:fail",o)})}})})}(a),a)}}}),ib=My(rb);const ab="manual";function Vu(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch(()=>{var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach(n=>{t.push(this[n])}),t},(t,n)=>{if(this.loadtime===ab)return;let o=!1;const s=[];for(let r=2;r<t.length;r++)t[r]!==n[r]&&(s.push(t[r]),o=!0);t[0]!==n[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(o,s)})},methods:{onMixinDatacomPropsChange(t,n){},mixinDatacomEasyGet({getone:t=!1,success:n,fail:o}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then(s=>{this.mixinDatacomLoading=!1;const{data:r,count:i}=s.result;this.getcount&&(this.mixinDatacomPage.count=i),this.mixinDatacomHasMore=r.length<this.pageSize;const a=t?r.length?r[0]:void 0:r;this.mixinDatacomResData=a,n&&n(a)}).catch(s=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=s,this.mixinDatacomError=s,o&&o(s)}))},mixinDatacomGet(t={}){let n;t=t||{},n=typeof __uniX<"u"&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const o=t.action||this.action;o&&(n=n.action(o));const s=t.collection||this.collection;n=Array.isArray(s)?n.collection(...s):n.collection(s);const r=t.where||this.where;r&&Object.keys(r).length&&(n=n.where(r));const i=t.field||this.field;i&&(n=n.field(i));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const u=t.groupField||this.groupField;u&&(n=n.groupField(u)),(t.distinct!==void 0?t.distinct:this.distinct)===!0&&(n=n.distinct());const f=t.orderby||this.orderby;f&&(n=n.orderBy(f));const l=t.pageCurrent!==void 0?t.pageCurrent:this.mixinDatacomPage.current,p=t.pageSize!==void 0?t.pageSize:this.mixinDatacomPage.size,g=t.getcount!==void 0?t.getcount:this.getcount,y=t.gettree!==void 0?t.gettree:this.gettree,h=t.gettreepath!==void 0?t.gettreepath:this.gettreepath,d={getCount:g},m={limitLevel:t.limitlevel!==void 0?t.limitlevel:this.limitlevel,startWith:t.startwith!==void 0?t.startwith:this.startwith};return y&&(d.getTree=m),h&&(d.getTreePath=m),n=n.skip(p*(l-1)).limit(p).get(d),n}}}}function cb(e){return function(t,n={}){n=function(c,u={}){return c.customUI=u.customUI||c.customUI,c.parseSystemError=u.parseSystemError||c.parseSystemError,Object.assign(c.loadingOptions,u.loadingOptions),Object.assign(c.errorOptions,u.errorOptions),typeof u.secretMethods=="object"&&(c.secretMethods=u.secretMethods),c}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);const{customUI:o,loadingOptions:s,errorOptions:r,parseSystemError:i}=n,a=!o;return new Proxy({},{get(c,u){switch(u){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:f,interceptorName:l,getCallbackArgs:p}={}){return async function(...g){const y=p?p({params:g}):{};let h,d;try{return await ie(ae(l,"invoke"),{...y}),h=await f(...g),await ie(ae(l,"success"),{...y,result:h}),h}catch(m){throw d=m,await ie(ae(l,"fail"),{...y,error:d}),d}finally{await ie(ae(l,"complete"),d?{...y,error:d}:{...y,result:h})}}}({fn:async function f(...l){let p;a&&N.showLoading({title:s.title,mask:s.mask});const g={name:t,type:By,data:{method:u,params:l}};typeof n.secretMethods=="object"&&function(_,v){const x=v.data.method,w=_.secretMethods||{},A=w[x]||w["*"];A&&(v.secretType=A)}(n,g);let y=!1;try{p=await e.callFunction(g)}catch(_){y=!0,p={result:new C(_)}}const{errSubject:h,errCode:d,errMsg:m,newToken:b}=p.result||{};if(a&&N.hideLoading(),b&&b.token&&b.tokenExpired&&(_u(b),pe(Gn,{...b})),d){let _=m;if(y&&i&&(_=(await i({objectName:t,methodName:u,params:l,errSubject:h,errCode:d,errMsg:m})).errMsg||m),a)if(r.type==="toast")N.showToast({title:_,icon:"none"});else{if(r.type!=="modal")throw new Error(`Invalid errorOptions.type: ${r.type}`);{const{confirm:x}=await async function({title:w,content:A,showCancel:D,cancelText:q,confirmText:$}={}){return new Promise((E,H)=>{N.showModal({title:w,content:A,showCancel:D,cancelText:q,confirmText:$,success(K){E(K)},fail(){E({confirm:!1,cancel:!0})}})})}({title:"提示",content:_,showCancel:r.retry,cancelText:"取消",confirmText:r.retry?"重试":"确定"});if(r.retry&&x)return f(...l)}}const v=new C({subject:h,code:d,message:m,requestId:p.requestId});throw v.detail=p.result,pe(be,{type:bi,content:v}),v}return pe(be,{type:bi,content:p.result}),p.result},interceptorName:"callObject",getCallbackArgs:function({params:f}={}){return{objectName:t,methodName:u,params:f}}})}})}}function Ku(e){return Tt("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function ub({openid:e,callLoginByWeixin:t=!1}={}){const n=Ku(this);if(e&&t)throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");if(e)return n.mpWeixinOpenid=e,{};const o=await new Promise((r,i)=>{N.login({success(a){r(a.code)},fail(a){i(new Error(a.errMsg))}})});return await this.importObject("uni-id-co",{customUI:!0}).secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:t}),n.mpWeixinCode=o,{code:o}}async function lb(e){const t=Ku(this);return t.initPromise||(t.initPromise=ub.call(this,e).then(n=>n).catch(n=>{throw delete t.initPromise,n})),t.initPromise}function fb(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return lb.call(e,{openid:t,callLoginByWeixin:n})}}function db(e){(function(t){ms=t})(e)}function Wi(e){const t={getSystemInfo:N.getSystemInfo,getPushClientId:N.getPushClientId};return function(n){return new Promise((o,s)=>{t[e]({...n,success(r){o(r)},fail(r){s(r)}})})}}class pb extends class{constructor(){this._callback={}}addListener(t,n){this._callback[t]||(this._callback[t]=[]),this._callback[t].push(n)}on(t,n){return this.addListener(t,n)}removeListener(t,n){if(!n)throw new Error('The "listener" argument must be of type function. Received undefined');const o=this._callback[t];if(!o)return;const s=function(r,i){for(let a=r.length-1;a>=0;a--)if(r[a]===i)return a;return-1}(o,n);o.splice(s,1)}off(t,n){return this.removeListener(t,n)}removeAllListener(t){delete this._callback[t]}emit(t,...n){const o=this._callback[t];if(o)for(let s=0;s<o.length;s++)o[s](...n)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([Wi("getSystemInfo")(),Wi("getPushClientId")()]).then(([{appId:t}={},{cid:n}={}]=[])=>{if(!t)throw new Error("Invalid appId, please check the manifest.json file");if(!n)throw new Error("Invalid push client id");this._appId=t,this._pushClientId=n,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()},t=>{throw this.emit("error",t),this.close(),t})}async open(){return this.init()}_isUniCloudSSE(t){if(t.type!=="receive")return!1;const n=t&&t.data&&t.data.payload;return!(!n||n.channel!=="UNI_CLOUD_SSE"||n.seqId!==this._seqId)}_receivePushMessage(t){if(!this._isUniCloudSSE(t))return;const n=t&&t.data&&t.data.payload,{action:o,messageId:s,message:r}=n;this._payloadQueue.push({action:o,messageId:s,message:r}),this._consumMessage()}_consumMessage(){for(;;){const t=this._payloadQueue.find(n=>n.messageId===this._currentMessageId+1);if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}_parseMessagePayload(t){const{action:n,messageId:o,message:s}=t;n==="end"?this._end({messageId:o,message:s}):n==="message"&&this._appendMessage({messageId:o,message:s})}_appendMessage({messageId:t,message:n}={}){this.emit("message",n)}_end({messageId:t,message:n}={}){this.emit("end",n),this.close()}_initMessageListener(){N.onPushMessage(this._uniPushMessageCallback)}_destroy(){N.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}async function hb(e){const t=e.__dev__;if(!t.debugInfo)return;const{address:n,servePort:o}=t.debugInfo,{address:s}=await Ou(n,o);if(s)return t.localAddress=s,void(t.localPort=o);const r=console.warn;let i="";if(t.debugInfo.initialLaunchType==="remote"?(t.debugInfo.forceRemote=!0,i=`当前客户端和HBuilderX不在同一局域网下（或其他网络原因无法连接HBuilderX），uniCloud本地调试服务不对当前客户端生效。
- 如果不使用uniCloud本地调试服务，请直接忽略此信息。
- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。`):i=`无法连接uniCloud本地调试服务，请检查当前客户端是否与主机在同一局域网下。
- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。`,i+=`
- 如果在HBuilderX开启的状态下切换过网络环境，请重启HBuilderX后再试
- 检查系统防火墙是否拦截了HBuilderX自带的nodejs
- 检查是否错误的使用拦截器修改uni.request方法的参数`,ct.indexOf("mp-")===0&&(i+=`
- 小程序中如何使用uniCloud，请参考：https://uniapp.dcloud.net.cn/uniCloud/publish.html#useinmp`),!t.debugInfo.forceRemote)throw new Error(i);r(i)}function gb(e){e._initPromiseHub||(e._initPromiseHub=new or({createPromise:function(){let t=Promise.resolve();var n;n=1,t=new Promise(s=>{setTimeout(()=>{s()},n)});const o=e.auth();return t.then(()=>o.getLoginState()).then(s=>s?Promise.resolve():o.signInAnonymously())}}))}const mb={tcb:ki,tencent:ki,aliyun:n_,private:Ri,dcloud:Ri,alipay:L_};let he=new class{init(e){let t={};const n=mb[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(o){const s={};o.__dev__=s,s.debugLog=ct==="mp-harmony";const r=Yy;r&&!r.code&&(s.debugInfo=r);const i=new or({createPromise:function(){return hb(o)}});s.initLocalNetwork=function(){return i.exec()}}(t),gb(t),B_(t),function(o){const s=o.uploadFile;o.uploadFile=function(r){return s.call(this,r)}}(t),function(o){o.database=function(s){if(s&&Object.keys(s).length>0)return o.init(s).database();if(this._database)return this._database;const r=Fi(qi,{uniClient:o});return this._database=r,r},o.databaseForJQL=function(s){if(s&&Object.keys(s).length>0)return o.init(s).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const r=Fi(qi,{uniClient:o,isJQL:!0});return this._databaseForJQL=r,r}}(t),function(o){o.getCurrentUserInfo=sb,o.chooseAndUploadFile=ib.initChooseAndUploadFile(o),Object.assign(o,{get mixinDatacom(){return Vu(o)}}),o.SSEChannel=pb,o.initSecureNetworkByWeixin=fb(o),o.setCustomClientInfo=db,o.importObject=cb(o)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach(o=>{if(!t[o])return;const s=t[o];t[o]=function(){return s.apply(t,Array.from(arguments))},t[o]=function(r,i){return function(a){let c=!1;i==="callFunction"&&(c=(a&&a.type||hi)!==hi);const u=i==="callFunction"&&!c,f=this._initPromiseHub.exec();a=a||{};const{success:l,fail:p,complete:g}=mu(a),y=f.then(()=>c?Promise.resolve():ie(ae(i,"invoke"),a)).then(()=>r.call(this,a)).then(h=>c?Promise.resolve(h):ie(ae(i,"success"),h).then(()=>ie(ae(i,"complete"),h)).then(()=>(u&&pe(be,{type:vn,content:h}),Promise.resolve(h))),h=>c?Promise.reject(h):ie(ae(i,"fail"),h).then(()=>ie(ae(i,"complete"),h)).then(()=>(pe(be,{type:vn,content:h}),Promise.reject(h))));if(!(l||p||g))return y;y.then(h=>{l&&l(h),g&&g(h),u&&pe(be,{type:vn,content:h})},h=>{p&&p(h),g&&g(h),u&&pe(be,{type:vn,content:h})})}}(t[o],o).bind(t)}),t.init=this.init,t}};(()=>{const e=du;let t={};if(e&&e.length===1)t=e[0],he=he.init(t),he._isDefault=!0;else{const n=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let o;o=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",n.forEach(s=>{he[s]=function(){return console.error(o),Promise.reject(new C({code:"SYS_ERR",message:o}))}})}Object.assign(he,{get mixinDatacom(){return Vu(he)}}),nb(he),he.addInterceptor=hu,he.removeInterceptor=Qy,he.interceptObject=Zy})();var yb=he;exports.Pinia=Cy;exports._export_sfc=sm;exports.computed=ao;exports.createPinia=nu;exports.createSSRApp=jp;exports.defineStore=ru;exports.e=Cp;exports.f=Ap;exports.index=N;exports.initVueI18n=vl;exports.n=$p;exports.nr=yb;exports.o=Ip;exports.onHide=ry;exports.onLaunch=iy;exports.onLoad=ay;exports.onMounted=Js;exports.onPageScroll=cy;exports.onShareAppMessage=ly;exports.onShareTimeline=uy;exports.onShow=sy;exports.onUnmounted=Ys;exports.p=Rp;exports.r=Tp;exports.reactive=on;exports.ref=Rt;exports.resolveComponent=Nf;exports.s=Op;exports.sr=Lp;exports.t=kp;exports.textEncodingShimExports=fy;exports.unref=Fs;exports.useCssVars=Xd;exports.w=Ep;exports.watch=Ht;
//# sourceMappingURL=../../.sourcemap/mp-weixin/common/vendor.js.map
