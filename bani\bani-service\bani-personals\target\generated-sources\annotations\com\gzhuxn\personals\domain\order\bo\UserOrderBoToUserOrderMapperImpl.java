package com.gzhuxn.personals.domain.order.bo;

import com.gzhuxn.personals.domain.order.UserOrder;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserOrderBoToUserOrderMapperImpl implements UserOrderBoToUserOrderMapper {

    @Override
    public UserOrder convert(UserOrderBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserOrder userOrder = new UserOrder();

        userOrder.setSearchValue( arg0.getSearchValue() );
        userOrder.setCreateBy( arg0.getCreateBy() );
        userOrder.setCreateTime( arg0.getCreateTime() );
        userOrder.setUpdateBy( arg0.getUpdateBy() );
        userOrder.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userOrder.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userOrder.setCreateDept( arg0.getCreateDept() );
        userOrder.setId( arg0.getId() );
        userOrder.setUserId( arg0.getUserId() );
        if ( arg0.getAmount() != null ) {
            userOrder.setAmount( BigDecimal.valueOf( arg0.getAmount() ) );
        }
        userOrder.setWithdrawCoin( arg0.getWithdrawCoin() );
        userOrder.setCoin( arg0.getCoin() );
        userOrder.setStatus( arg0.getStatus() );
        userOrder.setPayTime( arg0.getPayTime() );
        userOrder.setFailMsg( arg0.getFailMsg() );
        userOrder.setFailDesc( arg0.getFailDesc() );

        return userOrder;
    }

    @Override
    public UserOrder convert(UserOrderBo arg0, UserOrder arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        if ( arg0.getAmount() != null ) {
            arg1.setAmount( BigDecimal.valueOf( arg0.getAmount() ) );
        }
        else {
            arg1.setAmount( null );
        }
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setFailMsg( arg0.getFailMsg() );
        arg1.setFailDesc( arg0.getFailDesc() );

        return arg1;
    }
}
