/**
 * 用户打招呼相关接口
 */

import request from '@/utils/request'

/**
 * 发送打招呼
 * @param {Object} data - 打招呼数据
 * @param {number} data.oppositeUserId - 对方用户ID
 * @param {string} data.content - 打招呼内容
 * @returns {Promise} 返回发送结果
 */
export function sendUserGreeting(data) {
	return request({
		url: '/personals/user/greeting/send',
		method: 'POST',
		data: data
	})
}

/**
 * 查询收到的打招呼列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 分页大小
 * @param {number} params.pageNum - 当前页数
 * @returns {Promise} 返回打招呼列表
 */
export function getReceivedGreetingPage(params = {}) {
	return request({
		url: '/personals/user/greeting/received/page',
		method: 'GET',
		params: {
			pageSize: params.pageSize || 10,
			pageNum: params.pageNum || 1
		}
	})
}

/**
 * 查询发送的打招呼列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageSize - 分页大小
 * @param {number} params.pageNum - 当前页数
 * @returns {Promise} 返回打招呼列表
 */
export function getSentGreetingPage(params = {}) {
	return request({
		url: '/personals/user/greeting/sent/page',
		method: 'GET',
		params: {
			pageSize: params.pageSize || 10,
			pageNum: params.pageNum || 1
		}
	})
}

/**
 * 检查是否已经向对方打过招呼
 * @param {number} oppositeUserId - 对方用户ID
 * @returns {Promise} 返回检查结果
 */
export function checkUserGreeted(oppositeUserId) {
	return request({
		url: '/personals/user/greeting/check',
		method: 'GET',
		params: {
			oppositeUserId
		}
	})
}

/**
 * 回复打招呼
 * @param {number} id - 打招呼记录ID
 * @param {string} content - 回复内容
 * @returns {Promise} 返回回复结果
 */
export function replyGreeting(id, content) {
	return request({
		url: `/personals/user/greeting/reply/${id}`,
		method: 'POST',
		params: {
			content
		}
	})
}

/**
 * 忽略打招呼
 * @param {number} id - 打招呼记录ID
 * @returns {Promise} 返回忽略结果
 */
export function ignoreGreeting(id) {
	return request({
		url: `/personals/user/greeting/ignore/${id}`,
		method: 'POST'
	})
}
