package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserInvitationVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserInvitationToUserInvitationVoMapperImpl implements UserInvitationToUserInvitationVoMapper {

    @Override
    public UserInvitationVo convert(UserInvitation arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserInvitationVo userInvitationVo = new UserInvitationVo();

        userInvitationVo.setId( arg0.getId() );
        userInvitationVo.setUserId( arg0.getUserId() );
        userInvitationVo.setOppositeUserId( arg0.getOppositeUserId() );

        return userInvitationVo;
    }

    @Override
    public UserInvitationVo convert(UserInvitation arg0, UserInvitationVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
