package com.gzhuxn.personals.controller.app.message.bo;

import com.gzhuxn.personals.domain.message.bo.MsgGroupBo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppMsgGroupCreateBoToMsgGroupBoMapperImpl implements AppMsgGroupCreateBoToMsgGroupBoMapper {

    @Override
    public MsgGroupBo convert(AppMsgGroupCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroupBo msgGroupBo = new MsgGroupBo();

        msgGroupBo.setName( arg0.getName() );
        msgGroupBo.setType( arg0.getType() );
        msgGroupBo.setBusinessId( arg0.getBusinessId() );
        msgGroupBo.setRemark( arg0.getRemark() );

        return msgGroupBo;
    }

    @Override
    public MsgGroupBo convert(AppMsgGroupCreateBo arg0, MsgGroupBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
