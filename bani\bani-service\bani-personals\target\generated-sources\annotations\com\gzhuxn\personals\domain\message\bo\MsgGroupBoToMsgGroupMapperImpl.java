package com.gzhuxn.personals.domain.message.bo;

import com.gzhuxn.personals.domain.message.MsgGroup;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupBoToMsgGroupMapperImpl implements MsgGroupBoToMsgGroupMapper {

    @Override
    public MsgGroup convert(MsgGroupBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroup msgGroup = new MsgGroup();

        msgGroup.setSearchValue( arg0.getSearchValue() );
        msgGroup.setCreateBy( arg0.getCreateBy() );
        msgGroup.setCreateTime( arg0.getCreateTime() );
        msgGroup.setUpdateBy( arg0.getUpdateBy() );
        msgGroup.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            msgGroup.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        msgGroup.setCreateDept( arg0.getCreateDept() );
        msgGroup.setId( arg0.getId() );
        msgGroup.setName( arg0.getName() );
        msgGroup.setNum( arg0.getNum() );
        msgGroup.setType( arg0.getType() );
        msgGroup.setBusinessId( arg0.getBusinessId() );
        msgGroup.setStatus( arg0.getStatus() );
        msgGroup.setRemark( arg0.getRemark() );

        return msgGroup;
    }

    @Override
    public MsgGroup convert(MsgGroupBo arg0, MsgGroup arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setNum( arg0.getNum() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}
