package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.controller.app.group.bo.AppGroupBoToGroupMapper;
import com.gzhuxn.personals.controller.app.group.vo.AppGroupDetailVoToGroupMapper;
import com.gzhuxn.personals.controller.app.group.vo.AppGroupVoToGroupMapper;
import com.gzhuxn.personals.domain.group.bo.GroupBoToGroupMapper;
import com.gzhuxn.personals.domain.group.vo.GroupVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppGroupBoToGroupMapper.class,AppGroupDetailVoToGroupMapper.class,AppGroupVoToGroupMapper.class,GroupBoToGroupMapper.class},
    imports = {}
)
public interface GroupToGroupVoMapper extends BaseMapper<Group, GroupVo> {
}
