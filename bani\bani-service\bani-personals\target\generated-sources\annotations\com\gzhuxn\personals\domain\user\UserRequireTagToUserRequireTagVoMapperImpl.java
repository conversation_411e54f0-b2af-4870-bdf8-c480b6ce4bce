package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserRequireTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserRequireTagToUserRequireTagVoMapperImpl implements UserRequireTagToUserRequireTagVoMapper {

    @Override
    public UserRequireTagVo convert(UserRequireTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserRequireTagVo userRequireTagVo = new UserRequireTagVo();

        userRequireTagVo.setId( arg0.getId() );
        userRequireTagVo.setUserId( arg0.getUserId() );
        userRequireTagVo.setNamespace( arg0.getNamespace() );
        userRequireTagVo.setTagKey( arg0.getTagKey() );
        userRequireTagVo.setTagVal( arg0.getTagVal() );
        userRequireTagVo.setTagValName( arg0.getTagValName() );

        return userRequireTagVo;
    }

    @Override
    public UserRequireTagVo convert(UserRequireTag arg0, UserRequireTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setNamespace( arg0.getNamespace() );
        arg1.setTagKey( arg0.getTagKey() );
        arg1.setTagVal( arg0.getTagVal() );
        arg1.setTagValName( arg0.getTagValName() );

        return arg1;
    }
}
