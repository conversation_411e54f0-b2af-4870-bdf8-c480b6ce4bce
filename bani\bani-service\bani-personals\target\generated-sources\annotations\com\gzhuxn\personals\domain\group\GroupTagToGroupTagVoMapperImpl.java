package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.domain.group.vo.GroupTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class GroupTagToGroupTagVoMapperImpl implements GroupTagToGroupTagVoMapper {

    @Override
    public GroupTagVo convert(GroupTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupTagVo groupTagVo = new GroupTagVo();

        groupTagVo.setId( arg0.getId() );
        groupTagVo.setGroupId( arg0.getGroupId() );
        groupTagVo.setVal( arg0.getVal() );
        groupTagVo.setName( arg0.getName() );

        return groupTagVo;
    }

    @Override
    public GroupTagVo convert(GroupTag arg0, GroupTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setVal( arg0.getVal() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
