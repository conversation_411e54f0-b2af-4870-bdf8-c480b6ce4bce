"use strict";const e=require("../../../common/vendor.js"),i=require("../../../api/activity/buddy.js"),g=require("../../../api/activity/activityRecord.js"),$=require("../../../api/my/follow.js"),G=require("../../../utils/common.js");if(!Array){const T=e.resolveComponent("uni-tag"),o=e.resolveComponent("uni-icons"),v=e.resolveComponent("scroll-nav-page");(T+o+v)()}const k=()=>"../../../uni_modules/uni-tag/components/uni-tag/uni-tag.js",H=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",q=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(k+H+q)();const b={__name:"detail",setup(T){const o=e.ref(null),v=e.ref(0),f=e.ref(!1),n=e.ref(!1),r=e.ref([]),d=e.ref(!1);let y=null;const m=[{value:0,label:"全部"},{value:i.BUDDY_ACTIVITY_TYPES.DATING,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.DATING)},{value:i.BUDDY_ACTIVITY_TYPES.CHAT,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.CHAT)},{value:i.BUDDY_ACTIVITY_TYPES.DINING,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.DINING)},{value:i.BUDDY_ACTIVITY_TYPES.OUTDOOR,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.OUTDOOR)},{value:i.BUDDY_ACTIVITY_TYPES.EXHIBITION,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.EXHIBITION)},{value:i.BUDDY_ACTIVITY_TYPES.SPORTS,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.SPORTS)},{value:i.BUDDY_ACTIVITY_TYPES.STUDY,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.STUDY)},{value:i.BUDDY_ACTIVITY_TYPES.DRINKING,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.DRINKING)},{value:i.BUDDY_ACTIVITY_TYPES.GAMING,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.GAMING)},{value:i.BUDDY_ACTIVITY_TYPES.OTHER,label:i.getActivityTypeName(i.BUDDY_ACTIVITY_TYPES.OTHER)}],a=e.ref({}),p=async()=>{if(!o.value){e.index.showToast({title:"活动ID不能为空",icon:"none"});return}f.value=!0;try{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:205","加载活动详情，ID:",o.value);const t=await i.getBuddyActivityDetail(o.value);e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:208","API响应:",t),t.code===200&&t.data?(a.value=t.data,e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:213","活动详情加载成功:",a.value),e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:214","介绍图片字段:",a.value.introduceImages)):(e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:216","加载活动详情失败:",t.msg),e.index.showToast({title:t.msg||"加载失败",icon:"none"}))}catch(t){e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:223","加载活动详情异常:",t),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}finally{f.value=!1}},_=async()=>{d.value=!0,g.getActivityEnrollUsers({activityId:o.value,pageNum:1,pageSize:20}).then(t=>{r.value=t.rows||[]}).finally(()=>{d.value=!1})},x=t=>typeof t=="number"?i.getActivityStatusName(t):{notStarted:"未开始",enrolling:"报名中",enrollEnd:"报名已结束",inProgress:"活动进行中",ended:"活动已结束"}[t]||"",h=t=>typeof t=="number"?{1:"notStarted",2:"notStarted",3:"enrolling",4:"enrollEnd",10:"inProgress",11:"ended"}[t]||"notStarted":t||"notStarted",D=()=>{if(e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:285","getIntroduceImages 被调用"),e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:286","activityDetail.value:",a.value),e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:287","introduceImages 字段值:",a.value.introduceImages),!a.value.introduceImages)return e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:290","introduceImages 为空，返回空数组"),[];const t=a.value.introduceImages.split(",").filter(s=>s.trim());return e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:296","解析后的图片数组:",t),t},Y=t=>{if(!t)return"";if(t.startsWith("http://")||t.startsWith("https://"))return e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:306","完整URL:",t),t;const u="https://your-oss-domain.com/"+t;return e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:314","拼接后的URL:",u),u},w=t=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:320","图片加载成功:",t)},A=t=>{e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:325","图片加载失败:",t),e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:326","失败的图片src:",t.target.src)},I=()=>{if(a.value.hasEnrolled)return"已报名";const t=a.value.status;return{0:"已下架",1:"草稿",2:"已发布",3:"立即报名",4:"报名已结束",10:"活动进行中",11:"活动已结束"}[t]||"未知状态"},S=async()=>{if(!a.value.oppUserId){e.index.showToast({title:"用户信息不完整",icon:"none"});return}n.value||(y&&clearTimeout(y),y=setTimeout(async()=>{await E()},300))},E=async()=>{try{n.value=!0,e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:387","切换关注状态，用户ID:",a.value.oppUserId);const t=await $.toggleUserFollow(a.value.oppUserId,a.value.oppIsFollowed);if(t.code===200){const s=a.value.oppIsFollowed;a.value.oppIsFollowed=!a.value.oppIsFollowed,e.index.showToast({title:a.value.oppIsFollowed?"关注成功":"已取消关注",icon:"success",duration:1500}),!s&&a.value.oppIsFollowed&&e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:405","用户关注了:",a.value.oppNickName)}else e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:408","关注操作失败:",t.msg),e.index.showToast({title:t.msg||"操作失败",icon:"none"})}catch(t){e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:415","关注操作异常:",t),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}finally{n.value=!1}},P=()=>{e.index.showActionSheet({itemList:["分享给好友","分享到朋友圈","生成分享海报"],success:t=>{t.tapIndex===0?N():t.tapIndex===1?U():t.tapIndex===2&&C()},fail:()=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:444","取消分享")}})},N=()=>{const t={title:a.value.name||"精彩活动邀请",path:`/pagesubs/activity/buddy/detail?id=${o.value}`,imageUrl:a.value.backgroundImage||"",success:s=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:465","分享成功",s),e.index.showToast({title:"分享成功",icon:"success"})},fail:s=>{e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:472","分享失败",s),e.index.showToast({title:"分享失败",icon:"none"})}};e.index.shareAppMessage(t)},U=()=>{e.index.showModal({title:"分享到朋友圈",content:'请点击右上角的"..."按钮，选择"分享到朋友圈"',showCancel:!1,confirmText:"知道了"})},C=()=>{e.index.showLoading({title:"生成中..."}),setTimeout(()=>{e.index.hideLoading(),e.index.showToast({title:"海报生成功能开发中",icon:"none"})},1500)},B=()=>{e.index.showActionSheet({itemList:["举报"],success:t=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:521","选择了第"+(t.tapIndex+1)+"个按钮"),t.tapIndex===0&&V()},fail:()=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:528","取消选择")}})},V=()=>{e.index.navigateTo({url:`/pagesubs/my/report/report?type=2&targetId=${o.value}&targetName=${encodeURIComponent(a.value.name||"")}`})},F=async()=>{if(a.value.hasEnrolled){e.index.showToast({title:"您已报名此活动",icon:"none"});return}if(a.value.status!==3){const t=I();e.index.showToast({title:`当前状态：${t}，无法报名`,icon:"none"});return}if(!o.value){e.index.showToast({title:"活动ID不能为空",icon:"none"});return}try{const t=a.value.amount||0;t>0?await L(t):await M()}catch(t){e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:582","报名活动异常:",t),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}},M=async()=>{try{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:593","免费活动报名，活动ID:",o.value);const t=await g.enrollFreeActivity(o.value);t.code===200?(e.index.showToast({title:"报名成功",icon:"success"}),await p(o.value),await _()):(e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:607","免费活动报名失败:",t.msg),e.index.showToast({title:t.msg||"报名失败",icon:"none"}))}catch(t){throw e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:614","免费活动报名异常:",t),t}},L=async t=>{try{if(!(await e.index.showModal({title:"付费活动",content:`此活动需要支付 ¥${t}，是否继续？`,confirmText:"支付",cancelText:"取消"})).confirm)return;e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:634","付费活动报名，活动ID:",o.value,"金额:",t);const u=t*10,l=await g.enrollPaidActivity(o.value,t,u);l.code===200?l.data&&l.data.payData?(e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:645","支付数据:",l.data.payData),e.index.showToast({title:"支付功能开发中",icon:"none"})):(e.index.showToast({title:"报名成功",icon:"success"}),await p(o.value),await _()):(e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:662","付费活动报名失败:",l.msg),e.index.showToast({title:l.msg||"报名失败",icon:"none"}))}catch(s){throw e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:669","付费活动报名异常:",s),s}},O=t=>{v.value=t.scrollTop};return e.onPageScroll(O),e.onLoad(t=>{o.value=t.id}),e.onMounted(()=>{const t=G.getCurrentPagePath();e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:691","当前页面完整路径:",t),o.value&&(p(),_())}),e.onShareAppMessage(()=>({title:a.value.name||"精彩活动邀请",path:`/pagesubs/activity/buddy/detail?id=${o.value}`,imageUrl:a.value.backgroundImage||"",success:t=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:707","分享成功",t)},fail:t=>{e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:710","分享失败",t)}})),e.onShareTimeline(()=>({title:`${a.value.name||"精彩活动"} - 快来一起参加吧！`,query:`id=${o.value}`,imageUrl:a.value.backgroundImage||"",success:t=>{e.index.__f__("log","at pagesubs/activity/buddy/detail.vue:722","分享到朋友圈成功",t)},fail:t=>{e.index.__f__("error","at pagesubs/activity/buddy/detail.vue:725","分享到朋友圈失败",t)}})),(t,s)=>{var u;return e.e({a:a.value.backgroundImage,b:a.value.oppAvatar,c:e.t(a.value.oppNickName),d:e.p({text:(u=m.find(l=>l.value===a.value.classify))==null?void 0:u.label,type:"primary",inverted:!1,size:"small",color:"#696CF3"}),e:a.value.oppIsIdentity},a.value.oppIsIdentity?{f:e.p({text:"已实名",type:"success",inverted:!1,size:"small",color:"#52c41a"})}:{},{g:n.value},n.value?{h:e.p({type:"spinner-cycle",size:"16",color:"#999"})}:{i:e.t(a.value.oppIsFollowed?"已关注":"关注")},{j:a.value.oppIsFollowed?1:"",k:n.value?1:"",l:n.value,m:e.o(S),n:e.t(a.value.name),o:e.t(x(a.value.status)),p:e.n(h(a.value.status)),q:e.t(a.value.createTime),r:e.t(a.value.address),s:e.t(a.value.startTime),t:a.value.timeLength},a.value.timeLength?{v:e.t(a.value.timeLength)}:{},{w:e.t(a.value.amount>0?`¥${a.value.amount}`:"免费"),x:e.t(a.value.enrollNum),y:e.t(a.value.limitNum?"/"+a.value.limitNum:""),z:e.t(a.value.introduce),A:a.value.introduceImages},a.value.introduceImages?{B:e.f(D(),(l,c,R)=>({a:c,b:Y(l),c:e.o(A,c),d:e.o(w,c)}))}:{},{C:e.t(r.value.length),D:e.p({type:"refreshempty",size:"16",color:d.value?"$text-tertiary":"$primary-color"}),E:e.o(_),F:d.value?1:"",G:d.value},d.value?{H:e.p({type:"spinner-cycle",size:"20",color:"$primary-color"})}:r.value.length>0?{J:e.f(r.value,(l,c,R)=>({a:l.oppAvatar,b:e.t(l.oppNickName),c:e.t(l.enrollTime),d:l.uid||c}))}:{},{I:r.value.length>0,K:e.o(P),L:e.o(B),M:e.t(I()),N:a.value.status!==3||a.value.hasEnrolled?1:"",O:a.value.hasEnrolled?1:"",P:e.o(F),Q:e.p({title:"搭子详情","show-back":!0})})}}},z=e._export_sfc(b,[["__scopeId","data-v-8dc7ffb8"]]);b.__runtimeHooks=7;wx.createPage(z);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/activity/buddy/detail.js.map
