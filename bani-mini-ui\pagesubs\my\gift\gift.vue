<template>
	<scroll-nav-page title="礼物记录" :show-back="true" @heightChange="handleNavHeightChange">
		<template #content>
			<z-paging ref="paging" v-model="datas" :auto="true" :refresher-enabled="true" :loading-more-enabled="true"
				@query="queryList">
				<template #top>
					<!-- 顶部菜单 -->
					<view class="top-menu margin-split" :style="{ paddingTop: navBarHeight + 'px' }">
						<uni-segmented-control :current="segmentedIndex" :values="['收到的礼物', '发出的礼物']"
							@clickItem="switchTab" styleType="text" activeColor="#696CF3"></uni-segmented-control>
					</view>
				</template>
				<!-- 自定义刷新组件 -->
				<template #refresher="{ refresherStatus }">
					<custom-refresher :refresher-status="refresherStatus" />
				</template>
				<!-- 礼物记录列表 -->
				<view class="gift-list margin-split">
					<view class="gift-card" v-for="(item, index) in datas" :key="item.id">
						<!-- 卡片内容 -->
						<view class="card-content">
							<view class="user-section">
								<image class="avatar" :src="item.oppAvatar"></image>
								<view class="user-info">
									<!-- 昵称与时间同一行 -->
									<view class="name-time-row">
										<view class="name-icons">
											<text class="nick-name">{{ item.oppNickName }}</text>
											<!-- 性别图标 -->
											<text class="iconfont gender-icon"
												:class="item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'"
												:style="{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }"></text>
											<text v-if="item.oppIsIdentity" class="verified-tag">已实名</text>
										</view>
										<text class="time">{{ item.time }}</text>
									</view>
									<text class="user-detail">{{ item.oppAge }} · {{ item.oppHeight }} · {{ item.oppCity }}</text>
								</view>
							</view>

							<!-- 礼物信息 -->
							<view class="gift-section">
								<view class="gift-detail">
									<text class="gift-name">{{ item.giftName }}</text>
									<text class="gift-count"> × {{ item.giftNum }}</text>
								</view>
								<text class="gift-price">{{ item.coin }}花瓣</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<template #empty>
					<view class="empty-state">
						<image class="empty-icon" src="/static/image/empty.png" mode="aspectFit"></image>
						<text class="empty-text">暂无礼物记录</text>
					</view>
				</template>
			</z-paging>
		</template>
	</scroll-nav-page>
</template>
<script setup>
import { ref, computed } from 'vue'
import { onLoad, onPageScroll } from "@dcloudio/uni-app"
import { getUserGiftPage } from '@/api/my/gift'

// 导航栏相关
const pageScrollTop = ref(0)
const navBarHeight = ref(0) // 初始为0，等待组件传递真实高度

// 页面数据
const segmentedIndex = ref(0)
const queryType = ref(2) // 1-我送给他人的礼物、2-送给我的礼物

// z-paging组件
const paging = ref(null)
const datas = ref([])

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

onLoad((param) => {
	// 默认显示收到的礼物
	if (!param.type) {
		param.type = 2
	}
	const type = parseInt(param.type)
	segmentedIndex.value = type === 2 ? 0 : 1 // 2-收到的礼物(index=0), 1-发出的礼物(index=1)
	queryType.value = type
})

function queryList(pageNum, pageSize) {
	getUserGiftPage({
		pageNum: pageNum,
		pageSize: pageSize,
		queryType: queryType.value,
	}).then(res => {
		paging.value.complete(res.rows);
	})
}

const switchTab = (e) => {
	if (segmentedIndex.value !== e.currentIndex) {
		segmentedIndex.value = e.currentIndex
		// 0-收到的礼物(queryType=2), 1-发出的礼物(queryType=1)
		queryType.value = segmentedIndex.value === 0 ? 2 : 1
		paging.value.reload()
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

// z-paging组件样式
:deep(.z-paging-content) {
	min-height: calc(100vh - 200px);
}

.gift-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.gift-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	overflow: hidden;
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.12);
	}

	.card-content {
		padding: 24rpx;

		.user-section {
			display: flex;
			align-items: flex-start;
			gap: 16rpx;
			margin-bottom: 16rpx;

			.avatar {
				height: 80rpx;
				width: 80rpx;
				border-radius: 40rpx;
				box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.15);
				border: 2rpx solid rgba(255, 255, 255, 0.8);
				flex-shrink: 0;
			}

			.user-info {
				flex: 1;
				min-width: 0;

				.name-time-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 8rpx;

					.name-icons {
						display: flex;
						align-items: center;
						gap: 6rpx;

						.nick-name {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
							letter-spacing: 0.5rpx;
						}

						.gender-icon {
							margin-left: 4rpx;
						}

						.verified-tag {
							background: #696CF3;
							color: white;
							font-size: 20rpx;
							padding: 2rpx 6rpx;
							border-radius: 8rpx;
							font-weight: 500;
							margin-left: 4rpx;
						}
					}

					.time {
						font-size: 22rpx;
						color: #999;
						font-weight: 400;
						flex-shrink: 0;
					}
				}

				.user-detail {
					font-size: 24rpx;
					color: #666;
					line-height: 1.4;
				}
			}
		}

		.gift-section {
			background: rgba($primary-color, 0.05);
			border-radius: 12rpx;
			padding: 16rpx 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.gift-detail {
				display: flex;
				align-items: center;

				.gift-name {
					font-size: 26rpx;
					color: $primary-color;
					font-weight: 600;
				}

				.gift-count {
					font-size: 24rpx;
					color: #666;
					margin-left: 4rpx;
				}
			}

			.gift-price {
				font-size: 24rpx;
				color: #FF6B9D;
				font-weight: 600;
				background: rgba(255, 107, 157, 0.1);
				padding: 4rpx 12rpx;
				border-radius: 20rpx;
			}
		}
	}
}

// 空状态样式
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 32rpx;
		opacity: 0.6;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		font-weight: 500;
	}
}
</style>