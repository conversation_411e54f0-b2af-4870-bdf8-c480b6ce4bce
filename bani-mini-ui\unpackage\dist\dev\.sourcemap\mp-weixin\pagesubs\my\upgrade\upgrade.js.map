{"version": 3, "file": "upgrade.js", "sources": ["pagesubs/my/upgrade/upgrade.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcdXBncmFkZVx1cGdyYWRlLnZ1ZQ"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"资料等级\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-container\">\n\t\t\t\t<!-- 主要内容 -->\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<!-- 当前等级展示 -->\n\t\t\t\t\t<view class=\"current-level\">\n\t\t\t\t\t\t<!-- 背景文字 -->\n\t\t\t\t\t\t<view class=\"bg-text\">CURRENT LEVEL</view>\n\n\t\t\t\t\t\t<view class=\"level-info\">\n\t\t\t\t\t\t\t<text class=\"level-label\">当前等级：</text>\n\t\t\t\t\t\t\t<text class=\"level-name\">{{ getCurrentLevelName() }}</text>\n\t\t\t\t\t\t\t<view class=\"level-desc\" v-if=\"userLevelInfo.userLevelDesc\">\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"level-icon\">\n\t\t\t\t\t\t\t<view class=\"cat-icon\">\n\t\t\t\t\t\t\t\t<uni-icons :type=\"getCurrentLevelIcon()\" size=\"40\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"icon-label\">{{ getCurrentLevelName() }}</text>\n\t\t\t\t\t\t\t<view class=\"vip-badge\" v-if=\"userLevelInfo.isVip\">\n\t\t\t\t\t\t\t\t<text class=\"vip-text\">VIP</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 等级进度卡片 -->\n\t\t\t\t\t<view class=\"level-progress-card\">\n\t\t\t\t\t\t<!-- 等级图标进度条 -->\n\t\t\t\t\t\t<view class=\"level-icons\">\n\t\t\t\t\t\t\t<view class=\"level-step\"\n\t\t\t\t\t\t\t\t:class=\"{ active: userLevelInfo.userLevel >= 1, selected: selectedLevel === 1, current: userLevelInfo.userLevel === 1 }\"\n\t\t\t\t\t\t\t\t@click=\"selectLevel(1)\">\n\t\t\t\t\t\t\t\t<view class=\"step-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"star\" size=\"24\"\n\t\t\t\t\t\t\t\t\t\t:color=\"selectedLevel === 1 ? '#696CF3' : '#fff'\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"step-name\">萌新</text>\n\t\t\t\t\t\t\t\t<view class=\"current-badge\" v-if=\"userLevelInfo.userLevel === 1\">\n\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">当前</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"progress-line\" :class=\"{ active: userLevelInfo.userLevel >= 2 }\"></view>\n\t\t\t\t\t\t\t<view class=\"level-step\"\n\t\t\t\t\t\t\t\t:class=\"{ active: userLevelInfo.userLevel >= 2, selected: selectedLevel === 2, current: userLevelInfo.userLevel === 2 }\"\n\t\t\t\t\t\t\t\t@click=\"selectLevel(2)\">\n\t\t\t\t\t\t\t\t<view class=\"step-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"heart\" size=\"24\"\n\t\t\t\t\t\t\t\t\t\t:color=\"selectedLevel === 2 ? '#696CF3' : '#fff'\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"step-name\">缘友</text>\n\t\t\t\t\t\t\t\t<view class=\"current-badge\" v-if=\"userLevelInfo.userLevel === 2\">\n\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">当前</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"progress-line\" :class=\"{ active: userLevelInfo.userLevel >= 3 }\"></view>\n\t\t\t\t\t\t\t<view class=\"level-step\"\n\t\t\t\t\t\t\t\t:class=\"{ active: userLevelInfo.userLevel >= 3, selected: selectedLevel === 3, current: userLevelInfo.userLevel === 3 }\"\n\t\t\t\t\t\t\t\t@click=\"selectLevel(3)\">\n\t\t\t\t\t\t\t\t<view class=\"step-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"fire\" size=\"24\"\n\t\t\t\t\t\t\t\t\t\t:color=\"selectedLevel === 3 ? '#696CF3' : '#fff'\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"step-name\">情咖</text>\n\t\t\t\t\t\t\t\t<view class=\"current-badge\" v-if=\"userLevelInfo.userLevel === 3\">\n\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">当前</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"progress-line\" :class=\"{ active: userLevelInfo.userLevel >= 4 }\"></view>\n\t\t\t\t\t\t\t<view class=\"level-step\"\n\t\t\t\t\t\t\t\t:class=\"{ active: userLevelInfo.userLevel >= 4, selected: selectedLevel === 4, current: userLevelInfo.userLevel === 4 }\"\n\t\t\t\t\t\t\t\t@click=\"selectLevel(4)\">\n\t\t\t\t\t\t\t\t<view class=\"step-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons type=\"medal\" size=\"24\"\n\t\t\t\t\t\t\t\t\t\t:color=\"selectedLevel === 4 ? '#696CF3' : '#fff'\"></uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"step-name\">牵缘</text>\n\t\t\t\t\t\t\t\t<view class=\"current-badge\" v-if=\"userLevelInfo.userLevel === 4\">\n\t\t\t\t\t\t\t\t\t<text class=\"badge-text\">当前</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 升级提示 -->\n\t\t\t\t\t\t<view class=\"upgrade-tip\">\n\t\t\t\t\t\t\t<text>升级至“牵缘”，认识更优质的Ta</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 任务列表 -->\n\t\t\t\t\t<view class=\"task-section\">\n\t\t\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t\t\t<text>完成以下任务即可升级为“牵缘”</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 加载状态 -->\n\t\t\t\t\t\t<view class=\"loading-container\" v-if=\"isLoadingTasks\">\n\t\t\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 任务列表 -->\n\t\t\t\t\t\t<view class=\"task-list\" v-else-if=\"taskList.length > 0\">\n\t\t\t\t\t\t\t<view class=\"task-item\" v-for=\"task in taskList\" :key=\"task.taskId\">\n\t\t\t\t\t\t\t\t<view class=\"task-info\">\n\t\t\t\t\t\t\t\t\t<view class=\"task-title\">{{ task.taskName }}</view>\n\t\t\t\t\t\t\t\t\t<view class=\"task-reward\">\n\t\t\t\t\t\t\t\t\t\t<image src=\"/static/image/icons/coin.png\" class=\"coin-icon\"></image>\n\t\t\t\t\t\t\t\t\t\t<text class=\"reward-text\">+{{ task.coin }}花瓣</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"task-action\">\n\t\t\t\t\t\t\t\t\t<button class=\"action-btn\" :class=\"task.isCompleted ? 'completed' : 'primary'\"\n\t\t\t\t\t\t\t\t\t\t@click=\"handleTaskAction(task)\">\n\t\t\t\t\t\t\t\t\t\t{{ task.isCompleted ? '已完成' : '去完成' }}\n\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t\t<view class=\"empty-tasks\" v-else>\n\t\t\t\t\t\t\t<view class=\"empty-icon\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"info\" size=\"48\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"empty-text\">暂无任务</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, onMounted, watch } from 'vue'\nimport { onPageScroll } from '@dcloudio/uni-app'\nimport globalConfig from '@/config'\nimport { getTaskListByLevel, USER_LEVELS, TASK_STATUS } from '@/api/my/coin'\nimport { getUserLevel } from '@/api/my/my'\n\n\n// 页面状态\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0)\nconst currentLevel = ref(1) // 当前等级：1-萌新, 2-缘友, 3-情咖, 4-牵缘\nconst selectedLevel = ref(1) // 选中的等级，默认选中当前等级\n\n// 用户等级信息\nconst userLevelInfo = ref({\n\tuserId: null,\n\tuserLevel: 1,\n\tuserLevelDesc: '',\n\tnickName: '',\n\tisVip: false\n})\nconst isLoadingUserLevel = ref(false)\n\n// 任务列表数据\nconst taskList = ref([])\nconst isLoadingTasks = ref(false)\n\n// 等级名称映射\nconst levelNames = {\n\t[USER_LEVELS.NEWBIE]: '萌新',\n\t[USER_LEVELS.FRIEND]: '缘友',\n\t[USER_LEVELS.EXPERT]: '情咖',\n\t[USER_LEVELS.MASTER]: '牵缘'\n}\n\n// 等级图标映射\nconst levelIcons = {\n\t[USER_LEVELS.NEWBIE]: 'star',\n\t[USER_LEVELS.FRIEND]: 'heart',\n\t[USER_LEVELS.EXPERT]: 'fire',\n\t[USER_LEVELS.MASTER]: 'medal'\n}\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 加载用户等级信息\nconst loadUserLevel = async () => {\n\ttry {\n\t\tisLoadingUserLevel.value = true\n\t\tconst response = await getUserLevel()\n\t\tif (response.code === 200 && response.data) {\n\t\t\tuserLevelInfo.value = response.data\n\t\t\t// 更新当前等级和选中等级\n\t\t\tcurrentLevel.value = response.data.userLevel\n\t\t\tselectedLevel.value = response.data.userLevel\n\t\t\tconsole.log('用户等级信息加载成功:', response.data)\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载用户等级信息失败:', error)\n\t\tuni.showToast({\n\t\t\ttitle: '加载用户等级失败',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tisLoadingUserLevel.value = false\n\t}\n}\n\n// 页面挂载时初始化\nonMounted(async () => {\n\t// 先加载用户等级信息\n\tawait loadUserLevel()\n\t// 然后加载对应等级的任务列表\n\tloadTaskList(selectedLevel.value)\n})\n\n// 监听选中等级变化\nwatch(selectedLevel, (newLevel) => {\n\tloadTaskList(newLevel)\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\n// 计算导航栏文字颜色\nconst getNavTextColor = () => {\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\n\treturn opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor\n}\n\n// 加载任务列表\nconst loadTaskList = async (level) => {\n\ttry {\n\t\tisLoadingTasks.value = true\n\t\tconst response = await getTaskListByLevel(level)\n\t\tif (response.code === 200 && response.data) {\n\t\t\ttaskList.value = response.data\n\t\t\tconsole.log('任务列表加载成功:', response.data)\n\n\t\t\t// 检查每个任务的path字段\n\t\t\tresponse.data.forEach((task, index) => {\n\t\t\t\tconsole.log(`任务${index + 1}:`, {\n\t\t\t\t\tid: task.id,\n\t\t\t\t\ttitle: task.title,\n\t\t\t\t\tisCompleted: task.isCompleted,\n\t\t\t\t\tpath: task.path,\n\t\t\t\t\ttaskType: task.taskType\n\t\t\t\t})\n\t\t\t})\n\t\t} else {\n\t\t\ttaskList.value = []\n\t\t\tconsole.log('任务列表为空或加载失败:', response)\n\t\t}\n\t} catch (error) {\n\t\tconsole.error('加载任务列表失败:', error)\n\t\ttaskList.value = []\n\t\tuni.showToast({\n\t\t\ttitle: '加载任务列表失败',\n\t\t\ticon: 'none'\n\t\t})\n\t} finally {\n\t\tisLoadingTasks.value = false\n\t}\n}\n\n// 选择等级\nconst selectLevel = (level) => {\n\tselectedLevel.value = level\n\t// 根据选中的等级加载对应的任务列表\n\tloadTaskList(level)\n\tconsole.log('选中等级:', level, '等级名称:', levelNames[level])\n}\n\n// 获取当前等级名称\nconst getCurrentLevelName = () => {\n\treturn levelNames[userLevelInfo.value.userLevel] || '萌新'\n}\n\n// 获取当前等级图标\nconst getCurrentLevelIcon = () => {\n\treturn levelIcons[userLevelInfo.value.userLevel] || 'star'\n}\n\n// 获取任务区域标题\nconst getTaskSectionTitle = () => {\n\tconst nextLevel = selectedLevel.value + 1\n\tconst nextLevelName = levelNames[nextLevel] || '最高等级'\n\tif (selectedLevel.value >= 4) {\n\t\treturn '您已达到最高等级'\n\t}\n\treturn `完成以下任务即可升级为\"${nextLevelName}\"`\n}\n\n// 处理任务操作\nconst handleTaskAction = (task) => {\n\tif (task.isCompleted) {\n\t\treturn\n\t}\n\t// 检查任务是否有跳转路径\n\tif (task.path && task.path.trim()) {\n\t\tconst targetPath = task.path.trim()\n\t\tuni.navigateTo({\n\t\t\turl: targetPath\n\t\t})\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(180deg, #f0f2ff 0%, #fafbff 50%, #fff 100%);\n}\n\n.main-container {\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx 120rpx;\n}\n\n// 当前等级展示\n.current-level {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 40rpx 30rpx;\n\tmargin: 20rpx 0 30rpx;\n\tbackground: linear-gradient(135deg, rgba($primary-color, 0.08), rgba($primary-color, 0.15));\n\tborder-radius: 20rpx;\n\tborder: 1rpx solid rgba($primary-color, 0.2);\n\tbackdrop-filter: blur(10rpx);\n\tbox-shadow: 0 8rpx 24rpx rgba($primary-color, 0.1);\n\tposition: relative;\n\toverflow: hidden;\n\n\t// 背景文字\n\t.bg-text {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, -50%) rotate(-15deg);\n\t\tfont-size: 48rpx;\n\t\tfont-weight: 900;\n\t\tcolor: rgba($primary-color, 0.08);\n\t\tletter-spacing: 4rpx;\n\t\tz-index: 0;\n\t\tpointer-events: none;\n\t\tuser-select: none;\n\t\twhite-space: nowrap;\n\t\ttext-transform: uppercase;\n\t\tfont-family: 'Arial Black', Arial, sans-serif;\n\t}\n\n\t.level-info {\n\t\tposition: relative;\n\t\tz-index: 1;\n\n\t\t.level-label {\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #666;\n\t\t\tmargin-right: 16rpx;\n\t\t}\n\n\t\t.level-name {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: $primary-color;\n\t\t}\n\n\t\t.level-desc {\n\t\t\tmargin-top: 8rpx;\n\n\t\t\t.desc-text {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t\tline-height: 1.4;\n\t\t\t}\n\t\t}\n\t}\n\n\t.level-icon {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tposition: relative;\n\t\tz-index: 1;\n\n\t\t.cat-icon {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tmargin-bottom: 8rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground: #fff;\n\t\t\tborder-radius: 50%;\n\t\t\tbox-shadow: 0 6rpx 20rpx rgba($primary-color, 0.4);\n\t\t\tborder: 2rpx solid rgba($primary-color, 0.2);\n\t\t}\n\n\t\t.icon-label {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: $primary-color;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\t.vip-badge {\n\t\t\tposition: absolute;\n\t\t\ttop: -5rpx;\n\t\t\tright: -10rpx;\n\t\t\tbackground: linear-gradient(135deg, #FFD700, #FFA500);\n\t\t\tborder-radius: 20rpx;\n\t\t\tpadding: 4rpx 8rpx;\n\t\t\ttransform: scale(0.8);\n\t\t\tz-index: 10;\n\n\t\t\t.vip-text {\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 18rpx;\n\t\t\t\tfont-weight: 700;\n\t\t\t\ttext-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 等级进度卡片\n.level-progress-card {\n\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 15%));\n\tborder-radius: 24rpx;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 40rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba($primary-color, 0.3);\n\n\t.level-icons {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 30rpx;\n\n\t\t.level-step {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tflex: 1;\n\t\t\tcursor: pointer;\n\t\t\ttransition: all 0.3s ease;\n\n\t\t\t.step-icon {\n\t\t\t\twidth: 60rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbackground: rgba(255, 255, 255, 0.3);\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 40rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.step-name {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: rgba(255, 255, 255, 0.8);\n\t\t\t\ttext-align: center;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tmax-width: 80rpx;\n\t\t\t}\n\n\t\t\t// 选中状态优先级最高\n\t\t\t&.selected {\n\t\t\t\ttransform: scale(1.05);\n\n\t\t\t\t.step-icon {\n\t\t\t\t\tbackground: #fff !important;\n\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.4);\n\t\t\t\t\ttransform: scale(1.2);\n\t\t\t\t}\n\n\t\t\t\t.step-name {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\ttext-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 未选中的状态（包括激活和未激活）\n\t\t\t&:not(.selected) {\n\t\t\t\t.step-icon {\n\t\t\t\t\tbackground: rgba(255, 255, 255, 0.3);\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\t.step-icon {\n\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.5);\n\t\t\t\t\t\ttransform: scale(1.05);\n\t\t\t\t\t}\n\n\t\t\t\t\t.step-name {\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.current {\n\t\t\t\t\t.step-icon {\n\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\n\t\t\t\t\t\tborder: 2px solid rgba(255, 255, 255, 0.9);\n\t\t\t\t\t\tbox-shadow: 0 0 20rpx rgba(255, 255, 255, 0.3);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.current-badge {\n\t\t\tposition: absolute;\n\t\t\ttop: -8rpx;\n\t\t\tright: -15rpx;\n\t\t\tbackground: linear-gradient(135deg, #ff6b6b, #ff8e8e);\n\t\t\tborder-radius: 20rpx;\n\t\t\tpadding: 4rpx 12rpx;\n\t\t\ttransform: scale(0.8);\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.badge-text {\n\t\t\tcolor: white;\n\t\t\tfont-size: 20rpx;\n\t\t\tfont-weight: 600;\n\t\t}\n\n\t\t&:active {\n\t\t\ttransform: scale(0.95);\n\t\t}\n\n\t\t.progress-line {\n\t\t\tflex: 1;\n\t\t\theight: 4rpx;\n\t\t\tbackground: rgba(255, 255, 255, 0.3);\n\t\t\tmargin: 0 20rpx;\n\t\t\tborder-radius: 2rpx;\n\t\t\tmargin-top: -30rpx;\n\n\t\t\t&.active {\n\t\t\t\tbackground: rgba(255, 255, 255, 0.8);\n\t\t\t}\n\t\t}\n\t}\n\n\t.upgrade-tip {\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 16rpx 24rpx;\n\t\ttext-align: center;\n\n\t\ttext {\n\t\t\tcolor: #fff;\n\t\t\tfont-size: 26rpx;\n\t\t\tfont-weight: 500;\n\t\t}\n\t}\n}\n\n// 任务部分\n.task-section {\n\t.section-title {\n\t\tmargin-bottom: 30rpx;\n\n\t\ttext {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #333;\n\t\t}\n\t}\n\n\t// 加载状态样式\n\t.loading-container {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 80rpx 40rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t// 空状态样式\n\t.empty-tasks {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 80rpx 40rpx;\n\t}\n\n\t.empty-icon {\n\t\tmargin-bottom: 24rpx;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999;\n\t}\n\n\t.task-list {\n\t\t.task-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tbackground: #fff;\n\t\t\tborder-radius: 16rpx;\n\t\t\tpadding: 32rpx 24rpx;\n\t\t\tmargin-bottom: 16rpx;\n\t\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n\n\t\t\t.task-info {\n\t\t\t\tflex: 1;\n\n\t\t\t\t.task-title {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\n\t\t\t\t.task-reward {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.coin-icon {\n\t\t\t\t\t\twidth: 24rpx;\n\t\t\t\t\t\theight: 24rpx;\n\t\t\t\t\t\tmargin-right: 8rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.reward-text {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #ff9500;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.task-action {\n\t\t\t\t.action-btn {\n\t\t\t\t\theight: $btn-sm-height;\n\t\t\t\t\tpadding: $btn-sm-padding;\n\t\t\t\t\tborder-radius: $btn-sm-border-radius;\n\t\t\t\t\tfont-size: $btn-sm-font-size;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tmin-width: 120rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t&.completed {\n\t\t\t\t\t\tbackground: #e5e7eb;\n\t\t\t\t\t\tcolor: #9ca3af;\n\t\t\t\t\t}\n\n\t\t\t\t\t&.primary {\n\t\t\t\t\t\tbackground: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba($primary-color, 0.3);\n\t\t\t\t\t\tborder: 1rpx solid rgba($primary-color, 0.2);\n\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba($primary-color, 0.2);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/upgrade/upgrade.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "currentLevel", "selectedLevel", "userLevelInfo", "isLoadingUserLevel", "taskList", "isLoadingTasks", "levelNames", "USER_LEVELS", "levelIcons", "onPageScroll", "e", "loadUserLevel", "response", "getUserLevel", "uni", "error", "onMounted", "loadTaskList", "watch", "newLevel", "level", "getTaskListByLevel", "task", "index", "selectLevel", "getCurrentLevelName", "getCurrentLevelIcon", "handleTaskAction", "targetPath", "MiniProgramPage"], "mappings": "2cAgJA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EACAE,EAAAF,EAAA,IAAA,CAAA,EACAG,EAAAH,EAAA,IAAA,CAAA,EAGAI,EAAAJ,EAAAA,IAAA,CACA,OAAA,KACA,UAAA,EACA,cAAA,GACA,SAAA,GACA,MAAA,EACA,CAAA,EACAK,EAAAL,EAAA,IAAA,EAAA,EAGAM,EAAAN,EAAA,IAAA,EAAA,EACAO,EAAAP,EAAA,IAAA,EAAA,EAGAQ,EAAA,CACA,CAAAC,EAAAA,YAAA,MAAA,EAAA,KACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,KACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,KACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,IACA,EAGAC,EAAA,CACA,CAAAD,EAAAA,YAAA,MAAA,EAAA,OACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,QACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,OACA,CAAAA,EAAAA,YAAA,MAAA,EAAA,OACA,EAGAE,EAAA,aAAAC,GAAA,CACAb,EAAA,MAAAa,EAAA,SACA,CAAA,EAGA,MAAAC,EAAA,SAAA,CACA,GAAA,CACAR,EAAA,MAAA,GACA,MAAAS,EAAA,MAAAC,eAAA,EACAD,EAAA,OAAA,KAAAA,EAAA,OACAV,EAAA,MAAAU,EAAA,KAEAZ,EAAA,MAAAY,EAAA,KAAA,UACAX,EAAA,MAAAW,EAAA,KAAA,UACAE,EAAA,MAAA,MAAA,MAAA,yCAAA,cAAAF,EAAA,IAAA,EAEA,OAAAG,EAAA,CACAD,EAAAA,MAAA,MAAA,QAAA,yCAAA,cAAAC,CAAA,EACAD,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,CACA,QAAA,CACAX,EAAA,MAAA,EACA,CACA,EAGAa,EAAAA,UAAA,SAAA,CAEA,MAAAL,EAAA,EAEAM,EAAAhB,EAAA,KAAA,CACA,CAAA,EAGAiB,EAAAA,MAAAjB,EAAAkB,GAAA,CACAF,EAAAE,CAAA,CACA,CAAA,EAcA,MAAAF,EAAA,MAAAG,GAAA,CACA,GAAA,CACAf,EAAA,MAAA,GACA,MAAAO,EAAA,MAAAS,EAAA,mBAAAD,CAAA,EACAR,EAAA,OAAA,KAAAA,EAAA,MACAR,EAAA,MAAAQ,EAAA,KACAE,EAAA,MAAA,MAAA,MAAA,yCAAA,YAAAF,EAAA,IAAA,EAGAA,EAAA,KAAA,QAAA,CAAAU,EAAAC,IAAA,CACAT,QAAA,MAAA,MAAA,yCAAA,KAAAS,EAAA,CAAA,IAAA,CACA,GAAAD,EAAA,GACA,MAAAA,EAAA,MACA,YAAAA,EAAA,YACA,KAAAA,EAAA,KACA,SAAAA,EAAA,QACA,CAAA,CACA,CAAA,IAEAlB,EAAA,MAAA,CAAA,EACAU,EAAAA,MAAA,MAAA,MAAA,yCAAA,eAAAF,CAAA,EAEA,OAAAG,EAAA,CACAD,EAAAA,MAAA,MAAA,QAAA,yCAAA,YAAAC,CAAA,EACAX,EAAA,MAAA,CAAA,EACAU,EAAAA,MAAA,UAAA,CACA,MAAA,WACA,KAAA,MACA,CAAA,CACA,QAAA,CACAT,EAAA,MAAA,EACA,CACA,EAGAmB,EAAAJ,GAAA,CACAnB,EAAA,MAAAmB,EAEAH,EAAAG,CAAA,EACAN,QAAA,MAAA,MAAA,yCAAA,QAAAM,EAAA,QAAAd,EAAAc,CAAA,CAAA,CACA,EAGAK,EAAA,IACAnB,EAAAJ,EAAA,MAAA,SAAA,GAAA,KAIAwB,EAAA,IACAlB,EAAAN,EAAA,MAAA,SAAA,GAAA,OAcAyB,EAAAL,GAAA,CACA,GAAA,CAAAA,EAAA,aAIAA,EAAA,MAAAA,EAAA,KAAA,KAAA,EAAA,CACA,MAAAM,EAAAN,EAAA,KAAA,KAAA,EACAR,EAAAA,MAAA,WAAA,CACA,IAAAc,CACA,CAAA,CACA,CACA,2/CCjTA,GAAG,WAAWC,CAAe"}