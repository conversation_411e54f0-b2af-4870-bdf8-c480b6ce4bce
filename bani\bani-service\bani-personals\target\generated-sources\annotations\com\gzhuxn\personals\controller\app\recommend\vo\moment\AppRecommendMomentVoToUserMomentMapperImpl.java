package com.gzhuxn.personals.controller.app.recommend.vo.moment;

import com.gzhuxn.personals.domain.user.UserMoment;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppRecommendMomentVoToUserMomentMapperImpl implements AppRecommendMomentVoToUserMomentMapper {

    @Override
    public UserMoment convert(AppRecommendMomentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMoment userMoment = new UserMoment();

        userMoment.setId( arg0.getId() );
        userMoment.setContent( arg0.getContent() );
        userMoment.setImages( arg0.getImages() );

        return userMoment;
    }

    @Override
    public UserMoment convert(AppRecommendMomentVo arg0, UserMoment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );

        return arg1;
    }
}
