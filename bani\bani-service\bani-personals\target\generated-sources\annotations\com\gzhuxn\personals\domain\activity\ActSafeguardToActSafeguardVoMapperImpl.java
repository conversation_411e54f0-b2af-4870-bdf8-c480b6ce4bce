package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActSafeguardVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardToActSafeguardVoMapperImpl implements ActSafeguardToActSafeguardVoMapper {

    @Override
    public ActSafeguardVo convert(ActSafeguard arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguardVo actSafeguardVo = new ActSafeguardVo();

        actSafeguardVo.setId( arg0.getId() );
        actSafeguardVo.setName( arg0.getName() );
        actSafeguardVo.setAmount( arg0.getAmount() );
        actSafeguardVo.setSafeKey( arg0.getSafeKey() );
        actSafeguardVo.setStatus( arg0.getStatus() );
        actSafeguardVo.setCreateTime( arg0.getCreateTime() );

        return actSafeguardVo;
    }

    @Override
    public ActSafeguardVo convert(ActSafeguard arg0, ActSafeguardVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setSafeKey( arg0.getSafeKey() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
