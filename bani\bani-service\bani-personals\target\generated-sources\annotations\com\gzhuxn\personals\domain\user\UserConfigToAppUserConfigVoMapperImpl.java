package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.config.AppUserConfigVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserConfigToAppUserConfigVoMapperImpl implements UserConfigToAppUserConfigVoMapper {

    @Override
    public AppUserConfigVo convert(UserConfig arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserConfigVo appUserConfigVo = new AppUserConfigVo();

        appUserConfigVo.setConfigKey( arg0.getConfigKey() );
        appUserConfigVo.setVal( arg0.getVal() );

        return appUserConfigVo;
    }

    @Override
    public AppUserConfigVo convert(UserConfig arg0, AppUserConfigVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setConfigKey( arg0.getConfigKey() );
        arg1.setVal( arg0.getVal() );

        return arg1;
    }
}
