<template>
	<uni-popup ref="popup" type="bottom" :safe-area="false" @change="handlePopupChange">
		<view class="district-select-container">
			<!-- 头部 -->
			<view class="header">
				<view class="header-left">
					<text class="cancel-btn" @click="handleCancel">取消</text>
				</view>
				<view class="header-center">
					<text class="title">选择地区</text>
				</view>
				<view class="header-right">
					<text class="confirm-btn" @click="handleConfirm">完成</text>
				</view>
			</view>

			<!-- 面包屑导航 -->
			<view class="breadcrumb" v-if="breadcrumbList.length > 0">
				<view class="breadcrumb-item" v-for="(item, index) in breadcrumbList" :key="index"
					@click="handleBreadcrumbClick(index)">
					<text class="breadcrumb-text">{{ item.name }}</text>
					<text class="breadcrumb-arrow" v-if="index < breadcrumbList.length - 1">></text>
				</view>
			</view>

			<!-- 区域列表 -->
			<scroll-view class="district-list" scroll-y :enable-back-to-top="true" :scroll-with-animation="true">
				<view class="district-item" v-for="(item, index) in currentList"
					:key="`${forceUpdateKey}-${item.code}-${index}`"
					:class="{ 'selected': item.code === selectedItem?.code }" @click="handleItemClick(item)">
					<text class="district-name">{{ item.name }}</text>
					<view class="district-right">
						<text class="iconfont bani-check" v-if="item.code === selectedItem?.code"></text>
						<text class="iconfont bani-right" v-else-if="item.hasChild"></text>
					</view>
				</view>

				<!-- 加载状态 -->
				<view class="loading" v-if="loading">
					<uni-load-more status="loading"
						:content-text="{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...' }"></uni-load-more>
				</view>

				<!-- 空状态 -->
				<view class="empty" v-if="!loading && currentList.length === 0">
					<text class="empty-text">暂无数据</text>
				</view>
			</scroll-view>
		</view>
	</uni-popup>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { getDistrictList } from '@/api/district/district'

// Props
const props = defineProps({
	// 是否显示弹窗
	show: {
		type: Boolean,
		default: false
	},
	// 默认选中的值
	defaultValue: {
		type: Object,
		default: () => ({})
	}
})

// Emits
const emit = defineEmits(['confirm', 'cancel', 'update:show'])

// 响应式数据
const popup = ref(null)
const loading = ref(false)
const currentList = ref([]) // 当前显示的列表
const breadcrumbList = ref([]) // 面包屑导航
const selectedItem = ref(null) // 当前选中的项目
const selectedPath = ref([]) // 选中的路径
const forceUpdateKey = ref(0) // 强制更新key

// 监听显示状态变化
const handlePopupChange = (e) => {
	if (e.show) {
		// 弹窗打开时初始化数据
		initData()
	} else {
		// 弹窗关闭时重置数据
		resetData()
	}
}

// 初始化数据
const initData = () => {
	console.log('初始化行政区域选择器')
	breadcrumbList.value = [{
		name: '全国',
		code: '0'
	}]
	selectedPath.value = []
	selectedItem.value = null
	loadDistrictList('')
}

// 重置数据
const resetData = () => {
	currentList.value = []
	breadcrumbList.value = []
	selectedPath.value = []
	selectedItem.value = null
	loading.value = false
}

// 加载行政区划列表
const loadDistrictList = (code = '0') => {
	loading.value = true
	getDistrictList(code).then(response => {
		currentList.value = response.data
	}).finally(() => {
		loading.value = false
	})
}

// 处理项目点击
const handleItemClick = (item) => {
	selectedItem.value = item
	if (item.hasChild) {
		// 有下级，加载下级数据
		// 先检查是否已经在路径中，避免重复添加
		const currentLevel = breadcrumbList.value.length

		// 更新选择路径和面包屑
		if (selectedPath.value.length > currentLevel) {
			selectedPath.value = selectedPath.value.slice(0, currentLevel)
		}
		selectedPath.value.push(item)

		if (breadcrumbList.value.length > currentLevel) {
			breadcrumbList.value = breadcrumbList.value.slice(0, currentLevel)
		}
		breadcrumbList.value.push(item)
		loadDistrictList(item.code)
	} else {
		// 没有下级，选中该项目
		const currentLevel = breadcrumbList.value.length
		if (selectedPath.value.length > currentLevel) {
			selectedPath.value = selectedPath.value.slice(0, currentLevel)
		}
		selectedPath.value.push(item)
		handleConfirm()
	}
}

// 处理面包屑点击
const handleBreadcrumbClick = (index) => {
	// 如果点击的是最后一级，不需要处理
	if (index === breadcrumbList.value.length - 1) {
		return
	}

	// 截取到指定层级
	const targetItem = breadcrumbList.value[index]
	breadcrumbList.value = breadcrumbList.value.slice(0, index + 1)
	selectedPath.value = selectedPath.value.slice(0, index + 1)
	if (0 === index) {
		selectedPath.value = []
	}
	// 清空当前选中项
	selectedItem.value = null

	// 重新加载该层级的下级数据
	loadDistrictList(targetItem.code)
}

// 处理确认
const handleConfirm = () => {
	if (selectedPath.value.length === 0) {
		uni.showToast({
			title: '请选择地区',
			icon: 'none'
		})
		return
	}
	const flag = hasMunicipality(selectedPath.value[0])
	// 构建返回数据
	const result = {
		province: selectedPath.value[0] || null,
		city: selectedPath.value[flag ? 0 : 1] || null,
		district: selectedPath.value[flag ? 1 : 2] || null,
		street: selectedPath.value[flag ? 2 : 3] || null,
		fullPath: selectedPath.value,
		fullName: selectedPath.value.map(item => item.name).join(''),
		codes: {
			provinceCode: selectedPath.value[0]?.code || '',
			cityCode: selectedPath.value[flag ? 0 : 1]?.code || '',
			districtCode: selectedPath.value[flag ? 1 : 2]?.code || '',
			streetCode: selectedPath.value[flag ? 2 : 3]?.code || ''
		}
	}
	console.log('确认选择地区:', result)
	emit('confirm', result)
	handleClose()
}

/**
 * 判断是否是直辖市
 */
const hasMunicipality = (item) => {
	switch (item.name) {
		case '北京市':
		case '天津市':
		case '上海市':
		case '重庆市':
		case '香港':
		case '澳门':
			return true;
		default:
			return false;
	}
}

// 处理取消
const handleCancel = () => {
	console.log('取消选择地区')
	emit('cancel')
	handleClose()
}

// 关闭弹窗
const handleClose = () => {
	popup.value?.close()
	emit('update:show', false)
}

// 打开弹窗
const open = () => {
	popup.value?.open()
}

// 关闭弹窗
const close = () => {
	popup.value?.close()
}

// 暴露方法
defineExpose({
	open,
	close
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';
@import '@/static/fonts/iconfont.css';

.district-select-container {
	background: #fff;
	border-radius: 16rpx 16rpx 0 0;
	height: 60vh;
	max-height: 60vh;
	display: flex;
	flex-direction: column;
}

.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	background: #fff;
	border-radius: 16rpx 16rpx 0 0;
}

.header-left,
.header-right {
	min-width: 100rpx;
}

.header-center {
	flex: 1;
	text-align: center;
}

.title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.cancel-btn {
	font-size: 28rpx;
	color: #999;
}

.confirm-btn {
	font-size: 28rpx;
	color: #696CF3;
	font-weight: 500;
}

.breadcrumb {
	display: flex;
	align-items: center;
	padding: 20rpx 32rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	flex-wrap: wrap;
}

.breadcrumb-item {
	display: flex;
	align-items: center;
	margin-right: 16rpx;
	cursor: pointer;
}

.breadcrumb-text {
	font-size: 26rpx;
	color: #696CF3;
	margin-right: 8rpx;
}

.breadcrumb-arrow {
	font-size: 24rpx;
	color: #ccc;
}

.district-list {
	flex: 1;
	height: 0; /* 关键：设置高度为0，配合flex: 1使其能够正确计算滚动区域 */
	padding: 0 32rpx;
}

.district-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: background-color 0.3s ease;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: rgba(0, 0, 0, 0.02);
	}

	&.selected {
		background-color: rgba(105, 108, 243, 0.05);
	}
}

.district-name {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.district-right {
	display: flex;
	align-items: center;
}

.district-right .iconfont {
	font-size: 24rpx;
	color: #696CF3;

	&.bani-right {
		color: #ccc;
	}
}

.loading {
	padding: 40rpx 0;
	text-align: center;
}

.empty {
	padding: 80rpx 0;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.header {
		padding: 20rpx 24rpx;
	}

	.breadcrumb {
		padding: 16rpx 24rpx;
	}

	.district-list {
		padding: 0 24rpx;
	}

	.district-item {
		padding: 20rpx 0;
	}
}
</style>
