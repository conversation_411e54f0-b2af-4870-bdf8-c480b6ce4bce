package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageVipVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageVipToAppManageVipVoMapperImpl implements ManageVipToAppManageVipVoMapper {

    @Override
    public AppManageVipVo convert(ManageVip arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppManageVipVo appManageVipVo = new AppManageVipVo();

        appManageVipVo.setId( arg0.getId() );
        appManageVipVo.setMonths( arg0.getMonths() );
        appManageVipVo.setOriginalAmount( arg0.getOriginalAmount() );
        appManageVipVo.setAmount( arg0.getAmount() );
        appManageVipVo.setCoin( arg0.getCoin() );

        return appManageVipVo;
    }

    @Override
    public AppManageVipVo convert(ManageVip arg0, AppManageVipVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setMonths( arg0.getMonths() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
