package com.gzhuxn.personals.controller.app.user.bo.moment;

import com.gzhuxn.personals.domain.user.UserMoment;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppUserMomentCreateBoToUserMomentMapper extends BaseMapper<AppUserMomentCreateBo, UserMoment> {
}
