package com.gzhuxn.personals.controller.app.group.vo;

import com.gzhuxn.personals.domain.group.Group;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppGroupVoToGroupMapperImpl implements AppGroupVoToGroupMapper {

    @Override
    public Group convert(AppGroupVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Group group = new Group();

        group.setId( arg0.getId() );
        group.setName( arg0.getName() );
        group.setAvatar( arg0.getAvatar() );
        group.setIntroduce( arg0.getIntroduce() );
        group.setBackgroundImg( arg0.getBackgroundImg() );
        group.setOfficialFlag( arg0.getOfficialFlag() );
        group.setOwnerUserId( arg0.getOwnerUserId() );

        return group;
    }

    @Override
    public Group convert(AppGroupVo arg0, Group arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setBackgroundImg( arg0.getBackgroundImg() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setOwnerUserId( arg0.getOwnerUserId() );

        return arg1;
    }
}
