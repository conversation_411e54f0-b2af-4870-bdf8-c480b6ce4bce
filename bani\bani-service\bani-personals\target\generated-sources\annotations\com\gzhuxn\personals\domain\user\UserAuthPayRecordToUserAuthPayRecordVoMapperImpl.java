package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAuthPayRecordToUserAuthPayRecordVoMapperImpl implements UserAuthPayRecordToUserAuthPayRecordVoMapper {

    @Override
    public UserAuthPayRecordVo convert(UserAuthPayRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAuthPayRecordVo userAuthPayRecordVo = new UserAuthPayRecordVo();

        userAuthPayRecordVo.setId( arg0.getId() );
        userAuthPayRecordVo.setUserId( arg0.getUserId() );
        userAuthPayRecordVo.setAuthApplyId( arg0.getAuthApplyId() );
        userAuthPayRecordVo.setAuthType( arg0.getAuthType() );
        userAuthPayRecordVo.setOriginalAmount( arg0.getOriginalAmount() );
        userAuthPayRecordVo.setAmount( arg0.getAmount() );
        userAuthPayRecordVo.setCoin( arg0.getCoin() );
        userAuthPayRecordVo.setPayStatus( arg0.getPayStatus() );
        userAuthPayRecordVo.setOrderId( arg0.getOrderId() );
        userAuthPayRecordVo.setCreateTime( arg0.getCreateTime() );
        userAuthPayRecordVo.setUpdateTime( arg0.getUpdateTime() );
        userAuthPayRecordVo.setUseStatus( arg0.getUseStatus() );

        return userAuthPayRecordVo;
    }

    @Override
    public UserAuthPayRecordVo convert(UserAuthPayRecord arg0, UserAuthPayRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAuthApplyId( arg0.getAuthApplyId() );
        arg1.setAuthType( arg0.getAuthType() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayStatus( arg0.getPayStatus() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUseStatus( arg0.getUseStatus() );

        return arg1;
    }
}
