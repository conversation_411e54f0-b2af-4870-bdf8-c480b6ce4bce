package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserAuthApply;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAuthApplyBoToUserAuthApplyMapperImpl implements UserAuthApplyBoToUserAuthApplyMapper {

    @Override
    public UserAuthApply convert(UserAuthApplyBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAuthApply userAuthApply = new UserAuthApply();

        userAuthApply.setUserId( arg0.getUserId() );
        userAuthApply.setType( arg0.getType() );
        userAuthApply.setContentJs( arg0.getContentJs() );

        return userAuthApply;
    }

    @Override
    public UserAuthApply convert(UserAuthApplyBo arg0, UserAuthApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setContentJs( arg0.getContentJs() );

        return arg1;
    }
}
