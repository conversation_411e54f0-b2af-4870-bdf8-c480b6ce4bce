package com.gzhuxn.personals.controller.app.user.bo.questionapply;

import com.gzhuxn.personals.domain.user.UserQuestionApply;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserQuestionApplyAnswerBoToUserQuestionApplyMapperImpl implements AppUserQuestionApplyAnswerBoToUserQuestionApplyMapper {

    @Override
    public UserQuestionApply convert(AppUserQuestionApplyAnswerBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserQuestionApply userQuestionApply = new UserQuestionApply();

        userQuestionApply.setId( arg0.getId() );
        userQuestionApply.setAnswerContent( arg0.getAnswerContent() );

        return userQuestionApply;
    }

    @Override
    public UserQuestionApply convert(AppUserQuestionApplyAnswerBo arg0, UserQuestionApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setAnswerContent( arg0.getAnswerContent() );

        return arg1;
    }
}
