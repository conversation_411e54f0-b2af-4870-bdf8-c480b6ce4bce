import {
	ref,
	reactive
} from 'vue'
import $store from '@/store'
import { updateUserLocation } from '@/api/my/my.js'
import { getPayStatus } from '@/api/order/order.js'
/**
 * 显示消息提示框
 * @param content 提示的标题
 */
export function toast(content) {
	uni.showToast({
		icon: 'none',
		title: content
	})
}

/**
 * 显示模态弹窗
 * @param content 提示的标题
 */
export function showConfirm(content) {
	return new Promise((resolve, reject) => {
		uni.showModal({
			title: '提示',
			content: content,
			cancelText: '取消',
			confirmText: '确定',
			success: function (res) {
				resolve(res)
			}
		})
	})
}

/**
 * 处理支付并轮询查询支付状态
 * @param {Object} payData - 支付数据
 * @param {string} payData.appId - 应用ID
 * @param {string} payData.timeStamp - 时间戳
 * @param {string} payData.nonceStr - 随机字符串
 * @param {string} payData.packageValue - 订单详情扩展字符串
 * @param {string} payData.signType - 签名方式
 * @param {string} payData.paySign - 签名
 * @param {string} payOrderNo - 支付订单号，用于查询支付状态
 * @param {Object} options - 配置选项
 * @param {number} [options.pollInterval=2000] - 轮询间隔时间（毫秒），默认2秒
 * @param {number} [options.maxPollCount=30] - 最大轮询次数，默认30次（1分钟）
 * @returns {Promise} 返回Promise对象，resolve时返回支付结果 {success: boolean, status: number, message: string}
 */
export function handlePaymentWithPolling(data, options = {}) {
	const payData = data.payData;
	return new Promise((resolve, reject) => {
		const { pollInterval = 2000, maxPollCount = 30 } = options
		let pollCount = 0
		let pollTimer = null

		// 清理轮询定时器
		const clearPollTimer = () => {
			if (pollTimer) {
				clearInterval(pollTimer)
				pollTimer = null
			}
			uni.hideLoading()
		}

		// 轮询查询支付状态
		const pollPayStatus = () => {
			pollCount++
			getPayStatus(data.payOrderNo).then(res => {
				const status = res.data.status
				// 支付成功
				if (status === 10) {
					clearPollTimer()
					resolve({
						success: true,
						status: status,
						message: '支付成功'
					})
					return
				}

				// 支付失败或关闭
				if (status === 3 || status === 4) {
					clearPollTimer()
					resolve({
						success: false,
						status: status,
						message: status === 3 ? '支付失败' : '支付已关闭'
					})
					return
				}

				// 其他状态继续轮询
				if (pollCount >= maxPollCount) {
					clearPollTimer()
					resolve({
						success: false,
						status: status,
						message: '支付状态查询超时'
					})
				}
			}).catch(err => {
				console.error('支付状态查询异常:', err)
				if (pollCount >= maxPollCount) {
					clearPollTimer()
					resolve({
						success: false,
						status: -1,
						message: '支付状态查询异常'
					})
				}
			})
		}

		// 显示支付中提示
		uni.showLoading({
			title: '支付中...'
		})

		// 执行微信支付
		uni.requestPayment({
			provider: 'wxpay',
			appId: payData.appId,
			timeStamp: payData.timeStamp,
			nonceStr: payData.nonceStr,
			package: payData.packageValue,
			signType: payData.signType,
			paySign: payData.paySign,
			success: () => {
				// 支付调用成功后，开始轮询查询支付状态
				pollTimer = setInterval(pollPayStatus, pollInterval)
				// 立即执行一次查询
				pollPayStatus()
			},
			fail: (error) => {
				console.error('微信支付调用失败:', error)
				clearPollTimer()

				// 处理不同的支付错误
				let errorMessage = '支付失败'
				if (error.errMsg) {
					if (error.errMsg.includes('cancel')) {
						errorMessage = '支付已取消'
					} else if (error.errMsg.includes('fail')) {
						errorMessage = '支付失败，请重试'
					} else {
						errorMessage = '支付异常，请重试'
					}
				}

				resolve({
					success: false,
					status: -1,
					message: errorMessage
				})
			}
		})
	})
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params) {
	let result = ''
	for (const propName of Object.keys(params)) {
		const value = params[propName]
		var part = encodeURIComponent(propName) + "="
		if (value !== null && value !== "" && typeof (value) !== "undefined") {
			if (typeof value === 'object') {
				for (const key of Object.keys(value)) {
					if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
						let params = propName + '[' + key + ']'
						var subPart = encodeURIComponent(params) + "="
						result += subPart + encodeURIComponent(value[key]) + "&"
					}
				}
			} else {
				result += part + encodeURIComponent(value) + "&"
			}
		}
	}
	return result
}

/**
 * 创建UUID
 */
export function createUUID() {
	var dt = new Date().getTime(); // 获取当前时间的毫秒数
	var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = (dt + Math.random() * 16) % 16 | 0;
		dt = Math.floor(dt / 16);
		return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
	});
	return uuid;
}

/**
 * 创建rule规则
 * @param {Object} form
 */
export function createFormRules(form) {
	var rules = {};
	Object.keys(form).forEach(key => {
		var rule = form[key]['rule']
		if (rule) {
			rules[key] = rule
		}
	})
	return reactive(rules)
}

/**
 * 初始化加载表单
 */
export function initForm(form) {
	// 加载字典数据
	var loadTypes = [];
	Object.keys(form).forEach(key => {
		var t = form[key]
		// 拉取字典数据
		if (t.dictType) {
			loadTypes.push(t.dictType)
		}
	})
	if (loadTypes.length > 0) {
		$store.dict.loads(loadTypes)
	}

	// 初始化表单
	var retJs = {};
	Object.keys(form).forEach(key => {
		var t = form[key]
		// 添加字段名称
		t['field'] = key
		// 值数据初始化
		valNullSetDefault(t, 'val', '')
		valNullSetDefault(t, 'name', '')
		// 显示状态
		valNullSetDefault(t, 'show', false)
		// 拉取字典数据
		// if (t.dictType) {
		// 	t['dicts'] = $store.dict.get(t.dictType)
		// }
		const isSelect = t.dicts ? true : false;
		const placeholderMsg = (isSelect ? '请选择' : '请填写') + t.title;
		t['placeholder'] = placeholderMsg;
		// 字段验证
		if (t.rule) {
			if ((typeof t.rule) == 'object') {
				formatFormRuleObj(r.rule, placeholderMsg);
			} else if (Array.isArray(t.rule)) {
				t.rule.forEach(item => {
					formatFormRuleObj(item, placeholderMsg);
				})
			}
		}
		retJs[key] = t;
	})
	return reactive(retJs);
}
/**
 * 格式化校验规则
 */
function formatFormRuleObj(ruleObj, placeholderMsg) {
	valNullSetDefault(ruleObj, 'type', 'string');
	valNullSetDefault(ruleObj, 'required', true);

	// 校验提示
	valNullSetDefault(ruleObj, 'message', placeholderMsg);
}

/**
 * 值为空设置默认值
 */
function valNullSetDefault(source, field, defVal) {
	if (source[field] === undefined || source[field] === null || source[field] === '') {
		source[field] = defVal;
	}
}

/**
 * 初始化字典数据
 * @param {Object} form
 */
export function initFormDicts(form) {
	Object.keys(form).forEach(key => {
		var t = form[key]
		// 拉取字典数据
		if (t.dictType) {
			t['dicts'] = $store.dict.get(t.dictType)
		}
		if (t.dicts) {
			// 字典名称
			t['dictNames'] = []
			t['dicts'].forEach(item => {
				t['dictNames'].push(item.name)
			})
		}
	})
}

/**
 * 加载表单数据
 * @param {Object} form
 * @param {Object} data
 */
export function initFormData(form, data) {
	if (!data) {
		return;
	}
	Object.keys(form).forEach(key => {
		var field = form[key]
		if (data[key]) {
			field.val = data[key]
			field.name = data[key]
			if (field.dicts) {
				field.dicts.forEach((item, index) => {
					if (item.id == field.val) {
						field.name = item.name
					}
				})
			}
		}
	})
}

/**
 * 校验提交数据
 * @param {Object} form
 */
export function validFormData(form) {
	var flag = true
	Object.keys(form).forEach(key => {
		var field = form[key]
		if (field.requried && isEmpty(field.val)) {
			toast(field.title + "必填")
			flag = false;
			return false;
		}
	})
	return flag
}
/**
 * 获取提交数据
 * @param {Object} form
 */
export function getSubmitData(form) {
	var retJs = {}
	Object.keys(form).forEach(key => {
		var field = form[key]
		retJs[key] = field.val
	})
	return retJs;
}
/**
 * 日期格式化
 * @param {Object} date date对象
 * @param {Object} format 格式化
 */
export function formatDateTime(date, format) {
	const o = {
		'M+': date.getMonth() + 1, // 月份
		'd+': date.getDate(), // 日
		'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
		'H+': date.getHours(), // 小时
		'm+': date.getMinutes(), // 分
		's+': date.getSeconds(), // 秒
		'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
		S: date.getMilliseconds(), // 毫秒
		a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
		A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
	};
	if (/(y+)/.test(format)) {
		format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
	}
	for (let k in o) {
		if (new RegExp('(' + k + ')').test(format)) {
			format = format.replace(
				RegExp.$1,
				RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
			);
		}
	}
	return format;
}

/**
 *  身高（cm）字典
 */
export function heightDicts() {
	var heightVals = [];
	for (var i = 140; i <= 220; i++) {
		heightVals.push({
			id: i,
			name: i + 'cm'
		})
	}
	return heightVals;
}
/**
 *  体重（kg）字典
 */
export function weightDicts() {
	var weightVals = [];
	for (var i = 40; i <= 120; i++) {
		weightVals.push({
			id: i,
			name: i + 'kg'
		})
	}
	return weightVals;
}

/**
 * 点击复制
 * @param {Object} data
 */
export function clickCopy(data) {
	if (!data) {
		toast("复制内容为空")
		return
	}
	uni.setClipboardData({
		data: String(data),
		success: function () {
			toast("复制成功")
		},
		fail: function (err) {
			toast("复制失败")
		}
	});
}

/**
 * 点击后toast 弹出功能开发中，敬请期待！
 */
export function clickDeveloping() {
	toast("功能开发中，敬请期待！")
}

// 判断是否为空
export function isEmpty(val) {
	if (val === null || val === undefined || val === '') {
		return true;
	}
	return false;
}
/**
 * 判断是否为字符串
 * @param {Object} val
 */
export function isString(val) {
	return typeof val === 'string'
}


/**
 * 图片压缩
 * @param {Object} filePath 图片路径
 * @param {Object} quality 压缩质量
 */
export function compressImage(filePath, quality) {
	return new Promise((resolve) => {
		uni.compressImage({
			src: filePath,
			quality: quality ? quality : 80, // 压缩质量，范围0-100，数值越小，质量越低，体积越小
			success: (res) => {
				resolve(res.tempFilePath)
			},
			fail: (err) => {
				console.error('图片压缩失败:', err)
				// 压缩失败时使用原图
				resolve(filePath)
			}
		})
	})
}

/**
 * 获取当前页面完整路径
 * @description 获取当前页面的路径，包含查询参数
 * @returns {string} 返回完整的页面路径，如 '/pages/user/profile?id=123'，如果获取失败返回空字符串
 */
export function getCurrentPagePath() {
	try {
		// 获取当前页面路径
		const pages = getCurrentPages()
		if (!pages || !Array.isArray(pages) || pages.length === 0) {
			console.warn('无法获取当前页面信息')
			return ''
		}
		const currentPage = pages[pages.length - 1]
		if (!currentPage) {
			console.warn('当前页面对象不存在')
			return ''
		}

		const currentRoute = currentPage.route
		const currentOptions = currentPage.options || {}

		console.log('当前页面路径getCurrentPagePath:', currentRoute)

		// 构建完整的页面路径（包含参数）
		let fullPath = '/' + currentRoute
		if (currentOptions && Object.keys(currentOptions).length > 0) {
			const queryString = Object.keys(currentOptions)
				.map(key => `${key}=${encodeURIComponent(currentOptions[key] || '')}`)
				.join('&')
			fullPath += '?' + queryString
		}
		return fullPath
	} catch (error) {
		console.error('获取当前页面路径失败:', error)
		return ''
	}
}

/**
 * 获取经纬度信息
 * @description 先查询本地存储的经纬度信息，检查是否过期（10分钟），超过10分钟则重新获取，否则从本地存储中获取并返回
 * @returns {Promise} 返回Promise对象，包含经纬度信息 {longitude, latitude}
 */
export function getLocation() {
	return new Promise((resolve, reject) => {
		// 从本地存储获取经纬度信息
		const locationData = uni.getStorageSync('user_location')
		const currentTime = Date.now()

		// 检查本地存储的数据是否存在且未过期（10分钟 = 600000毫秒）
		if (locationData && locationData.timestamp && (currentTime - locationData.timestamp) < 600000) {
			resolve({
				longitude: locationData.longitude,
				latitude: locationData.latitude
			})
			return
		}

		// 本地数据不存在或已过期，重新获取位置信息
		uni.showLoading({
			title: '获取位置中...'
		})
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: (res) => {
				const locationInfo = {
					longitude: res.longitude,
					latitude: res.latitude,
					timestamp: currentTime
				}

				// 保存到本地存储
				uni.setStorageSync('user_location', locationInfo)
				uni.hideLoading()
				// 调用后台接口更新用户经纬度
				if (!$store.isUserShort()) {
					updateUserLocation(res.longitude, res.latitude)
				}
				resolve({
					longitude: res.longitude,
					latitude: res.latitude
				})
			},
			fail: (err) => {
				uni.hideLoading()
				console.error('获取位置失败:', err)
				// 如果获取失败但本地有缓存数据，则使用缓存数据
				if (locationData && locationData.longitude && locationData.latitude) {
					resolve({
						longitude: locationData.longitude,
						latitude: locationData.latitude
					})
				} else {
					reject(err)
				}
			}
		})
	})
}
