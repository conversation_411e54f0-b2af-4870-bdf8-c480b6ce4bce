package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailVo;
import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailVoToMsgGroupMapper;
import com.gzhuxn.personals.domain.message.bo.MsgGroupBoToMsgGroupMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupUserToAppMsgGroupUserVoMapper.class,MsgGroupUserToAppMsgGroupDetailUserVoMapper.class,MsgGroupBoToMsgGroupMapper.class,AppMsgGroupDetailVoToMsgGroupMapper.class,MsgGroupToMsgGroupVoMapper.class},
    imports = {}
)
public interface MsgGroupToAppMsgGroupDetailVoMapper extends BaseMapper<MsgGroup, AppMsgGroupDetailVo> {
}
