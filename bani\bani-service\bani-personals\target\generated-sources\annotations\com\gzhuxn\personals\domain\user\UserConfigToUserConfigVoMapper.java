package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.config.AppUserConfigBoToUserConfigMapper;
import com.gzhuxn.personals.domain.user.bo.UserConfigBoToUserConfigMapper;
import com.gzhuxn.personals.domain.user.vo.UserConfigVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserConfigBoToUserConfigMapper.class,AppUserConfigBoToUserConfigMapper.class,UserConfigToAppUserConfigVoMapper.class},
    imports = {}
)
public interface UserConfigToUserConfigVoMapper extends BaseMapper<UserConfig, UserConfigVo> {
}
