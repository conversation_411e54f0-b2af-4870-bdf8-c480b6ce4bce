package com.gzhuxn.personals.controller.app.recommend.vo.moment;

import com.gzhuxn.personals.domain.user.UserMoment;
import com.gzhuxn.personals.domain.user.UserMomentToAppRecommendMomentVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserMomentToAppRecommendMomentVoMapper.class},
    imports = {}
)
public interface AppRecommendMomentVoToUserMomentMapper extends BaseMapper<AppRecommendMomentVo, UserMoment> {
}
