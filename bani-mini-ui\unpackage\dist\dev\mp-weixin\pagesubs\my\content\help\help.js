"use strict";const e=require("../../../../common/vendor.js"),c=require("../../../../utils/common.js"),h=require("../../../../api/content/help.js");Array||e.resolveComponent("scroll-nav-page")();const v=()=>"../../../../components/scroll-nav-page/scroll-nav-page.js";Math||v();const r={__name:"help",setup(s){const l=e.ref(0),p=e.ref(0),o=e.ref(""),n=e.ref(""),_=a=>{l.value=a.scrollTop};e.onLoad(a=>{if(!a.key){c.toast("不存在的页面Key"),e.index.navigateBack({delta:1});return}o.value="",h.getHelpBy<PERSON><PERSON>(a.key).then(t=>{n.value=t.data.content,o.value=t.data.name}).catch(t=>{c.toast("获取内容失败"),u(a.key)})});const u=a=>{const t={userGuide:`
			<h2>欢迎使用伴你有约</h2>
			<p>这里是用户指南的默认内容...</p>
		`,faq:`
			<h2>常见问题</h2>
			<h3>Q: 如何注册账号？</h3>
			<p>A: 您可以通过手机号码快速注册...</p>
		`,contactUs:`
			<h2>联系我们</h2>
			<p>客服电话：400-123-4567</p>
			<p>客服邮箱：<EMAIL></p>
		`,feedback:`
			<h2>意见反馈</h2>
			<p>您的意见对我们很重要...</p>
		`,safetyTips:`
			<h2>安全提示</h2>
			<p>为了您的账号安全，请注意以下事项...</p>
		`,featureIntro:`
			<h2>功能介绍</h2>
			<p>伴你有约为您提供以下功能...</p>
		`};n.value=t[a]||"<p>内容加载中...</p>"};return e.onPageScroll(_),(a,t)=>({a:n.value,b:p.value+"px",c:e.p({title:o.value,"show-back":!0})})}},i=e._export_sfc(r,[["__scopeId","data-v-ebd2236c"]]);r.__runtimeHooks=1;wx.createPage(i);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/pagesubs/my/content/help/help.js.map
