package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupUserToAppMsgGroupUserVoMapperImpl implements MsgGroupUserToAppMsgGroupUserVoMapper {

    @Override
    public AppMsgGroupUserVo convert(MsgGroupUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppMsgGroupUserVo appMsgGroupUserVo = new AppMsgGroupUserVo();

        appMsgGroupUserVo.setId( arg0.getId() );
        appMsgGroupUserVo.setGroupId( arg0.getGroupId() );
        appMsgGroupUserVo.setUserId( arg0.getUserId() );
        appMsgGroupUserVo.setName( arg0.getName() );
        appMsgGroupUserVo.setType( arg0.getType() );
        appMsgGroupUserVo.setDisturb( arg0.getDisturb() );
        appMsgGroupUserVo.setStatus( arg0.getStatus() );

        return appMsgGroupUserVo;
    }

    @Override
    public AppMsgGroupUserVo convert(MsgGroupUser arg0, AppMsgGroupUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setDisturb( arg0.getDisturb() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
