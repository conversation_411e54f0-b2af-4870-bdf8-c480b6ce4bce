package com.gzhuxn.personals.controller.app.recommend.bo.activity;

import com.gzhuxn.personals.domain.activity.Activity;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppRecommendActivityQueryBoToActivityMapper extends BaseMapper<AppRecommendActivityQueryBo, Activity> {
}
