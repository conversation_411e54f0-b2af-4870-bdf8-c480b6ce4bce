package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserGreetingVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserGreetingToUserGreetingVoMapperImpl implements UserGreetingToUserGreetingVoMapper {

    @Override
    public UserGreetingVo convert(UserGreeting arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserGreetingVo userGreetingVo = new UserGreetingVo();

        userGreetingVo.setId( arg0.getId() );
        userGreetingVo.setUserId( arg0.getUserId() );
        userGreetingVo.setOppositeUserId( arg0.getOppositeUserId() );
        userGreetingVo.setContent( arg0.getContent() );
        userGreetingVo.setStatus( arg0.getStatus() );
        userGreetingVo.setCreateTime( arg0.getCreateTime() );

        return userGreetingVo;
    }

    @Override
    public UserGreetingVo convert(UserGreeting arg0, UserGreetingVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setContent( arg0.getContent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
