<template>
	<scroll-nav-page title="点亮礼物" :show-back="true">
		<template #content>
			<view class="page-content">
				<!-- 背景装饰 -->
				<view class="background-decoration">
					<view class="star star-1"></view>
					<view class="star star-2"></view>
					<view class="star star-3"></view>
					<view class="star star-4"></view>
					<view class="star star-5"></view>
				</view>
				
				<!-- 主要内容 -->
				<view class="main-container">
					<!-- 用户信息头部 -->
					<view class="user-header" v-if="userInfo">
						<view class="user-avatar-container">
							<view class="avatar-ring"></view>
							<image :src="userInfo.avatar" mode="aspectFill" class="user-avatar"></image>
							<view class="avatar-glow"></view>
						</view>
						<view class="user-info">
							<text class="user-nickname">{{ userInfo.nickname }}</text>
							<view class="user-gender" v-if="userInfo.sex !== undefined">
								<text class="iconfont gender-icon"
									:class="userInfo.sex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'"
									:style="{ color: userInfo.sex === '0' ? '#4A90E2' : '#E91E63' }"></text>
							</view>
						</view>
						<view class="gift-count-info">
							<view class="count-badge">
								<text class="count-text">已点亮 {{ totalGiftCount }}/{{ giftList.length }}</text>
								<view class="progress-bar">
									<view class="progress-fill" :style="{ width: (totalGiftCount / giftList.length * 100) + '%' }"></view>
								</view>
							</view>
						</view>
					</view>

					<!-- 礼物网格 -->
					<view class="gift-grid" v-if="giftList.length > 0">
						<view class="gift-item" v-for="(gift, index) in giftList" :key="gift.id"
							@click="handleGiftClick(gift)"
							:class="{ 'gift-received': gift.isReceived }">
							<view class="gift-icon-container" :class="{ 'lit': gift.isReceived }">
								<view class="gift-border"></view>
								<image :src="gift.icon" mode="aspectFill" class="gift-icon"
									:class="{ 'grayscale': !gift.isReceived }"></image>
								<view class="gift-glow" v-if="gift.isReceived"></view>
								<view class="gift-sparkle" v-if="gift.isReceived">
									<view class="sparkle sparkle-1"></view>
									<view class="sparkle sparkle-2"></view>
									<view class="sparkle sparkle-3"></view>
								</view>
								<view class="lock-icon" v-if="!gift.isReceived">
									<uni-icons type="locked" size="20" color="rgba(255,255,255,0.5)"></uni-icons>
								</view>
							</view>
							<text class="gift-name">{{ gift.name }}</text>
							<view class="gift-count-badge" v-if="gift.isReceived && gift.receivedCount > 0">
								<text class="gift-count">{{ gift.receivedCount }}</text>
							</view>
						</view>
					</view>

					<!-- 加载状态 -->
					<view v-if="loading" class="loading-container">
						<uni-load-more status="loading"></uni-load-more>
					</view>

					<!-- 空状态 -->
					<view v-else-if="giftList.length === 0" class="empty-container">
						<view class="empty-content">
							<uni-icons type="gift" size="80" color="#ccc"></uni-icons>
							<text class="empty-text">暂无礼物数据</text>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { getUserBasicInfo } from '@/api/user/user'
import { getLitGiftList } from '@/api/personals/gift'
import globalConfig from '@/config'

// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const loading = ref(true)

// 用户信息
const userInfo = ref(null)
const userId = ref(null)

// 礼物数据
const giftList = ref([])
const totalGiftCount = ref(0)

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 页面加载
onLoad((options) => {
	if (options.userId) {
		userId.value = options.userId
		loadData()
	} else {
		loading.value = false
		uni.showToast({
			title: '参数错误',
			icon: 'none'
		})
	}
})

// 加载数据
const loadData = async () => {
	try {
		loading.value = true

		// 并行加载用户信息和礼物列表
		const [userResponse, giftResponse] = await Promise.all([
			getUserBasicInfo(userId.value),
			getLitGiftList(userId.value)
		])

		if (userResponse.code === 200) {
			userInfo.value = userResponse.data
		}

		if (giftResponse.code === 200) {
			giftList.value = giftResponse.data.giftList || []
			totalGiftCount.value = giftResponse.data.totalGiftCount || 0
		}
	} catch (error) {
		console.error('加载数据失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

// 处理礼物点击
const handleGiftClick = (gift) => {
	if (gift.isReceived) {
		// 显示礼物详情或其他操作
		uni.showToast({
			title: `${gift.name} x${gift.receivedCount}`,
			icon: 'none'
		})
	} else {
		// 显示未点亮提示
		uni.showToast({
			title: '该礼物尚未点亮',
			icon: 'none'
		})
	}
}
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-content {
	position: relative;
	min-height: 100vh;
	background: #ffffff;
	overflow: hidden;
}

.background-decoration {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		z-index: 0;

		.star {
			position: absolute;
			width: 4rpx;
			height: 4rpx;
			background: rgba(200, 200, 200, 0.6);
			border-radius: 50%;
			animation: twinkle 3s infinite;

		&.star-1 {
			top: 20%;
			left: 10%;
			animation-delay: 0s;
		}

		&.star-2 {
			top: 40%;
			right: 15%;
			animation-delay: 1s;
		}

		&.star-3 {
			top: 60%;
			left: 20%;
			animation-delay: 2s;
		}

		&.star-4 {
			top: 80%;
			right: 25%;
			animation-delay: 0.5s;
		}

		&.star-5 {
			top: 30%;
			left: 50%;
			animation-delay: 1.5s;
		}
	}
}

.main-container {
	position: relative;
	z-index: 1;
	min-height: 100vh;
	padding: 20rpx;
}

.user-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 20rpx 40rpx;
		margin-bottom: 40rpx;
		background: rgba(248, 248, 248, 0.8);
		border-radius: 30rpx;
		backdrop-filter: blur(20rpx);
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(230, 230, 230, 0.5);

	.user-avatar-container {
		position: relative;
		margin-bottom: 24rpx;

		.avatar-ring {
			position: absolute;
			top: -8rpx;
			left: -8rpx;
			width: 136rpx;
			height: 136rpx;
			border: 3rpx solid transparent;
			border-radius: 50%;
			background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4) border-box;
			mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
			mask-composite: exclude;
			animation: rotate 4s linear infinite;
		}

		.user-avatar {
			position: relative;
			z-index: 2;
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
			border: 4rpx solid rgba(255, 255, 255, 0.8);
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
		}

		.avatar-glow {
			position: absolute;
			top: -20rpx;
			left: -20rpx;
			width: 160rpx;
			height: 160rpx;
			background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
			border-radius: 50%;
			animation: pulse 2s ease-in-out infinite;
		}
	}

	.user-info {
		display: flex;
		align-items: center;
		gap: 12rpx;
		margin-bottom: 16rpx;

		.user-nickname {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
		}

		.user-gender {
			display: flex;
			align-items: center;

			.gender-icon {
				font-size: 28rpx;
			}
		}
	}

	.gift-count-info {
		.count-badge {
				background: rgba(240, 240, 240, 0.8);
				border-radius: 20rpx;
				padding: 12rpx 24rpx;
				backdrop-filter: blur(10rpx);
				border: 1rpx solid rgba(220, 220, 220, 0.5);

			.count-text {
				font-size: 28rpx;
				color: #666666;
				font-weight: 600;
				margin-bottom: 8rpx;
				display: block;
			}

			.progress-bar {
					width: 200rpx;
					height: 6rpx;
					background: rgba(200, 200, 200, 0.5);
					border-radius: 3rpx;
					overflow: hidden;

				.progress-fill {
					height: 100%;
					background: linear-gradient(90deg, #FFD700, #FFA500);
					border-radius: 3rpx;
					transition: width 0.8s ease;
					box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
				}
			}
		}
	}
}

.gift-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 24rpx;
	padding: 0 20rpx;

	.gift-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		border-radius: 20rpx;
		background: rgba(250, 250, 250, 0.9);
			backdrop-filter: blur(15rpx);
			border: 1rpx solid rgba(230, 230, 230, 0.6);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(240, 240, 240, 0.3), transparent);
			transition: left 0.6s ease;
		}

		&:active {
			transform: scale(0.95);
		}

		&:hover::before {
			left: 100%;
		}

		&.gift-received {
				background: rgba(255, 248, 220, 0.9);
				border-color: rgba(255, 215, 0, 0.4);
				box-shadow: 0 6rpx 30rpx rgba(255, 215, 0, 0.2);
			}

		.gift-icon-container {
			position: relative;
			width: 88rpx;
			height: 88rpx;
			margin-bottom: 16rpx;
			border-radius: 16rpx;
			overflow: visible;

			.gift-border {
				position: absolute;
				top: -2rpx;
				left: -2rpx;
				width: 92rpx;
				height: 92rpx;
				border: 2rpx solid rgba(220, 220, 220, 0.6);
				border-radius: 18rpx;
				transition: all 0.3s ease;
			}

			.gift-icon {
				width: 100%;
				height: 100%;
				border-radius: 16rpx;
				transition: all 0.4s ease;

				&.grayscale {
					filter: grayscale(100%) brightness(0.5) contrast(0.8);
				}
			}

			.gift-glow {
				position: absolute;
				top: -15rpx;
				left: -15rpx;
				right: -15rpx;
				bottom: -15rpx;
				background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 165, 0, 0.2) 50%, transparent 80%);
				border-radius: 25rpx;
				animation: glow 2.5s ease-in-out infinite alternate;
			}

			.gift-sparkle {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				pointer-events: none;

				.sparkle {
					position: absolute;
					width: 6rpx;
					height: 6rpx;
					background: #FFD700;
					border-radius: 50%;
					animation: sparkle 2s infinite;

					&.sparkle-1 {
						top: 10rpx;
						left: 10rpx;
						animation-delay: 0s;
					}

					&.sparkle-2 {
						top: 20rpx;
						right: 15rpx;
						animation-delay: 0.7s;
					}

					&.sparkle-3 {
						bottom: 15rpx;
						left: 20rpx;
						animation-delay: 1.4s;
					}
				}
			}

			.lock-icon {
				position: absolute;
				bottom: -5rpx;
				right: -5rpx;
				width: 32rpx;
				height: 32rpx;
				background: rgba(0, 0, 0, 0.6);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				border: 2rpx solid rgba(255, 255, 255, 0.3);
			}

			&.lit {
				.gift-border {
					border-color: rgba(255, 215, 0, 0.8);
					box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.3);
				}

				.gift-icon {
					filter: none;
					box-shadow: 0 4rpx 25rpx rgba(255, 215, 0, 0.4);
					transform: scale(1.05);
				}
			}
		}

		.gift-name {
			font-size: 24rpx;
			color: #333333;
			text-align: center;
			margin-bottom: 8rpx;
			font-weight: 500;
			line-height: 1.2;
		}

		.gift-count-badge {
			background: linear-gradient(45deg, #FFD700, #FFA500);
			border-radius: 12rpx;
			padding: 4rpx 12rpx;
			min-width: 32rpx;
			height: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);

			.gift-count {
				font-size: 20rpx;
				color: #333;
				font-weight: bold;
				line-height: 1;
			}
		}
	}
}

.loading-container,
.empty-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.empty-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20rpx;

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
}

@keyframes glow {
	0% {
		opacity: 0.4;
		transform: scale(1);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.05);
	}
	100% {
		opacity: 0.4;
		transform: scale(1);
	}
}

@keyframes sparkle {
	0%, 100% {
		opacity: 0;
		transform: scale(0);
	}
	50% {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes twinkle {
	0%, 100% {
		opacity: 0.3;
		transform: scale(1);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.3;
		transform: scale(1);
	}
	50% {
		opacity: 0.6;
		transform: scale(1.1);
	}
}
</style>
