package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.comment.AppUserCommentItemVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserCommentToAppUserCommentItemVoMapperImpl implements UserCommentToAppUserCommentItemVoMapper {

    @Override
    public AppUserCommentItemVo convert(UserComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserCommentItemVo appUserCommentItemVo = new AppUserCommentItemVo();

        appUserCommentItemVo.setId( arg0.getId() );
        appUserCommentItemVo.setRootId( arg0.getRootId() );
        appUserCommentItemVo.setContent( arg0.getContent() );
        appUserCommentItemVo.setImages( arg0.getImages() );
        appUserCommentItemVo.setCreateTime( arg0.getCreateTime() );

        return appUserCommentItemVo;
    }

    @Override
    public AppUserCommentItemVo convert(UserComment arg0, AppUserCommentItemVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setRootId( arg0.getRootId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
