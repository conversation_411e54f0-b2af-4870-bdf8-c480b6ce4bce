{"version": 3, "file": "baseEdit.js", "sources": ["pagesubs/my/profile/baseEdit.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlccHJvZmlsZVxiYXNlRWRpdC52dWU"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page :show-back=\"true\" title=\"基本信息\">\r\n\t\t<template #content>\r\n\t\t\t<!-- 主要内容 -->\r\n\t\t\t<view class=\"page-content\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t\t<!-- 基本信息表单 -->\r\n\t\t\t\t<!-- 昵称 -->\r\n\t\t\t\t<view class=\"form-item\" @click=\"editNickName()\">\r\n\t\t\t\t\t<text class=\"label\">昵称<text class=\"required\">*</text></text>\r\n\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.nickName }\">\r\n\t\t\t\t\t\t{{ formData.nickName || '请输入昵称' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 性别 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"genderOptions\" range-key=\"name\" :value=\"getGenderIndex()\"\r\n\t\t\t\t\t@change=\"handleGenderChange\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">性别<text class=\"required\">*</text></text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.gender }\">\r\n\t\t\t\t\t\t\t{{ getGenderText(formData.gender) || '请选择性别' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 出生日期 -->\r\n\t\t\t\t<picker mode=\"date\" :value=\"getBirthdayValue()\" @change=\"handleBirthdayChange\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">出生日期<text class=\"required\">*</text></text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.birthday }\">\r\n\t\t\t\t\t\t\t{{ formData.birthday || '请选择出生日期' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 身高 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"heightOptions\" range-key=\"name\" :value=\"getHeightIndex()\"\r\n\t\t\t\t\t@change=\"handleHeightChange\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">身高<text class=\"required\">*</text></text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.height }\">\r\n\t\t\t\t\t\t\t{{ formData.height ? formData.height + 'cm' : '请选择身高' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 体重 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"weightOptions\" range-key=\"name\" :value=\"getWeightIndex()\"\r\n\t\t\t\t\t@change=\"handleWeightChange\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">体重<text class=\"required\">*</text></text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.weight }\">\r\n\t\t\t\t\t\t\t{{ formData.weight ? formData.weight + 'kg' : '请选择体重' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 学历 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"getDictOptions('user_edu')\" range-key=\"name\"\r\n\t\t\t\t\t:value=\"getDictIndex('user_edu', formData.edu)\"\r\n\t\t\t\t\t@change=\"handleDictChange('user_edu', 'edu', $event)\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">学历<text class=\"required\">*</text></text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.edu }\">\r\n\t\t\t\t\t\t\t{{ dictTexts.eduText || '请选择学历' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 职业 -->\r\n\t\t\t\t<view class=\"form-item\" @click=\"jobSelectRef.open()\">\r\n\t\t\t\t\t<text class=\"label\">职业<text class=\"required\">*</text></text>\r\n\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.job }\">\r\n\t\t\t\t\t\t{{ dictTexts.jobText || '请选择职业' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 情感状况 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"getDictOptions('user_affective_status')\" range-key=\"name\"\r\n\t\t\t\t\t:value=\"getDictIndex('user_affective_status', formData.affectiveStatus)\"\r\n\t\t\t\t\t@change=\"handleDictChange('user_affective_status', 'affectiveStatus', $event)\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">情感状况</text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !dictTexts.affectiveStatusText }\">\r\n\t\t\t\t\t\t\t{{ dictTexts.affectiveStatusText || '请选择情感状况' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 收入 -->\r\n\t\t\t\t<picker mode=\"selector\" :range=\"getDictOptions('user_revenue')\" range-key=\"name\"\r\n\t\t\t\t\t:value=\"getDictIndex('user_revenue', formData.revenue)\"\r\n\t\t\t\t\t@change=\"handleDictChange('user_revenue', 'revenue', $event)\">\r\n\t\t\t\t\t<view class=\"form-item\">\r\n\t\t\t\t\t\t<text class=\"label\">收入</text>\r\n\t\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !dictTexts.revenueText }\">\r\n\t\t\t\t\t\t\t{{ dictTexts.revenueText || '请选择收入' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\r\n\t\t\t\t<!-- 户籍地址 -->\r\n\t\t\t\t<view class=\"form-item\" @click=\"editAddress('registered')\">\r\n\t\t\t\t\t<text class=\"label\">户籍地址<text class=\"required\">*</text></text>\r\n\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.addr }\">\r\n\t\t\t\t\t\t{{ formData.addr || '请选择户籍地址' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 现居地址 -->\r\n\t\t\t\t<view class=\"form-item\" @click=\"editAddress('current')\">\r\n\t\t\t\t\t<text class=\"label\">现居地址<text class=\"required\">*</text></text>\r\n\t\t\t\t\t<text class=\"value\" :class=\"{ 'placeholder': !formData.addrNew }\">\r\n\t\t\t\t\t\t{{ formData.addrNew || '请选择现居地址' }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"16\" color=\"#ccc\"></uni-icons>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"warning-tips\">\r\n\t\t\t\t\t<text>为了保证真实、真诚的交友，请如实填写个人信息，虚假敷衍的内容不会审核通过，请知悉</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"submit\">\r\n\t\t\t\t\t<button @click=\"submit\">\r\n\t\t\t\t\t\t<text v-if=\"baseType === 0\">下一步</text>\r\n\t\t\t\t\t\t<text v-else>保存</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n\r\n\t<!-- 昵称编辑弹窗 -->\r\n\t<uni-popup ref=\"nicknamePopup\" type=\"dialog\">\r\n\t\t<uni-popup-dialog mode=\"input\" title=\"编辑昵称\" placeholder=\"请输入昵称\" :value=\"nicknameInput\"\r\n\t\t\t@confirm=\"handleNicknameConfirm\" @close=\"handleNicknameClose\">\r\n\t\t\t<template #default>\r\n\t\t\t\t<input v-model=\"nicknameInput\" type=\"nickname\" placeholder=\"请输入昵称\" class=\"nickname-input\" />\r\n\t\t\t</template>\r\n\t\t</uni-popup-dialog>\r\n\t</uni-popup>\r\n\r\n\t<!-- 地址选择组件 -->\r\n\t<DistrictSelect ref=\"districtSelectRef\" @confirm=\"handleDistrictConfirm\" />\r\n\r\n\t<!-- 职业选择组件 -->\r\n\t<TagSelect ref=\"jobSelectRef\" title=\"请选择您的职业\" :localdata=\"jobLocalData\" @confirm=\"handleJobSelect\" />\r\n</template>\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { onLoad, onPageScroll } from \"@dcloudio/uni-app\"\r\nimport globalConfig from '@/config'\r\nimport $store from '@/store/index'\r\nimport {\r\n\tgetUserBase,\r\n\tupdateUserBase\r\n} from '@/api/my/my'\r\nimport { heightDicts, weightDicts } from '@/utils/common.js'\r\nimport DistrictSelect from '@/components/district-select/district-select.vue'\r\nimport TagSelect from '@/components/tag-select/tag-select.vue'\r\n\r\n// 注册组件\r\nconst components = {\r\n\tDistrictSelect,\r\n\tTagSelect\r\n}\r\n\r\n// 页面状态\r\nconst baseType = ref(0)\r\nconst pageScrollTop = ref(0)\r\nconst navBarHeight = ref(0)\r\nconst loading = ref(false)\r\n\r\n// 表单数据\r\nconst formData = ref({\r\n\tnickName: '',\r\n\tgender: '',\r\n\tbirthday: '',\r\n\theight: '',\r\n\tweight: '',\r\n\tedu: '',\r\n\tjob: '',\r\n\taffectiveStatus: '',\r\n\trevenue: '',\r\n\taddrProvinceCode: '',\r\n\taddrCityCode: '',\r\n\taddrDistrictCode: '',\r\n\taddrStreetCode: '',\r\n\taddr: '',\r\n\taddrNewProvinceCode: '',\r\n\taddrNewCityCode: '',\r\n\taddrNewDistrictCode: '',\r\n\taddrNewStreetCode: '',\r\n\taddrNew: ''\r\n})\r\n\r\n// 昵称弹窗相关\r\nconst nicknamePopup = ref(null)\r\nconst nicknameInput = ref('')\r\n\r\n// 地址选择相关\r\nconst districtSelectRef = ref(null)\r\nconst currentAddressField = ref('')\r\n\r\n// 职业选择相关\r\nconst jobSelectRef = ref(null)\r\n\r\n// 身高体重选项\r\nconst heightOptions = ref([])\r\nconst weightOptions = ref([])\r\n\r\n// 性别选项\r\nconst genderOptions = ref([\r\n\t{ value: '0', name: '男' },\r\n\t{ value: '1', name: '女' }\r\n])\r\n\r\n// 获取字典文本的辅助函数\r\nconst getDictText = (dictType, value) => {\r\n\tif (!value || !dictType) return ''\r\n\ttry {\r\n\t\tif (!$store || !$store.dict || typeof $store.dict.getNameById !== 'function') {\r\n\t\t\tconsole.warn('Store 或字典方法不可用')\r\n\t\t\treturn ''\r\n\t\t}\r\n\t\tconst result = $store.dict.getNameById(dictType, value)\r\n\t\treturn result || ''\r\n\t} catch (error) {\r\n\t\tconsole.error('获取字典文本失败:', error)\r\n\t\treturn ''\r\n\t}\r\n}\r\n\r\n// 计算属性 - 字典文本回显\r\nconst dictTexts = computed(() => {\r\n\tif (!formData.value) {\r\n\t\treturn {\r\n\t\t\taffectiveStatusText: '',\r\n\t\t\teduText: '',\r\n\t\t\tjobText: '',\r\n\t\t\trevenueText: ''\r\n\t\t}\r\n\t}\r\n\treturn {\r\n\t\t// 情感状况\r\n\t\taffectiveStatusText: getDictText('user_affective_status', formData.value.affectiveStatus),\r\n\t\t// 学历\r\n\t\teduText: getDictText('user_edu', formData.value.edu),\r\n\t\t// 职业\r\n\t\tjobText: getDictText('user_job', formData.value.job),\r\n\t\t// 收入\r\n\t\trevenueText: getDictText('user_revenue', formData.value.revenue)\r\n\t}\r\n})\r\n\r\n// 计算属性 - 职业选择数据\r\nconst jobLocalData = computed(() => {\r\n\ttry {\r\n\t\tconst dictData = $store.dict.get('user_job')\r\n\t\tif (!dictData || !Array.isArray(dictData)) {\r\n\t\t\treturn []\r\n\t\t}\r\n\t\t// 转换为 tag-select 需要的格式\r\n\t\treturn dictData.map(item => ({\r\n\t\t\tid: item.id,\r\n\t\t\tname: item.name\r\n\t\t}))\r\n\t} catch (error) {\r\n\t\tconsole.error('获取职业数据失败:', error)\r\n\t\treturn []\r\n\t}\r\n})\r\n\r\n// 获取字典选项数据\r\nconst getDictOptions = (dictType) => {\r\n\ttry {\r\n\t\tconst dictData = $store.dict.get(dictType)\r\n\t\tif (!dictData || !Array.isArray(dictData)) {\r\n\t\t\tconsole.warn('字典数据不存在或格式错误:', dictType)\r\n\t\t\treturn []\r\n\t\t}\r\n\t\treturn dictData\r\n\t} catch (error) {\r\n\t\tconsole.error('获取字典选项失败:', error)\r\n\t\treturn []\r\n\t}\r\n}\r\n\r\n// 页面滚动监听\r\nonPageScroll((e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n})\r\n\r\n// 页面加载\r\nonLoad((options) => {\r\n\tbaseType.value = parseInt(options.baseType || 0)\r\n\t// 加载基础信息\r\n\tloadBaseInfo()\r\n})\r\n\r\n// 加载基础信息\r\nconst loadBaseInfo = async () => {\r\n\ttry {\r\n\t\t// 初始化身高体重选项\r\n\t\theightOptions.value = heightDicts()\r\n\t\tweightOptions.value = weightDicts()\r\n\r\n\t\tconst res = await getUserBase()\r\n\t\tif (res.code === 200 && res.data) {\r\n\t\t\tformData.value = {\r\n\t\t\t\tnickName: res.data.nickName || '',\r\n\t\t\t\tgender: res.data.sex || '',\r\n\t\t\t\tbirthday: res.data.birthday || '',\r\n\t\t\t\theight: res.data.height || '',\r\n\t\t\t\tweight: res.data.weight || '',\r\n\t\t\t\tedu: res.data.edu || '',\r\n\t\t\t\tjob: res.data.job || '',\r\n\t\t\t\taffectiveStatus: res.data.affectiveStatus || '',\r\n\t\t\t\trevenue: res.data.revenue || '',\r\n\t\t\t\taddrProvinceCode: res.data.addrProvinceCode || '',\r\n\t\t\t\taddrCityCode: res.data.addrCityCode || '',\r\n\t\t\t\taddrDistrictCode: res.data.addrDistrictCode || '',\r\n\t\t\t\taddrStreetCode: res.data.addrStreetCode || '',\r\n\t\t\t\taddr: res.data.addr || '',\r\n\t\t\t\taddrNewProvinceCode: res.data.addrNewProvinceCode || '',\r\n\t\t\t\taddrNewCityCode: res.data.addrNewCityCode || '',\r\n\t\t\t\taddrNewDistrictCode: res.data.addrNewDistrictCode || '',\r\n\t\t\t\taddrNewStreetCode: res.data.addrNewStreetCode || '',\r\n\t\t\t\taddrNew: res.data.addrNew || ''\r\n\t\t\t}\r\n\t\t}\r\n\t\tconsole.log('基础信息:', res)\r\n\t} catch (error) {\r\n\t\tconsole.error('加载基础信息失败:', error)\r\n\t}\r\n}\r\n\r\n// 获取身高选中索引\r\nconst getHeightIndex = () => {\r\n\tif (!heightOptions.value.length) return 0\r\n\t// 如果没有身高值，默认选择168cm\r\n\tconst targetHeight = formData.value.height || 168\r\n\tconst index = heightOptions.value.findIndex(item => item.id === targetHeight)\r\n\treturn index >= 0 ? index : 0\r\n}\r\n\r\n// 获取体重选中索引\r\nconst getWeightIndex = () => {\r\n\tif (!weightOptions.value.length) return 0\r\n\t// 如果没有体重值，默认选择50kg\r\n\tconst targetWeight = formData.value.weight || 50\r\n\tconst index = weightOptions.value.findIndex(item => item.id === targetWeight)\r\n\treturn index >= 0 ? index : 0\r\n}\r\n\r\n// 获取十八年前的今天日期\r\nconst getEighteenYearsAgo = () => {\r\n\tconst today = new Date()\r\n\tconst eighteenYearsAgo = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate())\r\n\treturn eighteenYearsAgo.toISOString().split('T')[0]\r\n}\r\n\r\n// 获取生日显示值\r\nconst getBirthdayValue = () => {\r\n\treturn formData.value.birthday || getEighteenYearsAgo()\r\n}\r\n\r\n// 获取性别文本\r\nconst getGenderText = (sex) => {\r\n\tif (sex === '0') return '男'\r\n\tif (sex === '1') return '女'\r\n\treturn ''\r\n}\r\n\r\n// 获取性别选中索引\r\nconst getGenderIndex = () => {\r\n\tif (!formData.value.gender) return 0\r\n\tconst index = genderOptions.value.findIndex(item => item.value === formData.value.gender)\r\n\treturn index >= 0 ? index : 0\r\n}\r\n\r\n// 处理性别变化\r\nconst handleGenderChange = (e) => {\r\n\tconst selectedGender = genderOptions.value[e.detail.value]\r\n\tformData.value.gender = selectedGender.value\r\n}\r\n\r\n// 编辑昵称\r\nconst editNickName = () => {\r\n\tnicknameInput.value = formData.value.nickName\r\n\tnicknamePopup.value.open()\r\n}\r\n\r\n// 昵称确认\r\nconst handleNicknameConfirm = (value) => {\r\n\t// 使用输入框的值而不是参数值\r\n\tconst inputValue = nicknameInput.value || value\r\n\tif (inputValue && inputValue.trim()) {\r\n\t\tformData.value.nickName = inputValue.trim()\r\n\t}\r\n\tnicknamePopup.value.close()\r\n}\r\n\r\n// 昵称取消\r\nconst handleNicknameClose = () => {\r\n\tnicknamePopup.value.close()\r\n}\r\n\r\n// 处理生日变化\r\nconst handleBirthdayChange = (e) => {\r\n\tformData.value.birthday = e.detail.value\r\n}\r\n\r\n// 处理身高变化\r\nconst handleHeightChange = (e) => {\r\n\tconst selectedHeight = heightOptions.value[e.detail.value]\r\n\tformData.value.height = selectedHeight.id\r\n}\r\n\r\n// 处理体重变化\r\nconst handleWeightChange = (e) => {\r\n\tconst selectedWeight = weightOptions.value[e.detail.value]\r\n\tformData.value.weight = selectedWeight.id\r\n}\r\n\r\n// 编辑地址\r\nconst editAddress = (type) => {\r\n\tcurrentAddressField.value = type\r\n\tdistrictSelectRef.value.open()\r\n}\r\n\r\n// 处理地址选择确认\r\nconst handleDistrictConfirm = (result) => {\r\n\tif (currentAddressField.value === 'registered') {\r\n\t\t// 户籍地址\r\n\t\tformData.value.addrProvinceCode = result.codes.provinceCode\r\n\t\tformData.value.addrCityCode = result.codes.cityCode\r\n\t\tformData.value.addrDistrictCode = result.codes.districtCode\r\n\t\tformData.value.addrStreetCode = result.codes.streetCode\r\n\t\tformData.value.addr = result.fullName\r\n\t} else if (currentAddressField.value === 'current') {\r\n\t\t// 现居地址\r\n\t\tformData.value.addrNewProvinceCode = result.codes.provinceCode\r\n\t\tformData.value.addrNewCityCode = result.codes.cityCode\r\n\t\tformData.value.addrNewDistrictCode = result.codes.districtCode\r\n\t\tformData.value.addrNewStreetCode = result.codes.streetCode\r\n\t\tformData.value.addrNew = result.fullName\r\n\t}\r\n}\r\n\r\n// 处理字典选择变化\r\nconst handleDictChange = (dictType, field, e) => {\r\n\tconst options = getDictOptions(dictType)\r\n\tif (options && options[e.detail.value]) {\r\n\t\tformData.value[field] = options[e.detail.value].id\r\n\t}\r\n}\r\n\r\n// 获取字典选中索引\r\nconst getDictIndex = (dictType, value) => {\r\n\tif (!value) return 0\r\n\tconst options = getDictOptions(dictType)\r\n\tconst index = options.findIndex(item => item.id === value)\r\n\treturn index >= 0 ? index : 0\r\n}\r\n\r\n// 处理职业选择\r\nconst handleJobSelect = (event) => {\r\n\tconsole.log('职业选择:', event)\r\n\tif (event && event.value) {\r\n\t\t// 更新职业值\r\n\t\tformData.value.job = event.value\r\n\t}\r\n}\r\n\r\n// 提交数据\r\nconst submit = () => {\r\n\t// 基本验证\r\n\tif (!formData.value.nickName) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请输入昵称',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\tif (!formData.value.gender) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择性别',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\tloading.value = true\r\n\t// 准备提交数据，将gender字段映射为sex\r\n\tconst submitData = {\r\n\t\t...formData.value,\r\n\t\tsex: formData.value.gender\r\n\t}\r\n\tdelete submitData.gender\r\n\tupdateUserBase(submitData).then(res => {\r\n\t\tif (baseType.value === 0) {\r\n\t\t\t// 跳转到下一步\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pagesubs/my/profile/avatar/avatarEdit'\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\t// 返回上一页\r\n\t\t\tuni.navigateBack()\r\n\t\t}\r\n\t}).finally(() => {\r\n\t\tloading.value = false\r\n\t})\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/uni.scss';\r\n\r\n.submit {\r\n\tmargin: 25rpx 0 20rpx;\r\n\r\n\tbutton {\r\n\t\twidth: 100%;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground: $primary-gradient;\r\n\t\tfont-size: 34rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tbox-shadow: 0 8rpx 24rpx $primary-shadow;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\r\n\t\t&::before {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: -100%;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n\t\t\ttransition: left 0.6s ease;\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: translateY(2rpx);\r\n\t\t\tbox-shadow: 0 4rpx 16rpx $primary-shadow;\r\n\r\n\t\t\t&::before {\r\n\t\t\t\tleft: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&[disabled] {\r\n\t\t\tbackground: linear-gradient(to right, #ccc, #ddd);\r\n\t\t\tbox-shadow: none;\r\n\t\t\ttransform: none;\r\n\t\t\topacity: 0.6;\r\n\r\n\t\t\t&::before {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.form-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 32rpx 24rpx;\r\n\tborder-bottom: 1rpx solid #f5f5f5;\r\n\tposition: relative;\r\n\ttransition: background-color 0.2s;\r\n}\r\n\r\n.form-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.form-item:active {\r\n\tbackground-color: #f8f8f8;\r\n}\r\n\r\n.form-item .label {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\twidth: 140rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.form-item .label .required {\r\n\tcolor: #ff4757;\r\n\tfont-size: 32rpx;\r\n\tmargin-left: 4rpx;\r\n}\r\n\r\n.form-item .value {\r\n\tflex: 1;\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\ttext-align: right;\r\n\tmargin-right: 16rpx;\r\n}\r\n\r\n.form-item .value.placeholder {\r\n\tcolor: #999;\r\n\ttext-align: right;\r\n}\r\n\r\n.nickname-input {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tpadding: 0rpx 20rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 12rpx;\r\n\tfont-size: 32rpx;\r\n\tbackground: #fff;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.nickname-input:focus {\r\n\tborder-color: var(--primary-color, #007aff);\r\n\toutline: none;\r\n\tbox-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);\r\n}\r\n\r\n.warning-tips {\r\n\tmargin: 20rpx 0 15rpx;\r\n\tpadding: 16rpx 20rpx;\r\n\tbackground: linear-gradient(135deg, #fff9e6, #fffbf0);\r\n\tborder-radius: 12rpx;\r\n\ttext-align: center;\r\n\tborder: 2rpx solid #ffe7ba;\r\n\tposition: relative;\r\n\tbox-shadow: 0 2rpx 12rpx rgba(212, 136, 6, 0.08);\r\n\r\n\t&::before {\r\n\t\tposition: absolute;\r\n\t\ttop: -8rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tbackground: #fff9e6;\r\n\t\tpadding: 4rpx 12rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tborder: 2rpx solid #ffe7ba;\r\n\t\tfont-size: 20rpx;\r\n\t}\r\n\r\n\ttext {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #d48806;\r\n\t\tline-height: 1.4;\r\n\t\tfont-weight: 500;\r\n\t\tletter-spacing: 0.3rpx;\r\n\t\tdisplay: block;\r\n\t\tmargin-top: 2rpx;\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/profile/baseEdit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["DistrictSelect", "TagSelect", "baseType", "ref", "pageScrollTop", "navBarHeight", "loading", "formData", "nicknamePopup", "nicknameInput", "districtSelectRef", "currentAddressField", "jobSelectRef", "heightOptions", "weightOptions", "genderOptions", "getDictText", "dictType", "value", "$store", "uni", "error", "dictTexts", "computed", "jobLocalData", "dictData", "item", "getDictOptions", "onPageScroll", "onLoad", "options", "loadBaseInfo", "heightDicts", "weightDicts", "res", "getUserBase", "getHeightIndex", "targetHeight", "index", "getWeightIndex", "targetWeight", "getEighteenYearsAgo", "today", "getBirthdayValue", "getGenderText", "sex", "getGenderIndex", "handleGenderChange", "selected<PERSON><PERSON>", "editNickName", "handleNicknameConfirm", "inputValue", "handleNicknameClose", "handleBirthdayChange", "handleHeightChange", "selected<PERSON><PERSON>ght", "handleWeightChange", "selected<PERSON><PERSON>ght", "<PERSON><PERSON><PERSON><PERSON>", "type", "handleDistrictConfirm", "result", "handleDictChange", "field", "e", "getDictIndex", "handleJobSelect", "event", "submit", "submitData", "updateUserBase", "MiniProgramPage"], "mappings": "8pBAuKA,MAAAA,EAAA,IAAA,yDACAC,EAAA,IAAA,6EASA,MAAAC,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EACAE,EAAAF,EAAA,IAAA,CAAA,EACAG,EAAAH,EAAA,IAAA,EAAA,EAGAI,EAAAJ,EAAAA,IAAA,CACA,SAAA,GACA,OAAA,GACA,SAAA,GACA,OAAA,GACA,OAAA,GACA,IAAA,GACA,IAAA,GACA,gBAAA,GACA,QAAA,GACA,iBAAA,GACA,aAAA,GACA,iBAAA,GACA,eAAA,GACA,KAAA,GACA,oBAAA,GACA,gBAAA,GACA,oBAAA,GACA,kBAAA,GACA,QAAA,EACA,CAAA,EAGAK,EAAAL,EAAA,IAAA,IAAA,EACAM,EAAAN,EAAA,IAAA,EAAA,EAGAO,EAAAP,EAAA,IAAA,IAAA,EACAQ,EAAAR,EAAA,IAAA,EAAA,EAGAS,EAAAT,EAAA,IAAA,IAAA,EAGAU,EAAAV,EAAA,IAAA,EAAA,EACAW,EAAAX,EAAA,IAAA,EAAA,EAGAY,EAAAZ,EAAAA,IAAA,CACA,CAAA,MAAA,IAAA,KAAA,GAAA,EACA,CAAA,MAAA,IAAA,KAAA,GAAA,CACA,CAAA,EAGAa,EAAA,CAAAC,EAAAC,IAAA,CACA,GAAA,CAAAA,GAAA,CAAAD,EAAA,MAAA,GACA,GAAA,CACA,MAAA,CAAAE,EAAAA,QAAA,CAAAA,SAAA,MAAA,OAAAA,SAAA,KAAA,aAAA,YACAC,EAAAA,MAAA,MAAA,OAAA,0CAAA,gBAAA,EACA,IAEAD,EAAAA,OAAA,KAAA,YAAAF,EAAAC,CAAA,GACA,EACA,OAAAG,EAAA,CACAD,OAAAA,EAAAA,MAAA,MAAA,QAAA,0CAAA,YAAAC,CAAA,EACA,EACA,CACA,EAGAC,EAAAC,EAAA,SAAA,IACAhB,EAAA,MAQA,CAEA,oBAAAS,EAAA,wBAAAT,EAAA,MAAA,eAAA,EAEA,QAAAS,EAAA,WAAAT,EAAA,MAAA,GAAA,EAEA,QAAAS,EAAA,WAAAT,EAAA,MAAA,GAAA,EAEA,YAAAS,EAAA,eAAAT,EAAA,MAAA,OAAA,CACA,EAhBA,CACA,oBAAA,GACA,QAAA,GACA,QAAA,GACA,YAAA,EACA,CAYA,EAGAiB,EAAAD,EAAA,SAAA,IAAA,CACA,GAAA,CACA,MAAAE,EAAAN,EAAA,OAAA,KAAA,IAAA,UAAA,EACA,MAAA,CAAAM,GAAA,CAAA,MAAA,QAAAA,CAAA,EACA,CAAA,EAGAA,EAAA,IAAAC,IAAA,CACA,GAAAA,EAAA,GACA,KAAAA,EAAA,IACA,EAAA,CACA,OAAAL,EAAA,CACAD,OAAAA,EAAAA,MAAA,MAAA,QAAA,0CAAA,YAAAC,CAAA,EACA,CAAA,CACA,CACA,CAAA,EAGAM,EAAAV,GAAA,CACA,GAAA,CACA,MAAAQ,EAAAN,EAAA,OAAA,KAAA,IAAAF,CAAA,EACA,MAAA,CAAAQ,GAAA,CAAA,MAAA,QAAAA,CAAA,GACAL,EAAAA,MAAA,MAAA,OAAA,0CAAA,gBAAAH,CAAA,EACA,CAAA,GAEAQ,CACA,OAAAJ,EAAA,CACAD,OAAAA,EAAAA,MAAA,MAAA,QAAA,0CAAA,YAAAC,CAAA,EACA,CAAA,CACA,CACA,EAGAO,EAAA,aAAA,GAAA,CACAxB,EAAA,MAAA,EAAA,SACA,CAAA,EAGAyB,EAAA,OAAAC,GAAA,CACA5B,EAAA,MAAA,SAAA4B,EAAA,UAAA,CAAA,EAEAC,EAAA,CACA,CAAA,EAGA,MAAAA,EAAA,SAAA,CACA,GAAA,CAEAlB,EAAA,MAAAmB,cAAA,EACAlB,EAAA,MAAAmB,cAAA,EAEA,MAAAC,EAAA,MAAAC,cAAA,EACAD,EAAA,OAAA,KAAAA,EAAA,OACA3B,EAAA,MAAA,CACA,SAAA2B,EAAA,KAAA,UAAA,GACA,OAAAA,EAAA,KAAA,KAAA,GACA,SAAAA,EAAA,KAAA,UAAA,GACA,OAAAA,EAAA,KAAA,QAAA,GACA,OAAAA,EAAA,KAAA,QAAA,GACA,IAAAA,EAAA,KAAA,KAAA,GACA,IAAAA,EAAA,KAAA,KAAA,GACA,gBAAAA,EAAA,KAAA,iBAAA,GACA,QAAAA,EAAA,KAAA,SAAA,GACA,iBAAAA,EAAA,KAAA,kBAAA,GACA,aAAAA,EAAA,KAAA,cAAA,GACA,iBAAAA,EAAA,KAAA,kBAAA,GACA,eAAAA,EAAA,KAAA,gBAAA,GACA,KAAAA,EAAA,KAAA,MAAA,GACA,oBAAAA,EAAA,KAAA,qBAAA,GACA,gBAAAA,EAAA,KAAA,iBAAA,GACA,oBAAAA,EAAA,KAAA,qBAAA,GACA,kBAAAA,EAAA,KAAA,mBAAA,GACA,QAAAA,EAAA,KAAA,SAAA,EACA,GAEAd,EAAAA,MAAA,MAAA,MAAA,0CAAA,QAAAc,CAAA,CACA,OAAAb,EAAA,CACAD,EAAAA,MAAA,MAAA,QAAA,0CAAA,YAAAC,CAAA,CACA,CACA,EAGAe,EAAA,IAAA,CACA,GAAA,CAAAvB,EAAA,MAAA,OAAA,MAAA,GAEA,MAAAwB,EAAA9B,EAAA,MAAA,QAAA,IACA+B,EAAAzB,EAAA,MAAA,UAAAa,GAAAA,EAAA,KAAAW,CAAA,EACA,OAAAC,GAAA,EAAAA,EAAA,CACA,EAGAC,EAAA,IAAA,CACA,GAAA,CAAAzB,EAAA,MAAA,OAAA,MAAA,GAEA,MAAA0B,EAAAjC,EAAA,MAAA,QAAA,GACA+B,EAAAxB,EAAA,MAAA,UAAAY,GAAAA,EAAA,KAAAc,CAAA,EACA,OAAAF,GAAA,EAAAA,EAAA,CACA,EAGAG,EAAA,IAAA,CACA,MAAAC,EAAA,IAAA,KAEA,OADA,IAAA,KAAAA,EAAA,YAAA,EAAA,GAAAA,EAAA,SAAA,EAAAA,EAAA,QAAA,CAAA,EACA,YAAA,EAAA,MAAA,GAAA,EAAA,CAAA,CACA,EAGAC,EAAA,IACApC,EAAA,MAAA,UAAAkC,EAAA,EAIAG,EAAAC,GACAA,IAAA,IAAA,IACAA,IAAA,IAAA,IACA,GAIAC,EAAA,IAAA,CACA,GAAA,CAAAvC,EAAA,MAAA,OAAA,MAAA,GACA,MAAA+B,EAAAvB,EAAA,MAAA,UAAAW,GAAAA,EAAA,QAAAnB,EAAA,MAAA,MAAA,EACA,OAAA+B,GAAA,EAAAA,EAAA,CACA,EAGAS,EAAA,GAAA,CACA,MAAAC,EAAAjC,EAAA,MAAA,EAAA,OAAA,KAAA,EACAR,EAAA,MAAA,OAAAyC,EAAA,KACA,EAGAC,EAAA,IAAA,CACAxC,EAAA,MAAAF,EAAA,MAAA,SACAC,EAAA,MAAA,KAAA,CACA,EAGA0C,EAAAhC,GAAA,CAEA,MAAAiC,EAAA1C,EAAA,OAAAS,EACAiC,GAAAA,EAAA,SACA5C,EAAA,MAAA,SAAA4C,EAAA,KAAA,GAEA3C,EAAA,MAAA,MAAA,CACA,EAGA4C,EAAA,IAAA,CACA5C,EAAA,MAAA,MAAA,CACA,EAGA6C,EAAA,GAAA,CACA9C,EAAA,MAAA,SAAA,EAAA,OAAA,KACA,EAGA+C,EAAA,GAAA,CACA,MAAAC,EAAA1C,EAAA,MAAA,EAAA,OAAA,KAAA,EACAN,EAAA,MAAA,OAAAgD,EAAA,EACA,EAGAC,EAAA,GAAA,CACA,MAAAC,EAAA3C,EAAA,MAAA,EAAA,OAAA,KAAA,EACAP,EAAA,MAAA,OAAAkD,EAAA,EACA,EAGAC,EAAAC,GAAA,CACAhD,EAAA,MAAAgD,EACAjD,EAAA,MAAA,KAAA,CACA,EAGAkD,EAAAC,GAAA,CACAlD,EAAA,QAAA,cAEAJ,EAAA,MAAA,iBAAAsD,EAAA,MAAA,aACAtD,EAAA,MAAA,aAAAsD,EAAA,MAAA,SACAtD,EAAA,MAAA,iBAAAsD,EAAA,MAAA,aACAtD,EAAA,MAAA,eAAAsD,EAAA,MAAA,WACAtD,EAAA,MAAA,KAAAsD,EAAA,UACAlD,EAAA,QAAA,YAEAJ,EAAA,MAAA,oBAAAsD,EAAA,MAAA,aACAtD,EAAA,MAAA,gBAAAsD,EAAA,MAAA,SACAtD,EAAA,MAAA,oBAAAsD,EAAA,MAAA,aACAtD,EAAA,MAAA,kBAAAsD,EAAA,MAAA,WACAtD,EAAA,MAAA,QAAAsD,EAAA,SAEA,EAGAC,EAAA,CAAA7C,EAAA8C,EAAAC,IAAA,CACA,MAAAlC,EAAAH,EAAAV,CAAA,EACAa,GAAAA,EAAAkC,EAAA,OAAA,KAAA,IACAzD,EAAA,MAAAwD,CAAA,EAAAjC,EAAAkC,EAAA,OAAA,KAAA,EAAA,GAEA,EAGAC,EAAA,CAAAhD,EAAAC,IAAA,CACA,GAAA,CAAAA,EAAA,MAAA,GAEA,MAAAoB,EADAX,EAAAV,CAAA,EACA,UAAAS,GAAAA,EAAA,KAAAR,CAAA,EACA,OAAAoB,GAAA,EAAAA,EAAA,CACA,EAGA4B,EAAAC,GAAA,CACA/C,EAAAA,MAAA,MAAA,MAAA,0CAAA,QAAA+C,CAAA,EACAA,GAAAA,EAAA,QAEA5D,EAAA,MAAA,IAAA4D,EAAA,MAEA,EAGAC,EAAA,IAAA,CAEA,GAAA,CAAA7D,EAAA,MAAA,SAAA,CACAa,EAAAA,MAAA,UAAA,CACA,MAAA,QACA,KAAA,MACA,CAAA,EACA,MACA,CAEA,GAAA,CAAAb,EAAA,MAAA,OAAA,CACAa,EAAAA,MAAA,UAAA,CACA,MAAA,QACA,KAAA,MACA,CAAA,EACA,MACA,CACAd,EAAA,MAAA,GAEA,MAAA+D,EAAA,CACA,GAAA9D,EAAA,MACA,IAAAA,EAAA,MAAA,MACA,EACA,OAAA8D,EAAA,OACAC,EAAAA,eAAAD,CAAA,EAAA,KAAAnC,GAAA,CACAhC,EAAA,QAAA,EAEAkB,EAAAA,MAAA,WAAA,CACA,IAAA,wCACA,CAAA,EAGAA,EAAAA,MAAA,aAAA,CAEA,CAAA,EAAA,QAAA,IAAA,CACAd,EAAA,MAAA,EACA,CAAA,CACA,mnECzgBA,GAAG,WAAWiE,CAAe"}