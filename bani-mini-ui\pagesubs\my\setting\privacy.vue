<template>
	<scroll-nav-page title="隐私设置" :show-back="true" @heightChange="handleNavHeightChange">
		<template #content>
			<!-- 主要内容 -->
			<view class="main-container">
			<!-- 页面描述 -->
			<view class="page-description">
				<view class="desc-content">
					<text>管理您的隐私设置，保护个人信息安全</text>
				</view>
			</view>

			<!-- 隐私设置列表 -->
			<view class="privacy-list">
				<!-- 隐身设置 -->
				<view class="privacy-section">
					<view class="section-header">
						<text class="section-title">在线状态</text>
					</view>
					<view class="privacy-item">
						<view class="item-left">
							<uni-icons type="eye-slash" size="20" color="#696CF3"></uni-icons>
							<view class="item-info">
								<text class="item-title">隐身模式</text>
								<text class="item-desc">开启后，其他用户无法看到您的在线状态</text>
							</view>
						</view>
						<view class="item-right">
							<switch :checked="privacySettings.privacy_online_invisible" @change="handleInvisibleChange"
								color="#696CF3" style="transform: scale(0.8);" />
						</view>
					</view>
				</view>

				<!-- 资料可见性 -->
				<view class="privacy-section">
					<view class="section-header">
						<text class="section-title">资料可见性</text>
					</view>
					<view class="privacy-item">
						<view class="item-left">
							<uni-icons type="person" size="20" color="#696CF3"></uni-icons>
							<view class="item-info">
								<text class="item-title">个人资料</text>
								<text class="item-desc">设置谁可以查看您的详细资料</text>
							</view>
						</view>
						<view class="item-right">
							<picker @change="handleProfileVisibilityChange" :range="profileVisibilityOptions"
								:value="privacySettings.privacy_profile_visibility">
								<view class="picker-text">{{
									profileVisibilityOptions[privacySettings.privacy_profile_visibility] }}</view>
							</picker>
						</view>
					</view>
					<view class="privacy-item">
						<view class="item-left">
							<uni-icons type="location" size="20" color="#696CF3"></uni-icons>
							<view class="item-info">
								<text class="item-title">位置信息</text>
								<text class="item-desc">是否显示您的位置信息</text>
							</view>
						</view>
						<view class="item-right">
							<switch :checked="privacySettings.privacy_location_show" @change="handleLocationChange"
								color="#696CF3" style="transform: scale(0.8);" />
						</view>
					</view>
				</view>
			</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { toast } from '@/utils/common'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import globalConfig from '@/config'
import { getUserConfigMap, updateUserConfig } from '@/api/my/config'

// 页面状态
const navBarHeight = ref(0)
const saving = ref(false)

// 隐私设置数据
const privacySettings = reactive({
	privacy_online_invisible: false,        // 隐身模式
	privacy_profile_visibility: 0,      // 资料可见性 0-所有人 1-仅好友 2-仅自己
	privacy_location_show: true,        // 显示位置
})

// 资料可见性选项
const profileVisibilityOptions = ['所有人', '关注我的', '互相关注', '仅自己']

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 隐身模式切换
const handleInvisibleChange = (e) => {
	privacySettings.privacy_online_invisible = e.detail.value
	toast(e.detail.value ? '已开启隐身模式' : '已关闭隐身模式')
	updateUserConfig({
		configKey: 'privacy_online_invisible',
		val: e.detail.value ? 1 : 0
	})
}

// 资料可见性切换
const handleProfileVisibilityChange = (e) => {
	privacySettings.privacy_profile_visibility = e.detail.value
	toast(`资料可见性已设置为：${profileVisibilityOptions[e.detail.value]}`)
	updateUserConfig({
		configKey: 'privacy_profile_visibility',
		val: e.detail.value
	})
}

// 位置信息切换
const handleLocationChange = (e) => {
	privacySettings.privacy_location_show = e.detail.value
	toast(e.detail.value ? '已开启位置显示' : '已关闭位置显示')
	updateUserConfig({
		configKey: 'privacy_location_show',
		val: e.detail.value ? 1 : 0
	})
}

// 页面加载时获取当前设置
onLoad(() => {
	getUserConfigMap(3).then(result => {
		if (result.data) {
			for (let key in result.data) {
				if (privacySettings[key] instanceof Boolean || typeof privacySettings[key] === 'boolean') {
					privacySettings[key] = result.data[key] === '1' ? true : false
				} else {
					privacySettings[key] = result.data[key]
				}
			}
		}
	})
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.main-container {
	min-height: 100vh;
	box-sizing: border-box;
	padding: 0 20rpx 120rpx;
}

.page-description {
	text-align: center;
	margin: 20rpx 0 32rpx;
	padding: 20rpx 16rpx;
	background: rgba($primary-color, 0.03);
	border-radius: 12rpx;
	border: 1rpx solid rgba($primary-color, 0.08);

	.desc-content {
		text {
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
		}
	}
}

.privacy-list {
	.privacy-section {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 6rpx 24rpx rgba($primary-color, 0.08);
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		overflow: hidden;

		.section-header {
			padding: 24rpx 30rpx 16rpx;
			border-bottom: 1rpx solid rgba($primary-color, 0.08);

			.section-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				letter-spacing: 0.5rpx;
			}
		}

		.privacy-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 30rpx;
			border-bottom: 1rpx solid rgba($primary-color, 0.05);
			transition: all 0.3s ease;

			&:last-child {
				border-bottom: none;
			}

			&:hover {
				background: rgba($primary-color, 0.02);
			}

			.item-left {
				display: flex;
				align-items: flex-start;
				flex: 1;

				.item-info {
					margin-left: 20rpx;
					flex: 1;

					.item-title {
						font-size: 30rpx;
						color: #333;
						font-weight: 500;
						display: block;
						margin-bottom: 8rpx;
					}

					.item-desc {
						font-size: 24rpx;
						color: #666;
						line-height: 1.4;
					}
				}
			}

			.item-right {
				.picker-text {
					font-size: 28rpx;
					color: $primary-color;
					font-weight: 500;
					padding: 8rpx 16rpx;
					background: rgba($primary-color, 0.1);
					border-radius: 16rpx;
					border: 1rpx solid rgba($primary-color, 0.2);
				}
			}
		}
	}
}

.save-section {
	margin-top: 40rpx;
	padding: 0 20rpx;

	.save-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 600;
		background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
		color: #fff;
		border: none;
		box-shadow: 0 6rpx 20rpx rgba($primary-color, 0.2);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.15);
		}
	}
}
</style>
