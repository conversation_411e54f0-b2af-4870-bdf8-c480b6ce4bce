package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.greeting.AppUserGreetingVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserGreetingToAppUserGreetingVoMapperImpl implements UserGreetingToAppUserGreetingVoMapper {

    @Override
    public AppUserGreetingVo convert(UserGreeting source) {
        if ( source == null ) {
            return null;
        }

        AppUserGreetingVo appUserGreetingVo = new AppUserGreetingVo();

        appUserGreetingVo.setTime( source.getCreateTime() );
        appUserGreetingVo.setId( source.getId() );
        appUserGreetingVo.setUserId( source.getUserId() );
        appUserGreetingVo.setOppositeUserId( source.getOppositeUserId() );
        appUserGreetingVo.setContent( source.getContent() );
        appUserGreetingVo.setReplyContent( source.getReplyContent() );
        appUserGreetingVo.setStatus( source.getStatus() );

        return appUserGreetingVo;
    }

    @Override
    public AppUserGreetingVo convert(UserGreeting source, AppUserGreetingVo target) {
        if ( source == null ) {
            return target;
        }

        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );
        target.setOppositeUserId( source.getOppositeUserId() );
        target.setContent( source.getContent() );
        target.setReplyContent( source.getReplyContent() );
        target.setStatus( source.getStatus() );

        return target;
    }
}
