package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.tag.AppUserTagBoToUserTagMapper;
import com.gzhuxn.personals.domain.user.bo.UserTagBoToUserTagMapper;
import com.gzhuxn.personals.domain.user.vo.UserTagVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserTagBoToUserTagMapper.class,UserTagBoToUserTagMapper.class},
    imports = {}
)
public interface UserTagToUserTagVoMapper extends BaseMapper<UserTag, UserTagVo> {
}
