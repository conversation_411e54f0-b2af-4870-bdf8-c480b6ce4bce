package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.invitation.AppUserInvitationVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserInvitationToAppUserInvitationVoMapperImpl implements UserInvitationToAppUserInvitationVoMapper {

    @Override
    public AppUserInvitationVo convert(UserInvitation arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserInvitationVo appUserInvitationVo = new AppUserInvitationVo();

        appUserInvitationVo.setId( arg0.getId() );
        appUserInvitationVo.setUserId( arg0.getUserId() );
        appUserInvitationVo.setOppositeUserId( arg0.getOppositeUserId() );

        return appUserInvitationVo;
    }

    @Override
    public AppUserInvitationVo convert(UserInvitation arg0, AppUserInvitationVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
