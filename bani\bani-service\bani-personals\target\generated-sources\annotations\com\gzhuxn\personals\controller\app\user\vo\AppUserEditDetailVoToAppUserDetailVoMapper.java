package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.UserDetailToAppUserEditDetailVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserDetailToAppUserEditDetailVoMapper.class},
    imports = {}
)
public interface AppUserEditDetailVoToAppUserDetailVoMapper extends BaseMapper<AppUserEditDetailVo, AppUserDetailVo> {
}
