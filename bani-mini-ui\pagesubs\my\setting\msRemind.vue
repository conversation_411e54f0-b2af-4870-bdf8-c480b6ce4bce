<template>
	<scroll-nav-page title="消息通知" :show-back="true">
		<template #content>
			<!-- 主要内容 -->
			<view class="main-container" :style="{ paddingTop: navBarHeight + 'px' }">
				<!-- 页面描述 -->
				<view class="page-description">
					<view class="desc-content">
						<text>管理您的消息通知设置，控制接收的通知类型</text>
					</view>
				</view>

				<!-- 通知设置列表 -->
				<view class="notification-list">
					<!-- 互动通知 -->
					<view class="notification-section">
						<view class="section-header">
							<text class="section-title">互动通知</text>
						</view>
						<view class="notification-item">
							<view class="item-left">
								<uni-icons type="eye" size="20" color="#696CF3"></uni-icons>
								<view class="item-info">
									<text class="item-title">每日来访提醒</text>
									<text class="item-desc">每天定时提醒您有新的访客查看了您的资料</text>
								</view>
							</view>
							<view class="item-right">
								<switch :checked="notificationSettings.ms_interaction_daily_visit"
									@change="handleDailyVisitChange" color="#696CF3" style="transform: scale(0.8);" />
							</view>
						</view>
						<view class="notification-item">
							<view class="item-left">
								<uni-icons type="chatbubble" size="20" color="#696CF3"></uni-icons>
								<view class="item-info">
									<text class="item-title">被获取微信</text>
									<text class="item-desc">当有人获取您的微信联系方式时通知您</text>
								</view>
							</view>
							<view class="item-right">
								<switch :checked="notificationSettings.ms_interaction_apply_wechat"
									@change="handleWechatObtainedChange" color="#696CF3"
									style="transform: scale(0.8);" />
							</view>
						</view>
					</view>

					<!-- 推荐通知 -->
					<view class="notification-section">
						<view class="section-header">
							<text class="section-title">推荐通知</text>
						</view>
						<view class="notification-item">
							<view class="item-left">
								<uni-icons type="heart" size="20" color="#696CF3"></uni-icons>
								<view class="item-info">
									<text class="item-title">每日推荐消息</text>
									<text class="item-desc">每天为您推送符合条件的优质用户</text>
								</view>
							</view>
							<view class="item-right">
								<switch :checked="notificationSettings.ms_recommend_daily"
									@change="handleDailyRecommendationChange" color="#696CF3"
									style="transform: scale(0.8);" />
							</view>
						</view>
					</view>

					<!-- 活动通知 -->
					<view class="notification-section">
						<view class="section-header">
							<text class="section-title">活动通知</text>
						</view>
						<view class="notification-item">
							<view class="item-left">
								<uni-icons type="gift" size="20" color="#696CF3"></uni-icons>
								<view class="item-info">
									<text class="item-title">活动订阅通知</text>
									<text class="item-desc">接收平台活动、优惠和新功能的通知</text>
								</view>
							</view>
							<view class="item-right">
								<switch :checked="notificationSettings.ms_activity_subscription"
									@change="handleActivitySubscriptionChange" color="#696CF3"
									style="transform: scale(0.8);" />
							</view>
						</view>
					</view>

					<!-- 系统通知 -->
					<view class="notification-section">
						<view class="section-header">
							<text class="section-title">系统通知</text>
						</view>
						<view class="notification-item">
							<view class="item-left">
								<uni-icons type="notification" size="20" color="#696CF3"></uni-icons>
								<view class="item-info">
									<text class="item-title">系统消息</text>
									<text class="item-desc">接收系统重要通知和安全提醒</text>
								</view>
							</view>
							<view class="item-right">
								<switch :checked="notificationSettings.ms_system" @change="handleSystemMessageChange"
									color="#696CF3" style="transform: scale(0.8);" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import { toast } from '@/utils/common'
import globalConfig from '@/config'
import { getUserConfigMap, updateUserConfig } from '@/api/my/config'

// 页面状态
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const saving = ref(false)

// 通知设置数据
const notificationSettings = reactive({
	ms_interaction_daily_visit: true,      // 每日来访提醒
	ms_interaction_apply_wechat: true,          // 被获取微信
	ms_recommend_daily: true,     // 每日推荐消息
	ms_activity_subscription: true,    // 活动订阅通知
	ms_system: true,           // 系统消息
})

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 计算导航栏文字颜色
const getNavTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? globalConfig.theme.navTextBlackColor : globalConfig.theme.navTextWhiteColor
}

// 每日来访提醒切换
const handleDailyVisitChange = (e) => {
	notificationSettings.ms_interaction_daily_visit = e.detail.value
	toast(e.detail.value ? '已开启每日来访提醒' : '已关闭每日来访提醒')
	updateUserConfig({
		configKey: 'ms_interaction_daily_visit',
		val: e.detail.value ? 1 : 0
	})
}

// 每日推荐消息切换
const handleDailyRecommendationChange = (e) => {
	notificationSettings.ms_recommend_daily = e.detail.value
	toast(e.detail.value ? '已开启每日推荐消息' : '已关闭每日推荐消息')
	updateUserConfig({
		configKey: 'ms_recommend_daily',
		val: e.detail.value ? 1 : 0
	})
}

// 被获取微信切换
const handleWechatObtainedChange = (e) => {
	notificationSettings.ms_interaction_apply_wechat = e.detail.value
	toast(e.detail.value ? '已开启微信获取通知' : '已关闭微信获取通知')
	updateUserConfig({
		configKey: 'ms_interaction_apply_wechat',
		val: e.detail.value ? 1 : 0
	})
}

// 活动订阅通知切换
const handleActivitySubscriptionChange = (e) => {
	notificationSettings.ms_activity_subscription = e.detail.value
	toast(e.detail.value ? '已开启活动订阅通知' : '已关闭活动订阅通知')
	updateUserConfig({
		configKey: 'ms_activity_subscription',
		val: e.detail.value ? 1 : 0
	})
}

// 系统消息切换
const handleSystemMessageChange = (e) => {
	notificationSettings.ms_system = e.detail.value
	toast(e.detail.value ? '已开启系统消息' : '已关闭系统消息')
	updateUserConfig({
		configKey: 'ms_system',
		val: e.detail.value ? 1 : 0
	})
}

// 页面加载时获取当前设置
onLoad(() => {
	getUserConfigMap(1).then(result => {
		if (result.data) {
			for (let key in result.data) {
				if (notificationSettings[key] instanceof Boolean || typeof notificationSettings[key] === 'boolean') {
					notificationSettings[key] = result.data[key] === '1' ? true : false
				} else {
					notificationSettings[key] = result.data[key]
				}
			}
		}
	})
})
</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.main-container {
	min-height: 100vh;
	box-sizing: border-box;
	padding: 0 20rpx 120rpx;
}

.page-description {
	text-align: center;
	margin: 20rpx 0 32rpx;
	padding: 20rpx 16rpx;
	background: rgba($primary-color, 0.03);
	border-radius: 12rpx;
	border: 1rpx solid rgba($primary-color, 0.08);

	.desc-content {
		text {
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
		}
	}
}

.notification-list {
	.notification-section {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 6rpx 24rpx rgba($primary-color, 0.08);
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		overflow: hidden;

		.section-header {
			padding: 24rpx 30rpx 16rpx;
			border-bottom: 1rpx solid rgba($primary-color, 0.08);

			.section-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
				letter-spacing: 0.5rpx;
			}
		}

		.notification-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 30rpx;
			border-bottom: 1rpx solid rgba($primary-color, 0.05);
			transition: all 0.3s ease;

			&:last-child {
				border-bottom: none;
			}

			&:hover {
				background: rgba($primary-color, 0.02);
			}

			.item-left {
				display: flex;
				align-items: flex-start;
				flex: 1;

				.item-info {
					margin-left: 20rpx;
					flex: 1;

					.item-title {
						font-size: 30rpx;
						color: #333;
						font-weight: 500;
						display: block;
						margin-bottom: 8rpx;
					}

					.item-desc {
						font-size: 24rpx;
						color: #666;
						line-height: 1.4;
					}
				}
			}
		}
	}
}

.save-section {
	margin-top: 40rpx;
	padding: 0 20rpx;

	.save-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: 600;
		background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
		color: #fff;
		border: none;
		box-shadow: 0 6rpx 20rpx rgba($primary-color, 0.2);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.15);
		}
	}
}
</style>
