package com.gzhuxn.personals.controller.app.user.bo.wechatapply;

import com.gzhuxn.personals.domain.user.UserWechatApply;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserWechatApplyCreateBoToUserWechatApplyMapperImpl implements AppUserWechatApplyCreateBoToUserWechatApplyMapper {

    @Override
    public UserWechatApply convert(AppUserWechatApplyCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserWechatApply userWechatApply = new UserWechatApply();

        userWechatApply.setUserId( arg0.getUserId() );
        userWechatApply.setOppositeUserId( arg0.getOppositeUserId() );
        userWechatApply.setContent( arg0.getContent() );

        return userWechatApply;
    }

    @Override
    public UserWechatApply convert(AppUserWechatApplyCreateBo arg0, UserWechatApply arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}
