"use strict";const e=require("../../../common/vendor.js"),l=require("../../../common/assets.js"),c=require("../../../utils/common.js"),g=require("../../../api/my/coin.js"),k=require("../../../api/my/account.js"),P=require("../../../config.js");if(!Array){const p=e.resolveComponent("uni-icons"),t=e.resolveComponent("signin");(p+t)()}const E=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",I=()=>"../../../components/signin/signin.js";Math||(E+I+L+q)();const L=()=>"./components/recharge-popup.js",q=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",R={__name:"coin",setup(p){e.ref(0);const t=e.ref({userId:0,coin:0,lockCoin:0,availableCoin:0,withdrawCoin:0,lockWithdrawCoin:0,availableWithdrawCoin:0,totalCoin:0}),r=e.ref(0),u=e.ref([]),i=e.ref([]),s=e.ref(!1),d=e.ref(null),m=e.ref(!0);e.onLoad(()=>{_(),f(),m.value=!1});const v=()=>{e.index.navigateTo({url:P.globalConfig.help.coinIntroduction})},_=async()=>{try{const o=await k.getUserAccountInfo();if(o.code===200)t.value=o.data,r.value=o.data.totalCoin||0;else throw new Error(o.msg||"获取账户信息失败")}catch(o){e.index.__f__("error","at pagesubs/my/coin/coin.vue:184","获取账户信息失败:",o),r.value=0}},f=async()=>{try{s.value=!0;const o=await g.getTaskListByLevel(g.USER_LEVELS.EXPERT);o.code===200&&o.data?(i.value=o.data,e.index.__f__("log","at pagesubs/my/coin/coin.vue:197","第三等级任务加载成功:",o.data),o.data.forEach((n,a)=>{e.index.__f__("log","at pagesubs/my/coin/coin.vue:201",`任务${a+1}:`,{taskId:n.taskId,taskName:n.taskName,isCompleted:n.isCompleted,path:n.path,taskType:n.taskType,coin:n.coin})})):(i.value=[],e.index.__f__("log","at pagesubs/my/coin/coin.vue:212","第三等级任务为空或加载失败:",o))}catch(o){e.index.__f__("error","at pagesubs/my/coin/coin.vue:215","获取第三等级任务失败:",o),i.value=[],e.index.showToast({title:"加载任务失败",icon:"none"})}finally{s.value=!1}},h=o=>{if(!o.completed)switch(o.id){case 3:e.index.navigateTo({url:"/pagesubs/my/identity/identity"});break;case 4:e.index.navigateTo({url:"/pagesubs/my/profile-edit/profile-edit"});break;case 5:e.index.navigateTo({url:"/pagesubs/my/education/education"});break;case 6:e.index.navigateTo({url:"/pagesubs/my/work/work"});break;default:c.toast("功能开发中")}},y=o=>{if(e.index.__f__("log","at pagesubs/my/coin/coin.vue:263","点击任务:",o),o.isCompleted){c.toast("任务已完成");return}if(o.path&&o.path.trim()){const n=o.path.trim();e.index.__f__("log","at pagesubs/my/coin/coin.vue:274","使用path跳转到:",n),n.startsWith("http://")||n.startsWith("https://")?e.index.navigateTo({url:`/pages/webview/webview?url=${encodeURIComponent(n)}`}):e.index.navigateTo({url:n,success:()=>{e.index.__f__("log","at pagesubs/my/coin/coin.vue:287","页面跳转成功:",n)},fail:a=>{e.index.__f__("error","at pagesubs/my/coin/coin.vue:290","页面跳转失败:",a),c.toast("页面跳转失败")}});return}switch(e.index.__f__("log","at pagesubs/my/coin/coin.vue:299","使用taskType跳转:",o.taskType),o.taskType){case"profile":e.index.navigateTo({url:"/pagesubs/my/profile/baseEdit"});break;case"avatar":e.index.navigateTo({url:"/pagesubs/my/profile/profileEdit"});break;case"auth_identity":e.index.navigateTo({url:"/pagesubs/my/auth/identity"});break;case"photos":e.index.navigateTo({url:"/pagesubs/my/profile-edit/profile-edit"});break;case"auth_education":e.index.navigateTo({url:"/pagesubs/my/education/education"});break;case"auth_work":e.index.navigateTo({url:"/pagesubs/my/work/work"});break;case"auth_car":e.index.navigateTo({url:"/pagesubs/my/auth/car"});break;case"auth_house":e.index.navigateTo({url:"/pagesubs/my/auth/house"});break;default:c.toast("功能开发中")}},b=()=>{var o;(o=d.value)==null||o.openPopup()},T=()=>{e.index.navigateTo({url:"/pagesubs/my/coin/coin-detail"})};return(o,n)=>e.e({a:l._imports_0$1,b:e.o(v),c:e.p({type:"help",size:"16",color:"#999"}),d:e.t(r.value),e:e.p({type:"right",size:"16",color:"#999"}),f:e.o(T),g:e.o(b),h:e.t(t.value.coin),i:e.t(t.value.withdrawCoin),j:u.value.length>0},u.value.length>0?{k:e.f(u.value,(a,x,w)=>({a:e.t(a.title),b:e.t(a.reward),c:e.t(a.completed?"已完成":a.buttonText),d:a.completed?1:"",e:a.completed,f:a.id,g:e.o(C=>h(a),a.id)})),l:l._imports_0$1}:{},{m:s.value},s.value?{}:i.value.length>0?{o:e.f(i.value,(a,x,w)=>({a:e.t(a.taskName),b:e.t(a.coin),c:e.t(a.isCompleted?"已完成":"去完成"),d:a.isCompleted?1:"",e:a.isCompleted,f:e.o(C=>y(a),a.taskId),g:a.taskId,h:a.isCompleted?1:""})),p:l._imports_0$1}:{q:e.p({type:"info",size:"48",color:"#ccc"})},{n:i.value.length>0,r:e.sr(d,"7a17e612-5,7a17e612-0",{k:"rechargePopupRef"}),s:e.o(_),t:e.p({title:"我的花瓣","show-back":!0})})}},$=e._export_sfc(R,[["__scopeId","data-v-7a17e612"]]);wx.createPage($);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/coin/coin.js.map
