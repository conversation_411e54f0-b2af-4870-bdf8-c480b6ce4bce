package com.gzhuxn.personals.domain.group;

import com.gzhuxn.personals.domain.group.vo.GroupVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class GroupToGroupVoMapperImpl implements GroupToGroupVoMapper {

    @Override
    public GroupVo convert(Group arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupVo groupVo = new GroupVo();

        groupVo.setId( arg0.getId() );
        groupVo.setName( arg0.getName() );
        groupVo.setAvatar( arg0.getAvatar() );
        groupVo.setIntroduce( arg0.getIntroduce() );
        groupVo.setBackgroundImg( arg0.getBackgroundImg() );
        groupVo.setOfficialFlag( arg0.getOfficialFlag() );
        groupVo.setOwnerUserId( arg0.getOwnerUserId() );

        return groupVo;
    }

    @Override
    public GroupVo convert(Group arg0, GroupVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setBackgroundImg( arg0.getBackgroundImg() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setOwnerUserId( arg0.getOwnerUserId() );

        return arg1;
    }
}
