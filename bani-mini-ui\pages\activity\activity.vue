<template>
	<!-- 自定义导航栏 -->
	<scroll-nav-page :enableScrollGradient="false" @scroll="handlePageScroll" @heightChange="handleNavHeightChange">
		<template #nav-left>
			<nav-tabs v-model="currentTab" :tabs="activityTabs" :text-color="getTabTextColor()"
				@change="handleTabChange" />
		</template>
		<template #content>
			<!-- 官方活动列表 -->
			<OfficialList v-if="currentTab === 'official'" :nav-bar-height="navBarHeight" />

			<!-- 找搭子列表 -->
			<BuddyList v-else :nav-bar-height="navBarHeight" />

			<!-- 发布按钮 -->
			<view class="publish-btn" :class="{ 'loading': isPublishing }" @click="goToPublish">
				<uni-icons v-if="isPublishing" type="spinner-cycle" size="24" color="#fff"></uni-icons>
				<uni-icons v-else type="plus" size="24" color="#fff"></uni-icons>
			</view>
		</template>
	</scroll-nav-page>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { onPageScroll, onShow } from '@dcloudio/uni-app'
import BuddyList from './components/buddy-list.vue'
import OfficialList from './components/official-list.vue'
import { isIdentityVerified } from '@/api/my/my'

// 页面滚动距离
const pageScrollTop = ref(0)
// 导航栏高度
const navBarHeight = ref(0)

// 当前选中的标签页
const currentTab = ref('buddy')
// 发布按钮加载状态
const isPublishing = ref(false)
// 活动页面标签数据
const activityTabs = ref([
	{ label: '找搭子', value: 'buddy' },
	// { label: '官方活动', value: 'official' }
])

// 处理页面滚动
const handlePageScroll = (e) => {
	pageScrollTop.value = e.scrollTop
	console.log('页面滚动监听触发:', e.scrollTop)
}

// 处理导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 移除了 handleNavScroll 函数，使用默认配置

// 计算标签文字颜色
const getTabTextColor = () => {
	const opacity = Math.min(pageScrollTop.value / 100, 1)
	return opacity > 0.5 ? '#333333' : '#ffffff'
}



// 页面滚动监听
onPageScroll(handlePageScroll)

// 页面加载时获取位置
onMounted(() => {
	console.log('Activity页面已挂载，滚动监听已注册')
	console.log('初始滚动距离:', pageScrollTop.value)
})

// 页面显示时重新加载数据（tabBar重复点击时会触发）
onShow(() => {
	console.log('活动页面显示，重新加载数据')
	// 由于数据加载逻辑在子组件中，这里可以通过事件或其他方式通知子组件刷新
	// 子组件应该监听页面显示事件或提供刷新方法
})

// 切换标签页处理
const handleTabChange = (tab) => {
	currentTab.value = tab
}



// 跳转到发布页
const goToPublish = async () => {
	if (isPublishing.value) return

	isPublishing.value = true
	try {
		// 检查用户是否已认证
		const response = await isIdentityVerified()

		if (response.code === 200) {
			if (response.data) {
				// 已认证，正常跳转
				const url = currentTab.value === 'official'
					? '/pages/activity/publish/publish'
					: '/pagesubs/activity/buddy/add'

				uni.navigateTo({
					url: url
				})
			} else {
				// 未认证，显示提示并跳转到认证页面
				uni.showModal({
					title: '需要实名认证',
					content: '发布活动需要先完成实名认证，是否前往认证？',
					confirmText: '去认证',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: '/pagesubs/my/auth/auth'
							})
						}
					}
				})
			}
		} else {
			console.error('查询认证状态失败:', response.msg)
			uni.showToast({
				title: response.msg || '查询认证状态失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('查询认证状态异常:', error)
		uni.showToast({
			title: '网络异常，请重试',
			icon: 'none'
		})
	} finally {
		isPublishing.value = false
	}
}
</script>

<style lang="scss" scoped>
// nav-tabs 样式已移至独立组件中
// 容器样式已移至各自组件中

.publish-btn {
	position: fixed;
	right: 30rpx;
	bottom: 30rpx;
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(to right, #696CF3, #9B9DF5);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);
	transition: all 0.3s ease;

	&.loading {
		opacity: 0.7;
		transform: scale(0.95);
	}

	&:active {
		transform: scale(0.9);
	}
}
</style>