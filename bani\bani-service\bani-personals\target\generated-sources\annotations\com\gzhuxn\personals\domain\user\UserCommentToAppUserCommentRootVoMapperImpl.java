package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.comment.AppUserCommentRootVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserCommentToAppUserCommentRootVoMapperImpl implements UserCommentToAppUserCommentRootVoMapper {

    @Override
    public AppUserCommentRootVo convert(UserComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserCommentRootVo appUserCommentRootVo = new AppUserCommentRootVo();

        appUserCommentRootVo.setId( arg0.getId() );
        appUserCommentRootVo.setContent( arg0.getContent() );
        appUserCommentRootVo.setImages( arg0.getImages() );
        appUserCommentRootVo.setCreateTime( arg0.getCreateTime() );
        appUserCommentRootVo.setChildCount( arg0.getChildCount() );

        return appUserCommentRootVo;
    }

    @Override
    public AppUserCommentRootVo convert(UserComment arg0, AppUserCommentRootVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setChildCount( arg0.getChildCount() );

        return arg1;
    }
}
