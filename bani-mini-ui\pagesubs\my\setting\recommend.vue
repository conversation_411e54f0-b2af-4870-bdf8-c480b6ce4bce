<template>
	<scroll-nav-page title="推荐设置" :show-back="true" @heightChange="handleNavHeightChange">
		<template #content>
			<!-- 推荐设置内容 -->
			<view class="main-container">
			<!-- 提示卡片 -->
			<view class="tips-card">
				<view class="tips-header">
					<uni-icons type="info-filled" size="20" color="#696CF3"></uni-icons>
					<text class="tips-title">智能推荐说明</text>
				</view>
				<text class="tips-text">根据您的择偶条件，为您推荐匹配的用户。条件不足时会适当放宽匹配范围，设置次日生效。</text>
			</view>

			<!-- 设置卡片 -->
			<view class="settings-card">
				<!-- 年龄设置 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-title">年龄范围</text>
						<text class="setting-value">{{ recommendConfig.ageMin }}岁 - {{ recommendConfig.ageMax }}岁</text>
					</view>
					<view class="slider-container">
						<slider-range :min="18" :max="70" :step="1"
							:value="[recommendConfig.ageMin, recommendConfig.ageMax]" @change="onAgeChange"
							activeColor="#696CF3" backgroundColor="#E5E6F3" block-size="24" :format="formatAge" />
					</view>
				</view>

				<!-- 身高设置 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-title">身高范围</text>
						<text class="setting-value">{{ recommendConfig.heightMin }}cm - {{ recommendConfig.heightMax
						}}cm</text>
					</view>
					<view class="slider-container">
						<slider-range :min="140" :max="220" :step="5"
							:value="[recommendConfig.heightMin, recommendConfig.heightMax]" @change="onHeightChange"
							activeColor="#696CF3" backgroundColor="#E5E6F3" block-size="24" :format="formatHeight" />
					</view>
				</view>

				<!-- 学历设置 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-title">学历要求</text>
						<text class="setting-subtitle">最低学历要求</text>
					</view>
					<picker @change="onEducationChange" :value="educationIndex" :range="educationOptions">
						<view class="picker-container">
							<text class="picker-text">{{ educationOptions[educationIndex] }}</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</picker>
				</view>

				<!-- 地区设置 -->
				<view class="setting-item">
					<view class="setting-header">
						<text class="setting-title">地区要求</text>
						<text class="setting-subtitle">期望交友范围</text>
					</view>
					<picker @change="onLocationChange" :value="locationIndex" :range="locationOptions">
						<view class="picker-container">
							<text class="picker-text">{{ locationOptions[locationIndex] }}</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</picker>
				</view>
			</view>

			<!-- 保存按钮 -->
			<view class="action-container">
				<button class="save-button" @click="saveSettings" :disabled="isSaving">
					<text v-if="!isSaving">保存设置</text>
					<text v-else>保存中...</text>
				</button>
			</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getUserConfigMap, updateUserConfig } from '@/api/my/config'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import $store from '@/store'

// 导航栏高度
const navBarHeight = ref(0)

// 推荐设置相关数据
const recommendConfig = ref({
	ageMin: 18,
	ageMax: 70,
	heightMin: 140,
	heightMax: 220,
	education: 0,
	location: 0
})
const educationIndex = ref(0)
const locationIndex = ref(0)
const isSaving = ref(false)

const educationOptions = $store.dict.getNames('recommend_set_education')
const locationOptions = $store.dict.getNames('recommend_set_location')

// 导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 格式化年龄显示
const formatAge = (val) => {
	return val + '岁'
}

// 格式化身高显示
const formatHeight = (val) => {
	return val + 'cm'
}

// 年龄范围变化
const onAgeChange = (e) => {
	recommendConfig.value.ageMin = e[0]
	recommendConfig.value.ageMax = e[1]
}

// 身高范围变化
const onHeightChange = (e) => {
	recommendConfig.value.heightMin = e[0]
	recommendConfig.value.heightMax = e[1]
}

// 学历选择变化
const onEducationChange = (e) => {
	educationIndex.value = e.detail.value
}

// 地区选择变化
const onLocationChange = (e) => {
	locationIndex.value = e.detail.value
}

// 保存设置
const saveSettings = async () => {
	if (isSaving.value) return

	// 验证数据
	if (recommendConfig.value.ageMin >= recommendConfig.value.ageMax) {
		uni.showToast({
			title: '年龄范围设置错误',
			icon: 'none'
		})
		return
	}

	if (recommendConfig.value.heightMin >= recommendConfig.value.heightMax) {
		uni.showToast({
			title: '身高范围设置错误',
			icon: 'none'
		})
		return
	}
	isSaving.value = true
	recommendConfig.value.education = $store.dict.getIdByIndex('recommend_set_education', educationIndex.value)
	recommendConfig.value.location = $store.dict.getIdByIndex('recommend_set_location', locationIndex.value)
	updateUserConfig({
		configKey: 'user_recommend_settings',
		val: JSON.stringify(recommendConfig.value)
	}).then(res => {
		uni.showToast({
			title: '设置保存成功',
			icon: 'success'
		})
		// 延迟返回上一页
		setTimeout(() => {
			uni.navigateBack()
			isSaving.value = false
		}, 1500)
	})
}
// 页面加载时获取当前设置
onLoad(() => {
	getUserConfigMap(2).then(res => {
		if (res.data) {
			const config = JSON.parse(res.data['user_recommend_settings'])
			Object.assign(recommendConfig.value,config)
			educationIndex.value = $store.dict.getIndexById('recommend_set_education', config.education)
			locationIndex.value = $store.dict.getIndexById('recommend_set_location', config.location)
		}
	})
})

</script>

<style lang="scss" scoped>
// 引入uni.scss变量
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			rgba(105, 108, 243, 0.08) 0%,
			rgba(105, 108, 243, 0.05) 30%,
			rgba(105, 108, 243, 0.02) 60%,
			rgba(255, 255, 255, 1) 100%);
}

.main-container {
	margin-top: 20rpx;
	padding: 0 24rpx 24rpx;

	.tips-card {
		background: linear-gradient(135deg, rgba(105, 108, 243, 0.08), rgba(155, 157, 245, 0.12));
		border-radius: 20rpx;
		padding: 24rpx;
		margin-bottom: 24rpx;
		border: 1rpx solid rgba(105, 108, 243, 0.15);
		backdrop-filter: blur(10rpx);

		.tips-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;

			.tips-title {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				margin-left: 12rpx;
			}
		}

		.tips-text {
			font-size: 26rpx;
			color: #666;
			line-height: 1.6;
		}
	}

	.settings-card {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		padding: 32rpx 24rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(105, 108, 243, 0.08);
		backdrop-filter: blur(10rpx);

		.setting-item {
			margin-bottom: 48rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.setting-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 24rpx;

				.setting-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
				}

				.setting-subtitle {
					font-size: 24rpx;
					color: #999;
				}

				.setting-value {
					font-size: 26rpx;
					color: #696CF3;
					font-weight: 500;
				}
			}

			.slider-container {
				padding: 0 8rpx;
			}

			.picker-container {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 24rpx 20rpx;
				background: rgba(105, 108, 243, 0.05);
				border-radius: 16rpx;
				border: 1rpx solid rgba(105, 108, 243, 0.1);
				transition: all 0.3s ease;

				&:active {
					background: rgba(105, 108, 243, 0.1);
					transform: scale(0.98);
				}

				.picker-text {
					font-size: 30rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}
	}

	.action-container {
		padding: 24rpx 0 40rpx;

		.save-button {
			width: 100%;
			height: 88rpx;
			line-height: 88rpx;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: 600;
			background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
			color: #fff;
			border: none;
			box-shadow: 0 4rpx 12rpx rgba($primary-color, 0.2);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
				box-shadow: 0 2rpx 8rpx rgba($primary-color, 0.15);
				background: linear-gradient(135deg, darken($primary-color, 5%), darken($primary-color, 2%));
			}

			&[disabled] {
				background: #ccc;
				box-shadow: none;
				transform: none;
				color: #999;
			}
		}
	}
}
</style>
