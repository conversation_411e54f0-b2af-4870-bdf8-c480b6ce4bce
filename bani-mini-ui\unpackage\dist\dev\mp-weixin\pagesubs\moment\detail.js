"use strict";const e=require("../../common/vendor.js"),P=require("../../api/moment/recommend.js"),Q=require("../../api/my/like.js"),W=require("../../api/my/follow.js"),d=require("../../api/my/comment.js"),h=require("../../store/index.js");if(!Array){const b=e.resolveComponent("uni-icons"),w=e.resolveComponent("scroll-nav-page");(b+w)()}const X=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",Z=()=>"../../components/scroll-nav-page/scroll-nav-page.js";Math||(X+Z)();const $={__name:"detail",setup(b){const w=e.ref(0),F=e.ref(0),a=e.ref({}),_=e.ref([]),r=e.ref(""),x=e.ref(!1),k=e.ref(null),M=e.ref(!1),I=e.ref(0),C=e.ref(1),O=e.ref(10),S=e.ref(!0),p=e.ref(null),g=e.ref(null),v=e.ref(""),T=e.ref(new Map),m=e.ref(new Map),f=e.ref(new Set);e.onLoad(t=>{const n=t.id;n&&(p.value=n,R(n),y(n))}),e.onPageScroll(t=>{w.value=t.scrollTop});const R=async t=>{P.getRecommendMomentDetail(t).then(n=>{a.value=P.formatRecommendMoment(n.data)})},y=async(t,n=!0)=>{if(!M.value)try{M.value=!0,n&&(C.value=1,_.value=[]);const o={type:d.COMMENT_TYPE.MOMENT,businessId:t,pageSize:O.value,pageNum:C.value,orderByColumn:"createTime",isAsc:"desc"};e.index.__f__("log","at pagesubs/moment/detail.vue:307","加载评论列表，参数:",o);const s=await d.getRootComments(o);if(s.code===200&&s.data){const{total:c,rows:i}=s.data,l=i.map(u=>({id:u.id,rootId:u.rootId||null,avatar:u.oppAvatar,nickname:u.oppNickName,uid:u.uid,content:u.content,images:u.images,createTime:u.createTime,childCount:u.childCount||0,isMe:h.$store.isMe(u.uid)}));n?_.value=l:_.value.push(...l),I.value=s.data.totalCount,S.value=_.value.length<c,e.index.__f__("log","at pagesubs/moment/detail.vue:336","评论列表加载成功:",_.value)}else e.index.__f__("error","at pagesubs/moment/detail.vue:338","加载评论列表失败:",s.msg),e.index.showToast({title:s.msg||"加载失败",icon:"none"})}catch(o){e.index.__f__("error","at pagesubs/moment/detail.vue:345","加载评论列表异常:",o),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}finally{M.value=!1}},q=t=>t===1?"single-image":t===2?"two-images":t===3?"three-images":"multiple-images",A=(t,n)=>{e.index.previewImage({urls:t,current:n})},Y=async()=>{try{e.index.__f__("log","at pagesubs/moment/detail.vue:401","点赞动态:",a.value.id,"当前状态:",a.value.isLiked);const t=await Q.toggleMomentLike(a.value.id,a.value.isLiked);t.code===200?(a.value.isLiked=!a.value.isLiked,a.value.isLiked?(a.value.likes=(a.value.likes||0)+1,e.index.showToast({title:"点赞成功",icon:"none",duration:1e3})):(a.value.likes=Math.max((a.value.likes||0)-1,0),e.index.showToast({title:"取消点赞",icon:"none",duration:1e3}))):(e.index.__f__("error","at pagesubs/moment/detail.vue:425","点赞操作失败:",t.msg||"未知错误"),e.index.showToast({title:t.msg||"操作失败，请重试",icon:"none",duration:2e3}))}catch(t){e.index.__f__("error","at pagesubs/moment/detail.vue:433","点赞操作异常:",t),e.index.showToast({title:"网络错误，请检查网络连接",icon:"none",duration:2e3})}},B=async()=>{e.index.__f__("log","at pagesubs/moment/detail.vue:444","关注用户:",a.value.user.id,"当前状态:",a.value.isFollowed);const t=await W.toggleUserFollow(a.value.user.id,a.value.isFollowed);t.code===200?(a.value.isFollowed=!a.value.isFollowed,e.index.showToast({title:a.value.isFollowed?"关注成功":"已取消关注",icon:"none",duration:1500})):(e.index.__f__("error","at pagesubs/moment/detail.vue:458","关注操作失败:",t.msg||"未知错误"),e.index.showToast({title:t.msg||"操作失败，请重试",icon:"none",duration:2e3}))},D=()=>{e.index.showToast({title:"分享功能开发中",icon:"none"})},N=()=>{k.value&&k.value.focus()},j=()=>{x.value=!0},H=()=>{x.value=!1},E=async()=>{if(r.value.trim()){if(!p.value){e.index.showToast({title:"动态ID不能为空",icon:"none"});return}try{let t;if(g.value){const n=g.value.rootId||g.value.id,o=g.value.id,s=v.value+r.value.trim();e.index.__f__("log","at pagesubs/moment/detail.vue:517","发送回复:",{businessId:p.value,type:d.COMMENT_TYPE.MOMENT,rootId:n,parentId:o,content:s,replyTarget:g.value.nickname,originalContent:r.value.trim(),prefix:v.value}),t=await d.replyComment(p.value,d.COMMENT_TYPE.MOMENT,n,o,s)}else e.index.__f__("log","at pagesubs/moment/detail.vue:537","发送评论:",{businessId:p.value,type:d.COMMENT_TYPE.MOMENT,content:r.value.trim()}),t=await d.addRootComment(p.value,d.COMMENT_TYPE.MOMENT,r.value.trim());t.code===200?(r.value="",g.value=null,v.value="",e.index.showToast({title:g.value?"回复成功":"评论成功",icon:"success"}),await y(p.value,!0),a.value.comments!==void 0&&(a.value.comments=_.value.length)):(e.index.__f__("error","at pagesubs/moment/detail.vue:570","发送失败:",t.msg),e.index.showToast({title:t.msg||"发送失败",icon:"none"}))}catch(t){e.index.__f__("error","at pagesubs/moment/detail.vue:577","发送异常:",t),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}}},L=t=>{g.value=t,v.value=`回复${t.nickname}: `,N(),e.index.__f__("log","at pagesubs/moment/detail.vue:594","设置回复目标:",{target:t,prefix:v.value})},G=()=>{g.value=null,v.value="",r.value="",e.index.__f__("log","at pagesubs/moment/detail.vue:606","取消回复")},z=t=>{h.$store.isMe(t.uid)&&e.index.showActionSheet({itemList:["删除评论"],itemColor:"#ff4757",success:n=>{n.tapIndex===0&&V(t)},fail:n=>{e.index.__f__("log","at pagesubs/moment/detail.vue:626","取消操作:",n)}})},U=async t=>{const n=t.id;if(f.value.has(n)){f.value.delete(n);return}if(!m.value.get(n))try{m.value.set(n,!0);const o={type:d.COMMENT_TYPE.MOMENT,businessId:p.value,rootId:n,pageSize:10,pageNum:1,orderByColumn:"createTime",isAsc:"asc"};e.index.__f__("log","at pagesubs/moment/detail.vue:660","加载子评论，参数:",o);const s=await d.getChildComments(o);if(s.code===200&&s.data){const{rows:c}=s.data,i=c.map(l=>({id:l.id,rootId:l.rootId,avatar:l.oppAvatar,nickname:l.oppNickName,uid:l.uid,content:l.content,images:l.images,createTime:l.createTime,publishTime:l.createTime,isMe:h.$store.isMe(l.uid)}));T.value.set(n,i),f.value.add(n),e.index.__f__("log","at pagesubs/moment/detail.vue:686","子评论加载成功:",{rootCommentId:n,childComments:i})}else e.index.__f__("error","at pagesubs/moment/detail.vue:691","加载子评论失败:",s.msg),e.index.showToast({title:s.msg||"加载失败",icon:"none"})}catch(o){e.index.__f__("error","at pagesubs/moment/detail.vue:698","加载子评论异常:",o),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}finally{m.value.set(n,!1)}},V=async t=>{if(!h.$store.isMe(t.uid)){e.index.showToast({title:"只能删除自己的评论",icon:"none"});return}try{if((await e.index.showModal({title:"确认删除",content:"确定要删除这条评论吗？",confirmText:"删除",confirmColor:"#ff4757"})).confirm){e.index.__f__("log","at pagesubs/moment/detail.vue:729","删除评论:",{commentId:t.id,uid:t.uid,isMe:h.$store.isMe(t.uid)});const o=await d.deleteSingleComment(t.id);o.code===200?(e.index.showToast({title:"删除成功",icon:"success"}),await y(p.value,!0),a.value.comments!==void 0&&(a.value.comments=_.value.length)):(e.index.__f__("error","at pagesubs/moment/detail.vue:751","删除评论失败:",o.msg),e.index.showToast({title:o.msg||"删除失败",icon:"none"}))}}catch(n){e.index.__f__("error","at pagesubs/moment/detail.vue:759","删除评论异常:",n),e.index.showToast({title:"网络异常，请稍后重试",icon:"none"})}},J=t=>{e.index.showToast({title:`点击了话题: #${t.name}`,icon:"none"})};return(t,n)=>e.e({a:a.value.user.avatar,b:a.value.user.isIdentity},a.value.user.isIdentity?{c:e.p({type:"checkmarkempty",size:"12",color:"#fff"})}:{},{d:e.t(a.value.user.nickname),e:e.t(a.value.user.age),f:e.t(a.value.user.height),g:e.t(a.value.user.city),h:!a.value.user.isMe},a.value.user.isMe?{}:{i:e.t(a.value.isFollowed?"已关注":"关注"),j:e.o(B)},{k:a.value.content},a.value.content?{l:e.t(a.value.content)}:{},{m:a.value.images&&a.value.images.length>0},a.value.images&&a.value.images.length>0?{n:e.f(a.value.images,(o,s,c)=>({a:o,b:s,c:e.o(i=>A(a.value.images,o),s)})),o:e.n(q(a.value.images.length))}:{},{p:a.value.tags&&a.value.tags.length>0},a.value.tags&&a.value.tags.length>0?{q:e.f(a.value.tags,(o,s,c)=>({a:e.t(o.tagValName),b:s,c:e.o(i=>J(o),s)}))}:{},{r:e.t(a.value.time),s:a.value.location},a.value.location?{t:e.t(a.value.location)}:{},{v:e.t(a.value.likes),w:a.value.pv>0},a.value.pv>0?{x:e.p({type:"eye",size:"16",color:"#999"}),y:e.t(a.value.pv)}:{},{z:e.t(I.value||_.value.length),A:e.f(_.value,(o,s,c)=>e.e({a:o.avatar,b:e.t(o.nickname),c:o.isMe},o.isMe?{d:e.o(i=>z(o),o.id)}:{},{e:e.t(o.content),f:e.t(o.createTime),g:"03d35165-3-"+c+",03d35165-0",h:e.o(i=>L(o),o.id),i:o.childCount>0},o.childCount>0?{j:"03d35165-4-"+c+",03d35165-0",k:e.p({type:f.value.has(o.id)?"up":"down",size:"14",color:"#999"}),l:e.t(o.childCount),m:e.o(i=>U(o),o.id)}:{},{n:f.value.has(o.id)&&T.value.get(o.id)},f.value.has(o.id)&&T.value.get(o.id)?e.e({o:e.f(T.value.get(o.id),(i,l,u)=>e.e({a:i.avatar,b:e.t(i.nickname),c:e.t(i.createTime),d:i.isMe},i.isMe?{e:e.o(K=>z(i),i.id)}:{},{f:e.t(i.content),g:"03d35165-5-"+c+"-"+u+",03d35165-0",h:e.o(K=>L(i),i.id),i:i.id})),p:e.p({type:"chat",size:"12",color:"#999"}),q:m.value.get(o.id)},m.value.get(o.id)?{r:"03d35165-6-"+c+",03d35165-0",s:e.p({type:"spinner-cycle",size:"16",color:"#999"})}:{}):{},{t:o.id})),B:e.p({type:"chat",size:"14",color:"#999"}),C:F.value+"px",D:v.value},v.value?{E:e.t(v.value),F:e.p({type:"close",size:"16",color:"#999"}),G:e.o(G)}:{},{H:v.value?"输入回复内容...":"说点什么吧",I:e.o(j),J:e.o(H),K:e.o(E),L:r.value,M:e.o(o=>r.value=o.detail.value),N:r.value.trim()},r.value.trim()?{O:e.p({type:"paperplane",size:"18",color:"#fff"}),P:e.o(E)}:{},{Q:e.o(N),R:!x.value},x.value?{}:{S:e.p({type:"redo",size:"20",color:"#999"}),T:e.o(D),U:e.n(a.value.isLiked?"bani-dianzan-fill":"bani-dianzan"),V:a.value.isLiked?"#696CF3":"#999",W:e.t(a.value.likes),X:a.value.isLiked?"#696CF3":"#999",Y:e.o(Y)},{Z:e.p({title:"动态详情","show-back":!0})})}},ee=e._export_sfc($,[["__scopeId","data-v-03d35165"]]);$.__runtimeHooks=1;wx.createPage(ee);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesubs/moment/detail.js.map
