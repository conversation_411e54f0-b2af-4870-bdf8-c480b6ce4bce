package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.domain.activity.vo.ActSafeguardItemStorageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActSafeguardItemToActSafeguardItemStorageVoMapperImpl implements ActSafeguardItemToActSafeguardItemStorageVoMapper {

    @Override
    public ActSafeguardItemStorageVo convert(ActSafeguardItem arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ActSafeguardItemStorageVo actSafeguardItemStorageVo = new ActSafeguardItemStorageVo();

        actSafeguardItemStorageVo.setId( arg0.getId() );
        if ( arg0.getIcon() != null ) {
            actSafeguardItemStorageVo.setIcon( String.valueOf( arg0.getIcon() ) );
        }
        actSafeguardItemStorageVo.setName( arg0.getName() );
        actSafeguardItemStorageVo.setDes( arg0.getDes() );

        return actSafeguardItemStorageVo;
    }

    @Override
    public ActSafeguardItemStorageVo convert(ActSafeguardItem arg0, ActSafeguardItemStorageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        if ( arg0.getIcon() != null ) {
            arg1.setIcon( String.valueOf( arg0.getIcon() ) );
        }
        else {
            arg1.setIcon( null );
        }
        arg1.setName( arg0.getName() );
        arg1.setDes( arg0.getDes() );

        return arg1;
    }
}
