package com.gzhuxn.personals.controller.app.recommend.bo.activity;

import com.gzhuxn.personals.domain.activity.Activity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppRecommendActivityQueryBoToActivityMapperImpl implements AppRecommendActivityQueryBoToActivityMapper {

    @Override
    public Activity convert(AppRecommendActivityQueryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Activity activity = new Activity();

        activity.setId( arg0.getId() );
        activity.setGroupId( arg0.getGroupId() );
        activity.setName( arg0.getName() );
        activity.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        activity.setAddrCityCode( arg0.getAddrCityCode() );
        activity.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        activity.setAddrStreetCode( arg0.getAddrStreetCode() );
        activity.setLocation( arg0.getLocation() );
        activity.setOfficialFlag( arg0.getOfficialFlag() );
        activity.setStatus( arg0.getStatus() );
        activity.setType( arg0.getType() );
        activity.setLon( arg0.getLon() );
        activity.setLat( arg0.getLat() );

        return activity;
    }

    @Override
    public Activity convert(AppRecommendActivityQueryBo arg0, Activity arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
