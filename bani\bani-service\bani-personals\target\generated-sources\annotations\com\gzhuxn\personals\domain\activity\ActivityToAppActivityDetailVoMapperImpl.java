package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.vo.AppActivityDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppActivityDetailVoMapperImpl implements ActivityToAppActivityDetailVoMapper {

    @Override
    public AppActivityDetailVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppActivityDetailVo appActivityDetailVo = new AppActivityDetailVo();

        appActivityDetailVo.setId( arg0.getId() );
        appActivityDetailVo.setGroupId( arg0.getGroupId() );
        appActivityDetailVo.setName( arg0.getName() );
        appActivityDetailVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        appActivityDetailVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        appActivityDetailVo.setStartTime( arg0.getStartTime() );
        appActivityDetailVo.setEndTime( arg0.getEndTime() );
        appActivityDetailVo.setTimeLength( arg0.getTimeLength() );
        appActivityDetailVo.setRefundTime( arg0.getRefundTime() );
        appActivityDetailVo.setOfficialFlag( arg0.getOfficialFlag() );
        appActivityDetailVo.setIntroduce( arg0.getIntroduce() );
        appActivityDetailVo.setOriginalAmount( arg0.getOriginalAmount() );
        appActivityDetailVo.setAmount( arg0.getAmount() );
        appActivityDetailVo.setStatus( arg0.getStatus() );
        appActivityDetailVo.setAuditStatus( arg0.getAuditStatus() );
        appActivityDetailVo.setType( arg0.getType() );
        appActivityDetailVo.setLon( arg0.getLon() );
        appActivityDetailVo.setLat( arg0.getLat() );
        appActivityDetailVo.setCreateByName( arg0.getCreateByName() );

        return appActivityDetailVo;
    }

    @Override
    public AppActivityDetailVo convert(Activity arg0, AppActivityDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setIntroduce( arg0.getIntroduce() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setType( arg0.getType() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
