package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.activity.vo.AppDaziActivityPageVo;
import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppDaziActivityPageVoMapperImpl implements ActivityToAppDaziActivityPageVoMapper {

    @Override
    public AppDaziActivityPageVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppDaziActivityPageVo appDaziActivityPageVo = new AppDaziActivityPageVo();

        appDaziActivityPageVo.setId( arg0.getId() );
        appDaziActivityPageVo.setGroupId( arg0.getGroupId() );
        appDaziActivityPageVo.setName( arg0.getName() );
        appDaziActivityPageVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        appDaziActivityPageVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            appDaziActivityPageVo.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        appDaziActivityPageVo.setEndTime( arg0.getEndTime() );
        appDaziActivityPageVo.setTimeLength( arg0.getTimeLength() );
        appDaziActivityPageVo.setRefundTime( arg0.getRefundTime() );
        appDaziActivityPageVo.setOriginalAmount( arg0.getOriginalAmount() );
        appDaziActivityPageVo.setAmount( arg0.getAmount() );
        appDaziActivityPageVo.setStatus( arg0.getStatus() );
        appDaziActivityPageVo.setAuditStatus( arg0.getAuditStatus() );
        appDaziActivityPageVo.setType( arg0.getType() );
        appDaziActivityPageVo.setCreateByName( arg0.getCreateByName() );

        return appDaziActivityPageVo;
    }

    @Override
    public AppDaziActivityPageVo convert(Activity arg0, AppDaziActivityPageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        if ( arg0.getStartTime() != null ) {
            arg1.setStartTime( Date.from( arg0.getStartTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setStartTime( null );
        }
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setRefundTime( arg0.getRefundTime() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setType( arg0.getType() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
