package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.domain.message.vo.MsgGroupUserVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class MsgGroupUserToMsgGroupUserVoMapperImpl implements MsgGroupUserToMsgGroupUserVoMapper {

    @Override
    public MsgGroupUserVo convert(MsgGroupUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgGroupUserVo msgGroupUserVo = new MsgGroupUserVo();

        msgGroupUserVo.setId( arg0.getId() );
        msgGroupUserVo.setGroupId( arg0.getGroupId() );
        msgGroupUserVo.setUserId( arg0.getUserId() );
        msgGroupUserVo.setName( arg0.getName() );
        msgGroupUserVo.setType( arg0.getType() );
        msgGroupUserVo.setDisturb( arg0.getDisturb() );
        msgGroupUserVo.setStatus( arg0.getStatus() );

        return msgGroupUserVo;
    }

    @Override
    public MsgGroupUserVo convert(MsgGroupUser arg0, MsgGroupUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setName( arg0.getName() );
        arg1.setType( arg0.getType() );
        arg1.setDisturb( arg0.getDisturb() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
