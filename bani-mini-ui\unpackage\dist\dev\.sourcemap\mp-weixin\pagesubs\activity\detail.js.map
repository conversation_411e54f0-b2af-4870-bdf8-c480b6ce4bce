{"version": 3, "file": "detail.js", "sources": ["pagesubs/activity/detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcYWN0aXZpdHlcZGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"活动详情\" :show-back=\"true\">\r\n\t\t<template #content>\r\n\t\t\t<view class=\"page-container\">\r\n\t\t\t\t<!-- 活动图片 -->\r\n\t\t\t\t<image class=\"activity-image\" :src=\"activityDetail.image\" mode=\"aspectFill\"></image>\r\n\r\n\t\t\t\t<!-- 活动信息 -->\r\n\t\t\t\t<view class=\"activity-info\">\r\n\t\t\t\t\t<!-- 标题和状态 -->\r\n\t\t\t\t\t<view class=\"header\">\r\n\t\t\t\t\t\t<text class=\"title\">{{ activityDetail.title }}</text>\r\n\t\t\t\t\t\t<view class=\"status\" :class=\"activityDetail.status\">{{ getStatusText(activityDetail.status) }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 发起人信息（仅搭子活动显示） -->\r\n\t\t\t\t\t<view v-if=\"type === 'buddy'\" class=\"organizer\">\r\n\t\t\t\t\t\t<image class=\"avatar\" :src=\"activityDetail.avatar\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t<text class=\"name\">{{ activityDetail.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"type-tag\">{{ getTypeLabel(activityDetail.type) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 活动详情 -->\r\n\t\t\t\t\t<view class=\"detail-list\">\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"location\" size=\"16\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text class=\"label\">活动地点：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">{{ activityDetail.location }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"calendar\" size=\"16\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text class=\"label\">开始时间：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">{{ activityDetail.startTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"clock\" size=\"16\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text class=\"label\">活动时长：</text>\r\n\t\t\t\t\t\t\t<text class=\"value\">{{ activityDetail.duration }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"detail-item\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"wallet\" size=\"16\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text class=\"label\">活动费用：</text>\r\n\t\t\t\t\t\t\t<text class=\"value price\">¥{{ activityDetail.price }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 活动介绍 -->\r\n\t\t\t\t\t<view class=\"description\">\r\n\t\t\t\t\t\t<text class=\"section-title\">活动介绍</text>\r\n\t\t\t\t\t\t<text class=\"content\">{{ activityDetail.description }}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 报名须知 -->\r\n\t\t\t\t\t<view class=\"notice\">\r\n\t\t\t\t\t\t<text class=\"section-title\">报名须知</text>\r\n\t\t\t\t\t\t<view class=\"notice-list\">\r\n\t\t\t\t\t\t\t<view class=\"notice-item\" v-for=\"(item, index) in activityDetail.notices\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<text class=\"dot\">·</text>\r\n\t\t\t\t\t\t\t\t<text class=\"text\">{{ item }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 底部操作栏 -->\r\n\t\t\t\t<view class=\"bottom-bar\">\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<button class=\"share-btn\" open-type=\"share\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text>分享</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<button class=\"contact-btn\" @click=\"contactOrganizer\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"20\" color=\"$text-secondary\"></uni-icons>\r\n\t\t\t\t\t\t\t<text>联系</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t<button class=\"enroll-btn\" :class=\"{ disabled: !canEnroll }\" @click=\"handleEnroll\">\r\n\t\t\t\t\t\t\t{{ getEnrollButtonText() }}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onPageScroll } from '@dcloudio/uni-app'\r\n\r\n// 页面滚动距离\r\nconst pageScrollTop = ref(0)\r\n// 导航栏高度\r\nconst navBarHeight = ref(0)\r\n\r\n// 活动类型\r\nconst type = ref('official')\r\n// 活动ID\r\nconst id = ref(null)\r\n\r\n// 活动详情\r\nconst activityDetail = ref({\r\n\tid: 1,\r\n\ttitle: '2024春季相亲交友会',\r\n\timage: 'https://p3.toutiaoimg.com/large/tos-cn-i-qvj2lq49k0/b66de365c7614b24af852e21b4271976.jpg',\r\n\tname: '小明',\r\n\tavatar: '/static/image/avatar/1.png',\r\n\ttype: 1,\r\n\tlocation: '北京市朝阳区某咖啡厅',\r\n\tstartTime: '2024-04-20 14:00',\r\n\tduration: '3小时',\r\n\tprice: 99,\r\n\tstatus: 'enrolling',\r\n\tdescription: '本次活动旨在为单身人士提供一个轻松愉快的交友平台。通过精心设计的互动环节，帮助参与者更好地了解彼此，找到心仪的对象。',\r\n\tnotices: [\r\n\t\t'请准时到达活动地点',\r\n\t\t'活动期间请保持手机静音',\r\n\t\t'请着装整洁得体',\r\n\t\t'活动费用包含场地费和饮品'\r\n\t]\r\n})\r\n\r\n// 是否可以报名\r\nconst canEnroll = ref(true)\r\n\r\n// 获取状态文本\r\nconst getStatusText = (status) => {\r\n\tconst statusMap = {\r\n\t\tnotStarted: '未开始',\r\n\t\tenrolling: '报名中',\r\n\t\tenrollEnd: '报名已结束',\r\n\t\tinProgress: '活动进行中',\r\n\t\tended: '活动已结束'\r\n\t}\r\n\treturn statusMap[status] || ''\r\n}\r\n\r\n// 获取类型标签\r\nconst getTypeLabel = (type) => {\r\n\tconst typeMap = {\r\n\t\t1: '相亲会',\r\n\t\t2: '聊天',\r\n\t\t3: '干饭',\r\n\t\t4: '户外',\r\n\t\t5: '看展',\r\n\t\t6: '运动',\r\n\t\t7: '学习',\r\n\t\t8: '喝酒',\r\n\t\t9: '打游戏',\r\n\t\t10: '其他'\r\n\t}\r\n\treturn typeMap[type] || ''\r\n}\r\n\r\n// 获取报名按钮文本\r\nconst getEnrollButtonText = () => {\r\n\tconst status = activityDetail.value.status\r\n\tconst textMap = {\r\n\t\tnotStarted: '预约报名',\r\n\t\tenrolling: '立即报名',\r\n\t\tenrollEnd: '报名已结束',\r\n\t\tinProgress: '活动进行中',\r\n\t\tended: '活动已结束'\r\n\t}\r\n\treturn textMap[status] || '立即报名'\r\n}\r\n\r\n// 处理报名\r\nconst handleEnroll = () => {\r\n\tif (!canEnroll.value) return\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/activity/enroll/enroll?id=${activityDetail.value.id}`\r\n\t})\r\n}\r\n\r\n// 联系发起人\r\nconst contactOrganizer = () => {\r\n\tuni.navigateTo({\r\n\t\turl: `/pages/message/chat/chat?id=${activityDetail.value.id}&name=${activityDetail.value.name}`\r\n\t})\r\n}\r\n\r\n// 返回上一页\r\nconst goBack = () => {\r\n\tuni.navigateBack()\r\n}\r\n\r\n// 分享\r\nconst onShareAppMessage = () => {\r\n\treturn {\r\n\t\ttitle: activityDetail.value.title,\r\n\t\tpath: `/pages/activity/detail/detail?id=${activityDetail.value.id}&type=${type.value}`,\r\n\t\timageUrl: activityDetail.value.image\r\n\t}\r\n}\r\n\r\n// 处理页面滚动\r\nconst handlePageScroll = (e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n}\r\n\r\n// 处理导航栏高度变化\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\n// 计算导航栏图标颜色\r\nconst getNavIconColor = () => {\r\n\tconst opacity = Math.min(pageScrollTop.value / 100, 1)\r\n\treturn opacity > 0.5 ? '#333333' : '#ffffff'\r\n}\r\n\r\n// 页面滚动监听\r\nonPageScroll(handlePageScroll)\r\n\r\n// 页面加载\r\nonMounted((options) => {\r\n\tid.value = options.id\r\n\ttype.value = options.type\r\n\t// TODO: 根据id和type获取活动详情\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-container {\r\n\tmin-height: 100vh;\r\n\tbackground: #f5f5f5;\r\n\tpadding-bottom: 120rpx;\r\n\tposition: relative;\r\n}\r\n\r\n.nav-back-btn {\r\n\twidth: 88rpx;\r\n\theight: 88rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: rgba(0, 0, 0, 0.3);\r\n\tborder-radius: 50%;\r\n\tmargin-left: 20rpx;\r\n}\r\n\r\n.activity-image {\r\n\twidth: 100%;\r\n\theight: 400rpx;\r\n}\r\n\r\n.activity-info {\r\n\tbackground: #fff;\r\n\tpadding: 30rpx;\r\n\tmargin-top: -30rpx;\r\n\tborder-radius: 30rpx 30rpx 0 0;\r\n\tposition: relative;\r\n\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t.title {\r\n\t\t\tfont-size: $font-size-xl;\r\n\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\tcolor: $text-primary;\r\n\t\t\tflex: 1;\r\n\t\t\tmargin-right: $spacing-md;\r\n\t\t}\r\n\r\n\t\t.status {\r\n\t\t\tfont-size: $font-size-xs;\r\n\t\t\tpadding: $spacing-xs $spacing-md;\r\n\t\t\tborder-radius: $radius-full;\r\n\r\n\t\t\t&.notStarted {\r\n\t\t\t\tbackground: $primary-light;\r\n\t\t\t\tcolor: $primary-color;\r\n\t\t\t}\r\n\r\n\t\t\t&.enrolling {\r\n\t\t\t\tbackground: $success-light;\r\n\t\t\t\tcolor: $success-color;\r\n\t\t\t}\r\n\r\n\t\t\t&.enrollEnd {\r\n\t\t\t\tbackground: $warning-light;\r\n\t\t\t\tcolor: $warning-color;\r\n\t\t\t}\r\n\r\n\t\t\t&.inProgress {\r\n\t\t\t\tbackground: $info-light;\r\n\t\t\t\tcolor: $info-color;\r\n\t\t\t}\r\n\r\n\t\t\t&.ended {\r\n\t\t\t\tbackground: rgba(0, 0, 0, 0.1);\r\n\t\t\t\tcolor: $text-tertiary;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.organizer {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: $spacing-lg;\r\n\t\tpadding: $spacing-md;\r\n\t\tbackground: $primary-lighter;\r\n\t\tborder-radius: $radius-md;\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tmargin-right: $spacing-md;\r\n\t\t}\r\n\r\n\t\t.user-info {\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\tcolor: $text-primary;\r\n\t\t\t\tfont-weight: $font-weight-medium;\r\n\t\t\t\tmargin-bottom: $spacing-xs;\r\n\t\t\t}\r\n\r\n\t\t\t.type-tag {\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tcolor: $primary-color;\r\n\t\t\t\tbackground: $primary-light;\r\n\t\t\t\tpadding: $spacing-xs $spacing-sm;\r\n\t\t\t\tborder-radius: $radius-full;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.detail-list {\r\n\t\tmargin-bottom: $spacing-lg;\r\n\r\n\t\t.detail-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: $spacing-md;\r\n\r\n\t\t\t.label {\r\n\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\tmargin: 0 $spacing-xs;\r\n\t\t\t}\r\n\r\n\t\t\t.value {\r\n\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\tcolor: $text-primary;\r\n\r\n\t\t\t\t&.price {\r\n\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.description,\r\n\t.notice {\r\n\t\tmargin-bottom: $spacing-lg;\r\n\r\n\t\t.section-title {\r\n\t\t\tfont-size: $font-size-lg;\r\n\t\t\tfont-weight: $font-weight-bold;\r\n\t\t\tcolor: $text-primary;\r\n\t\t\tmargin-bottom: $spacing-md;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\r\n\t\t.content {\r\n\t\t\tfont-size: $font-size-md;\r\n\t\t\tcolor: $text-secondary;\r\n\t\t\tline-height: 1.6;\r\n\t\t}\r\n\r\n\t\t.notice-list {\r\n\t\t\t.notice-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tmargin-bottom: $spacing-md;\r\n\r\n\t\t\t\t.dot {\r\n\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\tmargin-right: $spacing-xs;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text {\r\n\t\t\t\t\tfont-size: $font-size-md;\r\n\t\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\t\tline-height: 1.6;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.bottom-bar {\r\n\tposition: fixed;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\theight: 100rpx;\r\n\tbackground: $bg-primary;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 0 $spacing-lg;\r\n\tbox-shadow: $shadow-sm;\r\n\r\n\t.left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\tbutton {\r\n\t\t\tbackground: none;\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin: 0;\r\n\t\t\tline-height: 1;\r\n\t\t\tborder: none;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-right: $spacing-xl;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: $font-size-xs;\r\n\t\t\t\tcolor: $text-secondary;\r\n\t\t\t\tmargin-top: $spacing-xs;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.right {\r\n\t\t.enroll-btn {\r\n\t\t\tbackground: $primary-gradient;\r\n\t\t\tcolor: $text-white;\r\n\t\t\tfont-size: $font-size-md;\r\n\t\t\tpadding: $spacing-md $spacing-xl;\r\n\t\t\tborder-radius: $radius-full;\r\n\t\t\tborder: none;\r\n\r\n\t\t\t&.disabled {\r\n\t\t\t\tbackground: $text-tertiary;\r\n\t\t\t}\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/activity/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "type", "id", "activityDetail", "canEnroll", "getStatusText", "status", "getTypeLabel", "getEnrollButtonText", "handleEnroll", "uni", "contactOrganizer", "handlePageScroll", "e", "onPageScroll", "onMounted", "options", "MiniProgramPage"], "mappings": "kVA+FA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EAEAA,EAAA,IAAA,CAAA,EAGA,MAAAC,EAAAD,EAAA,IAAA,UAAA,EAEAE,EAAAF,EAAA,IAAA,IAAA,EAGAG,EAAAH,EAAAA,IAAA,CACA,GAAA,EACA,MAAA,cACA,MAAA,2FACA,KAAA,KACA,OAAA,6BACA,KAAA,EACA,SAAA,aACA,UAAA,mBACA,SAAA,MACA,MAAA,GACA,OAAA,YACA,YAAA,6DACA,QAAA,CACA,YACA,cACA,UACA,cACA,CACA,CAAA,EAGAI,EAAAJ,EAAA,IAAA,EAAA,EAGAK,EAAAC,IACA,CACA,WAAA,MACA,UAAA,MACA,UAAA,QACA,WAAA,QACA,MAAA,OACA,GACAA,CAAA,GAAA,GAIAC,EAAAN,IACA,CACA,EAAA,MACA,EAAA,KACA,EAAA,KACA,EAAA,KACA,EAAA,KACA,EAAA,KACA,EAAA,KACA,EAAA,KACA,EAAA,MACA,GAAA,IACA,GACAA,CAAA,GAAA,GAIAO,EAAA,IAAA,CACA,MAAAF,EAAAH,EAAA,MAAA,OAQA,MAPA,CACA,WAAA,OACA,UAAA,OACA,UAAA,QACA,WAAA,QACA,MAAA,OACA,EACAG,CAAA,GAAA,MACA,EAGAG,EAAA,IAAA,CACAL,EAAA,OACAM,EAAAA,MAAA,WAAA,CACA,IAAA,oCAAAP,EAAA,MAAA,EAAA,EACA,CAAA,CACA,EAGAQ,EAAA,IAAA,CACAD,EAAAA,MAAA,WAAA,CACA,IAAA,+BAAAP,EAAA,MAAA,EAAA,SAAAA,EAAA,MAAA,IAAA,EACA,CAAA,CACA,EAiBAS,EAAAC,GAAA,CACAd,EAAA,MAAAc,EAAA,SACA,EAcAC,OAAAA,EAAA,aAAAF,CAAA,EAGAG,EAAA,UAAAC,GAAA,CACAd,EAAA,MAAAc,EAAA,GACAf,EAAA,MAAAe,EAAA,IAEA,CAAA,u2BC/NA,GAAG,WAAWC,CAAe"}