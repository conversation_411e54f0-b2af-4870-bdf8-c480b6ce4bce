package com.gzhuxn.personals.controller.app.message.bo;

import com.gzhuxn.personals.domain.message.MsgContentUser;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppMsgContentUserReadBoToMsgContentUserMapperImpl implements AppMsgContentUserReadBoToMsgContentUserMapper {

    @Override
    public MsgContentUser convert(AppMsgContentUserReadBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContentUser msgContentUser = new MsgContentUser();

        msgContentUser.setUserId( arg0.getUserId() );

        return msgContentUser;
    }

    @Override
    public MsgContentUser convert(AppMsgContentUserReadBo arg0, MsgContentUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
