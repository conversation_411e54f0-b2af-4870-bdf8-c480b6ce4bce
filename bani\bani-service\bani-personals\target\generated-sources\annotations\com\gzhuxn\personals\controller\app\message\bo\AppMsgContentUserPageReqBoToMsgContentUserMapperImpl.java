package com.gzhuxn.personals.controller.app.message.bo;

import com.gzhuxn.personals.domain.message.MsgContentUser;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppMsgContentUserPageReqBoToMsgContentUserMapperImpl implements AppMsgContentUserPageReqBoToMsgContentUserMapper {

    @Override
    public MsgContentUser convert(AppMsgContentUserPageReqBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        MsgContentUser msgContentUser = new MsgContentUser();

        msgContentUser.setUserId( arg0.getUserId() );
        msgContentUser.setId( arg0.getId() );
        msgContentUser.setGroupId( arg0.getGroupId() );
        msgContentUser.setSendUserId( arg0.getSendUserId() );
        msgContentUser.setType( arg0.getType() );
        msgContentUser.setSubType( arg0.getSubType() );

        return msgContentUser;
    }

    @Override
    public MsgContentUser convert(AppMsgContentUserPageReqBo arg0, MsgContentUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setSendUserId( arg0.getSendUserId() );
        arg1.setType( arg0.getType() );
        arg1.setSubType( arg0.getSubType() );

        return arg1;
    }
}
