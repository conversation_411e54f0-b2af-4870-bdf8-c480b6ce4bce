package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserWechatApply;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface UserWechatApplyBoToUserWechatApplyMapper extends BaseMapper<UserWechatApplyBo, UserWechatApply> {
}
