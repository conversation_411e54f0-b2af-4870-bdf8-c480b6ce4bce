package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.vo.ManageRechargeVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageRechargeToManageRechargeVoMapperImpl implements ManageRechargeToManageRechargeVoMapper {

    @Override
    public ManageRechargeVo convert(ManageRecharge arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageRechargeVo manageRechargeVo = new ManageRechargeVo();

        manageRechargeVo.setId( arg0.getId() );
        manageRechargeVo.setOriginalAmount( arg0.getOriginalAmount() );
        manageRechargeVo.setAmount( arg0.getAmount() );
        manageRechargeVo.setDiscountRate( arg0.getDiscountRate() );
        manageRechargeVo.setDiscountType( arg0.getDiscountType() );
        manageRechargeVo.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        manageRechargeVo.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        manageRechargeVo.setRechargeNum( arg0.getRechargeNum() );
        manageRechargeVo.setCoin( arg0.getCoin() );

        return manageRechargeVo;
    }

    @Override
    public ManageRechargeVo convert(ManageRecharge arg0, ManageRechargeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setDiscountRate( arg0.getDiscountRate() );
        arg1.setDiscountType( arg0.getDiscountType() );
        arg1.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        arg1.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        arg1.setRechargeNum( arg0.getRechargeNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
