package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserWithdrawBoToUserWithdrawMapper;
import com.gzhuxn.personals.domain.user.vo.UserWithdrawVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserWithdrawBoToUserWithdrawMapper.class},
    imports = {}
)
public interface UserWithdrawToUserWithdrawVoMapper extends BaseMapper<UserWithdraw, UserWithdrawVo> {
}
