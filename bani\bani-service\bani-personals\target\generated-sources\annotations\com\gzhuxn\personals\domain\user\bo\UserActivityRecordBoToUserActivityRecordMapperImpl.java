package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserActivityRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserActivityRecordBoToUserActivityRecordMapperImpl implements UserActivityRecordBoToUserActivityRecordMapper {

    @Override
    public UserActivityRecord convert(UserActivityRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserActivityRecord userActivityRecord = new UserActivityRecord();

        userActivityRecord.setSearchValue( arg0.getSearchValue() );
        userActivityRecord.setCreateBy( arg0.getCreateBy() );
        userActivityRecord.setCreateTime( arg0.getCreateTime() );
        userActivityRecord.setUpdateBy( arg0.getUpdateBy() );
        userActivityRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userActivityRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userActivityRecord.setCreateDept( arg0.getCreateDept() );
        userActivityRecord.setId( arg0.getId() );
        userActivityRecord.setUserId( arg0.getUserId() );
        userActivityRecord.setOrderId( arg0.getOrderId() );
        userActivityRecord.setOriginalAmount( arg0.getOriginalAmount() );
        userActivityRecord.setAmount( arg0.getAmount() );
        userActivityRecord.setCoin( arg0.getCoin() );
        userActivityRecord.setPayTime( arg0.getPayTime() );
        userActivityRecord.setPayStatus( arg0.getPayStatus() );
        userActivityRecord.setActivityId( arg0.getActivityId() );
        userActivityRecord.setSignIn( arg0.getSignIn() );
        userActivityRecord.setSignInTime( arg0.getSignInTime() );
        userActivityRecord.setSignInAddr( arg0.getSignInAddr() );
        userActivityRecord.setSignInLon( arg0.getSignInLon() );
        userActivityRecord.setSignInLat( arg0.getSignInLat() );

        return userActivityRecord;
    }

    @Override
    public UserActivityRecord convert(UserActivityRecordBo arg0, UserActivityRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );
        arg1.setActivityId( arg0.getActivityId() );
        arg1.setSignIn( arg0.getSignIn() );
        arg1.setSignInTime( arg0.getSignInTime() );
        arg1.setSignInAddr( arg0.getSignInAddr() );
        arg1.setSignInLon( arg0.getSignInLon() );
        arg1.setSignInLat( arg0.getSignInLat() );

        return arg1;
    }
}
