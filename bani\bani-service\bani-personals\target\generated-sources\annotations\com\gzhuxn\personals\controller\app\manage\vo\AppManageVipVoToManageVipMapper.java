package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageVip;
import com.gzhuxn.personals.domain.manage.ManageVipToAppManageVipVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageVipToAppManageVipVoMapper.class},
    imports = {}
)
public interface AppManageVipVoToManageVipMapper extends BaseMapper<AppManageVipVo, ManageVip> {
}
