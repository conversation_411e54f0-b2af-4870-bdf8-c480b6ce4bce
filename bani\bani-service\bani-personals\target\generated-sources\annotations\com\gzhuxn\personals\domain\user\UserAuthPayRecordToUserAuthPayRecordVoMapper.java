package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserAuthPayRecordBoToUserAuthPayRecordMapper;
import com.gzhuxn.personals.domain.user.vo.UserAuthPayRecordVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAuthPayRecordBoToUserAuthPayRecordMapper.class},
    imports = {}
)
public interface UserAuthPayRecordToUserAuthPayRecordVoMapper extends BaseMapper<UserAuthPayRecord, UserAuthPayRecordVo> {
}
