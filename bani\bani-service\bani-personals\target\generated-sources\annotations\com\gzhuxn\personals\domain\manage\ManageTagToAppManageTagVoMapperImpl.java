package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageTagToAppManageTagVoMapperImpl implements ManageTagToAppManageTagVoMapper {

    @Override
    public AppManageTagVo convert(ManageTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppManageTagVo appManageTagVo = new AppManageTagVo();

        appManageTagVo.setId( arg0.getId() );
        appManageTagVo.setName( arg0.getName() );
        appManageTagVo.setIcon( arg0.getIcon() );

        return appManageTagVo;
    }

    @Override
    public AppManageTagVo convert(ManageTag arg0, AppManageTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );

        return arg1;
    }
}
