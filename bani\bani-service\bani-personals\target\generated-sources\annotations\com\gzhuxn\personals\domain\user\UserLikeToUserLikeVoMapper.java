package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.AppUserLikeCreateBoToUserLikeMapper;
import com.gzhuxn.personals.controller.app.user.vo.AppUserLikePageResultVoToUserLikeMapper;
import com.gzhuxn.personals.domain.user.bo.UserLikeBoToUserLikeMapper;
import com.gzhuxn.personals.domain.user.vo.UserLikeVo;
import com.gzhuxn.personals.domain.user.vo.UserLikeVoToUserLikeMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserLikeVoToUserLikeMapper.class,AppUserLikeCreateBoToUserLikeMapper.class,UserLikeBoToUserLikeMapper.class,AppUserLikePageResultVoToUserLikeMapper.class,UserLikeToAppUserLikePageResultVoMapper.class},
    imports = {}
)
public interface UserLikeToUserLikeVoMapper extends BaseMapper<UserLike, UserLikeVo> {
}
