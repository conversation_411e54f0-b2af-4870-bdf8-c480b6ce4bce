package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserRechargeRecord;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserRechargeRecordBoToUserRechargeRecordMapperImpl implements UserRechargeRecordBoToUserRechargeRecordMapper {

    @Override
    public UserRechargeRecord convert(UserRechargeRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserRechargeRecord userRechargeRecord = new UserRechargeRecord();

        userRechargeRecord.setSearchValue( arg0.getSearchValue() );
        userRechargeRecord.setCreateBy( arg0.getCreateBy() );
        userRechargeRecord.setCreateTime( arg0.getCreateTime() );
        userRechargeRecord.setUpdateBy( arg0.getUpdateBy() );
        userRechargeRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userRechargeRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userRechargeRecord.setCreateDept( arg0.getCreateDept() );
        userRechargeRecord.setId( arg0.getId() );
        userRechargeRecord.setUserId( arg0.getUserId() );
        userRechargeRecord.setOrderId( arg0.getOrderId() );
        userRechargeRecord.setOriginalAmount( arg0.getOriginalAmount() );
        userRechargeRecord.setAmount( arg0.getAmount() );
        userRechargeRecord.setCoin( arg0.getCoin() );
        userRechargeRecord.setPayTime( arg0.getPayTime() );
        userRechargeRecord.setPayStatus( arg0.getPayStatus() );
        userRechargeRecord.setManageId( arg0.getManageId() );

        return userRechargeRecord;
    }

    @Override
    public UserRechargeRecord convert(UserRechargeRecordBo arg0, UserRechargeRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );
        arg1.setManageId( arg0.getManageId() );

        return arg1;
    }
}
