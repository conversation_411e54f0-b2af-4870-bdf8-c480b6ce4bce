package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.admin.audit.vo.AdminContentAuditUserDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAdminContentAuditUserDetailVoMapperImpl implements UserDetailToAdminContentAuditUserDetailVoMapper {

    @Override
    public AdminContentAuditUserDetailVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminContentAuditUserDetailVo adminContentAuditUserDetailVo = new AdminContentAuditUserDetailVo();

        adminContentAuditUserDetailVo.setUserId( arg0.getUserId() );
        adminContentAuditUserDetailVo.setNickName( arg0.getNickName() );
        adminContentAuditUserDetailVo.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            adminContentAuditUserDetailVo.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        adminContentAuditUserDetailVo.setPid( arg0.getPid() );
        adminContentAuditUserDetailVo.setBirthday( arg0.getBirthday() );
        adminContentAuditUserDetailVo.setStar( arg0.getStar() );
        adminContentAuditUserDetailVo.setAnimal( arg0.getAnimal() );
        adminContentAuditUserDetailVo.setHeight( arg0.getHeight() );
        adminContentAuditUserDetailVo.setWeight( arg0.getWeight() );
        adminContentAuditUserDetailVo.setEdu( arg0.getEdu() );
        adminContentAuditUserDetailVo.setJob( arg0.getJob() );
        adminContentAuditUserDetailVo.setRevenue( arg0.getRevenue() );
        adminContentAuditUserDetailVo.setWechat( arg0.getWechat() );
        adminContentAuditUserDetailVo.setAddr( arg0.getAddr() );
        adminContentAuditUserDetailVo.setAddrNew( arg0.getAddrNew() );
        adminContentAuditUserDetailVo.setProgress( arg0.getProgress() );
        adminContentAuditUserDetailVo.setAuditStatus( arg0.getAuditStatus() );
        adminContentAuditUserDetailVo.setIsIdentity( arg0.getIsIdentity() );
        adminContentAuditUserDetailVo.setUserLevel( arg0.getUserLevel() );
        adminContentAuditUserDetailVo.setIsMatched( arg0.getIsMatched() );
        adminContentAuditUserDetailVo.setStatus( arg0.getStatus() );

        return adminContentAuditUserDetailVo;
    }

    @Override
    public AdminContentAuditUserDetailVo convert(UserDetail arg0, AdminContentAuditUserDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        if ( arg0.getAvatar() != null ) {
            arg1.setAvatar( String.valueOf( arg0.getAvatar() ) );
        }
        else {
            arg1.setAvatar( null );
        }
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setIsIdentity( arg0.getIsIdentity() );
        arg1.setUserLevel( arg0.getUserLevel() );
        arg1.setIsMatched( arg0.getIsMatched() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
