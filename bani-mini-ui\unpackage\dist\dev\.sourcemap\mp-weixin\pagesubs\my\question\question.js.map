{"version": 3, "file": "question.js", "sources": ["pagesubs/my/question/question.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlccXVlc3Rpb25ccXVlc3Rpb24udnVl"], "sourcesContent": ["<template>\n\t<scroll-nav-page title=\"我的问答\" :show-back=\"true\">\n\t\t<template #content>\n\t\t\t<view class=\"page-container\">\n\t\t\t\t<!-- 主要内容 -->\n\t\t\t\t<view class=\"main-container\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\n\t\t\t\t\t<!-- 顶部菜单 -->\n\t\t\t\t\t<view class=\"top-menu\">\n\t\t\t\t\t\t<uni-segmented-control :current=\"segmentedIndex\" :values=\"['我的提问', '我的回答']\"\n\t\t\t\t\t\t\t@clickItem=\"switchTab\" styleType=\"text\" activeColor=\"#696CF3\"></uni-segmented-control>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- z-paging分页组件 -->\n\t\t\t\t\t<z-paging ref=\"paging\" v-model=\"datas\" :fixed=\"false\" :use-page-scroll=\"false\" @query=\"queryList\">\n\n\t\t\t\t\t\t<!-- 问答记录列表 -->\n\t\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t\t<view class=\"question-item\" v-for=\"(item, index) in datas\" :key=\"item.id\">\n\t\t\t\t\t\t\t\t<view class=\"question-header\">\n\t\t\t\t\t\t\t\t\t<view class=\"question-type\">\n\t\t\t\t\t\t\t\t\t\t<uni-icons :type=\"segmentedIndex === 0 ? 'help' : 'chatbubble'\" size=\"16\"\n\t\t\t\t\t\t\t\t\t\t\t:color=\"segmentedIndex === 0 ? '#ff6b6b' : '#4ecb73'\">\n\t\t\t\t\t\t\t\t\t\t</uni-icons>\n\t\t\t\t\t\t\t\t\t\t<text class=\"type-text\">{{ segmentedIndex === 0 ? '提问' : '回答' }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<text class=\"create-time\">{{ item.createTime }}</text>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<view class=\"question-content\">\n\t\t\t\t\t\t\t\t\t<text class=\"question-title\">{{ item.title || item.question }}</text>\n\t\t\t\t\t\t\t\t\t<text class=\"question-desc\" v-if=\"item.content\">{{ item.content }}</text>\n\t\t\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t\t\t<view class=\"question-footer\">\n\t\t\t\t\t\t\t\t\t<view class=\"stats\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"stat-text\">{{ item.viewCount || 0 }}次查看</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"segmentedIndex === 0\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"chatbubble\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"stat-text\">{{ item.answerCount || 0 }}个回答</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"item.isResolved\">\n\t\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"checkmarkempty\" size=\"14\" color=\"#4ecb73\"></uni-icons>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"stat-text resolved\">已解决</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"action-btn\" @click=\"viewDetail(item)\">\n\t\t\t\t\t\t\t\t\t\t<text>查看详情</text>\n\t\t\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"12\" color=\"#696CF3\"></uni-icons>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t\t<template #empty>\n\t\t\t\t\t\t\t<view class=\"empty-state\">\n\t\t\t\t\t\t\t\t<view class=\"empty-icon\">\n\t\t\t\t\t\t\t\t\t<uni-icons :type=\"segmentedIndex === 0 ? 'help' : 'chatbubble'\" size=\"80\"\n\t\t\t\t\t\t\t\t\t\tcolor=\"#ddd\">\n\t\t\t\t\t\t\t\t\t</uni-icons>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"empty-text\">{{ segmentedIndex === 0 ? '暂无提问记录' : '暂无回答记录' }}</text>\n\t\t\t\t\t\t\t\t<text class=\"empty-desc\">{{ segmentedIndex === 0 ? '您还没有提出过问题' : '您还没有回答过问题' }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</z-paging>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</scroll-nav-page>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\nimport { onLoad, onPageScroll } from \"@dcloudio/uni-app\"\n\n// 导航栏相关\nconst pageScrollTop = ref(0)\nconst navBarHeight = ref(0)\n\n// 计算属性\nconst getNavTextColor = computed(() => {\n\treturn pageScrollTop.value > 50 ? '#333' : '#fff'\n})\n\n// 页面数据\nconst segmentedIndex = ref(0)\nconst type = ref('')\n\n// z-paging组件\nconst paging = ref(null)\nconst datas = ref([])\n\n// 页面滚动监听\nonPageScroll((e) => {\n\tpageScrollTop.value = e.scrollTop\n})\n\n// 导航栏高度变化处理\nconst handleNavHeightChange = (height) => {\n\tnavBarHeight.value = height\n}\n\nonLoad((param) => {\n\tif (!param.type) {\n\t\tparam.type = 1\n\t}\n\tsegmentedIndex.value = parseInt(param.type) - 1\n\ttype.value = param.type\n})\n\nfunction queryList(pageNum, pageSize) {\n\t// TODO: 调用问答记录API\n\t// getQuestionAnswerPage({\n\t// \tpageNum: pageNum,\n\t// \tpageSize: pageSize,\n\t// \ttype: type.value,\n\t// }).then(res => {\n\t// \tpaging.value.complete(res.rows);\n\t// }).catch(() => {\n\t// \tpaging.value.complete(false);\n\t// })\n\n\t// 模拟数据\n\tconst mockData = [\n\t\t{\n\t\t\tid: 1,\n\t\t\ttitle: '如何提高个人魅力？',\n\t\t\tcontent: '想要在交友中更有吸引力，有什么好的建议吗？',\n\t\t\tcreateTime: '2024-01-15 14:30',\n\t\t\tviewCount: 25,\n\t\t\tanswerCount: 3,\n\t\t\tisResolved: true\n\t\t},\n\t\t{\n\t\t\tid: 2,\n\t\t\ttitle: '第一次约会应该注意什么？',\n\t\t\tcontent: '马上要和心仪的人第一次见面，有点紧张，求建议！',\n\t\t\tcreateTime: '2024-01-14 10:20',\n\t\t\tviewCount: 18,\n\t\t\tanswerCount: 5,\n\t\t\tisResolved: false\n\t\t}\n\t]\n\n\tsetTimeout(() => {\n\t\tpaging.value.complete(pageNum === 1 ? mockData : [])\n\t}, 500)\n}\n\nconst switchTab = (e) => {\n\tif (segmentedIndex.value !== e.currentIndex) {\n\t\tsegmentedIndex.value = e.currentIndex\n\t\ttype.value = segmentedIndex.value + 1\n\t\tpaging.value.reload()\n\t}\n}\n\nconst viewDetail = (item) => {\n\t// TODO: 跳转到问答详情页面\n\tuni.navigateTo({\n\t\turl: `/pagesubs/my/question/detail?id=${item.id}&type=${type.value}`\n\t})\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n\n.page-container {\n\tmin-height: 100vh;\n\tbackground: #f5f5f5;\n}\n\n.main-container {\n\tmin-height: 100vh;\n\tbox-sizing: border-box;\n\tpadding: 0 20rpx 120rpx;\n}\n\n// z-paging组件样式\n:deep(.z-paging-content) {\n\tmin-height: calc(100vh - 200px);\n}\n\n.top-menu {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tpadding: 24rpx 32rpx;\n\tmargin: 20rpx 0 16rpx;\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\n\t:deep(.uni-segmented-control) {\n\t\tbackground-color: transparent;\n\n\t\t.segmented-control__item {\n\t\t\tcolor: #666;\n\t\t\tfont-weight: 500;\n\n\t\t\t&.segmented-control__item--button--active {\n\t\t\t\tcolor: $primary-color;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.content {\n\tbackground: rgba(255, 255, 255, 0.95);\n\tborder-radius: 16rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\toverflow: hidden;\n\n\t.question-item {\n\t\tpadding: 32rpx 24rpx;\n\t\tborder-bottom: 1rpx solid rgba($primary-color, 0.08);\n\t\ttransition: all 0.3s ease;\n\n\t\t&:last-child {\n\t\t\tborder-bottom: none;\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground: rgba($primary-color, 0.02);\n\t\t}\n\n\t\t.question-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 16rpx;\n\n\t\t\t.question-type {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tbackground: rgba($primary-color, 0.1);\n\n\t\t\t\t.type-text {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tmargin-left: 8rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.create-time {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t}\n\t\t}\n\n\t\t.question-content {\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.question-title {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 600;\n\t\t\t\tcolor: #333;\n\t\t\t\tline-height: 1.5;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t}\n\n\t\t\t.question-desc {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tline-height: 1.6;\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\n\t\t.question-footer {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\n\t\t\t.stats {\n\t\t\t\tdisplay: flex;\n\t\t\t\tgap: 24rpx;\n\n\t\t\t\t.stat-item {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.stat-text {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\tmargin-left: 6rpx;\n\n\t\t\t\t\t\t&.resolved {\n\t\t\t\t\t\t\tcolor: #4ecb73;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.action-btn {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 8rpx 16rpx;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tbackground: rgba($primary-color, 0.1);\n\t\t\t\tborder: 1rpx solid rgba($primary-color, 0.2);\n\t\t\t\ttransition: all 0.3s ease;\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\tbackground: rgba($primary-color, 0.15);\n\t\t\t\t}\n\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: $primary-color;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tmargin-right: 6rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// 空状态样式\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 120rpx 40rpx;\n\n\t.empty-icon {\n\t\twidth: 160rpx;\n\t\theight: 160rpx;\n\t\tmargin-bottom: 32rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: rgba($primary-color, 0.05);\n\t\tborder-radius: 50%;\n\t\tborder: 2rpx dashed rgba($primary-color, 0.2);\n\t}\n\n\t.empty-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t\tfont-weight: 600;\n\t\tmargin-bottom: 12rpx;\n\t}\n\n\t.empty-desc {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\ttext-align: center;\n\t\tline-height: 1.5;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/question/question.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "computed", "segmentedIndex", "type", "paging", "datas", "onPageScroll", "e", "onLoad", "param", "queryList", "pageNum", "pageSize", "mockData", "switchTab", "viewDetail", "item", "uni", "MiniProgramPage"], "mappings": "umBAgFA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAA,SAAA,IACAH,EAAA,MAAA,GAAA,OAAA,MACA,EAGA,MAAAI,EAAAH,EAAA,IAAA,CAAA,EACAI,EAAAJ,EAAA,IAAA,EAAA,EAGAK,EAAAL,EAAA,IAAA,IAAA,EACAM,EAAAN,EAAA,IAAA,EAAA,EAGAO,EAAA,aAAAC,GAAA,CACAT,EAAA,MAAAS,EAAA,SACA,CAAA,EAOAC,EAAA,OAAAC,GAAA,CACAA,EAAA,OACAA,EAAA,KAAA,GAEAP,EAAA,MAAA,SAAAO,EAAA,IAAA,EAAA,EACAN,EAAA,MAAAM,EAAA,IACA,CAAA,EAEA,SAAAC,EAAAC,EAAAC,EAAA,CAaA,MAAAC,EAAA,CACA,CACA,GAAA,EACA,MAAA,YACA,QAAA,wBACA,WAAA,mBACA,UAAA,GACA,YAAA,EACA,WAAA,EACA,EACA,CACA,GAAA,EACA,MAAA,eACA,QAAA,0BACA,WAAA,mBACA,UAAA,GACA,YAAA,EACA,WAAA,EACA,CACA,EAEA,WAAA,IAAA,CACAT,EAAA,MAAA,SAAAO,IAAA,EAAAE,EAAA,EAAA,CACA,EAAA,GAAA,CACA,CAEA,MAAAC,EAAAP,GAAA,CACAL,EAAA,QAAAK,EAAA,eACAL,EAAA,MAAAK,EAAA,aACAJ,EAAA,MAAAD,EAAA,MAAA,EACAE,EAAA,MAAA,OAAA,EAEA,EAEAW,EAAAC,GAAA,CAEAC,EAAAA,MAAA,WAAA,CACA,IAAA,mCAAAD,EAAA,EAAA,SAAAb,EAAA,KAAA,EACA,CAAA,CACA,svCCrKA,GAAG,WAAWe,CAAe"}