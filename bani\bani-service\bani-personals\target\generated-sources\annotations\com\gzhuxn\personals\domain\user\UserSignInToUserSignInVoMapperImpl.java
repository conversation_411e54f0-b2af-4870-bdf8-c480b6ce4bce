package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserSignInVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserSignInToUserSignInVoMapperImpl implements UserSignInToUserSignInVoMapper {

    @Override
    public UserSignInVo convert(UserSignIn arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserSignInVo userSignInVo = new UserSignInVo();

        userSignInVo.setId( arg0.getId() );
        userSignInVo.setUserId( arg0.getUserId() );
        userSignInVo.setDate( arg0.getDate() );
        userSignInVo.setConsecutiveDays( arg0.getConsecutiveDays() );
        userSignInVo.setCoin( arg0.getCoin() );

        return userSignInVo;
    }

    @Override
    public UserSignInVo convert(UserSignIn arg0, UserSignInVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setDate( arg0.getDate() );
        arg1.setConsecutiveDays( arg0.getConsecutiveDays() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
