package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserDetail;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserDetailBoToUserDetailMapperImpl implements AppUserDetailBoToUserDetailMapper {

    @Override
    public UserDetail convert(AppUserDetailBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetail userDetail = new UserDetail();

        userDetail.setUserId( arg0.getUserId() );
        userDetail.setStar( arg0.getStar() );
        userDetail.setAnimal( arg0.getAnimal() );
        userDetail.setWeight( arg0.getWeight() );
        userDetail.setEdu( arg0.getEdu() );
        userDetail.setJob( arg0.getJob() );
        userDetail.setRevenue( arg0.getRevenue() );
        userDetail.setWechat( arg0.getWechat() );
        userDetail.setAddr( arg0.getAddr() );
        userDetail.setAddrNew( arg0.getAddrNew() );

        return userDetail;
    }

    @Override
    public UserDetail convert(AppUserDetailBo arg0, UserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );

        return arg1;
    }
}
