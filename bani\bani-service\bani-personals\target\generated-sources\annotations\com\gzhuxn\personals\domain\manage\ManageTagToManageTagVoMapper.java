package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.domain.manage.bo.ManageTagBoToManageTagMapper;
import com.gzhuxn.personals.domain.manage.vo.ManageTagVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageTagBoToManageTagMapper.class,ManageTagToAppManageTagListVoMapper.class,ManageTagToAdminActTagVoMapper.class,ManageTagToAppManageTagVoMapper.class},
    imports = {}
)
public interface ManageTagToManageTagVoMapper extends BaseMapper<ManageTag, ManageTagVo> {
}
