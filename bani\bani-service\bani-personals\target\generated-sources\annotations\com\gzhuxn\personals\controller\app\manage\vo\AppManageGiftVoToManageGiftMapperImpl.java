package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageGift;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppManageGiftVoToManageGiftMapperImpl implements AppManageGiftVoToManageGiftMapper {

    @Override
    public ManageGift convert(AppManageGiftVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageGift manageGift = new ManageGift();

        manageGift.setId( arg0.getId() );
        manageGift.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            manageGift.setIcon( Long.parseLong( arg0.getIcon() ) );
        }
        manageGift.setPrice( arg0.getPrice() );

        return manageGift;
    }

    @Override
    public ManageGift convert(AppManageGiftVo arg0, ManageGift arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            arg1.setIcon( Long.parseLong( arg0.getIcon() ) );
        }
        else {
            arg1.setIcon( null );
        }
        arg1.setPrice( arg0.getPrice() );

        return arg1;
    }
}
