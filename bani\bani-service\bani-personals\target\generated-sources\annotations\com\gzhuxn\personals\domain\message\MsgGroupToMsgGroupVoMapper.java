package com.gzhuxn.personals.domain.message;

import com.gzhuxn.personals.controller.app.message.vo.AppMsgGroupDetailVoToMsgGroupMapper;
import com.gzhuxn.personals.domain.message.bo.MsgGroupBoToMsgGroupMapper;
import com.gzhuxn.personals.domain.message.vo.MsgGroupVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupBoToMsgGroupMapper.class,AppMsgGroupDetailVoToMsgGroupMapper.class,MsgGroupToAppMsgGroupDetailVoMapper.class},
    imports = {}
)
public interface MsgGroupToMsgGroupVoMapper extends BaseMapper<MsgGroup, MsgGroupVo> {
}
