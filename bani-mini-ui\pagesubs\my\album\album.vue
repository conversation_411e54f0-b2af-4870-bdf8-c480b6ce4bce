<template>
	<scroll-nav-page title="我的相册" :show-back="true">
		<template #content>
			<!-- 相册内容 -->
			<view class="main-container">
			<!-- 相册统计 -->
			<view class="album-stats">
				<view class="stats-item">
					<text class="stats-number">{{ albumStats.imageCount }}</text>
					<text class="stats-label">张照片</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">1</text>
					<text class="stats-label">个相册</text>
				</view>
				<view class="stats-item">
					<text class="stats-number">{{ albumStats.totalBrowseCount }}</text>
					<text class="stats-label">次浏览</text>
				</view>
			</view>

			<!-- 相册网格 -->
			<view class="photo-grid" v-if="photoList.length > 0">
				<view class="photo-item" v-for="(photo, index) in photoList" :key="photo.id"
					@click="previewPhoto(index)"
					@longpress="showPhotoActions(photo, index)">
					<image :src="photo.imageUrl" mode="aspectFill" class="photo-image" :lazy-load="true"></image>
					<view class="photo-overlay" :class="{ 'show': showActionsIndex === index }">
						<view class="photo-actions">
							<view class="action-btn" @click.stop="editPhoto(photo)">
								<uni-icons type="compose" size="16" color="#696CF3"></uni-icons>
							</view>
							<view class="action-btn" @click.stop="deletePhoto(photo)">
								<uni-icons type="trash" size="16" color="#ff6b6b"></uni-icons>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 - 只在没有照片时显示 -->
			<view class="empty-state" v-if="photoList.length === 0">
				<view class="empty-icon">
					<uni-icons type="image" size="80" color="#E5E6F3"></uni-icons>
				</view>
				<text class="empty-text">还没有上传照片</text>
				<text class="empty-desc">上传照片让更多人了解你</text>
			</view>

			<!-- 上传按钮区域 - 在照片少于6张时显示 -->
			<view class="upload-section" v-if="photoList.length < 6">
				<button class="upload-btn" @click="handleAddPhoto">
					<uni-icons type="camera" size="20" color="#fff"></uni-icons>
					<text>上传照片</text>
				</button>
			</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { uploadImgAndSmallFile } from '@/api/oss/oss'
import { uploadAlbumImage, deleteAlbumImages, getStatisticsAlbumData } from '@/api/my/album'
import { toast } from '@/utils/common'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'

// 相册数据
const photoList = ref([])
const albumStats = ref({
	imageCount: 0,
	totalBrowseCount: 0
})

// 加载状态
const isLoading = ref(false)

// 显示操作按钮的图片索引
const showActionsIndex = ref(-1)

// 加载相册数据
const loadAlbumData = async () => {
	try {
		isLoading.value = true
		const response = await getStatisticsAlbumData()

		if (response.code === 200) {
			photoList.value = response.data.albumList || []
			albumStats.value = response.data.statistics || {
				imageCount: 0,
				totalBrowseCount: 0
			}
		} else {
			toast(response.msg || '加载相册数据失败')
		}
	} catch (error) {
		console.error('加载相册数据失败:', error)
		toast('网络错误，请重试')
	} finally {
		isLoading.value = false
	}
}

// 添加照片
const handleAddPhoto = () => {
	uni.chooseImage({
		count: 9,
		sizeType: ['original', 'compressed'],
		sourceType: ['album', 'camera'],
		success: async (res) => {
			try {
				uni.showLoading({
					title: '照片上传中...'
				})

				// 逐个上传照片
				for (let i = 0; i < res.tempFilePaths.length; i++) {
					const filePath = res.tempFilePaths[i]

					// 1. 先上传到OSS
					const ossResult = await uploadImgAndSmallFile(filePath, 'user_album.image')
					const ossData = ossResult
					if (ossData.code == 200) {
						// 2. 将图片添加到相册
						const albumResult = await uploadAlbumImage(ossData.data.ossId)

						if (albumResult.code !== 200) {
							throw new Error(albumResult.msg || '添加到相册失败')
						}
					}
				}

				uni.hideLoading()
				toast('照片上传成功')

				// 重新加载相册数据
				await loadAlbumData()

			} catch (error) {
				uni.hideLoading()
				console.error('上传照片失败:', error)
				toast(error.message || '上传失败，请重试')
			}
		},
		fail: (err) => {
			console.error('选择照片失败:', err)
			toast('选择照片失败')
		}
	})
}

// 预览照片
const previewPhoto = (index) => {
	// 如果当前显示操作按钮，则隐藏它们
	if (showActionsIndex.value === index) {
		showActionsIndex.value = -1
		return
	}

	const urls = photoList.value.map(photo => photo.imageUrl)
	uni.previewImage({
		current: index,
		urls: urls
	})
}

// 显示照片操作按钮
const showPhotoActions = (photo, index) => {
	showActionsIndex.value = showActionsIndex.value === index ? -1 : index
}

// 编辑照片
const editPhoto = (photo) => {
	uni.showActionSheet({
		itemList: ['下载到本地', '删除照片'],
		success: (res) => {
			switch (res.tapIndex) {
				case 0:
					downloadPhoto(photo)
					break
				case 1:
					deletePhoto(photo)
					break
			}
		}
	})
}

// 删除照片
const deletePhoto = (photo) => {
	uni.showModal({
		title: '确认删除',
		content: '确定要删除这张照片吗？删除后无法恢复。',
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '删除中...'
					})

					const result = await deleteAlbumImages([photo.id])

					if (result.code === 200) {
						uni.hideLoading()
						toast('删除成功')
						// 重新加载相册数据
						await loadAlbumData()
					} else {
						throw new Error(result.msg || '删除失败')
					}
				} catch (error) {
					uni.hideLoading()
					console.error('删除照片失败:', error)
					toast(error.message || '删除失败，请重试')
				}
			}
		}
	})
}

// 下载照片
const downloadPhoto = (photo) => {
	uni.downloadFile({
		url: photo.url,
		success: (res) => {
			uni.saveImageToPhotosAlbum({
				filePath: res.tempFilePath,
				success: () => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				},
				fail: () => {
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					})
				}
			})
		},
		fail: () => {
			uni.showToast({
				title: '下载失败',
				icon: 'none'
			})
		}
	})
}

// 页面初始化
onMounted(() => {
	loadAlbumData()
})
</script>

<style lang="scss" scoped>
// 引入uni.scss变量
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			rgba(105, 108, 243, 0.08) 0%,
			rgba(105, 108, 243, 0.05) 30%,
			rgba(105, 108, 243, 0.02) 60%,
			rgba(255, 255, 255, 1) 100%);
}

.nav-right-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.1);
	}
}

.main-container {
	margin-top: 20rpx;
	padding: 24rpx;

	.album-stats {
		display: flex;
		justify-content: space-around;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.95) 100%);
		border-radius: 20rpx;
		padding: 32rpx 24rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 12rpx 40rpx rgba(105, 108, 243, 0.12);
		backdrop-filter: blur(15rpx);
		border: 1rpx solid rgba(105, 108, 243, 0.1);

		.stats-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.stats-number {
				font-size: 36rpx;
				font-weight: bold;
				color: #696CF3;
				margin-bottom: 8rpx;
			}

			.stats-label {
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.photo-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 8rpx;

		.photo-item {
			position: relative;
			aspect-ratio: 1;
			border-radius: 16rpx;
			overflow: hidden;
			background: linear-gradient(135deg, #f8f9ff 0%, #e8ebff 100%);
			box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.1);
			border: 2rpx solid rgba(255, 255, 255, 0.8);

			.photo-image {
				width: 100%;
				height: 100%;
				transition: transform 0.3s ease;
			}

			.photo-overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(105, 108, 243, 0.8) 0%, rgba(155, 157, 245, 0.8) 100%);
				display: flex;
				align-items: center;
				justify-content: center;
				opacity: 0;
				transition: opacity 0.3s ease;
				pointer-events: none;

				&.show {
					opacity: 1;
					pointer-events: auto;
				}

				.photo-actions {
					display: flex;
					gap: 16rpx;

					.action-btn {
						display: flex;
						align-items: center;
						justify-content: center;
						width: 48rpx;
						height: 48rpx;
						background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 255, 0.95) 100%);
						border-radius: 50%;
						transition: all 0.3s ease;
						box-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.2);
						border: 1rpx solid rgba(255, 255, 255, 0.8);

						&:active {
							transform: scale(0.9);
						}
					}
				}
			}

			&:active {
				.photo-image {
					transform: scale(0.98);
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
		text-align: center;

		.empty-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 32rpx;
			background: linear-gradient(135deg, rgba(105, 108, 243, 0.08) 0%, rgba(155, 157, 245, 0.08) 100%);
			border-radius: 50%;
			box-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.1);
			border: 2rpx solid rgba(105, 108, 243, 0.1);
		}

		.empty-text {
			font-size: 32rpx;
			background: linear-gradient(135deg, #696CF3, #9B9DF5);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			font-weight: 600;
			margin-bottom: 16rpx;
		}

		.empty-desc {
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
			font-weight: 500;
		}


	}

	.upload-section {
		display: flex;
		justify-content: center;
		padding: 40rpx;

		.upload-btn {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			background: linear-gradient(135deg, #696CF3, #9B9DF5);
			color: white;
			border: none;
			border-radius: 50rpx;
			padding: 14rpx 32rpx;
			font-size: 28rpx;
			font-weight: 600;
			min-width: 200rpx;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			box-shadow: 0 8rpx 24rpx rgba(105, 108, 243, 0.25);
			overflow: hidden;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
				transition: left 0.6s ease;
			}

			&::after {
				border: none;
			}

			&:hover {
				transform: translateY(-2rpx);
				box-shadow: 0 12rpx 32rpx rgba(105, 108, 243, 0.35);
			}

			&:active {
				transform: translateY(0) scale(0.98);
				box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.2);

				&::before {
					left: 100%;
				}
			}

			uni-icons {
				font-size: 32rpx !important;
			}

			text {
				font-weight: 600;
				letter-spacing: 1rpx;
			}
		}
	}
}
</style>
