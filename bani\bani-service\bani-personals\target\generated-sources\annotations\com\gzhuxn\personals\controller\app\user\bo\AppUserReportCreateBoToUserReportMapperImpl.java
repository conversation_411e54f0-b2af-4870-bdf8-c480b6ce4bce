package com.gzhuxn.personals.controller.app.user.bo;

import com.gzhuxn.personals.domain.user.UserReport;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserReportCreateBoToUserReportMapperImpl implements AppUserReportCreateBoToUserReportMapper {

    @Override
    public UserReport convert(AppUserReportCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserReport userReport = new UserReport();

        userReport.setTitle( arg0.getTitle() );
        userReport.setType( arg0.getType() );
        userReport.setBusinessId( arg0.getBusinessId() );
        userReport.setContent( arg0.getContent() );
        userReport.setImages( arg0.getImages() );

        return userReport;
    }

    @Override
    public UserReport convert(AppUserReportCreateBo arg0, UserReport arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );

        return arg1;
    }
}
