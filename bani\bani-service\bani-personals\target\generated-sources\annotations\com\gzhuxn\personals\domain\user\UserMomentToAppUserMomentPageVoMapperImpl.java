package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.moment.AppUserMomentPageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToAppUserMomentPageVoMapperImpl implements UserMomentToAppUserMomentPageVoMapper {

    @Override
    public AppUserMomentPageVo convert(UserMoment source) {
        if ( source == null ) {
            return null;
        }

        AppUserMomentPageVo appUserMomentPageVo = new AppUserMomentPageVo();

        appUserMomentPageVo.setCityName( source.getCityCode() );
        appUserMomentPageVo.setTime( source.getCreateTime() );
        appUserMomentPageVo.setId( source.getId() );
        appUserMomentPageVo.setUserId( source.getUserId() );
        appUserMomentPageVo.setContent( source.getContent() );
        appUserMomentPageVo.setImages( source.getImages() );
        appUserMomentPageVo.setLocation( source.getLocation() );
        appUserMomentPageVo.setPv( source.getPv() );
        appUserMomentPageVo.setLv( source.getLv() );
        appUserMomentPageVo.setCv( source.getCv() );
        appUserMomentPageVo.setCreateTime( source.getCreateTime() );

        return appUserMomentPageVo;
    }

    @Override
    public AppUserMomentPageVo convert(UserMoment source, AppUserMomentPageVo target) {
        if ( source == null ) {
            return target;
        }

        target.setCityName( source.getCityCode() );
        target.setTime( source.getCreateTime() );
        target.setId( source.getId() );
        target.setUserId( source.getUserId() );
        target.setContent( source.getContent() );
        target.setImages( source.getImages() );
        target.setLocation( source.getLocation() );
        target.setPv( source.getPv() );
        target.setLv( source.getLv() );
        target.setCv( source.getCv() );
        target.setCreateTime( source.getCreateTime() );

        return target;
    }
}
