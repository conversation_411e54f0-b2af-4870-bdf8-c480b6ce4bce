package com.gzhuxn.personals.controller.app.activity.vo;

import com.gzhuxn.personals.domain.activity.Activity;
import com.gzhuxn.personals.domain.activity.ActivityToAppActivityPageVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ActivityToAppActivityPageVoMapper.class},
    imports = {}
)
public interface AppActivityPageVoToActivityMapper extends BaseMapper<AppActivityPageVo, Activity> {
}
