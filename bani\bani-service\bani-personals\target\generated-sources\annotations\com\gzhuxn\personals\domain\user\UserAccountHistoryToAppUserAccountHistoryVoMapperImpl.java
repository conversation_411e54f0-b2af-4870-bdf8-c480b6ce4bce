package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.account.AppUserAccountHistoryVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAccountHistoryToAppUserAccountHistoryVoMapperImpl implements UserAccountHistoryToAppUserAccountHistoryVoMapper {

    @Override
    public AppUserAccountHistoryVo convert(UserAccountHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserAccountHistoryVo appUserAccountHistoryVo = new AppUserAccountHistoryVo();

        appUserAccountHistoryVo.setId( arg0.getId() );
        appUserAccountHistoryVo.setEffectTime( arg0.getEffectTime() );
        appUserAccountHistoryVo.setDsc( arg0.getDsc() );
        appUserAccountHistoryVo.setCoin( arg0.getCoin() );
        appUserAccountHistoryVo.setCoinType( arg0.getCoinType() );
        appUserAccountHistoryVo.setOpType( arg0.getOpType() );

        return appUserAccountHistoryVo;
    }

    @Override
    public AppUserAccountHistoryVo convert(UserAccountHistory arg0, AppUserAccountHistoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setEffectTime( arg0.getEffectTime() );
        arg1.setDsc( arg0.getDsc() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setCoinType( arg0.getCoinType() );
        arg1.setOpType( arg0.getOpType() );

        return arg1;
    }
}
