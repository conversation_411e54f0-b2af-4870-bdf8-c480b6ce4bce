{"version": 3, "file": "dict.js", "sources": ["store/modules/dict.js"], "sourcesContent": ["// 字典管理\r\nimport {\r\n\tgetDicts\r\n} from '@/api/dict/dict'\r\nimport {\r\n\tdefineStore\r\n} from 'pinia'\r\nimport {\r\n\tref\r\n} from 'vue'\r\n\r\n// 字典列表\r\nconst initTypes = [\r\n\t// 性别\r\n\t'user_sex',\r\n\t// 个人收入状况\r\n\t'user_revenue',\r\n\t// 个人情感状况\r\n\t'user_affective_status',\r\n\t// 个人学历类型\r\n\t'user_edu',\r\n\t// 职业\r\n\t'user_job',\r\n\r\n\t// 个人标签\r\n\t'user_tag_ones',\r\n\t'user_tag_car',\r\n\t'user_tag_house',\r\n\t'user_tag_marriage',\r\n\t'user_tag_want_marry',\r\n\r\n\t// 需求标签\r\n\t'user_require_tag_edu',\r\n\t'user_require_tag_revenue',\r\n\t'user_require_tag_house',\r\n\t'user_require_tag_marriage',\r\n\t'user_require_tag_accept_child',\r\n\t'user_require_tag_trait',\r\n\r\n\t// 推荐设置\r\n\t'recommend_set_education',\r\n\t'recommend_set_location',\r\n]\r\nconst dictStore = defineStore('dict', () => {\r\n\tconst data = ref({\r\n\t\t// 性别\r\n\t\t// user_sex: [{\r\n\t\t// \tid: '0',\r\n\t\t// \tname: '男'\r\n\t\t// }]\r\n\t})\r\n\r\n\t// Storage key前缀\r\n\tconst STORAGE_PREFIX = 'dict_'\r\n\r\n\t/**\r\n\t * 从Storage获取字典数据\r\n\t * @param {string} type\r\n\t */\r\n\tfunction getFromStorage(type) {\r\n\t\ttry {\r\n\t\t\tconst storageData = uni.getStorageSync(STORAGE_PREFIX + type)\r\n\t\t\treturn storageData ? JSON.parse(storageData) : null\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('从Storage获取字典数据失败:', type, error)\r\n\t\t\treturn null\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 保存字典数据到Storage\r\n\t * @param {string} type\r\n\t * @param {Array} dictData\r\n\t */\r\n\tfunction saveToStorage(type, dictData) {\r\n\t\ttry {\r\n\t\t\tuni.setStorageSync(STORAGE_PREFIX + type, JSON.stringify(dictData))\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('保存字典数据到Storage失败:', type, error)\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 获取字典数据\r\n\t * @param {string} type\r\n\t */\r\n\tfunction get(type) {\r\n\t\t// 先从内存中取\r\n\t\tif (data.value[type]) {\r\n\t\t\treturn data.value[type]\r\n\t\t}\r\n\r\n\t\t// 内存中没有，从Storage取\r\n\t\tconst storageData = getFromStorage(type)\r\n\t\tif (storageData) {\r\n\t\t\t// 将Storage中的数据加载到内存\r\n\t\t\tdata.value[type] = storageData\r\n\t\t\treturn storageData\r\n\t\t}\r\n\r\n\t\t// 都没有，返回空数组\r\n\t\treturn []\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 获取字典数据\r\n\t * @param {string} type\r\n\t */\r\n\tfunction getByIndex(type, index) {\r\n\t\tif (index) {\r\n\t\t\treturn data[type][index]\r\n\t\t}\r\n\t\treturn get(type)\r\n\t}\r\n\r\n\t/**\r\n\t * 获取字典数据\r\n\t * @param {int} index\r\n\t */\r\n\tfunction getIdByIndex(type, index) {\r\n\t\tconst dicts = get(type)\r\n\t\tindex = parseInt(index)\r\n\t\tif (dicts && dicts.length > index) {\r\n\t\t\treturn dicts[index].id\r\n\t\t}\r\n\t\treturn '';\r\n\t}\r\n\r\n\t/**\r\n\t * 获取字典数据\r\n\t * @param {string} id\r\n\t */\r\n\tfunction getIndexById(type, id) {\r\n\t\tconst dicts = get(type)\r\n\t\tif (dicts && dicts.length > 0) {\r\n\t\t\tfor (let i = 0; i < dicts.length; i++) {\r\n\t\t\t\tif (dicts[i].id == id) {\r\n\t\t\t\t\treturn i\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn 0;\r\n\t}\r\n\r\n\r\n\t/**\r\n\t * 获取字典数据\r\n\t * @param {string} id\r\n\t */\r\n\tfunction getNameById(type, id) {\r\n\t\tconst dicts = get(type)\r\n\t\tif (dicts && dicts.length > 0) {\r\n\t\t\tfor (let i = 0; i < dicts.length; i++) {\r\n\t\t\t\tif (dicts[i].id == id) {\r\n\t\t\t\t\treturn dicts[i].name\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn 0;\r\n\t}\r\n\r\n\t/**\r\n\t * 获取字典名称\r\n\t * @param {string} type\r\n\t */\r\n\tfunction getNames(type) {\r\n\t\tconst dicts = get(type)\r\n\t\tif (dicts && dicts.length > 0) {\r\n\t\t\treturn dicts.map(item => item.name)\r\n\t\t}\r\n\t\treturn []\r\n\t}\r\n\r\n\t/**\r\n\t * 刷新字典数据\r\n\t */\r\n\tfunction refresh() {\r\n\t\tvar loadTypes = initTypes;\r\n\t\tObject.keys(data.value).forEach(key => {\r\n\t\t\tloadTypes.push(key)\r\n\t\t})\r\n\t\tloads(loadTypes)\r\n\t}\r\n\r\n\t/**\r\n\t * 加载字典数据\r\n\t * @param {Object} type\r\n\t */\r\n\tfunction load(type) {\r\n\t\tloads([type])\r\n\t}\r\n\r\n\t/**\r\n\t * 批量加载字典数据\r\n\t * @param {Object} types\r\n\t */\r\n\tfunction loads(types) {\r\n\t\tvar pullTypes = []\r\n\t\ttypes.forEach((type) => {\r\n\t\t\t// 先检查内存\r\n\t\t\tif (!data.value[type]) {\r\n\t\t\t\t// 内存中没有，检查Storage\r\n\t\t\t\tconst storageData = getFromStorage(type)\r\n\t\t\t\tif (storageData) {\r\n\t\t\t\t\t// Storage中有数据，加载到内存\r\n\t\t\t\t\tdata.value[type] = storageData\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// Storage中也没有，需要远程拉取\r\n\t\t\t\t\tpullTypes.push(type)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t\tif (pullTypes.length > 0) {\r\n\t\t\tremoteLoads(pullTypes)\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 远程加载字典数据\r\n\t * @param {ArrayL} types\r\n\t */\r\n\tasync function remoteLoads(types) {\r\n\t\tvar res = await getDicts(types)\r\n\t\tset(res.data)\r\n\t}\r\n\r\n\t/**\r\n\t * 设置字典数据\r\n\t * @param {Object} dicts\r\n\t */\r\n\tfunction set(dicts) {\r\n\t\tdicts.forEach(function (ele) {\r\n\t\t\t// 设置到内存\r\n\t\t\tdata.value[ele.type] = ele.vals;\r\n\t\t\t// 同时保存到Storage\r\n\t\t\tsaveToStorage(ele.type, ele.vals);\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * 清除字典缓存\r\n\t * @param {string} type - 可选，指定类型。不传则清除所有\r\n\t */\r\n\tfunction clearCache(type) {\r\n\t\tif (type) {\r\n\t\t\t// 清除指定类型\r\n\t\t\tdelete data.value[type]\r\n\t\t\ttry {\r\n\t\t\t\tuni.removeStorageSync(STORAGE_PREFIX + type)\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('清除Storage缓存失败:', type, error)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// 清除所有缓存\r\n\t\t\tdata.value = {\r\n\t\t\t\tuser_sex: [{\r\n\t\t\t\t\tid: '0',\r\n\t\t\t\t\tname: '男'\r\n\t\t\t\t}]\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\t// 获取所有Storage key，清除字典相关的\r\n\t\t\t\tconst storageInfo = uni.getStorageInfoSync()\r\n\t\t\t\tstorageInfo.keys.forEach(key => {\r\n\t\t\t\t\tif (key.startsWith(STORAGE_PREFIX)) {\r\n\t\t\t\t\t\tuni.removeStorageSync(key)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('清除所有Storage缓存失败:', error)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 返回\r\n\treturn {\r\n\t\tget,\r\n\t\tgetByIndex,\r\n\t\tgetIdByIndex,\r\n\t\tgetIndexById,\r\n\t\tgetNameById,\r\n\t\tgetNames,\r\n\t\trefresh,\r\n\t\tloads,\r\n\t\tclearCache\r\n\t}\r\n})\r\nexport default dictStore"], "names": ["initTypes", "dictStore", "defineStore", "data", "ref", "STORAGE_PREFIX", "getFromStorage", "type", "storageData", "uni", "error", "saveToStorage", "dictData", "get", "getByIndex", "index", "getIdByIndex", "dicts", "getIndexById", "id", "i", "getNameById", "getNames", "item", "refresh", "loadTypes", "key", "loads", "types", "pullTypes", "remoteLoads", "res", "getDicts", "set", "ele", "clearCache"], "mappings": "2FAYMA,EAAY,CAEjB,WAEA,eAEA,wBAEA,WAEA,WAGA,gBACA,eACA,iBACA,oBACA,sBAGA,uBACA,2BACA,yBACA,4BACA,gCACA,yBAGA,0BACA,wBACD,EACMC,EAAYC,EAAAA,YAAY,OAAQ,IAAM,CAC3C,MAAMC,EAAOC,EAAAA,IAAI,CAMlB,CAAE,EAGKC,EAAiB,QAMvB,SAASC,EAAeC,EAAM,CAC7B,GAAI,CACH,MAAMC,EAAcC,EAAG,MAAC,eAAeJ,EAAiBE,CAAI,EAC5D,OAAOC,EAAc,KAAK,MAAMA,CAAW,EAAI,IAC/C,OAAQE,EAAO,CACfD,OAAAA,EAAA,MAAA,MAAA,QAAA,8BAAc,oBAAqBF,EAAMG,CAAK,EACvC,IACP,CACD,CAOD,SAASC,EAAcJ,EAAMK,EAAU,CACtC,GAAI,CACHH,EAAG,MAAC,eAAeJ,EAAiBE,EAAM,KAAK,UAAUK,CAAQ,CAAC,CAClE,OAAQF,EAAO,CACfD,EAAA,MAAA,MAAA,QAAA,8BAAc,oBAAqBF,EAAMG,CAAK,CAC9C,CACD,CAMD,SAASG,EAAIN,EAAM,CAElB,GAAIJ,EAAK,MAAMI,CAAI,EAClB,OAAOJ,EAAK,MAAMI,CAAI,EAIvB,MAAMC,EAAcF,EAAeC,CAAI,EACvC,OAAIC,GAEHL,EAAK,MAAMI,CAAI,EAAIC,EACZA,GAID,CAAE,CACT,CAOD,SAASM,EAAWP,EAAMQ,EAAO,CAChC,OAAIA,EACIZ,EAAKI,CAAI,EAAEQ,CAAK,EAEjBF,EAAIN,CAAI,CACf,CAMD,SAASS,EAAaT,EAAMQ,EAAO,CAClC,MAAME,EAAQJ,EAAIN,CAAI,EAEtB,OADAQ,EAAQ,SAASA,CAAK,EAClBE,GAASA,EAAM,OAASF,EACpBE,EAAMF,CAAK,EAAE,GAEd,EACP,CAMD,SAASG,EAAaX,EAAMY,EAAI,CAC/B,MAAMF,EAAQJ,EAAIN,CAAI,EACtB,GAAIU,GAASA,EAAM,OAAS,GAC3B,QAASG,EAAI,EAAGA,EAAIH,EAAM,OAAQG,IACjC,GAAIH,EAAMG,CAAC,EAAE,IAAMD,EAClB,OAAOC,EAIV,MAAO,EACP,CAOD,SAASC,EAAYd,EAAMY,EAAI,CAC9B,MAAMF,EAAQJ,EAAIN,CAAI,EACtB,GAAIU,GAASA,EAAM,OAAS,GAC3B,QAASG,EAAI,EAAGA,EAAIH,EAAM,OAAQG,IACjC,GAAIH,EAAMG,CAAC,EAAE,IAAMD,EAClB,OAAOF,EAAMG,CAAC,EAAE,KAInB,MAAO,EACP,CAMD,SAASE,EAASf,EAAM,CACvB,MAAMU,EAAQJ,EAAIN,CAAI,EACtB,OAAIU,GAASA,EAAM,OAAS,EACpBA,EAAM,IAAIM,GAAQA,EAAK,IAAI,EAE5B,CAAE,CACT,CAKD,SAASC,GAAU,CAClB,IAAIC,EAAYzB,EAChB,OAAO,KAAKG,EAAK,KAAK,EAAE,QAAQuB,GAAO,CACtCD,EAAU,KAAKC,CAAG,CACrB,CAAG,EACDC,EAAMF,CAAS,CACf,CAcD,SAASE,EAAMC,EAAO,CACrB,IAAIC,EAAY,CAAE,EAClBD,EAAM,QAASrB,GAAS,CAEvB,GAAI,CAACJ,EAAK,MAAMI,CAAI,EAAG,CAEtB,MAAMC,EAAcF,EAAeC,CAAI,EACnCC,EAEHL,EAAK,MAAMI,CAAI,EAAIC,EAGnBqB,EAAU,KAAKtB,CAAI,CAEpB,CACJ,CAAG,EACGsB,EAAU,OAAS,GACtBC,EAAYD,CAAS,CAEtB,CAMD,eAAeC,EAAYF,EAAO,CACjC,IAAIG,EAAM,MAAMC,EAAQ,SAACJ,CAAK,EAC9BK,EAAIF,EAAI,IAAI,CACZ,CAMD,SAASE,EAAIhB,EAAO,CACnBA,EAAM,QAAQ,SAAUiB,EAAK,CAE5B/B,EAAK,MAAM+B,EAAI,IAAI,EAAIA,EAAI,KAE3BvB,EAAcuB,EAAI,KAAMA,EAAI,IAAI,CACnC,CAAG,CACD,CAMD,SAASC,EAAW5B,EAAM,CACzB,GAAIA,EAAM,CAET,OAAOJ,EAAK,MAAMI,CAAI,EACtB,GAAI,CACHE,QAAI,kBAAkBJ,EAAiBE,CAAI,CAC3C,OAAQG,EAAO,CACfD,EAAA,MAAA,MAAA,QAAA,+BAAc,iBAAkBF,EAAMG,CAAK,CAC3C,CACJ,KAAS,CAENP,EAAK,MAAQ,CACZ,SAAU,CAAC,CACV,GAAI,IACJ,KAAM,GACX,CAAK,CACD,EACD,GAAI,CAEiBM,EAAG,MAAC,mBAAoB,EAChC,KAAK,QAAQiB,GAAO,CAC3BA,EAAI,WAAWrB,CAAc,GAChCI,EAAG,MAAC,kBAAkBiB,CAAG,CAE/B,CAAK,CACD,OAAQhB,EAAO,CACfD,EAAAA,MAAA,MAAA,QAAA,+BAAc,mBAAoBC,CAAK,CACvC,CACD,CACD,CAGD,MAAO,CACN,IAAAG,EACA,WAAAC,EACA,aAAAE,EACA,aAAAE,EACA,YAAAG,EACA,SAAAC,EACA,QAAAE,EACA,MAAAG,EACA,WAAAQ,CACA,CACF,CAAC"}