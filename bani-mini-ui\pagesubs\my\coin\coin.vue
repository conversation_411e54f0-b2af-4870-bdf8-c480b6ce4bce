<template>
	<scroll-nav-page title="我的花瓣" :show-back="true">
		<template #content>
			<view class="main-container">
				<!-- 花瓣余额卡片 -->
				<view class="balance-card">
					<view class="balance-header">
						<view class="balance-left">
							<image class="coin-icon" src="/static/image/icons/coin.png" mode="aspectFit"></image>
							<view class="balance-info">
								<text class="balance-title">全部花瓣</text>
								<uni-icons type="help" size="16" color="#999" @click="showBalanceHelp"></uni-icons>
							</view>
						</view>
					</view>
					<view class="balance-amount">
						<view class="amount-with-arrow" @click="goToCoinDetail">
							<text class="amount-number">{{ userCoinBalance }}</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
						<button class="recharge-btn" @click="showRechargePopup">去充值</button>
					</view>

					<view class="balance-tips">
						<text class="tips-text">{{ accountInfo.coin }} 赠送花瓣 + {{ accountInfo.withdrawCoin }} 永久花瓣</text>
					</view>
				</view>

				<!-- 签到区域 -->
				<signin />
				<!-- 新手任务 -->
				<view class="newbie-tasks" v-if="newbieTasks.length > 0">
					<view class="section-title">新手任务</view>
					<view class="task-list">
						<view class="task-item" v-for="task in newbieTasks" :key="task.id"
							@click="handleTaskAction(task)">
							<view class="task-left">
								<text class="task-title">{{ task.title }}</text>
								<view class="task-reward">
									<image class="reward-icon" src="/static/image/icons/coin.png" mode="aspectFit">
									</image>
									<text class="reward-text">+{{ task.reward }}花瓣</text>
								</view>
							</view>
							<button class="task-btn" :class="{ 'completed': task.completed }"
								:disabled="task.completed">
								{{ task.completed ? '已完成' : task.buttonText }}
							</button>
						</view>
					</view>
				</view>

				<!-- 第三等级任务 -->
				<view class="level-tasks">
					<view class="section-title">新手任务</view>

					<!-- 加载状态 -->
					<view class="loading-container" v-if="isLoadingLevelTasks">
						<text class="loading-text">加载中...</text>
					</view>

					<!-- 任务列表 -->
					<view class="task-list" v-else-if="levelThreeTasks.length > 0">
						<view class="task-item" v-for="task in levelThreeTasks" :key="task.taskId"
							:class="{ 'completed': task.isCompleted }">
							<view class="task-left">
								<text class="task-title">{{ task.taskName }}</text>
								<view class="task-reward">
									<image class="reward-icon" src="/static/image/icons/coin.png" mode="aspectFit">
									</image>
									<text class="reward-text">+{{ task.coin }}花瓣</text>
								</view>
							</view>
							<button class="task-btn" :class="{ 'completed': task.isCompleted }"
								:disabled="task.isCompleted" @click="handleLevelTaskAction(task)">
								{{ task.isCompleted ? '已完成' : '去完成' }}
							</button>
						</view>
					</view>

					<!-- 空状态 -->
					<view class="empty-tasks" v-else>
						<view class="empty-icon">
							<uni-icons type="info" size="48" color="#ccc"></uni-icons>
						</view>
						<text class="empty-text">暂无任务</text>
					</view>
				</view>


			</view>

			<!-- 充值弹窗组件 -->
			<RechargePopup ref="rechargePopupRef" @close="loadUserCoinBalance" />
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { toast } from '@/utils/common'
import { getTaskListByLevel, USER_LEVELS } from '@/api/my/coin'
import { getUserAccountInfo } from '@/api/my/account'
import RechargePopup from './components/recharge-popup.vue'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import globalConfig from '@/config'
// 导航栏高度
const navBarHeight = ref(0)

// 用户账户信息
const accountInfo = ref({
	userId: 0,
	coin: 0,                    // 赠送类型花瓣数余额
	lockCoin: 0,               // 锁住的赠送花瓣数
	availableCoin: 0,      // 赠送花瓣数类型可用余额
	withdrawCoin: 0,           // 可提现类型花瓣数余额
	lockWithdrawCoin: 0,       // 锁住的可提现花瓣数
	availableWithdrawCoin: 0,  // 可提现花瓣数
	totalCoin: 0               // 总花瓣数
})

// 用户花瓣余额（兼容原有代码）
const userCoinBalance = ref(0)

// 新手任务数据
const newbieTasks = ref([])

// 第三等级任务数据
const levelThreeTasks = ref([])
const isLoadingLevelTasks = ref(false)

// 充值弹窗组件引用
const rechargePopupRef = ref(null)



// 页面加载标志
const isFirstLoad = ref(true)

// 页面加载
onLoad(() => {
	loadUserCoinBalance()
	loadLevelThreeTasks()
	isFirstLoad.value = false
})

// 导航栏高度变化处理
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}


// 显示更多菜单
const showMoreMenu = () => {
	// TODO: 实现更多菜单功能
	toast('功能开发中')
}

// 显示帮助
const showHelp = () => {
	// TODO: 实现帮助功能
	toast('功能开发中')
}

// 显示余额帮助
const showBalanceHelp = () => {
	uni.navigateTo({
		url: globalConfig.help.coinIntroduction
	})
}

// 加载用户账户信息
const loadUserCoinBalance = async () => {
	try {
		const res = await getUserAccountInfo()
		if (res.code === 200) {
			accountInfo.value = res.data
			userCoinBalance.value = res.data.totalCoin || 0
		} else {
			throw new Error(res.msg || '获取账户信息失败')
		}
	} catch (error) {
		console.error('获取账户信息失败:', error)
		// 使用默认值
		userCoinBalance.value = 0
	}
}

// 加载第三等级任务列表
const loadLevelThreeTasks = async () => {
	try {
		isLoadingLevelTasks.value = true
		const res = await getTaskListByLevel(USER_LEVELS.EXPERT) // 第三等级：情咖
		if (res.code === 200 && res.data) {
			levelThreeTasks.value = res.data
			console.log('第三等级任务加载成功:', res.data)

			// 检查每个任务的path字段
			res.data.forEach((task, index) => {
				console.log(`任务${index + 1}:`, {
					taskId: task.taskId,
					taskName: task.taskName,
					isCompleted: task.isCompleted,
					path: task.path,
					taskType: task.taskType,
					coin: task.coin
				})
			})
		} else {
			levelThreeTasks.value = []
			console.log('第三等级任务为空或加载失败:', res)
		}
	} catch (error) {
		console.error('获取第三等级任务失败:', error)
		levelThreeTasks.value = []
		uni.showToast({
			title: '加载任务失败',
			icon: 'none'
		})
	} finally {
		isLoadingLevelTasks.value = false
	}
}

// 处理任务操作
const handleTaskAction = (task) => {
	if (task.completed) return

	// 根据任务类型跳转到相应页面
	switch (task.id) {
		case 3:
			// 实名认证
			uni.navigateTo({
				url: '/pagesubs/my/identity/identity'
			})
			break
		case 4:
			// 精美照片和关于我
			uni.navigateTo({
				url: '/pagesubs/my/profile-edit/profile-edit'
			})
			break
		case 5:
			// 学历认证
			uni.navigateTo({
				url: '/pagesubs/my/education/education'
			})
			break
		case 6:
			// 工作认证
			uni.navigateTo({
				url: '/pagesubs/my/work/work'
			})
			break
		default:
			toast('功能开发中')
	}
}

// 处理第三等级任务操作
const handleLevelTaskAction = (task) => {
	console.log('点击任务:', task)

	// 检查任务是否已完成
	if (task.isCompleted) {
		toast('任务已完成')
		return
	}

	// 优先使用path字段进行跳转
	if (task.path && task.path.trim()) {
		const targetPath = task.path.trim()
		console.log('使用path跳转到:', targetPath)

		// 判断是否为外部链接
		if (targetPath.startsWith('http://') || targetPath.startsWith('https://')) {
			// 外部链接，使用webview打开
			uni.navigateTo({
				url: `/pages/webview/webview?url=${encodeURIComponent(targetPath)}`
			})
		} else {
			// 内部页面路径
			uni.navigateTo({
				url: targetPath,
				success: () => {
					console.log('页面跳转成功:', targetPath)
				},
				fail: (error) => {
					console.error('页面跳转失败:', error)
					toast('页面跳转失败')
				}
			})
		}
		return
	}

	// 如果没有path字段，使用taskType作为备用方案
	console.log('使用taskType跳转:', task.taskType)
	switch (task.taskType) {
		case 'profile':
			uni.navigateTo({
				url: '/pagesubs/my/profile/baseEdit'
			})
			break
		case 'avatar':
			uni.navigateTo({
				url: '/pagesubs/my/profile/profileEdit'
			})
			break
		case 'auth_identity':
			uni.navigateTo({
				url: '/pagesubs/my/auth/identity'
			})
			break
		case 'photos':
			uni.navigateTo({
				url: '/pagesubs/my/profile-edit/profile-edit'
			})
			break
		case 'auth_education':
			uni.navigateTo({
				url: '/pagesubs/my/education/education'
			})
			break
		case 'auth_work':
			uni.navigateTo({
				url: '/pagesubs/my/work/work'
			})
			break
		case 'auth_car':
			uni.navigateTo({
				url: '/pagesubs/my/auth/car'
			})
			break
		case 'auth_house':
			uni.navigateTo({
				url: '/pagesubs/my/auth/house'
			})
			break
		default:
			toast('功能开发中')
	}
}

// 显示充值弹窗
const showRechargePopup = () => {
	rechargePopupRef.value?.openPopup()
}

// 跳转到花瓣明细页面
const goToCoinDetail = () => {
	uni.navigateTo({
		url: '/pagesubs/my/coin/coin-detail'
	})
}


</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.page-container {
	min-height: 100vh;
	background: linear-gradient(180deg, $primary-color 0%, #f5f5f5 40%);
}

.nav-right-icons {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.main-container {
	padding: 0 30rpx 120rpx;
}

// 余额卡片
.balance-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: $radius-lg;
	padding: 40rpx 30rpx;
	margin: 30rpx 0;
	backdrop-filter: blur(10px);

	.balance-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;

		.balance-left {
			display: flex;
			align-items: center;
			gap: 20rpx;

			.coin-icon {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, #FFD700, #FFA500);
				border-radius: 50%;
				padding: 10rpx;
			}

			.balance-info {
				display: flex;
				align-items: center;
				gap: 10rpx;

				.balance-title {
					font-size: 32rpx;
					font-weight: 600;
					color: $text-primary;
				}
			}
		}


	}

	.balance-amount {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;

		.amount-with-arrow {
			display: flex;
			align-items: center;
			gap: 8rpx;
			flex: 1;
			cursor: pointer;

			.amount-number {
				font-size: 72rpx;
				font-weight: bold;
				color: $text-primary;
			}
		}

		.recharge-btn {
			background: $primary-color;
			color: white;
			border: none;
			height: $btn-sm-height;
			padding: $btn-sm-padding;
			font-size: $btn-sm-font-size;
			border-radius: $btn-sm-border-radius;
			font-weight: 500;
			display: flex;
			align-items: center;
			justify-content: center;
			line-height: 1;
			margin-left: 20rpx;
			flex-shrink: 0;
		}
	}

	.balance-tips {
		.tips-text {
			font-size: 24rpx;
			color: $text-tertiary;
		}
	}
}



// 新手任务
.newbie-tasks {
	background: white;
	border-radius: $radius-lg;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
}

// 第三等级任务
.level-tasks {
	background: white;
	border-radius: $radius-lg;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: $text-primary;
		margin-bottom: 30rpx;
	}

	.task-list {
		.task-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 0;
			border-bottom: 1rpx solid $bg-secondary;

			&:last-child {
				border-bottom: none;
			}

			// 已完成状态 - 移除置灰效果，保持任务项正常显示

			.task-left {
				flex: 1;

				.task-title {
					display: block;
					font-size: 28rpx;
					color: $text-primary;
					margin-bottom: 10rpx;
				}

				.task-reward {
					display: flex;
					align-items: center;
					gap: 8rpx;

					.reward-icon {
						width: 24rpx;
						height: 24rpx;
					}

					.reward-text {
						font-size: 24rpx;
						color: #FFA500;
						font-weight: 500;
					}
				}
			}

			.task-btn {
				background: $primary-color;
				color: white;
				border: none;
				height: $btn-sm-height;
				padding: $btn-sm-padding;
				font-size: $btn-sm-font-size;
				border-radius: $btn-sm-border-radius;
				display: flex;
				align-items: center;
				justify-content: center;
				line-height: 1;
				cursor: pointer;
				transition: all 0.3s ease;
				min-width: 120rpx;

				// 可点击状态的交互效果
				&:not(.completed):not(:disabled) {
					&:hover {
						background: lighten($primary-color, 10%);
						transform: translateY(-1rpx);
						box-shadow: 0 4rpx 12rpx rgba(105, 108, 243, 0.3);
					}

					&:active {
						background: darken($primary-color, 5%);
						transform: translateY(0);
						box-shadow: 0 2rpx 6rpx rgba(105, 108, 243, 0.2);
					}
				}

				&.completed {
					background: $bg-secondary;
					color: $text-tertiary;
					cursor: not-allowed;
				}
			}
		}
	}

	// 加载状态样式
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	// 空状态样式
	.empty-tasks {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 80rpx 40rpx;
	}

	.empty-icon {
		margin-bottom: 24rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
}
</style>
