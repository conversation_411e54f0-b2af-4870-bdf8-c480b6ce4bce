package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.admin.activity.vo.AdminActTagVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageTagToAdminActTagVoMapperImpl implements ManageTagToAdminActTagVoMapper {

    @Override
    public AdminActTagVo convert(ManageTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AdminActTagVo adminActTagVo = new AdminActTagVo();

        adminActTagVo.setId( arg0.getId() );
        adminActTagVo.setName( arg0.getName() );
        adminActTagVo.setIcon( arg0.getIcon() );
        adminActTagVo.setRemark( arg0.getRemark() );
        adminActTagVo.setSort( arg0.getSort() );
        adminActTagVo.setStatus( arg0.getStatus() );

        return adminActTagVo;
    }

    @Override
    public AdminActTagVo convert(ManageTag arg0, AdminActTagVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
