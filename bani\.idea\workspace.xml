<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="99f531ad-40a1-4ffc-b0c9-abcc60a4037f" name="Changes" comment="feat(personals): 添加用户打招呼功能&#10;&#10;- 新增 AppUserGreetingController 控制器实现用户打招呼相关接口&#10;- 创建 AppUserGreetingCreateBo 和 AppUserGreetingVo 用于打招呼的请求和响应模型&#10;- 添加 GreetingStatus 枚举表示打招呼状态&#10;- 实现 IUserGreetingService 接口和 UserGreetingServiceImpl 服务类- 新增 UserGreeting 实体类和 UserGreetingMapper 映射类&#10;- 更新 UserDetailMapper 中的查询统计SQL" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
        <option value="package-info" />
        <option value="Kotlin Class" />
        <option value="Interface" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="dev" />
                    <option name="lastUsedInstant" value="1732517259" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1732517258" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$MAVEN_REPOSITORY$/com/baomidou/mybatis-plus-extension/3.5.7/mybatis-plus-extension-3.5.7-sources.jar!/com/baomidou/mybatisplus/extension/service/impl/ServiceImpl.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2mSgOPCnNIaEAeYUUulU9wsv14I" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AhoCorasickUtilTest.executor": "JRebel Debug",
    "Application.SequenceNoUtil.executor": "Run",
    "Application.SysDistrictServiceImpl.executor": "Debug",
    "Application.WeChatUtils.executor": "Debug",
    "HTTP Request.BaniPersonalsApplication | #1.executor": "Run",
    "JUnit.ActivitySignInTest.testSuccessfulSignIn.executor": "Run",
    "Maven.bani [clean].executor": "Run",
    "Maven.bani [compile].executor": "Run",
    "Maven.bani [install].executor": "Run",
    "Maven.bani [verify].executor": "Run",
    "Maven.bani-api-personals [clean].executor": "Run",
    "Maven.bani-api-personals [install].executor": "Run",
    "Maven.bani-api-system [clean].executor": "Run",
    "Maven.bani-auth [clean].executor": "Run",
    "Maven.bani-auth [install].executor": "Run",
    "Maven.bani-common [clean].executor": "Run",
    "Maven.bani-common [install].executor": "Run",
    "Maven.bani-common [package].executor": "Run",
    "Maven.bani-common-sms [clean].executor": "Run",
    "Maven.bani-common-sms [install].executor": "Run",
    "Maven.bani-common-translation [clean].executor": "Run",
    "Maven.bani-common-translation [compile].executor": "Run",
    "Maven.bani-common-translation [install].executor": "Run",
    "Maven.bani-common-wx [clean].executor": "Run",
    "Maven.bani-common-wx [install].executor": "Run",
    "Maven.bani-gateway [clean].executor": "Run",
    "Maven.bani-gateway [install].executor": "Run",
    "Maven.bani-nacos [clean].executor": "Run",
    "Maven.bani-nacos [install].executor": "Run",
    "Maven.bani-personals [clean].executor": "Run",
    "Maven.bani-personals [compile].executor": "Run",
    "Maven.bani-personals [install].executor": "Run",
    "Maven.bani-personals [verify].executor": "Run",
    "Maven.bani-resource [clean].executor": "Run",
    "Maven.bani-resource [install].executor": "Run",
    "Maven.bani-service [clean].executor": "Run",
    "Maven.bani-service [install].executor": "Run",
    "Maven.bani-system [clean].executor": "Run",
    "Maven.bani-system [install].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SONARLINT_PRECOMMIT_ANALYSIS": "false",
    "Spring Boot.BaniAuthApplication.executor": "Run",
    "Spring Boot.BaniGatewayApplication.executor": "Run",
    "Spring Boot.BaniGenApplication.executor": "Run",
    "Spring Boot.BaniMonitorApplication.executor": "Debug",
    "Spring Boot.BaniPersonalsApplication.executor": "JRebel Debug",
    "Spring Boot.BaniResourceApplication.executor": "Debug",
    "Spring Boot.BaniSystemApplication.executor": "Debug",
    "Spring Boot.DashboardApplication.executor": "Debug",
    "Spring Boot.Nacos.executor": "Run",
    "Spring Boot.SeataServerApplication.executor": "Run",
    "Spring Boot.SnailJobServerApplication.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "dev",
    "jdk.selected.JAVA_MODULE": "graalvm-21",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/bani/code/bani/bani-service/bani-personals/src/main/java/com/gzhuxn/personals/controller/app/user/bo/greeting",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDKs",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.32528734",
    "run.configurations.included.in.services": "true",
    "service.view.auto.scroll.to.source": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "spring.configuration.checksum": "662c110bf423410acdbee7a2213f2ea5",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mariadb",
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\bani\code\bani\bani-service\bani-personals\src\main\java\com\gzhuxn\personals\controller\app\user\bo\greeting" />
      <recent name="E:\bani\code\bani\bani-service\bani-personals\src\main\java\com\gzhuxn\personals\controller\app\user\vo\greeting" />
      <recent name="E:\bani\code\bani\bani-service\bani-personals\src\main\java\com\gzhuxn\personals\controller\app\user" />
      <recent name="E:\bani\code\bani\bani-service\bani-personals\src\main\java\com\gzhuxn\personals\domain\user\vo" />
      <recent name="E:\bani\code\bani\bani-service\bani-personals\src\main\java\com\gzhuxn\personals\domain\user" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\ftpupdata\bani\bani-service\bani-personals\src\main\resources\mapper\manage" />
      <recent name="E:\ftpupdata\bani\docs\工具使用" />
      <recent name="E:\ftpupdata\bani\bani-service\bani-personals\src\main\resources\mapper\order" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.gzhuxn.personals.controller.app.user.vo" />
      <recent name="com.gzhuxn.personals.domain.manage" />
      <recent name="com.gzhuxn.personals.domain.manage.bo" />
      <recent name="com.gzhuxn.common.base" />
      <recent name="com.gzhuxn.system.controller.admin.app" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.gzhuxn.system.controller.app.system.vo" />
      <recent name="com.gzhuxn.personals.enums.message" />
      <recent name="com.gzhuxn.personals.controller.app.order.bo" />
      <recent name="com.gzhuxn.personals.controller.admin.audit.vo" />
      <recent name="com.gzhuxn.common.base.json" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
        <option value="Java Scratch" />
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.BaniPersonalsApplication">
    <configuration name="AhoCorasickUtilTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.gzhuxn.common.base.AhoCorasickUtilTest" />
      <module name="bani-common-base" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gzhuxn.common.base.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ActivitySignInTest.testSuccessfulSignIn" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="bani-personals" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gzhuxn.personals.service.user.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.gzhuxn.personals.service.user" />
      <option name="MAIN_CLASS_NAME" value="com.gzhuxn.personals.service.user.ActivitySignInTest" />
      <option name="METHOD_NAME" value="testSuccessfulSignIn" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaniGatewayApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="bani-gateway" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gzhuxn.gateway.BaniGatewayApplication" />
      <option name="VM_PARAMETERS" value="-Xmx300m -Xms256m" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gzhuxn.gateway.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaniGenApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bani-gen" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gzhuxn.gen.BaniGenApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaniPersonalsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bani-personals" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gzhuxn.personals.BaniPersonalsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaniSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bani-system" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gzhuxn.system.BaniSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Nacos" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="bani-nacos" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.alibaba.nacos.Nacos" />
      <option name="VM_PARAMETERS" value="-Xmx300m -Xms256m" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.AhoCorasickUtilTest" />
      <item itemvalue="JUnit.ActivitySignInTest.testSuccessfulSignIn" />
      <item itemvalue="Spring Boot.Nacos" />
      <item itemvalue="Spring Boot.BaniPersonalsApplication" />
      <item itemvalue="Spring Boot.BaniGenApplication" />
      <item itemvalue="Spring Boot.BaniSystemApplication" />
      <item itemvalue="Spring Boot.BaniGatewayApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.BaniGatewayApplication" />
        <item itemvalue="Application.AhoCorasickUtilTest" />
        <item itemvalue="JUnit.ActivitySignInTest.testSuccessfulSignIn" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="99f531ad-40a1-4ffc-b0c9-abcc60a4037f" name="Changes" comment="" />
      <created>1727075360860</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1727075360860</updated>
      <workItem from="1727075365809" duration="1836000" />
      <workItem from="1727079832289" duration="1936000" />
      <workItem from="1727081880535" duration="3042000" />
      <workItem from="1727085144344" duration="110000" />
      <workItem from="1727085299738" duration="648000" />
      <workItem from="1727085972005" duration="265000" />
      <workItem from="1727086341593" duration="1724000" />
      <workItem from="1727099161307" duration="1165000" />
      <workItem from="1727102021477" duration="132000" />
      <workItem from="1727102201810" duration="736000" />
      <workItem from="1727102962520" duration="2418000" />
      <workItem from="1727136245575" duration="1050000" />
      <workItem from="1727156985513" duration="2789000" />
      <workItem from="1727159821253" duration="133000" />
      <workItem from="1727159964998" duration="14953000" />
      <workItem from="1727269859013" duration="7897000" />
      <workItem from="1727439371344" duration="4574000" />
      <workItem from="1728391858977" duration="1071000" />
      <workItem from="1728394398113" duration="13000" />
      <workItem from="1728540750448" duration="1395000" />
      <workItem from="1728954701481" duration="657000" />
      <workItem from="1728961123290" duration="1361000" />
      <workItem from="1729694577267" duration="3506000" />
      <workItem from="1730108070880" duration="3096000" />
      <workItem from="1730622925304" duration="9652000" />
      <workItem from="1731049861790" duration="2943000" />
      <workItem from="1731507257223" duration="3561000" />
      <workItem from="1731547994025" duration="25803000" />
      <workItem from="1731746796976" duration="8487000" />
      <workItem from="1732112333416" duration="19569000" />
      <workItem from="1732320524857" duration="100311000" />
      <workItem from="1733013192129" duration="18406000" />
      <workItem from="1733034701869" duration="315000" />
      <workItem from="1733035048016" duration="76000" />
      <workItem from="1733035256429" duration="406000" />
      <workItem from="1733035922425" duration="135000" />
      <workItem from="1733036066298" duration="1390000" />
      <workItem from="1733039950613" duration="67566000" />
      <workItem from="1733389741883" duration="9211000" />
      <workItem from="1733533519419" duration="38295000" />
      <workItem from="1733964783064" duration="22744000" />
      <workItem from="1734269246971" duration="12427000" />
      <workItem from="1734603795939" duration="1573000" />
      <workItem from="1734618170693" duration="56666000" />
      <workItem from="1735626550726" duration="19528000" />
      <workItem from="1735745105749" duration="1143000" />
      <workItem from="1735746459649" duration="377000" />
      <workItem from="1735746852345" duration="291000" />
      <workItem from="1735747158050" duration="54471000" />
      <workItem from="1736170517546" duration="231000" />
      <workItem from="1736170758891" duration="108989000" />
      <workItem from="1736754724197" duration="64613000" />
      <workItem from="1736989462715" duration="56633000" />
      <workItem from="1737537399481" duration="1040000" />
      <workItem from="1737538473579" duration="1200000" />
      <workItem from="1737684719778" duration="77000" />
      <workItem from="1737685233844" duration="6266000" />
      <workItem from="1738740296320" duration="12694000" />
      <workItem from="1738977186171" duration="592000" />
      <workItem from="1739167219999" duration="10929000" />
      <workItem from="1739321626028" duration="8271000" />
      <workItem from="1739365185201" duration="34602000" />
      <workItem from="1740360481760" duration="639000" />
      <workItem from="1740365809427" duration="35503000" />
      <workItem from="1740965051396" duration="2996000" />
      <workItem from="1740988933819" duration="3627000" />
      <workItem from="1741087391922" duration="7022000" />
      <workItem from="1741182576725" duration="17448000" />
      <workItem from="1741600839930" duration="1192000" />
      <workItem from="1741618612611" duration="8463000" />
      <workItem from="1741830601165" duration="22913000" />
      <workItem from="1742045230032" duration="28593000" />
      <workItem from="1742199077038" duration="18418000" />
      <workItem from="1742441073153" duration="8233000" />
      <workItem from="1742916146594" duration="1256000" />
      <workItem from="1743125537278" duration="28577000" />
      <workItem from="1743397178723" duration="78129000" />
      <workItem from="1743584190275" duration="36741000" />
      <workItem from="1743950799854" duration="41586000" />
      <workItem from="1744247384849" duration="24153000" />
      <workItem from="1744701991407" duration="7335000" />
      <workItem from="1745333460722" duration="27447000" />
      <workItem from="1745622712560" duration="12442000" />
      <workItem from="1746027908015" duration="3967000" />
      <workItem from="1746377078898" duration="1098000" />
      <workItem from="1746402113451" duration="536000" />
      <workItem from="1746402669958" duration="18108000" />
      <workItem from="1746714163948" duration="2093000" />
      <workItem from="1747232329884" duration="685000" />
      <workItem from="1747275030594" duration="867000" />
      <workItem from="1747387413313" duration="20941000" />
      <workItem from="1747499173970" duration="5913000" />
      <workItem from="1747662325978" duration="4325000" />
      <workItem from="1747750364376" duration="11749000" />
      <workItem from="1747839284577" duration="5744000" />
      <workItem from="1747873091870" duration="10074000" />
      <workItem from="1747924733237" duration="2332000" />
      <workItem from="1747928005361" duration="3465000" />
      <workItem from="1748010002752" duration="130000" />
      <workItem from="1748010146820" duration="39645000" />
      <workItem from="1748525930744" duration="34000" />
      <workItem from="1748525990720" duration="4017000" />
      <workItem from="1748532654121" duration="10859000" />
      <workItem from="1748651993452" duration="6245000" />
      <workItem from="1748924710769" duration="846000" />
      <workItem from="1748926081030" duration="46983000" />
      <workItem from="1749310488143" duration="101000" />
      <workItem from="1749310612141" duration="10054000" />
      <workItem from="1749975969280" duration="417000" />
      <workItem from="1749976402235" duration="9482000" />
      <workItem from="1749999078057" duration="36496000" />
      <workItem from="1750166357909" duration="18758000" />
      <workItem from="1750252499191" duration="23497000" />
      <workItem from="1750604163750" duration="311000" />
      <workItem from="1750604492787" duration="404000" />
      <workItem from="1750635855386" duration="28518000" />
      <workItem from="1750867272886" duration="9131000" />
      <workItem from="1750983105414" duration="8188000" />
      <workItem from="1751037403363" duration="387000" />
      <workItem from="1751037807828" duration="183000" />
      <workItem from="1751041180340" duration="119461000" />
      <workItem from="1751254286050" duration="29283000" />
      <workItem from="1751378051940" duration="10009000" />
      <workItem from="1751463422145" duration="25133000" />
      <workItem from="1751889124680" duration="19042000" />
      <workItem from="1751956082576" duration="11288000" />
      <workItem from="1751982731209" duration="39025000" />
      <workItem from="1752104618224" duration="15855000" />
      <workItem from="1752148509546" duration="78511000" />
      <workItem from="1752401896085" duration="43024000" />
      <workItem from="1752549135656" duration="33863000" />
      <workItem from="1752675363018" duration="40539000" />
      <workItem from="1752928441035" duration="170000" />
      <workItem from="1752929980078" duration="139882000" />
      <workItem from="1753710742958" duration="21377000" />
      <workItem from="1753873072495" duration="237000" />
      <workItem from="1753873328705" duration="45305000" />
      <workItem from="1754310193790" duration="2373000" />
      <workItem from="1754312778075" duration="4085000" />
      <workItem from="1754318727916" duration="24683000" />
      <workItem from="1754438566772" duration="99000" />
      <workItem from="1754438681891" duration="69996000" />
    </task>
    <task id="LOCAL-00220" summary="feat(personals): 完善用户详情功能&#10;&#10;- 新增用户详情获取接口和相关 VO 类&#10;- 实现用户标签和需求标签的获取方法&#10;- 优化用户详情页面数据展示&#10;- 移除重复的用户详情获取接口">
      <option name="closed" value="true" />
      <created>1752590695224</created>
      <option name="number" value="00220" />
      <option name="presentableId" value="LOCAL-00220" />
      <option name="project" value="LOCAL" />
      <updated>1752590695224</updated>
    </task>
    <task id="LOCAL-00221" summary="refactor(system): 优化行政区划查询接口和相关代码&#10;&#10;- 修改 AppSysDistrictController 中的 list 方法，使用 parentCode 参数替代 code 参数&#10;- 在 AppUserFullBaseBo 中添加 isMatched 字段，用于表示是否相亲&#10;- 更新 SysDistrictMapper 中的 childrenCountByParentCode 方法，使用 @Param 注解&#10;- 在 SysDistrictMapper.xml 中添加 childrenCountByParentCode 方法的 SQL 语句&#10;- 优化 SysDistrictServiceImpl 中的 hasChild判断逻辑&#10;- 在 UserRequireTagKeyVal 中添加 TRAIT 枚举值，表示希望 TA 的特质&#10;- 在 UserTagKeyVal 中添加 ADVANTAGES 枚举值，表示个人优势">
      <option name="closed" value="true" />
      <created>1752630977084</created>
      <option name="number" value="00221" />
      <option name="presentableId" value="LOCAL-00221" />
      <option name="project" value="LOCAL" />
      <updated>1752630977084</updated>
    </task>
    <task id="LOCAL-00222" summary="feat(AppUserTagBo): 添加标签值名称字段&#10;&#10;- 在 AppUserTagBo 类中新增 tagValName 字段&#10;- 用于存储标签值的名称信息">
      <option name="closed" value="true" />
      <created>1752645120267</created>
      <option name="number" value="00222" />
      <option name="presentableId" value="LOCAL-00222" />
      <option name="project" value="LOCAL" />
      <updated>1752645120267</updated>
    </task>
    <task id="LOCAL-00223" summary="feat(user): 添加用户详情生日年份字段并优化标签处理&#10;&#10;- 在 AppUserDetailVo 中添加 birthdayYear 字段，用于展示生日年份&#10;- 优化 UserDetailServiceImpl 中的标签处理逻辑，提高代码可读性&#10;- 在 UserRequireTagKeyVal 枚举中为年龄和身高要求标签添加新参数&#10;- 新增 UserUtils.getBirthdayYearStr() 方法，用于获取生日年份字符串">
      <option name="closed" value="true" />
      <created>1752712640305</created>
      <option name="number" value="00223" />
      <option name="presentableId" value="LOCAL-00223" />
      <option name="project" value="LOCAL" />
      <updated>1752712640306</updated>
    </task>
    <task id="LOCAL-00224" summary="refactor(personals): 优化用户推荐和用户动态相关模型&#10;&#10;- 移除了 AppRecommendUserController 中未使用的 import 语句&#10;- 删除了 AppRecommendUserController 中未使用的 list 方法&#10;- 在 AppRecommendUserPageBo 中添加了 cityCode 字段，用于户籍地市编码&#10;- 优化了 AppRecommendUserPageVo 的结构，继承了 AppOppositeUserItem&#10;- 在 AppUserMomentPageVo 中添加了用户 ID、是否已点赞、创建时间和城市名称等字段">
      <option name="closed" value="true" />
      <created>1752766286614</created>
      <option name="number" value="00224" />
      <option name="presentableId" value="LOCAL-00224" />
      <option name="project" value="LOCAL" />
      <updated>1752766286614</updated>
    </task>
    <task id="LOCAL-00225" summary="feat(recommend): 完善用户推荐列表功能&#10;&#10;- 新增同城查询、附近查询和匹配过滤功能&#10;- 优化用户配置处理逻辑，支持默认配置自动应用&#10;- 完善年龄范围计算逻辑，支持学历要求过滤&#10;- 优化 SQL 查询，支持复杂条件和多种排序策略&#10;- 新增用户推荐相关枚举和配置类&#10;- 更新 API 接口文档和功能说明">
      <option name="closed" value="true" />
      <created>1752804229238</created>
      <option name="number" value="00225" />
      <option name="presentableId" value="LOCAL-00225" />
      <option name="project" value="LOCAL" />
      <updated>1752804229238</updated>
    </task>
    <task id="LOCAL-00226" summary="feat(personals): 新增同城和附近用户查询功能&#10;&#10;- 添加了 AppSameCityUserQueryBo 和 AppNearbyUserQueryBo 类用于同城和附近用户查询&#10;- 在 AppRecommendUserController 中增加了 /same-city 和 /nearby 接口&#10;- 在 IRecommendUserService 接口中添加了 querySameCityUsers 和 queryNearbyUsers 方法&#10;- 实现了 RecommendUserServiceImpl 中的 querySameCityUsers 和 queryNearbyUsers 方法- 优化了 UserDetailMapper 和 UserDetailMapper.xml 以支持新的查询功能&#10;- 添加了相关单元测试">
      <option name="closed" value="true" />
      <created>1752969107885</created>
      <option name="number" value="00226" />
      <option name="presentableId" value="LOCAL-00226" />
      <option name="project" value="LOCAL" />
      <updated>1752969107886</updated>
    </task>
    <task id="LOCAL-00227" summary="refactor(bani-personals):优化用户浏览历史记录添加接口返回逻辑&#10;&#10;- 移除了原有的 toAjax 方法调用，该方法可能包含不必要的逻辑处理&#10;- 直接使用 R.ok() 返回成功结果，简化了代码结构&#10;-这个改动提高了代码的可读性和维护性，同时确保了接口的正常功能">
      <option name="closed" value="true" />
      <created>1752969202051</created>
      <option name="number" value="00227" />
      <option name="presentableId" value="LOCAL-00227" />
      <option name="project" value="LOCAL" />
      <updated>1752969202051</updated>
    </task>
    <task id="LOCAL-00228" summary="feat(user): 添加用户关注相关功能并优化用户详情接口&#10;&#10;- 在 AppUserDetailVo 中添加 location 字段表示用户所在地&#10;- 在 AppUserDetailVo 中添加 isFollowed 字段表示是否已关注&#10;- 在 IUserFollowService 中添加 isFollowed 方法判断用户是否已关注- 在 UserDetailServiceImpl 中集成用户关注判断逻辑&#10;- 优化 UserFollowMapper 和 UserFollowServiceImpl 中的关注判断相关方法">
      <option name="closed" value="true" />
      <created>1752982020158</created>
      <option name="number" value="00228" />
      <option name="presentableId" value="LOCAL-00228" />
      <option name="project" value="LOCAL" />
      <updated>1752982020158</updated>
    </task>
    <task id="LOCAL-00229" summary="feat(user): 优化用户礼物查询接口&#10;&#10;- 新增礼物查询类型枚举，支持&quot;我送给他人的礼物&quot;和&quot;送给我的礼物&quot;两种查询类型&#10;- 修改 AppUserGiftBo，添加 queryType 字段用于指定查询类型&#10;- 在 UserGiftMapper 中添加 selectAppPageListByQueryType 方法，实现根据查询类型分页查询礼物列表- 优化 UserGiftServiceImpl 中的 queryAppPageList 方法，支持查询类型验证和默认值处理&#10;- 更新 AppUserGiftController 中的 page 接口文档，说明查询类型参数的使用&#10;- 添加完整的测试用例，覆盖各种查询场景">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00229" />
      <option name="presentableId" value="LOCAL-00229" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00230" summary="refactor(user): 优化用户账户和礼物相关功能&#10;&#10;- 移除 AppUserGiftVo 中的冗余字段&#10;- 添加 UserAccount 中的可用花瓣和提现花瓣计算方法&#10;- 修改 IUserAccountService 中的 isEnough 方法，增加 CoinType 参数&#10;- 优化 UserAccountMapper 中的 getCoinByUserId 方法，返回完整账户信息&#10;- 更新 UserAccountServiceImpl 中的 isEnough 方法，支持不同类型的花瓣判断&#10;-调整 UserGiftMapper.xml 中的查询结果，重命名 create_time为 time&#10;- 在 UserGiftServiceImpl 中更新送礼物逻辑，使用 CoinType.WITHDRAW">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00230" />
      <option name="presentableId" value="LOCAL-00230" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00231" summary="feat(personals): 优化礼物时间显示格式&#10;&#10;- 在 AppUserGiftVo 类中，为 time 字段添加 @JsonSerialize 注解&#10;- 使用 LocalDateTimeAgoSerialize 类来序列化时间字段&#10;- 这样可以将时间显示为相对当前时间的格式，如&quot;几分钟前&quot;、&quot;几小时前&quot;等">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00231" />
      <option name="presentableId" value="LOCAL-00231" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00232" summary="feat(personals): 优化礼物时间显示格式&#10;&#10;- 在 AppUserGiftVo 类中，为 time 字段添加 @JsonSerialize 注解&#10;- 使用 LocalDateTimeAgoSerialize 类来序列化时间字段&#10;- 这样可以将时间显示为相对当前时间的格式，如&quot;几分钟前&quot;、&quot;几小时前&quot;等">
      <option name="closed" value="true" />
      <created>1752999244379</created>
      <option name="number" value="00232" />
      <option name="presentableId" value="LOCAL-00232" />
      <option name="project" value="LOCAL" />
      <updated>1752999244379</updated>
    </task>
    <task id="LOCAL-00233" summary="fix(bani-personals): 修复用户礼物总数查询逻辑&#10;&#10;-将查询逻辑从统计礼物数量改为统计记录数&#10;- 简化了查询语句，提高了查询效率">
      <option name="closed" value="true" />
      <created>1753004966337</created>
      <option name="number" value="00233" />
      <option name="presentableId" value="LOCAL-00233" />
      <option name="project" value="LOCAL" />
      <updated>1753004966337</updated>
    </task>
    <task id="LOCAL-00234" summary="refactor(personals): 重构活动模块&#10;&#10;- 重命名 ActivityTypeVal 为 ActivityClassifyVal，用作活动分类&#10;- 新增 ActivityType 枚举，用于区分活动类型&#10;- 更新 Activity 实体，增加分类和类型字段&#10;- 重构 AppDaziActivityController 和 AppDaziActivityBo，改为 AppActivityBuddyController 和 AppActivityBuddyCreateBo&#10;- 优化公众号订阅逻辑&#10;- 删除冗余测试代码">
      <option name="closed" value="true" />
      <created>1753064975641</created>
      <option name="number" value="00234" />
      <option name="presentableId" value="LOCAL-00234" />
      <option name="project" value="LOCAL" />
      <updated>1753064975641</updated>
    </task>
    <task id="LOCAL-00235" summary="refactor(personals): 重构活动模块&#10;&#10;- 重命名 ActivityTypeVal 为 ActivityClassifyVal，用作活动分类&#10;- 新增 ActivityType 枚举，用于区分活动类型&#10;- 更新 Activity 实体，增加分类和类型字段&#10;- 重构 AppDaziActivityController 和 AppDaziActivityBo，改为 AppActivityBuddyController 和 AppActivityBuddyCreateBo&#10;- 优化公众号订阅逻辑&#10;- 删除冗余测试代码">
      <option name="closed" value="true" />
      <created>1753074421888</created>
      <option name="number" value="00235" />
      <option name="presentableId" value="LOCAL-00235" />
      <option name="project" value="LOCAL" />
      <updated>1753074421888</updated>
    </task>
    <task id="LOCAL-00236" summary="fix(personals): 修复微信支付成功时间解析&#10;&#10;- 更新了日期时间解析模式，将 'T' 和时区信息固定为 '+08:00'&#10;-确保支付成功时间能够正确解析为 LocalDateTime 对象">
      <option name="closed" value="true" />
      <created>1753107130597</created>
      <option name="number" value="00236" />
      <option name="presentableId" value="LOCAL-00236" />
      <option name="project" value="LOCAL" />
      <updated>1753107130597</updated>
    </task>
    <task id="LOCAL-00237" summary="refactor(personals): 优化活动模块代码结构&#10;&#10;- 新增 AppActivityBuddyUpdateBo 类用于活动更新&#10;- 修改 updateDaziByBo 方法参数类型&#10;- 更新相关控制器和接口定义">
      <option name="closed" value="true" />
      <created>1753116963963</created>
      <option name="number" value="00237" />
      <option name="presentableId" value="LOCAL-00237" />
      <option name="project" value="LOCAL" />
      <updated>1753116963963</updated>
    </task>
    <task id="LOCAL-00238" summary="feat(personals): 活动地址解析功能&#10;&#10;- 新增活动地址解析相关字段和方法&#10;- 实现根据经纬度解析活动地址并更新活动信息&#10;- 添加远程地址解析服务接口和实现类- 更新活动创建和编辑逻辑，支持地址解析">
      <option name="closed" value="true" />
      <created>1753143791537</created>
      <option name="number" value="00238" />
      <option name="presentableId" value="LOCAL-00238" />
      <option name="project" value="LOCAL" />
      <updated>1753143791537</updated>
    </task>
    <task id="LOCAL-00239" summary="feat(recommend): 新增同城和附近搭子活动查询功能&#10;&#10;- 新增 AppBuddyActivityQueryBo 和 AppBuddyActivityVo 类- 在 ActivityMapper 中添加 selectSameCityBuddyActivities 和 selectNearbyBuddyActivities 方法&#10;- 在 IRecommendActivityService 中添加 querySameCityBuddyActivities 和 queryNearbyBuddyActivities 方法&#10;- 在 RecommendActivityServiceImpl 中实现同城和附近搭子活动查询逻辑&#10;- 在 AppRecommendActivityController 中添加相应接口&#10;- 编写测试用例验证新功能">
      <option name="closed" value="true" />
      <created>1753175171176</created>
      <option name="number" value="00239" />
      <option name="presentableId" value="LOCAL-00239" />
      <option name="project" value="LOCAL" />
      <updated>1753175171176</updated>
    </task>
    <task id="LOCAL-00240" summary="feat(activity): 新增搭子活动详情查询功能&#10;&#10;- 添加了 AppBuddyActivityDetailVo 类用于搭子活动详情展示&#10;- 在 ActivityMapper 中添加了相关查询方法- 实现了 IRecommendActivityService 接口的 queryBuddyActivityDetail 方法&#10;- 在 AppRecommendActivityController 中添加了 getBuddyActivityDetail 接口&#10;- 更新了测试用例和相关文档">
      <option name="closed" value="true" />
      <created>1753192684666</created>
      <option name="number" value="00240" />
      <option name="presentableId" value="LOCAL-00240" />
      <option name="project" value="LOCAL" />
      <updated>1753192684666</updated>
    </task>
    <task id="LOCAL-00241" summary="refactor(personals): 重构用户评论模块&#10;&#10;- 新增 AppOppositeUserAvatarItem 类用于简化用户信息&#10;- 重构评论分页查询逻辑，分为根评论和子评论分别查询&#10;- 优化用户信息构建方法，支持不同类型的用户信息对象&#10;- 调整数据库查询 SQL，提高查询效率">
      <option name="closed" value="true" />
      <created>1753203021150</created>
      <option name="number" value="00241" />
      <option name="presentableId" value="LOCAL-00241" />
      <option name="project" value="LOCAL" />
      <updated>1753203021150</updated>
    </task>
    <task id="LOCAL-00242" summary="refactor(bani-personals):重构用户评论相关代码并优化字段命名&#10;&#10;- 删除了未使用的 AppRecommendActivityControllerTest 类&#10;-将 AppUserCommentRootVo 中的 childrenCount 字段改为 childCount- 更新了 UserCommentMapper.xml 中的字段映射&#10;- 优化了 UserDetailTransitionServiceImpl 中的用户头像构建逻辑&#10;- 移除了 UserCommentController 中的新增评论接口">
      <option name="closed" value="true" />
      <created>1753233195109</created>
      <option name="number" value="00242" />
      <option name="presentableId" value="LOCAL-00242" />
      <option name="project" value="LOCAL" />
      <updated>1753233195109</updated>
    </task>
    <task id="LOCAL-00243" summary="refactor(bani-personals):优化评论删除逻辑和数据结构- 添加默认根节点 ID常量&#10;- 新增递增和递减顶级评论子评论数的方法&#10;- 新增查询审核通过顶级评论的方法&#10;- 优化评论删除逻辑，支持级联删除子">
      <option name="closed" value="true" />
      <created>1753235578555</created>
      <option name="number" value="00243" />
      <option name="presentableId" value="LOCAL-00243" />
      <option name="project" value="LOCAL" />
      <updated>1753235578555</updated>
    </task>
    <task id="LOCAL-00244" summary="refactor(personals): 重构评论相关接口和实体&#10;&#10;- 修改地址字段名从 addr 改为 location&#10;- 新增 AppUserCommentRootTableInfo 类用于评论列表分页查询结果&#10;- 更新相关接口和实现类，使用新的分页信息类&#10;- 优化评论查询接口，增加评论总数字段&#10;-调整 UserMomentTagServiceImpl 中的标签添加逻辑">
      <option name="closed" value="true" />
      <created>1753238474886</created>
      <option name="number" value="00244" />
      <option name="presentableId" value="LOCAL-00244" />
      <option name="project" value="LOCAL" />
      <updated>1753238474886</updated>
    </task>
    <task id="LOCAL-00245" summary="feat(personals): 优化消息查询功能&#10;&#10;- 为 MessageSubType 枚举添加 of 方法，用于根据 value 获取枚举对象&#10;- 在 MsgContentItemVo 中添加 title 字段，用于存储消息标题- 修改 MsgContentUserMapper.xml，优化消息查询 SQL 语句- 在 MsgContentUserServiceImpl 中添加消息标题的处理逻辑">
      <option name="closed" value="true" />
      <created>1753288617094</created>
      <option name="number" value="00245" />
      <option name="presentableId" value="LOCAL-00245" />
      <option name="project" value="LOCAL" />
      <updated>1753288617094</updated>
    </task>
    <task id="LOCAL-00246" summary="refactor(message): 优化消息内容相关模型和逻辑&#10;&#10;- 将 AppMsgContentUserGroupItemVo 中的 uid 字段类型从 String改为 Long&#10;- 在 MsgContentUserBo 中添加 groupId 和 sendUserId 字段&#10;- 修改 MsgContentItemVo 继承 AppOppositeUserAvatarItem 并使用 @EqualsAndHashCode&#10;- 更新 MessageSseSendConsumer 中的 buildBo 方法逻辑&#10;- 调整 MsgContentUserServiceImpl 中的查询结果处理逻辑">
      <option name="closed" value="true" />
      <created>1753292730784</created>
      <option name="number" value="00246" />
      <option name="presentableId" value="LOCAL-00246" />
      <option name="project" value="LOCAL" />
      <updated>1753292730784</updated>
    </task>
    <task id="LOCAL-00247" summary="refactor(message): 优化消息内容相关模型和逻辑&#10;&#10;- 将 AppMsgContentUserGroupItemVo 中的 uid 字段类型从 String改为 Long&#10;- 在 MsgContentUserBo 中添加 groupId 和 sendUserId 字段&#10;- 修改 MsgContentItemVo 继承 AppOppositeUserAvatarItem 并使用 @EqualsAndHashCode&#10;- 更新 MessageSseSendConsumer 中的 buildBo 方法逻辑&#10;- 调整 MsgContentUserServiceImpl 中的查询结果处理逻辑">
      <option name="closed" value="true" />
      <created>1753372888674</created>
      <option name="number" value="00247" />
      <option name="presentableId" value="LOCAL-00247" />
      <option name="project" value="LOCAL" />
      <updated>1753372888674</updated>
    </task>
    <task id="LOCAL-00248" summary="feat(user): 添加用户经纬度更新功能并优化时间格式&#10;&#10;- 新增用户经纬度更新功能，支持更新用户位置信息&#10;- 在 AppBuddyActivityVo 中使用 LocalDateTimeMinuteSerialize 替代原有的 JsonFormat 注解&#10;- 删除了 AppRecommendActivityControllerTest 测试类">
      <option name="closed" value="true" />
      <created>1753623291212</created>
      <option name="number" value="00248" />
      <option name="presentableId" value="LOCAL-00248" />
      <option name="project" value="LOCAL" />
      <updated>1753623291212</updated>
    </task>
    <task id="LOCAL-00249" summary="feat(personals): 添加活动报名用户列表查询功能并优化活动详情展示&#10;&#10;- 新增 AppActivityEnrollUserVo 类用于活动报名用户列表展示&#10;- 在 AppUserActivityRecordController 中添加 getEnrollUsers接口&#10;- 在 IUserActivityRecordService 中添加 queryActivityEnrollUsers 方法&#10;- 在 UserActivityRecordServiceImpl 中实现 queryActivityEnrollUsers 方法&#10;- 优化 AppBuddyActivityDetailVo 中的时间格式化方式">
      <option name="closed" value="true" />
      <created>1753749009109</created>
      <option name="number" value="00249" />
      <option name="presentableId" value="LOCAL-00249" />
      <option name="project" value="LOCAL" />
      <updated>1753749009109</updated>
    </task>
    <task id="LOCAL-00250" summary="feat(user): 添加用户活动报名相关功能&#10;&#10;- 新增活动报名用户查询 BO&#10;- 修改用户基本信息 VO，将 phonenumber 字段重命名为 phoneNumber&#10;- 在用户详情控制器中添加查询用户是否已认证的方法- 新增用户举报创建 BO- 新增活动统计数量 VO- 更新用户活动记录相关代码，使用新的统计数量 VO">
      <option name="closed" value="true" />
      <created>1753836685779</created>
      <option name="number" value="00250" />
      <option name="presentableId" value="LOCAL-00250" />
      <option name="project" value="LOCAL" />
      <updated>1753836685779</updated>
    </task>
    <task id="LOCAL-00251" summary="refactor(bani-personals):重构用户活动相关代码&#10;&#10;- 删除了 ActivitySignInTest.java 文件&#10;- 重命名了多个类和包，以更好地组织代码结构&#10;- 更新了相关的控制器和服务接口&#10;- 优化了代码组织，提高了可维护性">
      <option name="closed" value="true" />
      <created>1753872976876</created>
      <option name="number" value="00251" />
      <option name="presentableId" value="LOCAL-00251" />
      <option name="project" value="LOCAL" />
      <updated>1753872976876</updated>
    </task>
    <task id="LOCAL-00252" summary="refactor(personals): 重构用户黑名单相关功能&#10;&#10;- 更新前端访问路由地址为:/personals/user/blacklist&#10;- 重构 AppUserBlacklistVo 类，继承 AppOppositeUserItem&#10;- 移除 AppUserReportCreateBo 中的 @NotBlank 注解&#10;- 在 UserBlacklistServiceImpl 中添加 IUserDetailTransitionService依赖&#10;- 在黑名单列表查询中增加对方用户信息处理">
      <option name="closed" value="true" />
      <created>1753892091192</created>
      <option name="number" value="00252" />
      <option name="presentableId" value="LOCAL-00252" />
      <option name="project" value="LOCAL" />
      <updated>1753892091194</updated>
    </task>
    <task id="LOCAL-00253" summary="feat(user): 新增用户奖励系统并优化用户数据填写流程&#10;&#10;- 新增 IUserRewardService 接口及其实现类 UserRewardServiceImpl，用于处理用户奖励逻辑&#10;- 在 UserAlbumServiceImpl 和 UserDetailServiceImpl 中集成用户奖励服务&#10;- 新增用户完成任务的记录和奖励发放功能&#10;- 优化用户数据填写流程，包括精美图片上传和关于我信息填写&#10;- 新增用户标签查询功能，优化用户信息获取">
      <option name="closed" value="true" />
      <created>1753921004737</created>
      <option name="number" value="00253" />
      <option name="presentableId" value="LOCAL-00253" />
      <option name="project" value="LOCAL" />
      <updated>1753921004737</updated>
    </task>
    <task id="LOCAL-00254" summary="refactor(personals): 优化用户相关服务和 mapper 层&#10;&#10;- 在 UserAlbumServiceImpl 和 UserDetailServiceImpl 中添加 @Transactional 注解，确保数据一致性&#10;- 在 UserAlbumServiceImpl 中添加 @Lazy 注解，优化依赖注入&#10;- 修改 UserTagMapper 中的查询方法，提高查询效率">
      <option name="closed" value="true" />
      <created>1753921994646</created>
      <option name="number" value="00254" />
      <option name="presentableId" value="LOCAL-00254" />
      <option name="project" value="LOCAL" />
      <updated>1753921994647</updated>
    </task>
    <task id="LOCAL-00255" summary="feat(user): 新增用户资料填写奖励功能&#10;&#10;- 在 AuditUserDetailProcessor 中添加头像审核通过后的奖励逻辑&#10;- 在 UserRewardService 接口中新增头像上传和基础资料填写的奖励方法&#10;- 在 UserDetailServiceImpl 中添加用户资料填写后的奖励逻辑&#10;- 在 UserRewardServiceImpl 中实现头像上传和基础资料填写的奖励方法">
      <option name="closed" value="true" />
      <created>1753926995579</created>
      <option name="number" value="00255" />
      <option name="presentableId" value="LOCAL-00255" />
      <option name="project" value="LOCAL" />
      <updated>1753926995579</updated>
    </task>
    <task id="LOCAL-00256" summary="refactor(audit): 优化用户详情审计处理器&#10;&#10;- 移除了未使用的 import 语句&#10;- 简化了 execute 方法，直接返回 ContentAuditRes.transferUser()，即直接转人工审核&#10;- 修改了 getMapDetailById 方法，直接返回 UserDetail 对象而不是 UserDetailVo 对象">
      <option name="closed" value="true" />
      <created>1753965873094</created>
      <option name="number" value="00256" />
      <option name="presentableId" value="LOCAL-00256" />
      <option name="project" value="LOCAL" />
      <updated>1753965873096</updated>
    </task>
    <task id="LOCAL-00257" summary="refactor(audit): 重构内容审核功能&#10;&#10;- 新增 AdminContentAuditUserCommentVo 和 AdminContentAuditUserDetailVo 类用于内容审核视图展示&#10;- 修改 AuditCommentProcessor 和 AuditUserDetailProcessor 的 getMapDetailById 方法&#10;- 更新 ContentAuditDetailVo 的 businessData 字段类型&#10;- 调整 ContentAuditRecordServiceImpl 中的代码逻辑&#10;- 在 IUserCommentService 和 IUserDetailService 接口中添加新的查询方法- 实现 UserCommentServiceImpl 和 UserDetailServiceImpl 中的新方法">
      <option name="closed" value="true" />
      <created>1754065262596</created>
      <option name="number" value="00257" />
      <option name="presentableId" value="LOCAL-00257" />
      <option name="project" value="LOCAL" />
      <updated>1754065262596</updated>
    </task>
    <task id="LOCAL-00258" summary="refactor(bani-personals):重构社群相关功能&#10;&#10;- 修改 AppGroupBo 中用户成员字段名称为社群标签&#10;- 更新 AppUserOrderController 中路由地址和方法名称- 重构 Group 实体类，增加户籍地编码等字段&#10;- 更新 GroupMapper 中查询条件&#10;- 修改 GroupServiceImpl 中内容审核逻辑- 优化 UserAuthApplyServiceImpl 中用户认证流程">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00258" />
      <option name="presentableId" value="LOCAL-00258" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00259" summary="refactor(order): 重构用户订单相关代码&#10;&#10;- 移除未使用的测试类 AppUserMomentControllerTest 和 UserAccountServiceTest&#10;- 新增 AppUserOrderQueryBo 类用于用户订单查询&#10;- 更新 AppUserOrderController 和 IUserOrderService 接口，使用新的查询参数类- 修改 UserOrderMapper 接口，增加新的查询方法&#10;- 更新 UserOrderServiceImpl 类，实现新的查询方法&#10;- 调整 AppUserOrderVo 类，增加新的字段">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00259" />
      <option name="presentableId" value="LOCAL-00259" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00260" summary="refactor(bani-personals):优化用户详情查询 SQL&#10;&#10;-将 user_order 表替换为 user_activity_record 表，以准确反映活动记录&#10;- 简化活动参与数的查询逻辑，提高查询效率&#10;- 移除冗余的条件判断，优化 SQL可读性">
      <option name="closed" value="true" />
      <created>1754353874753</created>
      <option name="number" value="00260" />
      <option name="presentableId" value="LOCAL-00260" />
      <option name="project" value="LOCAL" />
      <updated>1754353874753</updated>
    </task>
    <task id="LOCAL-00261" summary="feat(user): 添加我的活动相关功能&#10;&#10;- 新增我的活动列表查询功能，包括全部活动、待处理活动、已报名活动和已发布活动&#10;- 实现我的活动详情查询，包含活动基本信息、报名状态等&#10;- 添加更新我的活动功能，支持审核失败的活动编辑- 新增相关数据结构和接口定义">
      <option name="closed" value="true" />
      <created>1754376070419</created>
      <option name="number" value="00261" />
      <option name="presentableId" value="LOCAL-00261" />
      <option name="project" value="LOCAL" />
      <updated>1754376070419</updated>
    </task>
    <task id="LOCAL-00262" summary="refactor(personals): 重构我的活动列表接口&#10;&#10;- 合并查询我发布的和我报名的活动&#10;- 优化活动列表的查询性能&#10;- 简化活动详情的获取逻辑&#10;- 更新活动状态和审核状态的处理方式- 调整微信模板消息的发送逻辑">
      <option name="closed" value="true" />
      <created>1754494343011</created>
      <option name="number" value="00262" />
      <option name="presentableId" value="LOCAL-00262" />
      <option name="project" value="LOCAL" />
      <updated>1754494343011</updated>
    </task>
    <task id="LOCAL-00263" summary="refactor(message): 重构消息发送逻辑&#10;&#10;- 移除 MessageSseSendConsumer 中的冗余代码&#10;- 将消息用户关系的插入逻辑移至 MsgContentUserServiceImpl&#10;- 优化消息发送流程，提高代码复用性和可维护性">
      <option name="closed" value="true" />
      <created>1754495552177</created>
      <option name="number" value="00263" />
      <option name="presentableId" value="LOCAL-00263" />
      <option name="project" value="LOCAL" />
      <updated>1754495552177</updated>
    </task>
    <task id="LOCAL-00264" summary="refactor(app): 重构用户相关接口和数据结构&#10;&#10;- 新增 AppSysUserVo 类用于封装用户信息&#10;- 修改用户信息获取接口，使用新的 AppSysUserVo 类&#10;- 更新相册、认证申请、用户详情等接口，增加 userId 参数支持查询指定用户&#10;-优化订单列表查询，自动设置当前用户 ID&#10;- 重构用户服务接口，增加新的用户查询方法- 优化相册服务接口，增加用户 ID 参数">
      <option name="closed" value="true" />
      <created>1754577991767</created>
      <option name="number" value="00264" />
      <option name="presentableId" value="LOCAL-00264" />
      <option name="project" value="LOCAL" />
      <updated>1754577991767</updated>
    </task>
    <task id="LOCAL-00265" summary="refactor(personals): 重构用户推荐服务&#10;&#10;- 整合 Nearby 和 SameCity 查询逻辑&#10;-优化用户匹配配置获取方式&#10;- 添加用户基础信息相关字段&#10;- 调整用户相册图片上传逻辑&#10;- 修改公众号订阅相关字段名称">
      <option name="closed" value="true" />
      <created>1754658851045</created>
      <option name="number" value="00265" />
      <option name="presentableId" value="LOCAL-00265" />
      <option name="project" value="LOCAL" />
      <updated>1754658851045</updated>
    </task>
    <task id="LOCAL-00266" summary="refactor(bani-personals):优化用户详情接口参数&#10;&#10;- 在 AppUserDetailController 中，将 getDetail 方法的 userId 参数添加 required = false 属性&#10;- 这样做允许在没有提供 userId 参数时，使用 LoginHelper.getUserId() 获取当前用户 ID">
      <option name="closed" value="true" />
      <created>1754697454208</created>
      <option name="number" value="00266" />
      <option name="presentableId" value="LOCAL-00266" />
      <option name="project" value="LOCAL" />
      <updated>1754697454208</updated>
    </task>
    <task id="LOCAL-00267" summary="feat(user): 添加用户基本信息获取和点亮礼物列表功能&#10;&#10;- 新增 AppUserBasicInfoVo 类用于用户基本信息展示&#10;- 在 AppUserDetailController 中添加 getUserBasicInfo 接口&#10;- 在 IUserDetailService 中添加 getUserBasicInfo 方法&#10;- 在 UserDetailServiceImpl 中实现 getUserBasicInfo 方法&#10;&#10;- 新增 AppUserLitGiftItemVo 和 AppUserLitGiftListVo 类用于点亮礼物列表展示&#10;- 在 AppUserGiftController 中添加 getLitGiftList 接口&#10;- 在 IUserGiftService 中添加 queryLitGiftListByUserId 方法- 在 UserGiftServiceImpl 中实现 queryLitGiftListByUserId 方法&#10;&#10;- 在 UserGiftMapper 中添加 selectReceivedGiftListByUserId 方法&#10;- 在 UserGiftMapper.xml 中实现 selectReceivedGiftListByUserId 的 SQL 查询">
      <option name="closed" value="true" />
      <created>1754699370989</created>
      <option name="number" value="00267" />
      <option name="presentableId" value="LOCAL-00267" />
      <option name="project" value="LOCAL" />
      <updated>1754699370989</updated>
    </task>
    <task id="LOCAL-00268" summary="feat(personals): 添加用户打招呼功能&#10;&#10;- 新增 AppUserGreetingController 控制器实现用户打招呼相关接口&#10;- 创建 AppUserGreetingCreateBo 和 AppUserGreetingVo 用于打招呼的请求和响应模型&#10;- 添加 GreetingStatus 枚举表示打招呼状态&#10;- 实现 IUserGreetingService 接口和 UserGreetingServiceImpl 服务类- 新增 UserGreeting 实体类和 UserGreetingMapper 映射类&#10;- 更新 UserDetailMapper 中的查询统计SQL">
      <option name="closed" value="true" />
      <created>1754872028330</created>
      <option name="number" value="00268" />
      <option name="presentableId" value="LOCAL-00268" />
      <option name="project" value="LOCAL" />
      <updated>1754872028330</updated>
    </task>
    <option name="localTasksCounter" value="269" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="javascript:npm:angular" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.apache.dubbo:dubbo" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.integration:spring-integration-core" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-stream" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="java:io.grpc:grpc-api" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava3:rxjava" />
    <option featureType="dependencySupport" implementationName="java:org.thymeleaf:thymeleaf" />
    <option featureType="dependencySupport" implementationName="java:jakarta.ws.rs:jakarta.ws.rs-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="1f13404b-7c1c-40f7-a7b7-d973f7f329a8" value="TOOL_WINDOW" />
        <entry key="c1947202-470a-4ba6-a501-724c091343f4" value="TOOL_WINDOW" />
        <entry key="fcd8fc25-f313-4cce-a321-bbb4f74e8d00" value="TOOL_WINDOW" />
        <entry key="b0f6cb10-d724-4ef3-a014-b7c653c8fcc3" value="TOOL_WINDOW" />
        <entry key="644f1cc1-7319-4f22-9eb7-3778c01b8901" value="TOOL_WINDOW" />
        <entry key="8cbe123a-2973-4728-a681-19b5c5d3aae8" value="TOOL_WINDOW" />
        <entry key="1759b9ec-e7f2-4b0b-9957-1abc14a096d0" value="TOOL_WINDOW" />
        <entry key="f2d6d45d-ba6a-4b00-9ec0-7984b10be556" value="TOOL_WINDOW" />
        <entry key="753376c5-e338-42ae-a1a1-f3208db73c8a" value="TOOL_WINDOW" />
        <entry key="fa0a8316-45fc-40a5-aa17-4e2f44ad27d6" value="TOOL_WINDOW" />
        <entry key="4a6a64a9-318f-4dfd-be20-a31c27a229df" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="1759b9ec-e7f2-4b0b-9957-1abc14a096d0">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="1f13404b-7c1c-40f7-a7b7-d973f7f329a8">
          <value>
            <State>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="4a6a64a9-318f-4dfd-be20-a31c27a229df">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="644f1cc1-7319-4f22-9eb7-3778c01b8901">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="753376c5-e338-42ae-a1a1-f3208db73c8a">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="8cbe123a-2973-4728-a681-19b5c5d3aae8">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="b0f6cb10-d724-4ef3-a014-b7c653c8fcc3">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/ftpupdata/bani/bani-common" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="c1947202-470a-4ba6-a501-724c091343f4">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="f2d6d45d-ba6a-4b00-9ec0-7984b10be556">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:E:/ftpupdata/bani/bani-common" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="fa0a8316-45fc-40a5-aa17-4e2f44ad27d6">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="fcd8fc25-f313-4cce-a321-bbb4f74e8d00">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="refactor(bani-personals):优化评论删除逻辑和数据结构- 添加默认根节点 ID常量&#10;- 新增递增和递减顶级评论子评论数的方法&#10;- 新增查询审核通过顶级评论的方法&#10;- 优化评论删除逻辑，支持级联删除子" />
    <MESSAGE value="refactor(personals): 重构评论相关接口和实体&#10;&#10;- 修改地址字段名从 addr 改为 location&#10;- 新增 AppUserCommentRootTableInfo 类用于评论列表分页查询结果&#10;- 更新相关接口和实现类，使用新的分页信息类&#10;- 优化评论查询接口，增加评论总数字段&#10;-调整 UserMomentTagServiceImpl 中的标签添加逻辑" />
    <MESSAGE value="feat(personals): 优化消息查询功能&#10;&#10;- 为 MessageSubType 枚举添加 of 方法，用于根据 value 获取枚举对象&#10;- 在 MsgContentItemVo 中添加 title 字段，用于存储消息标题- 修改 MsgContentUserMapper.xml，优化消息查询 SQL 语句- 在 MsgContentUserServiceImpl 中添加消息标题的处理逻辑" />
    <MESSAGE value="refactor(message): 优化消息内容相关模型和逻辑&#10;&#10;- 将 AppMsgContentUserGroupItemVo 中的 uid 字段类型从 String改为 Long&#10;- 在 MsgContentUserBo 中添加 groupId 和 sendUserId 字段&#10;- 修改 MsgContentItemVo 继承 AppOppositeUserAvatarItem 并使用 @EqualsAndHashCode&#10;- 更新 MessageSseSendConsumer 中的 buildBo 方法逻辑&#10;- 调整 MsgContentUserServiceImpl 中的查询结果处理逻辑" />
    <MESSAGE value="feat(user): 添加用户经纬度更新功能并优化时间格式&#10;&#10;- 新增用户经纬度更新功能，支持更新用户位置信息&#10;- 在 AppBuddyActivityVo 中使用 LocalDateTimeMinuteSerialize 替代原有的 JsonFormat 注解&#10;- 删除了 AppRecommendActivityControllerTest 测试类" />
    <MESSAGE value="feat(personals): 添加活动报名用户列表查询功能并优化活动详情展示&#10;&#10;- 新增 AppActivityEnrollUserVo 类用于活动报名用户列表展示&#10;- 在 AppUserActivityRecordController 中添加 getEnrollUsers接口&#10;- 在 IUserActivityRecordService 中添加 queryActivityEnrollUsers 方法&#10;- 在 UserActivityRecordServiceImpl 中实现 queryActivityEnrollUsers 方法&#10;- 优化 AppBuddyActivityDetailVo 中的时间格式化方式" />
    <MESSAGE value="feat(user): 添加用户活动报名相关功能&#10;&#10;- 新增活动报名用户查询 BO&#10;- 修改用户基本信息 VO，将 phonenumber 字段重命名为 phoneNumber&#10;- 在用户详情控制器中添加查询用户是否已认证的方法- 新增用户举报创建 BO- 新增活动统计数量 VO- 更新用户活动记录相关代码，使用新的统计数量 VO" />
    <MESSAGE value="refactor(bani-personals):重构用户活动相关代码&#10;&#10;- 删除了 ActivitySignInTest.java 文件&#10;- 重命名了多个类和包，以更好地组织代码结构&#10;- 更新了相关的控制器和服务接口&#10;- 优化了代码组织，提高了可维护性" />
    <MESSAGE value="refactor(personals): 重构用户黑名单相关功能&#10;&#10;- 更新前端访问路由地址为:/personals/user/blacklist&#10;- 重构 AppUserBlacklistVo 类，继承 AppOppositeUserItem&#10;- 移除 AppUserReportCreateBo 中的 @NotBlank 注解&#10;- 在 UserBlacklistServiceImpl 中添加 IUserDetailTransitionService依赖&#10;- 在黑名单列表查询中增加对方用户信息处理" />
    <MESSAGE value="feat(user): 新增用户奖励系统并优化用户数据填写流程&#10;&#10;- 新增 IUserRewardService 接口及其实现类 UserRewardServiceImpl，用于处理用户奖励逻辑&#10;- 在 UserAlbumServiceImpl 和 UserDetailServiceImpl 中集成用户奖励服务&#10;- 新增用户完成任务的记录和奖励发放功能&#10;- 优化用户数据填写流程，包括精美图片上传和关于我信息填写&#10;- 新增用户标签查询功能，优化用户信息获取" />
    <MESSAGE value="refactor(personals): 优化用户相关服务和 mapper 层&#10;&#10;- 在 UserAlbumServiceImpl 和 UserDetailServiceImpl 中添加 @Transactional 注解，确保数据一致性&#10;- 在 UserAlbumServiceImpl 中添加 @Lazy 注解，优化依赖注入&#10;- 修改 UserTagMapper 中的查询方法，提高查询效率" />
    <MESSAGE value="feat(user): 新增用户资料填写奖励功能&#10;&#10;- 在 AuditUserDetailProcessor 中添加头像审核通过后的奖励逻辑&#10;- 在 UserRewardService 接口中新增头像上传和基础资料填写的奖励方法&#10;- 在 UserDetailServiceImpl 中添加用户资料填写后的奖励逻辑&#10;- 在 UserRewardServiceImpl 中实现头像上传和基础资料填写的奖励方法" />
    <MESSAGE value="refactor(audit): 优化用户详情审计处理器&#10;&#10;- 移除了未使用的 import 语句&#10;- 简化了 execute 方法，直接返回 ContentAuditRes.transferUser()，即直接转人工审核&#10;- 修改了 getMapDetailById 方法，直接返回 UserDetail 对象而不是 UserDetailVo 对象" />
    <MESSAGE value="refactor(audit): 重构内容审核功能&#10;&#10;- 新增 AdminContentAuditUserCommentVo 和 AdminContentAuditUserDetailVo 类用于内容审核视图展示&#10;- 修改 AuditCommentProcessor 和 AuditUserDetailProcessor 的 getMapDetailById 方法&#10;- 更新 ContentAuditDetailVo 的 businessData 字段类型&#10;- 调整 ContentAuditRecordServiceImpl 中的代码逻辑&#10;- 在 IUserCommentService 和 IUserDetailService 接口中添加新的查询方法- 实现 UserCommentServiceImpl 和 UserDetailServiceImpl 中的新方法" />
    <MESSAGE value="refactor(bani-personals):重构社群相关功能&#10;&#10;- 修改 AppGroupBo 中用户成员字段名称为社群标签&#10;- 更新 AppUserOrderController 中路由地址和方法名称- 重构 Group 实体类，增加户籍地编码等字段&#10;- 更新 GroupMapper 中查询条件&#10;- 修改 GroupServiceImpl 中内容审核逻辑- 优化 UserAuthApplyServiceImpl 中用户认证流程" />
    <MESSAGE value="refactor(order): 重构用户订单相关代码&#10;&#10;- 移除未使用的测试类 AppUserMomentControllerTest 和 UserAccountServiceTest&#10;- 新增 AppUserOrderQueryBo 类用于用户订单查询&#10;- 更新 AppUserOrderController 和 IUserOrderService 接口，使用新的查询参数类- 修改 UserOrderMapper 接口，增加新的查询方法&#10;- 更新 UserOrderServiceImpl 类，实现新的查询方法&#10;- 调整 AppUserOrderVo 类，增加新的字段" />
    <MESSAGE value="refactor(bani-personals):优化用户详情查询 SQL&#10;&#10;-将 user_order 表替换为 user_activity_record 表，以准确反映活动记录&#10;- 简化活动参与数的查询逻辑，提高查询效率&#10;- 移除冗余的条件判断，优化 SQL可读性" />
    <MESSAGE value="feat(user): 添加我的活动相关功能&#10;&#10;- 新增我的活动列表查询功能，包括全部活动、待处理活动、已报名活动和已发布活动&#10;- 实现我的活动详情查询，包含活动基本信息、报名状态等&#10;- 添加更新我的活动功能，支持审核失败的活动编辑- 新增相关数据结构和接口定义" />
    <MESSAGE value="refactor(personals): 重构我的活动列表接口&#10;&#10;- 合并查询我发布的和我报名的活动&#10;- 优化活动列表的查询性能&#10;- 简化活动详情的获取逻辑&#10;- 更新活动状态和审核状态的处理方式- 调整微信模板消息的发送逻辑" />
    <MESSAGE value="refactor(message): 重构消息发送逻辑&#10;&#10;- 移除 MessageSseSendConsumer 中的冗余代码&#10;- 将消息用户关系的插入逻辑移至 MsgContentUserServiceImpl&#10;- 优化消息发送流程，提高代码复用性和可维护性" />
    <MESSAGE value="refactor(app): 重构用户相关接口和数据结构&#10;&#10;- 新增 AppSysUserVo 类用于封装用户信息&#10;- 修改用户信息获取接口，使用新的 AppSysUserVo 类&#10;- 更新相册、认证申请、用户详情等接口，增加 userId 参数支持查询指定用户&#10;-优化订单列表查询，自动设置当前用户 ID&#10;- 重构用户服务接口，增加新的用户查询方法- 优化相册服务接口，增加用户 ID 参数" />
    <MESSAGE value="refactor(personals): 重构用户推荐服务&#10;&#10;- 整合 Nearby 和 SameCity 查询逻辑&#10;-优化用户匹配配置获取方式&#10;- 添加用户基础信息相关字段&#10;- 调整用户相册图片上传逻辑&#10;- 修改公众号订阅相关字段名称" />
    <MESSAGE value="refactor(bani-personals):优化用户详情接口参数&#10;&#10;- 在 AppUserDetailController 中，将 getDetail 方法的 userId 参数添加 required = false 属性&#10;- 这样做允许在没有提供 userId 参数时，使用 LoginHelper.getUserId() 获取当前用户 ID" />
    <MESSAGE value="feat(user): 添加用户基本信息获取和点亮礼物列表功能&#10;&#10;- 新增 AppUserBasicInfoVo 类用于用户基本信息展示&#10;- 在 AppUserDetailController 中添加 getUserBasicInfo 接口&#10;- 在 IUserDetailService 中添加 getUserBasicInfo 方法&#10;- 在 UserDetailServiceImpl 中实现 getUserBasicInfo 方法&#10;&#10;- 新增 AppUserLitGiftItemVo 和 AppUserLitGiftListVo 类用于点亮礼物列表展示&#10;- 在 AppUserGiftController 中添加 getLitGiftList 接口&#10;- 在 IUserGiftService 中添加 queryLitGiftListByUserId 方法- 在 UserGiftServiceImpl 中实现 queryLitGiftListByUserId 方法&#10;&#10;- 在 UserGiftMapper 中添加 selectReceivedGiftListByUserId 方法&#10;- 在 UserGiftMapper.xml 中实现 selectReceivedGiftListByUserId 的 SQL 查询" />
    <MESSAGE value="feat(personals): 添加用户打招呼功能&#10;&#10;- 新增 AppUserGreetingController 控制器实现用户打招呼相关接口&#10;- 创建 AppUserGreetingCreateBo 和 AppUserGreetingVo 用于打招呼的请求和响应模型&#10;- 添加 GreetingStatus 枚举表示打招呼状态&#10;- 实现 IUserGreetingService 接口和 UserGreetingServiceImpl 服务类- 新增 UserGreeting 实体类和 UserGreetingMapper 映射类&#10;- 更新 UserDetailMapper 中的查询统计SQL" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(personals): 添加用户打招呼功能&#10;&#10;- 新增 AppUserGreetingController 控制器实现用户打招呼相关接口&#10;- 创建 AppUserGreetingCreateBo 和 AppUserGreetingVo 用于打招呼的请求和响应模型&#10;- 添加 GreetingStatus 枚举表示打招呼状态&#10;- 实现 IUserGreetingService 接口和 UserGreetingServiceImpl 服务类- 新增 UserGreeting 实体类和 UserGreetingMapper 映射类&#10;- 更新 UserDetailMapper 中的查询统计SQL" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/cloud/spring-cloud-gateway-server/4.1.5/spring-cloud-gateway-server-4.1.5-sources.jar!/org/springframework/cloud/gateway/filter/factory/StripPrefixGatewayFilterFactory.java</url>
          <line>64</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="map.get(&quot;value&quot;)" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>