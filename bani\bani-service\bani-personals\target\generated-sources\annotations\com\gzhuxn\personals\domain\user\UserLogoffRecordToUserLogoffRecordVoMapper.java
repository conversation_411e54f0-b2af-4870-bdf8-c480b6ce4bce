package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserLogoffRecordBoToUserLogoffRecordMapper;
import com.gzhuxn.personals.domain.user.vo.UserLogoffRecordVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserLogoffRecordBoToUserLogoffRecordMapper.class,UserLogoffRecordToUserCurrentLogoffRecordVoMapper.class},
    imports = {}
)
public interface UserLogoffRecordToUserLogoffRecordVoMapper extends BaseMapper<UserLogoffRecord, UserLogoffRecordVo> {
}
