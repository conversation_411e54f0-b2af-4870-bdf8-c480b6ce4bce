package com.gzhuxn.personals.controller.app.group.bo;

import com.gzhuxn.personals.domain.group.GroupTag;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppGroupTagBoToGroupTagMapperImpl implements AppGroupTagBoToGroupTagMapper {

    @Override
    public GroupTag convert(AppGroupTagBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupTag groupTag = new GroupTag();

        groupTag.setId( arg0.getId() );
        groupTag.setGroupId( arg0.getGroupId() );
        groupTag.setVal( arg0.getVal() );
        groupTag.setName( arg0.getName() );

        return groupTag;
    }

    @Override
    public GroupTag convert(AppGroupTagBo arg0, GroupTag arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setVal( arg0.getVal() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
