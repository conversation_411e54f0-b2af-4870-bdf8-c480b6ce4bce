<script setup>
import {
	onLaunch,
	onShow,
	onHide
} from "@dcloudio/uni-app";
//注意：一定要下载text-encoding-shim包，后台地址和token和后台约束好，复制代码后替换url和地址，即可使用。
import * as TextEncoding from "text-encoding-shim";

let buffer = ''; //定义在页面的最外面。
let encoder = new TextEncoding.TextDecoder("utf-8");//定义在页面的最外面。
const requestTask = null; //定义在页面的最外面。

onLaunch(() => {
	console.log('App Launch')
	//startSSE()
}),
	onShow(() => {
		console.log('App Show')
	}),
	onHide(() => {
		console.log('App Hide')
	})

const startSSE = () => {
	requestTask = uni.request({
		url: "/resource/sse",
		method: 'get',
		header: {
			'Accept': 'text/event-stream',//必填返回的是文本
			'Cache-Control': 'no-cache',
			'Connection': 'keep-alive',
			'Authorization': 'Bearer '
		},
		responseType: 'arraybuffer',//接受的是流
		enableChunked: true,//开启分包
		success: (res) => { }
	});

	requestTask.onChunkReceived((res) => {
		try {
			// 将ArrayBuffer转为字符串并追加到缓冲区

			let arrayBuffer = new Uint8Array(res.data)
			let chunkStr = encoder.decode(arrayBuffer);
			buffer += chunkStr;

			// 分割完整事件（以\n\n分隔）
			let eventEndIndex;
			while ((eventEndIndex = buffer.indexOf('\n\n')) >= 0) {
				const eventData = buffer.slice(0, eventEndIndex);
				buffer = buffer.slice(eventEndIndex + 2);

				// 解析SSE事件内容
				const message = this.parseSSEEvent(eventData);
				if (message) {
					console.log('收到事件:', message);
					// 触发自定义事件或更新数据 
					//数据拿到后，做自己的业务处理
				}
			}
		} catch (e) {
			console.error('数据处理异常:', e);
		}
	});
}
// 解析SSE事件格式
const parseSSEEvent = (rawData) => {
	const lines = rawData.split('\n');
	let event = { data: '' };

	lines.forEach(line => {
		const colonIndex = line.indexOf(':');
		if (colonIndex > 0) {
			const field = line.slice(0, colonIndex).trim();
			const value = line.slice(colonIndex + 1).trim();
			if (field === 'data') {
				event.data += value + '\n';
			} else if (field === 'event') {
				event.type = value;
			} else if (field === 'id') {
				event.id = value;
			} else if (field === 'retry') {
				event.retry = parseInt(value, 10);
			}
		}
	});

	event.data = event.data.trimEnd(); // 移除末尾换行
	return event.data ? event : null;
}
</script>

<style lang="scss">
@import '@/static/fonts/iconfont.css';

#app {
	//background-image: linear-gradient(to bottom, #A5A0DD, #F5F7FA);
	background-image: linear-gradient(110deg, #FFEEF1 10%, #FAFAFA 60%, #F5F4FB);
}

.margin-split {
	margin: 0 20rpx;
}

.page-content {
	@extend .margin-split;
	margin-top: 20rpx;
}


.file-picker__box {
	border: 0rpx dashed #e5e5e5 !important;
	background: #fff !important;
}

.top-menu {
	background: rgba(255, 255, 255, 0.95);
	padding: 32rpx;
	margin-top: 26rpx;
	margin-bottom: 16rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);

	:deep(.uni-segmented-control) {
		background-color: transparent;

		.segmented-control__item {
			color: #666;
			font-weight: 500;

			&.segmented-control__item--button--active {
				color: $primary-color;
				font-weight: 600;
			}
		}
	}
}
</style>