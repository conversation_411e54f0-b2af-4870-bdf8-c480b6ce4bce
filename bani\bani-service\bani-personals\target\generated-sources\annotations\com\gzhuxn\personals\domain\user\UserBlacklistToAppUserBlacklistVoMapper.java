package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.blacklist.AppUserBlacklistVo;
import com.gzhuxn.personals.controller.app.user.vo.blacklist.AppUserBlacklistVoToUserBlacklistMapper;
import com.gzhuxn.personals.domain.user.bo.UserBlacklistBoToUserBlacklistMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserBlacklistBoToUserBlacklistMapper.class,AppUserBlacklistVoToUserBlacklistMapper.class,UserBlacklistToUserBlacklistVoMapper.class},
    imports = {}
)
public interface UserBlacklistToAppUserBlacklistVoMapper extends BaseMapper<UserBlacklist, AppUserBlacklistVo> {
  @Mapping(
      target = "uid",
      source = "oppositeUserId"
  )
  AppUserBlacklistVo convert(UserBlacklist source);

  @Mapping(
      target = "uid",
      source = "oppositeUserId"
  )
  AppUserBlacklistVo convert(UserBlacklist source, @MappingTarget AppUserBlacklistVo target);
}
