<template>
	<!-- 自定义导航栏 -->
	<scroll-nav-page :title="userInfo.nickName" :show-back="true">
		<template #content>
			<view class="profile-content">
				<!-- 用户头像和基本信息 -->
				<view class="user-header">
					<!-- 用户图片展示区域 -->
					<view class="user-gallery" v-if="userAlbums && userAlbums.length > 0">
						<view class="gallery-main">
							<!-- 主图片容器 -->
							<view class="main-photo-container">
								<image v-for="(album, index) in userAlbums" :key="index" class="main-photo"
									:class="{ 'active': index === currentPhotoIndex }" :src="album.imageUrl"
									mode="aspectFill" @click="previewPhoto" @load="handlePhotoLoad(index)" />
							</view>

							<!-- 图片缩略图导航 -->
							<view class="photo-thumbnails" v-if="userAlbums.length > 1">
								<view class="thumbnail-item" v-for="(album, index) in userAlbums" :key="index"
									:class="{ 'active': index === currentPhotoIndex }" @click="switchPhoto(index)">
									<image class="thumbnail-image" :src="album.imageUrl" mode="aspectFill" />
								</view>
							</view>

							<!-- 图片计数器 -->
							<view class="photo-counter" v-if="userAlbums.length > 1">
								<text>{{ currentPhotoIndex + 1 }}/{{ userAlbums.length }}</text>
							</view>
						</view>
					</view>

					<view class="user-info">
						<view class="basic-info">
							<view class="name-row">
								<view class="name-gender">
									<text class="nickname">{{ userInfo.nickName }}</text>
									<text class="iconfont gender-icon"
										:class="userInfo.gender === 'male' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'"
										:style="{ color: userInfo.gender === 'male' ? '#4A90E2' : '#E91E63' }"></text>
									<view class="user-id-container" @click="copyUserId">
										<text class="user-id-icon">ID</text>
										<text class="user-id-value">{{ userInfo.pid }}</text>
									</view>
								</view>
								<!-- 关注按钮 -->
								<view class="follow-btn-inline"
									:class="{ 'followed': userInfo.isFollowed, 'loading': isFollowLoading }"
									@click="toggleFollow">
									<uni-icons v-if="!isFollowLoading"
										:type="userInfo.isFollowed ? 'star-filled' : 'star'" size="20" color="#fff" />
									<uni-icons v-else type="spinner-cycle" size="20" color="#fff" />
									<text class="follow-text">
										{{ isFollowLoading ? '处理中...' : (userInfo.isFollowed ? '已关注' : '关注') }}
									</text>
								</view>
							</view>
							<view class="user-stats">
								<text class="stat-item">{{ userInfo.age }}</text>
								<text class="stat-item">{{ userInfo.height }}cm</text>
								<text class="stat-item">{{ userInfo.weight }}kg</text>
								<text class="stat-item">{{ userInfo.edu }}</text>
								<text class="stat-item">{{ userInfo.revenue }}</text>
							</view>
						</view>

						<view class="location-info">
							<!-- 出生年月·星座 -->
							<view class="location-item">
								<uni-icons type="calendar" size="16" color="#999" />
								<text class="location-text">{{ userInfo.birthdayYear }} · {{ userInfo.star }}</text>
							</view>
							<!-- 职业 -->
							<view class="location-item">
								<uni-icons type="wallet" size="16" color="#999" />
								<text class="location-text">{{ userInfo.job }}</text>
							</view>
							<!-- 现居地址·籍贯地址 -->
							<view class="location-item">
								<uni-icons type="location" size="16" color="#999" />
								<text class="location-text">现居{{ userInfo.addrNew }} · {{ userInfo.addr }}人</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 认证卡片 -->
				<AuthCard v-if="!authStatus.isAuthenticated" :user-id="userId" :auth-types="authStatus.authTypes"
					@auth-complete="handleAuthComplete" />

				<!-- 关于我 -->
				<view class="about-me">
					<text class="section-title">关于我</text>
					<view class="tags-container" v-if="userInfo.aboutMeTags && userInfo.aboutMeTags.length > 0">
						<view class="tag-group">
							<text v-for="(tag, index) in userInfo.aboutMeTags" :key="index" class="tag-label">
								{{ tag.label }}
							</text>
						</view>
					</view>
					<view class="tags-container" v-else>
						<view class="tag-group">
							<text class="tag-label no-data">暂无标签信息</text>
						</view>
					</view>
					<view class="divider-line"></view>
					<text class="intro-text" v-if="userInfo.introduction">{{ userInfo.introduction }}</text>
					<text class="intro-text no-data" v-else>暂无个人介绍</text>

					<!-- 个人优势 -->
					<view class="advantages-section" v-if="userInfo.advantages">
						<view class="divider-line"></view>
						<text class="advantages-title">个人优势</text>
						<text class="advantages-text">{{ userInfo.advantages }}</text>
					</view>
				</view>


				<!-- 最新动态 -->
				<view class="latest-moments">
					<view class="moment-header" @click="goToMoments">
						<view class="moment-title-row">
							<text class="section-title">最新动态</text>
							<text class="moment-count">（{{ latestMoment.recentCount || 0 }}）</text>
						</view>
						<view class="more-arrow">
							<uni-icons type="right" size="16" color="#999" />
						</view>
					</view>
					<view class="moment-content" @click="goToMoments">
						<view class="moment-main">
							<view class="moment-text-area">
								<text class="moment-text">{{ latestMoment.text }}</text>
								<text class="moment-time">{{ latestMoment.time }}</text>
							</view>
							<image v-if="latestMoment.image" :src="latestMoment.image" class="moment-image"
								mode="aspectFill" />
						</view>
					</view>
				</view>

				<!-- Ta希望你 -->
				<view class="mate-requirements" v-if="userInfo.isMatched">
					<text class="section-title">Ta希望你</text>
					<view class="tags-container" v-if="userInfo.requirementTags && userInfo.requirementTags.length > 0">
						<view class="tag-group">
							<text v-for="(tag, index) in userInfo.requirementTags" :key="index" class="tag-label">
								{{ tag.label }}
							</text>
						</view>
					</view>
					<view class="tags-container" v-else>
						<view class="tag-group">
							<text class="tag-label no-data">暂无要求标签</text>
						</view>
					</view>
					<view class="divider-line"></view>
					<text class="req-description" v-if="userInfo.dreamPartnerDescription">
						{{ userInfo.dreamPartnerDescription }}
					</text>
					<text class="req-description no-data" v-else>
						暂无理想型描述
					</text>
				</view>

				<!-- 礼物墙列表 -->
				<view class="gift-section">
					<view class="gift-header" @click="goToGiftPage">
						<view class="gift-title-row">
							<text class="section-title">礼物墙</text>
							<text class="gift-count-text">（已收到{{ giftWallData.totalGiftCount }}份礼物）</text>
						</view>
						<view class="gift-arrow">
							<uni-icons type="right" size="16" color="#999" />
						</view>
					</view>
					<view class="gifts-container" v-if="giftWallData.giftList && giftWallData.giftList.length > 0">
						<view class="gift-item" v-for="(gift, index) in giftWallData.giftList" :key="gift.id || index">
							<image :src="gift.giftIcon" class="gift-icon" />
							<text class="gift-name">{{ gift.giftName }}</text>
							<text class="gift-count">×{{ gift.giftNum }}</text>
						</view>
					</view>
					<view class="gifts-container" v-else>
						<view class="empty-gifts">
							<text class="empty-text">暂无收到的礼物</text>
						</view>
					</view>
				</view>
			</view>
		</template>
	</scroll-nav-page>

	<!-- 礼物弹窗 -->
	<GiftModal :visible="giftModalVisible" :target-user="userInfo" :sending="isSendingGift" @close="closeGiftModal"
		@send="handleSendGift" />

	<!-- 浮动底部操作按钮 -->
	<view class="floating-bottom-actions">
		<view class="action-btn gift-btn" @click="sendGift">
			<uni-icons type="gift" size="18" color="#696CF3" />
			<text class="btn-text">送礼物</text>
		</view>
		<view class="action-btn main-btn" @click="requestKnow">
			<text class="hi-text">Hi</text>
			<text class="btn-text">想认识Ta</text>
		</view>
		<view class="action-btn chat-btn" @click="startChat">
			<uni-icons type="chat" size="18" color="#fff" />
			<text class="btn-text">搭讪</text>
		</view>
	</view>

	<!-- 右侧浮动按钮组 -->
	<view class="floating-buttons-group">
		<!-- 分享按钮 -->
		<view class="floating-btn share-btn" @click="shareProfile">
			<uni-icons type="redo" size="20" color="#fff" />
		</view>

		<!-- 更多按钮 -->
		<view class="floating-btn more-btn" @click="toggleMoreMenu">
			<uni-icons type="more-filled" size="20" color="#fff" />
		</view>

		<!-- 横向弹出的操作按钮 -->
		<view class="horizontal-menu" :class="{ 'show': showMoreMenu }">
			<view class="menu-btn report-btn" @click="reportUser">
				<uni-icons type="info" size="18" color="#ff6b6b" />
				<text class="menu-text">举报</text>
			</view>
			<view class="menu-btn block-btn" @click="blockUser">
				<uni-icons type="minus-filled" size="18" color="#666" />
				<text class="menu-text">拉黑</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import GiftModal from './gift/gift-modal.vue'
import { onLoad } from '@dcloudio/uni-app'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import AuthCard from '@/components/auth-card/auth-card.vue'
import { getUserDetail } from '@/api/my/my'
import { getUserLatestMoment } from '@/api/moment/moment'
import { getAlbums } from '@/api/my/album'
import { createUserHomeBrowseHistory, createUserAlbumBrowseHistory } from '@/api/my/browse'
import { toggleUserFollow } from '@/api/my/follow'
import { sendGiftToUser, getMyReceivedGifts, getUserGiftWall } from '@/api/my/gift'
import { addBlacklist } from '@/api/my/blacklist'

// 获取页面参数
let userId = ref('1')

// 用户详细信息
const userDetail = ref({})

// 用户相册
const userAlbums = ref([])

// 认证状态
const authStatus = ref({
	isAuthenticated: false,
	authTypes: []
})

// 页面加载时获取参数
onLoad((options) => {
	console.log('页面参数:', options)
	if (options && options.userId) {
		userId.value = options.userId
		// 记录用户主页浏览历史
		recordUserHomeBrowse(options.userId)
		loadUserData()
	}
})

// 用户信息
const userInfo = ref({})
// 状态
const isLiked = ref(false)
const isFollowed = ref(false)
const currentPhotoIndex = ref(0)
const loadedPhotos = ref(new Set())
const showMoreMenu = ref(false)
const receivedGiftsCount = ref(23) // 已收到的礼物数量
const isFollowLoading = ref(false) // 关注操作加载状态
const isSendingGift = ref(false) // 送礼物加载状态

const giftModalVisible = ref(false) // 礼物弹窗显示状态

// 礼物墙数据
const giftWallData = ref({
	totalGiftCount: 0,
	giftList: []
})

// 最新动态数据
const latestMoment = reactive({
	recentCount: 0,
	text: '暂无动态内容',
	time: '',
	image: null
})

// 切换更多菜单显示状态
const toggleMoreMenu = () => {
	showMoreMenu.value = !showMoreMenu.value
}

// 图片相关方法
const switchPhoto = (index) => {
	if (index === currentPhotoIndex.value) return

	// 直接切换，不显示加载状态
	currentPhotoIndex.value = index

	// 记录相册图片浏览历史
	if (userAlbums.value[index] && userAlbums.value[index].id) {
		recordUserAlbumBrowse(userAlbums.value[index].id)
	}
}

const handlePhotoLoad = (index) => {
	loadedPhotos.value.add(index)
}

const toggleLike = () => {
	isLiked.value = !isLiked.value
	uni.showToast({
		title: isLiked.value ? '已喜欢' : '取消喜欢',
		icon: 'success'
	})
}

const toggleFollow = () => {
	// 防止重复点击
	if (isFollowLoading.value) {
		return
	}

	if (!userInfo.value.userId) {
		uni.showToast({
			title: '用户信息无效',
			icon: 'none'
		})
		return
	}

	// 设置加载状态
	isFollowLoading.value = true
	toggleUserFollow(userInfo.value.userId, userInfo.value.isFollowed).then(response => {
		// 更新关注状态
		userInfo.value.isFollowed = !userInfo.value.isFollowed
		isFollowed.value = userInfo.value.isFollowed

		uni.showToast({
			title: userInfo.value.isFollowed ? '关注成功' : '取消关注成功',
			icon: 'success',
			duration: 1000
		})

		console.log('关注状态切换成功:', userInfo.value.isFollowed)
	}).finally(() => {
		// 清除加载状态
		isFollowLoading.value = false
	})
}

const copyUserId = () => {
	uni.setClipboardData({
		data: userInfo.value.userId,
		success: () => {
			uni.showToast({
				title: '用户ID已复制',
				icon: 'success',
				duration: 1500
			})
		},
		fail: () => {
			uni.showToast({
				title: '复制失败',
				icon: 'none',
				duration: 1500
			})
		}
	})
}

// 送礼物
const sendGift = () => {
	giftModalVisible.value = true
}

// 关闭礼物弹窗
const closeGiftModal = () => {
	giftModalVisible.value = false
	// 重置发送状态
	isSendingGift.value = false
}

// 处理送礼物
const handleSendGift = async (giftData) => {
	// 防止重复点击
	if (isSendingGift.value) {
		return
	}

	// 设置加载状态
	isSendingGift.value = true

	sendGiftToUser(
		userId.value, // 目标用户ID
		{
			id: giftData.gift.id,
			name: giftData.gift.name,
			price: giftData.gift.price
		},
		giftData.quantity || 1 // 礼物数量
	).then(response => {
		uni.showToast({
			title: `成功赠送${giftData.gift.name}`,
			icon: 'success'
		})

		// 关闭礼物弹窗
		closeGiftModal()
		fetchReceivedGiftsCount()
	}).catch(error => {
		console.error('送礼物失败:', error)
		uni.showToast({
			title: '送礼物失败，请重试',
			icon: 'none',
			duration: 2000
		})
	}).finally(() => {
		// 清除加载状态
		isSendingGift.value = false
	})
}

// 申请认识
const requestKnow = () => {
	// 跳转到打招呼页面
	uni.navigateTo({
		url: `/pagesubs/personals/greeting/greeting?userId=${userId.value}&nickName=${encodeURIComponent(userInfo.value.nickName)}&avatar=${userInfo.value.avatar}`,
		fail: (err) => {
			console.error('跳转打招呼页面失败:', err)
			uni.showToast({
				title: '跳转失败',
				icon: 'error'
			})
		}
	})
}

// 开始聊天
const startChat = () => {
	uni.showToast({
		title: '开始聊天',
		icon: 'success'
	})
}

// 分享用户资料
const shareProfile = () => {
	uni.showToast({
		title: '分享用户资料',
		icon: 'success'
	})
}

// 举报用户
const reportUser = () => {
	showMoreMenu.value = false // 关闭更多菜单
	uni.navigateTo({
		url: `/pagesubs/my/report/report?type=1&targetId=${userId.value}&targetName=${encodeURIComponent(userInfo.value.nickName)}`
	})
}

// 拉黑用户
const blockUser = () => {
	showMoreMenu.value = false // 关闭更多菜单
	uni.showModal({
		title: '拉黑用户',
		content: '拉黑后将不再看到该用户的信息，确定要拉黑吗？',
		confirmText: '确定拉黑',
		cancelText: '取消',
		success: (res) => {
			if (res.confirm) {
				addBlacklist({
					oppositeUserId: userId.value
				}).then(response => {
					uni.showToast({
						title: '已拉黑该用户',
						icon: 'success'
					})
				})
			}
		}
	})
}

// 跳转到动态页面
const goToMoments = () => {
	uni.navigateTo({
		url: `/pagesubs/personals/moments?userId=${userId.value}`,
		fail: (err) => {
			console.error('跳转动态页面失败:', err)
			uni.showToast({
				title: '跳转失败',
				icon: 'error'
			})
		}
	})
}

// 跳转到礼物页面
const goToGiftPage = () => {
	uni.navigateTo({
		url: `/pagesubs/personals/gift/gift?userId=${userId.value}`,
		fail: (err) => {
			console.error('跳转礼物页面失败:', err)
			uni.showToast({
				title: '跳转失败',
				icon: 'error'
			})
		}
	})
}

// 获取用户详细信息
const fetchUserDetail = () => {
	getUserDetail(userId.value).then(response => {
		userDetail.value = response.data
		// 更新用户信息显示
		updateUserInfo(response.data)
		// 获取用户礼物墙数据
		fetchGiftWall()
	})
}

// 获取用户收到的礼物数量
const fetchReceivedGiftsCount = () => {
	// 如果是查看其他用户，暂时不获取礼物数量（需要后端支持）
	if (userId.value && userId.value !== 'default') {
		// 这里可以根据实际需求调用相应的API
		// 目前保持默认值
		return
	}

	// 获取当前用户收到的礼物数量
	getMyReceivedGifts({ pageSize: 1, pageNum: 1 }).then(response => {
		if (response.code === 200 || response.code === 1) {
			receivedGiftsCount.value = response.total || 0
			console.log('收到的礼物数量:', receivedGiftsCount.value)
		}
	}).catch(error => {
		console.error('获取礼物数量失败:', error)
	})
}

// 获取用户礼物墙数据
const fetchGiftWall = () => {
	getUserGiftWall({
		userId: userId.value,
		pageSize: 10,
		pageNum: 1
	}).then(response => {
		giftWallData.value = response.data || { totalGiftCount: 0, giftList: [] }
		// 更新礼物数量显示
		receivedGiftsCount.value = giftWallData.value.totalGiftCount || 0
		console.log('礼物墙数据:', giftWallData.value)
	})
}

// 获取用户最新动态
const fetchUserLatestMoment = () => {
	getUserLatestMoment(userId.value).then(response => {
		if (response.data) {
			// 更新最新动态数据
			latestMoment.text = response.data.content
			latestMoment.time = response.data.time
			latestMoment.image = response.data.firstImage
			latestMoment.recentCount = response.data.recentMomentCount
		}
	})
}

// 获取用户相册
const fetchUserAlbums = () => {
	getAlbums(userId.value).then(response => {
		userAlbums.value = response.data
		// 更新用户图片展示
		// 如果有相册图片，记录第一张图片的浏览历史
		if (response.data.length > 0 && response.data[0].id) {
			recordUserAlbumBrowse(response.data[0].id)
		}
	});
}

// 从 tags 中提取关于我的介绍
const extractIntroductionFromTags = (tags) => {
	if (!Array.isArray(tags)) return ''

	const aboutMeTag = tags.find(tag => tag.tagKey === 'about_me')
	return aboutMeTag ? aboutMeTag.tagVal : ''
}

// 从 tags 中提取个人优势
const extractAdvantagesFromTags = (tags) => {
	if (!Array.isArray(tags)) return ''

	const advantagesTag = tags.find(tag => tag.tagKey === 'advantages')
	return advantagesTag ? advantagesTag.tagVal : ''
}

// 从 tags 中提取关于我的标签（namespace=matched）
const extractAboutMeTagsFromTags = (tags) => {
	if (!Array.isArray(tags)) return []

	return tags
		.filter(tag => tag.namespace === 'matched')
		.map(tag => ({
			key: tag.tagKey,
			value: tag.tagVal,
			label: tag.tagVal
		}))
}

// 从 requireTags 中提取要求标签（namespace=matched）
const extractRequirementTagsFromRequireTags = (requireTags) => {
	if (!Array.isArray(requireTags)) return []

	return requireTags
		.filter(tag => tag.namespace === 'matched')
		.map(tag => ({
			key: tag.tagKey,
			value: tag.tagVal,
			label: tag.tagVal
		}))
}

// 从 requireTags 中提取理想型描述（namespace=profile, tagKey=dream_partner）
const extractDreamPartnerFromRequireTags = (requireTags) => {
	if (!Array.isArray(requireTags)) return ''

	const dreamPartnerTag = requireTags.find(tag =>
		tag.namespace === 'profile' && tag.tagKey === 'dream_partner'
	)
	return dreamPartnerTag ? dreamPartnerTag.tagVal : ''
}

// 更新用户信息显示
const updateUserInfo = (userData) => {
	userInfo.value = userData
	userInfo.value.gender = userData.sex === '0' ? 'male' : 'female'

	// 从 tags 字段中提取关于我的介绍
	userInfo.value.introduction = extractIntroductionFromTags(userData.tags) || ''

	// 从 tags 字段中提取个人优势
	userInfo.value.advantages = extractAdvantagesFromTags(userData.tags) || ''

	// 从 tags 字段中提取关于我的标签
	userInfo.value.aboutMeTags = extractAboutMeTagsFromTags(userData.tags) || []

	// 从 requireTags 字段中提取要求标签（namespace=matched）
	userInfo.value.requirementTags = extractRequirementTagsFromRequireTags(userData.requireTags) || []

	// 从 requireTags 字段中提取理想型描述（namespace=profile, tagKey=dream_partner）
	userInfo.value.dreamPartnerDescription = extractDreamPartnerFromRequireTags(userData.requireTags) || ''

	// 同步关注状态到本地变量
	isFollowed.value = userData.isFollowed || false

	console.log('用户介绍:', userInfo.value.introduction)
	console.log('个人优势:', userInfo.value.advantages)
	console.log('关于我标签:', userInfo.value.aboutMeTags)
	console.log('要求标签:', userInfo.value.requirementTags)
	console.log('理想型描述:', userInfo.value.dreamPartnerDescription)
	console.log('关注状态:', isFollowed.value)

	// 更新认证状态
	authStatus.value.isAuthenticated = userData.isAuthenticated || false
	authStatus.value.authTypes = userData.authTypes || []
}

// 计算年龄
const calculateAge = (birthday) => {
	if (!birthday) return null
	const birthDate = new Date(birthday)
	const today = new Date()
	let age = today.getFullYear() - birthDate.getFullYear()
	const monthDiff = today.getMonth() - birthDate.getMonth()
	if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
		age--
	}
	return age
}

// 创建用户主页浏览记录
const recordUserHomeBrowse = (userId) => {
	createUserHomeBrowseHistory(userId)
}

// 创建用户相册图片浏览记录
const recordUserAlbumBrowse = (albumId) => {
	createUserAlbumBrowseHistory(albumId)
}

// 认证完成处理
const handleAuthComplete = (authType) => {
	console.log('认证完成:', authType)
	// 重新获取用户详细信息以更新认证状态
	fetchUserDetail()
	uni.showToast({
		title: '认证完成',
		icon: 'success'
	})
}

// 根据用户ID加载用户数据
const loadUserData = async () => {
	// 并行获取用户数据
	await Promise.all([
		fetchUserDetail(),
		fetchUserLatestMoment(),
		fetchUserAlbums()
	])
}
</script>

<style lang="scss" scoped>
// 引入uni.scss变量
@import '@/uni.scss';

.nav-right-icons {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.profile-content {
	padding: 0 16rpx 120rpx;
	margin-top: 20rpx;
}

.user-header {
	background: white;
	border-radius: 16rpx;
	padding: 0rpx 16rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.avatar-section {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	position: relative;
}

/* 用户图片展示区域 */
.user-gallery {
	margin: 0 -16rpx 20rpx -16rpx;
}

.gallery-main {
	position: relative;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	overflow: hidden;
	background: #f5f5f5;
}

.main-photo-container {
	position: relative;
	width: 100%;
	height: 480rpx;
}

.main-photo {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: block;
	opacity: 0;
	transition: opacity 0.25s ease;
	will-change: opacity;
}

.main-photo.active {
	opacity: 1;
	z-index: 2;
}



/* 缩略图导航 */
.photo-thumbnails {
	position: absolute;
	bottom: 12rpx;
	right: 12rpx;
	display: flex;
	gap: 6rpx;
	background: rgba(0, 0, 0, 0.6);
	padding: 6rpx;
	border-radius: 10rpx;
	backdrop-filter: blur(10rpx);
	z-index: 10;
}

.thumbnail-item {
	width: 36rpx;
	height: 36rpx;
	border-radius: 6rpx;
	overflow: hidden;
	border: 1.5rpx solid transparent;
	transition: all 0.3s ease;
	cursor: pointer;
}

.thumbnail-item.active {
	border-color: #fff;
	transform: scale(1.1);
}

.thumbnail-image {
	width: 100%;
	height: 100%;
	display: block;
}

/* 图片计数器 */
.photo-counter {
	position: absolute;
	top: 12rpx;
	right: 12rpx;
	background: rgba(0, 0, 0, 0.6);
	color: #fff;
	padding: 4rpx 10rpx;
	border-radius: 16rpx;
	font-size: 20rpx;
	backdrop-filter: blur(10rpx);
	z-index: 10;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.vip-badge {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}



.vip-text {
	color: white;
	font-size: 24rpx;
	font-weight: 600;
}

.user-info {
	flex: 1;
	padding: 24rpx;
}

.basic-info {
	margin-bottom: 20rpx;
}

.name-row {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	margin-bottom: 8rpx;
}

.name-gender {
	display: flex;
	align-items: center;
	flex: 1;
}

.follow-btn-inline {
	min-width: 120rpx;
	height: 60rpx;
	border-radius: 30rpx;
	background: #FF6681;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6rpx;
	padding: 0 16rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	flex-shrink: 0;
}

.follow-btn-inline.followed {
	background: #FF6681;
	opacity: 0.8;
}

.follow-btn-inline.loading {
	opacity: 0.6;
	pointer-events: none;
}

.follow-btn-inline:active {
	transform: scale(0.9);
}


.nickname {
	font-size: $title-size-lg;
	font-weight: 600;
	color: #333;
	margin-right: 12rpx;
}

.gender-icon {
	font-size: 28rpx;
	line-height: 1;
	margin-right: 12rpx;
}

.user-id-label {
	font-size: 24rpx;
	color: #999;
	margin-right: 6rpx;
}

.user-id-value {
	font-size: 24rpx;
	color: $uni-text-color-disable;
	cursor: pointer;
	transition: all 0.3s ease;
	padding: 2rpx 6rpx;
	border-radius: 4rpx;
}

.user-id-value:active {
	background: rgba(105, 108, 243, 0.1);
	transform: scale(0.95);
}

.follow-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.follow-section:active {
	transform: scale(0.95);
}

.follow-text {
	line-height: 1;
	font-size: $font-size-xs;
	color: #fff;
	font-weight: 500;
}

.user-id-container {
	display: flex;
	align-items: center;
	margin-left: 12rpx;
	cursor: pointer;
}

.user-id-icon {
	font-size: 24rpx;
	color: #fff;
	margin-right: 6rpx;
	line-height: 1;
	border: none;
	border-radius: 12rpx;
	padding: 2rpx 4rpx;
	background: #999;
	display: inline-block;
	text-align: center;
	min-width: 36rpx;
	font-style: italic;
}

.user-id {
	font-size: $font-size-xs;
	color: #999;
	background: #f5f5f5;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.user-stats {
	display: flex;
	gap: 20rpx;
	margin-top: 16rpx;
}

.stat-item {
	font-size: $font-size-sm;
	color: #666;
	background: #f8f9fa;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
}

.location-info {
	margin-bottom: 20rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.location-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex: 1;
	min-width: calc(50% - 8rpx);
	margin-bottom: 8rpx;
}

.location-text {
	font-size: $font-size-sm;
	color: #666;
}

.verification-status {
	display: flex;
	gap: 20rpx;
}

.verify-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.verify-text {
	font-size: 24rpx;
	color: #4CD964;
}

.action-buttons {
	display: flex;
	justify-content: center;
	gap: 20rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
}

.action-btn {
	background: white;
	color: $primary-color;
	border: none;
	border-radius: 50rpx;
	padding: 12rpx 20rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	font-size: 26rpx;
	min-width: 120rpx;
	justify-content: center;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-btn::after {
	border: none;
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn.like-btn {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	color: white;
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.about-me,
.mate-requirements,
.latest-moments,
.identity-verification {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.gift-section {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 120rpx;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
}

.section-title {
	font-size: $title-size-md;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.gift-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.gift-arrow {
	display: flex;
	align-items: center;
}

.gift-header .section-title {
	margin-bottom: 0;
}

.gift-title-row {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.gift-title-row .section-title {
	margin-bottom: 0;
	display: inline;
}

.gift-count-text {
	font-size: 24rpx;
	color: #999;
	font-weight: 400;
}

.verification-header {
	margin-bottom: 20rpx;
}

.verification-title {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.verification-text {
	font-size: $title-size-md;
	font-weight: 600;
	color: #333;
}

.verification-status {
	font-size: $font-size-xs;
	color: #999;
}

.verification-items {
	display: flex;
	justify-content: space-between;
	gap: 16rpx;
	background: rgba(105, 108, 243, 0.05);
	border-radius: 12rpx;
	padding: 16rpx;
}

.verification-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	flex: 1;
	padding: 16rpx 8rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.verification-item.verified {
	background: rgba(105, 108, 243, 0.1);
}

.verification-item.unverified {
	background: rgba(204, 204, 204, 0.1);
}

.verification-item .item-label {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

.verification-item.unverified .item-label {
	color: #999;
}

.tags-container {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.tag-group {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.tag-label {
	background: #f8f9fa;
	color: #666;
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	font-size: $font-size-sm;
}

.intro-text {
	font-size: $font-size-md;
	color: #666;
	line-height: 1.6;
}

.intro-text.no-data,
.tag-label.no-data {
	color: #999;
	font-style: italic;
}

.advantages-section {
	margin-top: 16rpx;
}

.advantages-title {
	font-size: $font-size-md;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.advantages-text {
	font-size: $font-size-md;
	color: #666;
	line-height: 1.6;
}

.divider-line {
	width: 100%;
	height: 1rpx;
	background: #f0f0f0;
	margin: 20rpx 0;
}

.moment-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.moment-header:active {
	transform: scale(0.98);
}

.moment-title-row {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.moment-title-row .section-title {
	margin-bottom: 0;
	display: inline;
}

.moment-count {
	font-size: 24rpx;
	color: #999;
	font-weight: 400;
}

.more-arrow {
	display: flex;
	align-items: center;
}

.moment-content {
	cursor: pointer;
	transition: all 0.3s ease;
	padding: 12rpx 0;
	border-radius: 8rpx;
}

.moment-content:active {
	background: rgba(105, 108, 243, 0.05);
	transform: scale(0.98);
}

.moment-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
	margin-bottom: 8rpx;
	display: block;
}

.moment-time {
	font-size: 24rpx;
	color: #999;
}

.moment-main {
	display: flex;
	gap: 16rpx;
	align-items: flex-start;
}

.moment-text-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.moment-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	flex-shrink: 0;
}

.req-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

.gifts-container {
	display: flex;
	gap: 20rpx;
	overflow-x: auto;
}

.gift-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	min-width: 100rpx;
	position: relative;
}

.gift-icon {
	width: 60rpx;
	height: 60rpx;
}

.gift-name {
	font-size: 22rpx;
	color: #666;
	text-align: center;
}

.gift-count {
	font-size: 20rpx;
	color: #999;
	text-align: center;
	margin-top: 4rpx;
}

.empty-gifts {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx 0;
	width: 100%;
}

.empty-text {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}



.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 16rpx 24rpx;
	padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
	display: flex;
	gap: 16rpx;
	box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);
}

.bottom-btn {
	flex: 1;
	border: none;
	border-radius: 40rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6rpx;
	font-size: 24rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.bottom-btn::after {
	border: none;
}

.bottom-btn:active {
	transform: scale(0.95);
}

.send-msg-btn {
	background: $primary-color;
	color: white;
}

.like-btn {
	background: #ff4757;
	color: white;
}

.contact-btn {
	background: #2ed573;
	color: white;
}

// 浮动底部操作按钮
.floating-bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.6);
	padding: 20rpx 32rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	display: flex;
	align-items: center;
	gap: 16rpx;
	z-index: 1000;
}

.action-btn {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	min-height: 64rpx;
	border-radius: 50rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	gap: 8rpx;
	background: white;
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.action-btn:active {
	transform: scale(0.95);
}

.gift-btn {
	flex: 1;
	color: $primary-color;
}

.chat-btn {
	flex: 1;
	background: #FF6681 !important;
	color: white !important;
	border: none !important;
}

.chat-btn .btn-text {
	color: white !important;
}

.main-btn {
	flex: 2;
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
	color: white;
	font-weight: 600;
	box-shadow: 0 4rpx 16rpx rgba(105, 108, 243, 0.3);
}

.btn-text {
	font-size: 26rpx;
	color: #696CF3;
}

.main-btn .btn-text {
	color: white;
	font-size: 28rpx;
	font-weight: 600;
}

.hi-text {
	background: white;
	color: $primary-color;
	border: 1rpx solid $primary-color;
	border-radius: 50rpx;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	font-weight: 600;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

// 右侧浮动按钮组
.floating-buttons-group {
	position: fixed;
	bottom: 210rpx;
	right: 32rpx;
	z-index: 1001;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.floating-btn {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 6rpx 20rpx rgba(105, 108, 243, 0.3);
	transition: all 0.3s ease;
}

.floating-btn:active {
	transform: scale(0.9);
}

.share-btn {
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
}

.follow-btn {
	background: #FF6681;
	transition: all 0.3s ease;
}

.follow-btn.followed {
	background: #FF6681;
	opacity: 0.8;
}

.more-btn {
	background: linear-gradient(135deg, #696CF3, #9B9DF5);
}

// 横向弹出菜单
.horizontal-menu {
	position: absolute;
	right: 96rpx;
	bottom: 0;
	display: flex;
	gap: 12rpx;
	opacity: 0;
	transform: translateX(20rpx);
	transition: all 0.3s ease;
	pointer-events: none;
}

.horizontal-menu.show {
	opacity: 1;
	transform: translateX(0);
	pointer-events: auto;
}

.menu-btn {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	border-radius: 50rpx;
	background: white;
	border: 1rpx solid rgba(105, 108, 243, 0.2);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	min-width: 120rpx;
	justify-content: center;
}

.menu-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.report-btn {
	border-color: rgba(255, 107, 107, 0.3);
}

.report-btn .menu-text {
	color: #ff6b6b;
}

.block-btn {
	border-color: rgba(102, 102, 102, 0.3);
}

.block-btn .menu-text {
	color: #666;
}

.menu-text {
	font-size: 24rpx;
	color: $primary-color;
	font-weight: 500;
}
</style>
