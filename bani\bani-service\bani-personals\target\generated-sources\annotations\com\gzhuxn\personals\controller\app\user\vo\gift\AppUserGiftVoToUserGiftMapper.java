package com.gzhuxn.personals.controller.app.user.vo.gift;

import com.gzhuxn.personals.domain.user.UserGift;
import com.gzhuxn.personals.domain.user.UserGiftToAppUserGiftVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserGiftToAppUserGiftVoMapper.class},
    imports = {}
)
public interface AppUserGiftVoToUserGiftMapper extends BaseMapper<AppUserGiftVo, UserGift> {
}
