<template>
	<scroll-nav-page :show-back="true" title="基本信息">
		<template #content>
			<!-- 主要内容 -->
			<view class="page-content" :style="{ paddingTop: navBarHeight + 'px' }">
				<!-- 基本信息表单 -->
				<!-- 昵称 -->
				<view class="form-item" @click="editNickName()">
					<text class="label">昵称</text>
					<text class="value" :class="{ 'placeholder': !formData.nickName }">
						{{ formData.nickName || '请输入昵称' }}
					</text>
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>

				<!-- 性别 -->
				<picker mode="selector" :range="genderOptions" range-key="name" :value="getGenderIndex()"
					@change="handleGenderChange">
					<view class="form-item">
						<text class="label">性别</text>
						<text class="value" :class="{ 'placeholder': !formData.gender }">
							{{ getGenderText(formData.gender) || '请选择性别' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 出生日期 -->
				<picker mode="date" :value="getBirthdayValue()" @change="handleBirthdayChange">
					<view class="form-item">
						<text class="label">出生日期</text>
						<text class="value" :class="{ 'placeholder': !formData.birthday }">
							{{ formData.birthday || '请选择出生日期' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 身高 -->
				<picker mode="selector" :range="heightOptions" range-key="name" :value="getHeightIndex()"
					@change="handleHeightChange">
					<view class="form-item">
						<text class="label">身高</text>
						<text class="value" :class="{ 'placeholder': !formData.height }">
							{{ formData.height ? formData.height + 'cm' : '请选择身高' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 体重 -->
				<picker mode="selector" :range="weightOptions" range-key="name" :value="getWeightIndex()"
					@change="handleWeightChange">
					<view class="form-item">
						<text class="label">体重</text>
						<text class="value" :class="{ 'placeholder': !formData.weight }">
							{{ formData.weight ? formData.weight + 'kg' : '请选择体重' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 学历 -->
				<picker mode="selector" :range="getDictOptions('user_edu')" range-key="name"
					:value="getDictIndex('user_edu', formData.edu)"
					@change="handleDictChange('user_edu', 'edu', $event)">
					<view class="form-item">
						<text class="label">学历</text>
						<text class="value" :class="{ 'placeholder': !formData.edu }">
							{{ dictTexts.eduText || '请选择学历' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 职业 -->
					<view class="form-item" @click="jobSelectRef.open()">
						<text class="label">职业</text>
						<text class="value" :class="{ 'placeholder': !formData.job }">
							{{ dictTexts.jobText || '请选择职业' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>

				<!-- 情感状况 -->
				<picker mode="selector" :range="getDictOptions('user_affective_status')" range-key="name"
					:value="getDictIndex('user_affective_status', formData.affectiveStatus)"
					@change="handleDictChange('user_affective_status', 'affectiveStatus', $event)">
					<view class="form-item">
						<text class="label">情感状况</text>
						<text class="value" :class="{ 'placeholder': !dictTexts.affectiveStatusText }">
							{{ dictTexts.affectiveStatusText || '请选择情感状况' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 收入 -->
				<picker mode="selector" :range="getDictOptions('user_revenue')" range-key="name"
					:value="getDictIndex('user_revenue', formData.revenue)"
					@change="handleDictChange('user_revenue', 'revenue', $event)">
					<view class="form-item">
						<text class="label">收入</text>
						<text class="value" :class="{ 'placeholder': !dictTexts.revenueText }">
							{{ dictTexts.revenueText || '请选择收入' }}
						</text>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>

				<!-- 户籍地址 -->
				<view class="form-item" @click="editAddress('registered')">
					<text class="label">户籍地址</text>
					<text class="value" :class="{ 'placeholder': !formData.addr }">
						{{ formData.addr || '请选择户籍地址' }}
					</text>
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>

				<!-- 现居地址 -->
				<view class="form-item" @click="editAddress('current')">
					<text class="label">现居地址</text>
					<text class="value" :class="{ 'placeholder': !formData.addrNew }">
						{{ formData.addrNew || '请选择现居地址' }}
					</text>
					<uni-icons type="right" size="16" color="#ccc"></uni-icons>
				</view>

				<view class="warning-tips">
					<text>为了保证真实、真诚的交友，请如实填写个人信息，虚假敷衍的内容不会审核通过，请知悉</text>
				</view>
				<view class="submit">
					<button type="primary" @click="submit" :loading="loading">{{ baseType === 0 ? '下一步' : '保存'
					}}</button>
				</view>
			</view>
		</template>
	</scroll-nav-page>

	<!-- 昵称编辑弹窗 -->
	<uni-popup ref="nicknamePopup" type="dialog">
		<uni-popup-dialog mode="input" title="编辑昵称" placeholder="请输入昵称" :value="nicknameInput"
			@confirm="handleNicknameConfirm" @close="handleNicknameClose">
			<template #default>
				<input v-model="nicknameInput" type="nickname" placeholder="请输入昵称" class="nickname-input" />
			</template>
		</uni-popup-dialog>
	</uni-popup>

	<!-- 地址选择组件 -->
	<DistrictSelect
		ref="districtSelectRef"
		@confirm="handleDistrictConfirm" />

	<!-- 职业选择组件 -->
	<TagSelect
		ref="jobSelectRef"
		title="请选择您的职业"
		:localdata="jobLocalData"
		@confirm="handleJobSelect" />
</template>
<script setup>
import { ref, computed } from 'vue'
import { onLoad, onPageScroll } from "@dcloudio/uni-app"
import globalConfig from '@/config'
import $store from '@/store/index'
import {
	getUserBase,
	updateUserBase
} from '@/api/my/my'
import { heightDicts, weightDicts } from '@/utils/common.js'
import DistrictSelect from '@/components/district-select/district-select.vue'
import TagSelect from '@/components/tag-select/tag-select.vue'

// 注册组件
const components = {
	DistrictSelect,
	TagSelect
}

// 页面状态
const baseType = ref(0)
const pageScrollTop = ref(0)
const navBarHeight = ref(0)
const loading = ref(false)

// 表单数据
const formData = ref({
	nickName: '',
	gender: '',
	birthday: '',
	height: '',
	weight: '',
	edu: '',
	job: '',
	affectiveStatus: '',
	revenue: '',
	addrProvinceCode: '',
	addrCityCode: '',
	addrDistrictCode: '',
	addrStreetCode: '',
	addr: '',
	addrNewProvinceCode: '',
	addrNewCityCode: '',
	addrNewDistrictCode: '',
	addrNewStreetCode: '',
	addrNew: ''
})

// 昵称弹窗相关
const nicknamePopup = ref(null)
const nicknameInput = ref('')

// 地址选择相关
const districtSelectRef = ref(null)
const currentAddressField = ref('')

// 职业选择相关
const jobSelectRef = ref(null)

// 身高体重选项
const heightOptions = ref([])
const weightOptions = ref([])

// 性别选项
const genderOptions = ref([
	{ value: '0', name: '男' },
	{ value: '1', name: '女' }
])

// 获取字典文本的辅助函数
const getDictText = (dictType, value) => {
	if (!value || !dictType) return ''
	try {
		if (!$store || !$store.dict || typeof $store.dict.getNameById !== 'function') {
			console.warn('Store 或字典方法不可用')
			return ''
		}
		const result = $store.dict.getNameById(dictType, value)
		return result || ''
	} catch (error) {
		console.error('获取字典文本失败:', error)
		return ''
	}
}

// 计算属性 - 字典文本回显
const dictTexts = computed(() => {
	if (!formData.value) {
		return {
			affectiveStatusText: '',
			eduText: '',
			jobText: '',
			revenueText: ''
		}
	}
	return {
		// 情感状况
		affectiveStatusText: getDictText('user_affective_status', formData.value.affectiveStatus),
		// 学历
		eduText: getDictText('user_edu', formData.value.edu),
		// 职业
		jobText: getDictText('user_job', formData.value.job),
		// 收入
		revenueText: getDictText('user_revenue', formData.value.revenue)
	}
})

// 计算属性 - 职业选择数据
const jobLocalData = computed(() => {
	try {
		const dictData = $store.dict.get('user_job')
		if (!dictData || !Array.isArray(dictData)) {
			return []
		}
		// 转换为 tag-select 需要的格式
		return dictData.map(item => ({
			id: item.id,
			name: item.name
		}))
	} catch (error) {
		console.error('获取职业数据失败:', error)
		return []
	}
})

// 获取字典选项数据
const getDictOptions = (dictType) => {
	try {
		const dictData = $store.dict.get(dictType)
		if (!dictData || !Array.isArray(dictData)) {
			console.warn('字典数据不存在或格式错误:', dictType)
			return []
		}
		return dictData
	} catch (error) {
		console.error('获取字典选项失败:', error)
		return []
	}
}

// 页面滚动监听
onPageScroll((e) => {
	pageScrollTop.value = e.scrollTop
})

// 页面加载
onLoad((options) => {
	baseType.value = parseInt(options.baseType || 0)
	// 加载基础信息
	loadBaseInfo()
})

// 加载基础信息
const loadBaseInfo = async () => {
	try {
		// 初始化身高体重选项
		heightOptions.value = heightDicts()
		weightOptions.value = weightDicts()

		const res = await getUserBase()
		if (res.code === 200 && res.data) {
			formData.value = {
				nickName: res.data.nickName || '',
				gender: res.data.sex || '',
				birthday: res.data.birthday || '',
				height: res.data.height || '',
				weight: res.data.weight || '',
				edu: res.data.edu || '',
				job: res.data.job || '',
				affectiveStatus: res.data.affectiveStatus || '',
				revenue: res.data.revenue || '',
				addrProvinceCode: res.data.addrProvinceCode || '',
				addrCityCode: res.data.addrCityCode || '',
				addrDistrictCode: res.data.addrDistrictCode || '',
				addrStreetCode: res.data.addrStreetCode || '',
				addr: res.data.addr || '',
				addrNewProvinceCode: res.data.addrNewProvinceCode || '',
				addrNewCityCode: res.data.addrNewCityCode || '',
				addrNewDistrictCode: res.data.addrNewDistrictCode || '',
				addrNewStreetCode: res.data.addrNewStreetCode || '',
				addrNew: res.data.addrNew || ''
			}
		}
		console.log('基础信息:', res)
	} catch (error) {
		console.error('加载基础信息失败:', error)
	}
}

// 获取身高选中索引
const getHeightIndex = () => {
	if (!heightOptions.value.length) return 0
	// 如果没有身高值，默认选择168cm
	const targetHeight = formData.value.height || 168
	const index = heightOptions.value.findIndex(item => item.id === targetHeight)
	return index >= 0 ? index : 0
}

// 获取体重选中索引
const getWeightIndex = () => {
	if (!weightOptions.value.length) return 0
	// 如果没有体重值，默认选择50kg
	const targetWeight = formData.value.weight || 50
	const index = weightOptions.value.findIndex(item => item.id === targetWeight)
	return index >= 0 ? index : 0
}

// 获取十八年前的今天日期
const getEighteenYearsAgo = () => {
	const today = new Date()
	const eighteenYearsAgo = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate())
	return eighteenYearsAgo.toISOString().split('T')[0]
}

// 获取生日显示值
const getBirthdayValue = () => {
	return formData.value.birthday || getEighteenYearsAgo()
}

// 获取性别文本
const getGenderText = (sex) => {
	if (sex === '0') return '男'
	if (sex === '1') return '女'
	return ''
}

// 获取性别选中索引
const getGenderIndex = () => {
	if (!formData.value.gender) return 0
	const index = genderOptions.value.findIndex(item => item.value === formData.value.gender)
	return index >= 0 ? index : 0
}

// 处理性别变化
const handleGenderChange = (e) => {
	const selectedGender = genderOptions.value[e.detail.value]
	formData.value.gender = selectedGender.value
}

// 编辑昵称
const editNickName = () => {
	nicknameInput.value = formData.value.nickName
	nicknamePopup.value.open()
}

// 昵称确认
const handleNicknameConfirm = (value) => {
	// 使用输入框的值而不是参数值
	const inputValue = nicknameInput.value || value
	if (inputValue && inputValue.trim()) {
		formData.value.nickName = inputValue.trim()
	}
	nicknamePopup.value.close()
}

// 昵称取消
const handleNicknameClose = () => {
	nicknamePopup.value.close()
}



// 处理生日变化
const handleBirthdayChange = (e) => {
	formData.value.birthday = e.detail.value
}

// 处理身高变化
const handleHeightChange = (e) => {
	const selectedHeight = heightOptions.value[e.detail.value]
	formData.value.height = selectedHeight.id
}

// 处理体重变化
const handleWeightChange = (e) => {
	const selectedWeight = weightOptions.value[e.detail.value]
	formData.value.weight = selectedWeight.id
}

// 编辑普通字段
const editField = (field, title, value, type = 'text') => {
	uni.showModal({
		title: `编辑${title}`,
		editable: true,
		placeholderText: `请输入${title}`,
		value: value || '',
		success: (res) => {
			if (res.confirm && res.content) {
				if (type === 'number') {
					const num = parseFloat(res.content)
					if (!isNaN(num) && num > 0) {
						formData.value[field] = num
					} else {
						uni.showToast({
							title: '请输入有效数字',
							icon: 'none'
						})
					}
				} else {
					formData.value[field] = res.content.trim()
				}
			}
		}
	})
}

// 编辑地址
const editAddress = (type) => {
	currentAddressField.value = type
	districtSelectRef.value.open()
}

// 处理地址选择确认
const handleDistrictConfirm = (result) => {
	if (currentAddressField.value === 'registered') {
		// 户籍地址
		formData.value.addrProvinceCode = result.codes.provinceCode
		formData.value.addrCityCode = result.codes.cityCode
		formData.value.addrDistrictCode = result.codes.districtCode
		formData.value.addrStreetCode = result.codes.streetCode
		formData.value.addr = result.fullName
	} else if (currentAddressField.value === 'current') {
		// 现居地址
		formData.value.addrNewProvinceCode = result.codes.provinceCode
		formData.value.addrNewCityCode = result.codes.cityCode
		formData.value.addrNewDistrictCode = result.codes.districtCode
		formData.value.addrNewStreetCode = result.codes.streetCode
		formData.value.addrNew = result.fullName
	}
}

// 处理字典选择变化
const handleDictChange = (dictType, field, e) => {
	const options = getDictOptions(dictType)
	if (options && options[e.detail.value]) {
		formData.value[field] = options[e.detail.value].id
	}
}

// 获取字典选中索引
const getDictIndex = (dictType, value) => {
	if (!value) return 0
	const options = getDictOptions(dictType)
	const index = options.findIndex(item => item.id === value)
	return index >= 0 ? index : 0
}

// 处理职业选择
const handleJobSelect = (event) => {
	console.log('职业选择:', event)
	if (event && event.value) {
		// 更新职业值
		formData.value.job = event.value
	}
}

// 提交数据
const submit = async () => {
	// 基本验证
	if (!formData.value.nickName) {
		uni.showToast({
			title: '请输入昵称',
			icon: 'none'
		})
		return
	}

	if (!formData.value.gender) {
		uni.showToast({
			title: '请选择性别',
			icon: 'none'
		})
		return
	}

	loading.value = true
	try {
		// 准备提交数据，将gender字段映射为sex
		const submitData = {
			...formData.value,
			sex: formData.value.gender
		}
		delete submitData.gender

		const res = await updateUserBase(submitData)
		console.log('提交成功:', res)
		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})

		if (baseType.value === 0) {
			// 跳转到下一步
			uni.navigateTo({
				url: '/pagesubs/my/profile/avatar/avatarEdit'
			})
		} else {
			// 返回上一页
			uni.navigateBack()
		}
	} catch (error) {
		console.error('提交失败:', error)
		uni.showToast({
			title: '保存失败',
			icon: 'error'
		})
	} finally {
		loading.value = false
	}
}
</script>

<style lang="scss">
@import '@/uni.scss';

.steps-container {
	margin-bottom: 50rpx;
	padding: 0 10rpx;

	:deep(.uni-steps) {
		.uni-steps__column-line-container {
			.uni-steps__column-line {
				background-color: rgba($primary-color, 0.2);
			}
		}

		.uni-steps__column-container {
			.uni-steps__column-text-container {
				.uni-steps__column-title {
					color: #666;
					font-size: 26rpx;
					font-weight: 500;
				}
			}
		}
	}
}

.title {
	margin: 40rpx 0 50rpx;
	text-align: center;

	.base {
		font-size: 40rpx;
		color: #1a1a1a;
		font-weight: 700;
		display: block;
		margin-bottom: 16rpx;
	}

	.tip {
		font-size: 28rpx;
		color: #666;
		line-height: 1.5;
	}
}

.registerForm {
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx 32rpx;
	margin: 0 4rpx;
	box-shadow: 0 8rpx 32rpx rgba($primary-color, 0.08);

	.submit {
		margin: 60rpx 0 20rpx;

		button {
			width: 100%;
			height: 96rpx;
			border-radius: 48rpx;
			background: linear-gradient(135deg, $primary-color, lighten($primary-color, 10%));
			font-size: 34rpx;
			font-weight: 600;
			color: #fff;
			border: none;
			box-shadow: 0 8rpx 24rpx rgba($primary-color, 0.25);

			&[disabled] {
				background: #ccc;
				box-shadow: none;
			}
		}
	}
}

.form-section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 40rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-item {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx;
	border-bottom: 1rpx solid #f5f5f5;
	position: relative;
	transition: background-color 0.2s;
}

.form-item:last-child {
	border-bottom: none;
}

.form-item:active {
	background-color: #f8f8f8;
}

.form-item .label {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	width: 140rpx;
	flex-shrink: 0;
}

.form-item .value {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	text-align: right;
	margin-right: 16rpx;
}

.form-item .value.placeholder {
	color: #999;
	text-align: right;
}

.nickname-input {
	width: 100%;
	height: 80rpx;
	padding: 0rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 32rpx;
	background: #fff;
	box-sizing: border-box;
}

.nickname-input:focus {
	border-color: var(--primary-color, #007aff);
	outline: none;
	box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}

.warning-tips {
	margin: 40rpx 0;
	padding: 24rpx 28rpx;
	background: #fff9e6;
	border-radius: 16rpx;
	text-align: center;
	border: 1rpx solid #ffe7ba;

	text {
		font-size: 26rpx;
		color: #d48806;
		line-height: 1.6;
		font-weight: 500;
	}
}
</style>