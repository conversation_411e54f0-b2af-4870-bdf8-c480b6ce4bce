package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserVipRecordVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserVipRecordToUserVipRecordVoMapperImpl implements UserVipRecordToUserVipRecordVoMapper {

    @Override
    public UserVipRecordVo convert(UserVipRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserVipRecordVo userVipRecordVo = new UserVipRecordVo();

        userVipRecordVo.setId( arg0.getId() );
        userVipRecordVo.setUserId( arg0.getUserId() );
        userVipRecordVo.setManageId( arg0.getManageId() );
        userVipRecordVo.setOrderId( arg0.getOrderId() );
        userVipRecordVo.setStartDate( arg0.getStartDate() );
        userVipRecordVo.setEndDate( arg0.getEndDate() );
        userVipRecordVo.setMonths( arg0.getMonths() );
        userVipRecordVo.setOriginalAmount( arg0.getOriginalAmount() );
        userVipRecordVo.setAmount( arg0.getAmount() );
        userVipRecordVo.setCoin( arg0.getCoin() );
        userVipRecordVo.setPayTime( arg0.getPayTime() );
        userVipRecordVo.setPayStatus( arg0.getPayStatus() );

        return userVipRecordVo;
    }

    @Override
    public UserVipRecordVo convert(UserVipRecord arg0, UserVipRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setManageId( arg0.getManageId() );
        arg1.setOrderId( arg0.getOrderId() );
        arg1.setStartDate( arg0.getStartDate() );
        arg1.setEndDate( arg0.getEndDate() );
        arg1.setMonths( arg0.getMonths() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setPayStatus( arg0.getPayStatus() );

        return arg1;
    }
}
