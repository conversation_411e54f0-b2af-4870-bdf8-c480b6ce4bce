package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.AppUserMyProfileVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAppUserMyProfileVoMapperImpl implements UserDetailToAppUserMyProfileVoMapper {

    @Override
    public AppUserMyProfileVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserMyProfileVo appUserMyProfileVo = new AppUserMyProfileVo();

        appUserMyProfileVo.setUserId( arg0.getUserId() );
        appUserMyProfileVo.setNickName( arg0.getNickName() );
        appUserMyProfileVo.setSex( arg0.getSex() );
        appUserMyProfileVo.setAvatar( arg0.getAvatar() );
        appUserMyProfileVo.setPid( arg0.getPid() );
        appUserMyProfileVo.setIsIdentity( arg0.getIsIdentity() );

        return appUserMyProfileVo;
    }

    @Override
    public AppUserMyProfileVo convert(UserDetail arg0, AppUserMyProfileVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setSex( arg0.getSex() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setPid( arg0.getPid() );
        arg1.setIsIdentity( arg0.getIsIdentity() );

        return arg1;
    }
}
