package com.gzhuxn.personals.controller.app.message.bo;

import com.gzhuxn.personals.domain.message.MsgContentUser;
import com.gzhuxn.personals.domain.message.MsgContentUserToAppMsgContentUserPageReqBoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgContentUserToAppMsgContentUserPageReqBoMapper.class},
    imports = {}
)
public interface AppMsgContentUserPageReqBoToMsgContentUserMapper extends BaseMapper<AppMsgContentUserPageReqBo, MsgContentUser> {
}
