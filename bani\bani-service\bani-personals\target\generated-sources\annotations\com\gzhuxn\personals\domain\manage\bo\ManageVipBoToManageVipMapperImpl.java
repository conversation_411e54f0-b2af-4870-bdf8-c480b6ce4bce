package com.gzhuxn.personals.domain.manage.bo;

import com.gzhuxn.personals.domain.manage.ManageVip;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageVipBoToManageVipMapperImpl implements ManageVipBoToManageVipMapper {

    @Override
    public ManageVip convert(ManageVipBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageVip manageVip = new ManageVip();

        manageVip.setSearchValue( arg0.getSearchValue() );
        manageVip.setCreateBy( arg0.getCreateBy() );
        manageVip.setCreateTime( arg0.getCreateTime() );
        manageVip.setUpdateBy( arg0.getUpdateBy() );
        manageVip.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            manageVip.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        manageVip.setCreateDept( arg0.getCreateDept() );
        manageVip.setId( arg0.getId() );
        manageVip.setMonths( arg0.getMonths() );
        manageVip.setOriginalAmount( arg0.getOriginalAmount() );
        manageVip.setAmount( arg0.getAmount() );
        manageVip.setCoin( arg0.getCoin() );
        manageVip.setSellNum( arg0.getSellNum() );
        manageVip.setStatus( arg0.getStatus() );

        return manageVip;
    }

    @Override
    public ManageVip convert(ManageVipBo arg0, ManageVip arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setMonths( arg0.getMonths() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setCoin( arg0.getCoin() );
        arg1.setSellNum( arg0.getSellNum() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
