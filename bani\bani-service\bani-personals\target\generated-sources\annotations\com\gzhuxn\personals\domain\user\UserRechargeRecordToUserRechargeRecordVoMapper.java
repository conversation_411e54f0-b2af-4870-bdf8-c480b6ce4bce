package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserRechargeRecordBoToUserRechargeRecordMapper;
import com.gzhuxn.personals.domain.user.vo.UserRechargeRecordVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserRechargeRecordBoToUserRechargeRecordMapper.class},
    imports = {}
)
public interface UserRechargeRecordToUserRechargeRecordVoMapper extends BaseMapper<UserRechargeRecord, UserRechargeRecordVo> {
}
