package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.recommend.vo.moment.AppRecommendMomentVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserMomentToAppRecommendMomentVoMapperImpl implements UserMomentToAppRecommendMomentVoMapper {

    @Override
    public AppRecommendMomentVo convert(UserMoment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppRecommendMomentVo appRecommendMomentVo = new AppRecommendMomentVo();

        appRecommendMomentVo.setId( arg0.getId() );
        appRecommendMomentVo.setContent( arg0.getContent() );
        appRecommendMomentVo.setImages( arg0.getImages() );

        return appRecommendMomentVo;
    }

    @Override
    public AppRecommendMomentVo convert(UserMoment arg0, AppRecommendMomentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );

        return arg1;
    }
}
