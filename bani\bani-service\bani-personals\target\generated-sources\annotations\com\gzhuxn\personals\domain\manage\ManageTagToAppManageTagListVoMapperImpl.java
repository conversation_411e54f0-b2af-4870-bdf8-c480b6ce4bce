package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageTagListVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ManageTagToAppManageTagListVoMapperImpl implements ManageTagToAppManageTagListVoMapper {

    @Override
    public AppManageTagListVo convert(ManageTag arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppManageTagListVo appManageTagListVo = new AppManageTagListVo();

        appManageTagListVo.setId( arg0.getId() );
        appManageTagListVo.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            appManageTagListVo.setIcon( String.valueOf( arg0.getIcon() ) );
        }

        return appManageTagListVo;
    }

    @Override
    public AppManageTagListVo convert(ManageTag arg0, AppManageTagListVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getIcon() != null ) {
            arg1.setIcon( String.valueOf( arg0.getIcon() ) );
        }
        else {
            arg1.setIcon( null );
        }

        return arg1;
    }
}
