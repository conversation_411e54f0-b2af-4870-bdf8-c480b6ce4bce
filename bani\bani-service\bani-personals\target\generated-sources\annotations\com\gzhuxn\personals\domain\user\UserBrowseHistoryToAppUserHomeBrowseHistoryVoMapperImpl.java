package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.browsehistory.AppUserHomeBrowseHistoryVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserBrowseHistoryToAppUserHomeBrowseHistoryVoMapperImpl implements UserBrowseHistoryToAppUserHomeBrowseHistoryVoMapper {

    @Override
    public AppUserHomeBrowseHistoryVo convert(UserBrowseHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserHomeBrowseHistoryVo appUserHomeBrowseHistoryVo = new AppUserHomeBrowseHistoryVo();

        appUserHomeBrowseHistoryVo.setId( arg0.getId() );
        appUserHomeBrowseHistoryVo.setCreateTime( arg0.getCreateTime() );

        return appUserHomeBrowseHistoryVo;
    }

    @Override
    public AppUserHomeBrowseHistoryVo convert(UserBrowseHistory arg0, AppUserHomeBrowseHistoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
