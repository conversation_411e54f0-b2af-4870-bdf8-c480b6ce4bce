package com.gzhuxn.personals.controller.app.group.bo;

import com.gzhuxn.personals.domain.group.GroupUser;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {},
    imports = {}
)
public interface AppGroupUserBoToGroupUserMapper extends BaseMapper<AppGroupUserBo, GroupUser> {
}
