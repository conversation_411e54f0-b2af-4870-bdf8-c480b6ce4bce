<template>
	<scroll-nav-page title="设置" :show-back="true" @heightChange="handleNavHeightChange">
		<template #content>
			<!-- 设置列表 -->
			<view class="setting-list">
			<!-- 推荐设置 -->
			<view class="setting-section">
				<view class="section-header">
					<text class="section-title">推荐设置</text>
				</view>
				<view class="setting-item" @click="goToRecommendSetting">
					<view class="item-left">
						<uni-icons type="heart" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">择偶条件</text>
					</view>
					<view class="item-right">
						<text class="item-desc">设置推荐条件</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 账号设置 -->
			<view class="setting-section">
				<view class="section-header">
					<text class="section-title">账号设置</text>
				</view>
				<view class="setting-item" @click="goToProfile">
					<view class="item-left">
						<uni-icons type="person" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">个人资料</text>
					</view>
					<view class="item-right">
						<text class="item-desc">编辑个人信息</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="goToPrivacy">
					<view class="item-left">
						<uni-icons type="locked" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">隐私设置</text>
					</view>
					<view class="item-right">
						<text class="item-desc">隐私保护</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="showDeleteAccountModal">
					<view class="item-left">
						<uni-icons type="trash" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">账户注销</text>
					</view>
					<view class="item-right">
						<text class="item-desc">永久删除账户</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 通用设置 -->
			<view class="setting-section">
				<view class="section-header">
					<text class="section-title">通用设置</text>
				</view>
				<view class="setting-item" @click="goToNotification">
					<view class="item-left">
						<uni-icons type="notification" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">消息通知</text>
					</view>
					<view class="item-right">
						<text class="item-desc">通知设置</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="goToFeedback">
					<view class="item-left">
						<uni-icons type="chatbubble" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">意见反馈</text>
					</view>
					<view class="item-right">
						<text class="item-desc">问题反馈与建议</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="goToAbout">
					<view class="item-left">
						<uni-icons type="info" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">关于我们</text>
					</view>
					<view class="item-right">
						<text class="item-desc">应用信息</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>

			<!-- 法律条款 -->
			<view class="setting-section">
				<view class="section-header">
					<text class="section-title">法律条款</text>
				</view>
				<view class="setting-item" @click="goToUserAgreement">
					<view class="item-left">
						<uni-icons type="paperplane" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">用户协议</text>
					</view>
					<view class="item-right">
						<text class="item-desc">服务条款</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="goToPrivacyPolicy">
					<view class="item-left">
						<uni-icons type="locked-filled" size="20" color="#696CF3"></uni-icons>
						<text class="item-title">隐私政策</text>
					</view>
					<view class="item-right">
						<text class="item-desc">隐私保护条款</text>
						<uni-icons type="right" size="14" color="#999"></uni-icons>
					</view>
				</view>
			</view>
			</view>
		</template>
	</scroll-nav-page>
</template>

<script setup>
import { ref } from 'vue'
import ScrollNavPage from '@/components/scroll-nav-page/scroll-nav-page.vue'
import globalConfig from '@/config'
import { getCurrentLogoffRecord, applyLogoff, cancelLogoff } from '@/api/my/logoff'

// 导航栏高度
const navBarHeight = ref(0)

// 导航栏高度变化
const handleNavHeightChange = (height) => {
	navBarHeight.value = height
}

// 跳转到推荐设置页面
const goToRecommendSetting = () => {
	uni.navigateTo({
		url: '/pagesubs/my/setting/recommend'
	})
}

// 跳转到个人资料
const goToProfile = () => {
	uni.navigateTo({
		url: '/pagesubs/my/profile/profileEdit'
	})
}

// 跳转到隐私设置
const goToPrivacy = () => {
	uni.navigateTo({
		url: '/pagesubs/my/setting/privacy'
	})
}

// 跳转到消息通知设置
const goToNotification = () => {
	uni.navigateTo({
		url: '/pagesubs/my/setting/msRemind'
	})
}

// 跳转到意见反馈
const goToFeedback = () => {
	uni.navigateTo({
		url: '/pagesubs/my/feedback/feedback'
	})
}

// 跳转到关于我们
const goToAbout = () => {
	uni.navigateTo({
		url: globalConfig.help.aboutUs
	})
}

// 跳转到用户协议
const goToUserAgreement = () => {
	uni.navigateTo({
		url: globalConfig.agreements.userAgreement
	})
}

// 跳转到隐私政策
const goToPrivacyPolicy = () => {
	uni.navigateTo({
		url: globalConfig.agreements.privacyAgreement
	})
}

// 显示注销账户确认弹框
const showDeleteAccountModal = () => {
	getCurrentLogoffRecord().then(res => {
		if (res.data) {
			uni.showModal({
				title: '提示',
				content: '您已提交注销申请，系统将在' + res.data.logoffTime + '后删除，请耐心等待!',
				showCancel: true,
				cancelText: '取消注销',
				cancelColor: '#F56C6C',
				success: function (r) {
					if (r.cancel) {
						cancelLogoff(res.data.id).then(res => {
							uni.showToast({
								title: '取消注销成功!',
								icon: 'success'
							});
						})
					}
				}
			})
			return
		}
		uni.showModal({
			title: '注销账户',
			content: '账户将在7天后完成注销，注销后永久删除您的所有数据，包括个人信息、聊天记录、动态等，删除后无法恢复，且账户将无法使用，请确认是否继续？',
			confirmText: '确认注销',
			cancelText: '取消',
			confirmColor: '#F56C6C',
			success: (res) => {
				if (res.confirm) {
					// 用户确认注销
					handleDeleteAccount()
				}
			}
		})
	})
}

// 处理账户注销
const handleDeleteAccount = () => {
	// 这里可以调用注销账户的API
	uni.showLoading({
		title: '处理中...'
	})
	applyLogoff().then(() => {
		// 显示注销成功提示
		uni.showToast({
			title: '注销申请已提交!',
			icon: 'success',
			duration: 3000
		})
	})
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg,
			rgba(105, 108, 243, 0.08) 0%,
			rgba(105, 108, 243, 0.05) 30%,
			rgba(105, 108, 243, 0.02) 60%,
			rgba(255, 255, 255, 1) 100%);
}

.setting-list {
	margin-top: 20rpx;
	padding: 20rpx;
	box-sizing: border-box;
}

.setting-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 6rpx 24rpx rgba(105, 108, 243, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1px solid rgba(255, 255, 255, 0.2);
	overflow: hidden;

	.section-header {
		padding: 24rpx 30rpx 16rpx;
		border-bottom: 1px solid rgba(105, 108, 243, 0.08);

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			letter-spacing: 0.5rpx;
		}
	}

	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
		border-bottom: 1px solid rgba(105, 108, 243, 0.05);
		transition: all 0.3s ease;
		cursor: pointer;

		&:last-child {
			border-bottom: none;
		}

		&:hover {
			background: rgba(105, 108, 243, 0.04);
			transform: translateX(4rpx);
		}

		&:active {
			transform: scale(0.98);
		}

		.item-left {
			display: flex;
			align-items: center;
			flex: 1;

			.item-title {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
				margin-left: 20rpx;
				letter-spacing: 0.3rpx;
			}
		}

		.item-right {
			display: flex;
			align-items: center;

			.item-desc {
				font-size: 26rpx;
				color: #666;
				margin-right: 12rpx;
			}
		}
	}
}

// 响应式设计
@media screen and (max-width: 750rpx) {
	.setting-section {
		margin-bottom: 20rpx;

		.section-header {
			padding: 20rpx 24rpx 12rpx;

			.section-title {
				font-size: 30rpx;
			}
		}

		.setting-item {
			padding: 20rpx 24rpx;

			.item-left .item-title {
				font-size: 28rpx;
				margin-left: 16rpx;
			}

			.item-right .item-desc {
				font-size: 24rpx;
			}
		}
	}
}
</style>
