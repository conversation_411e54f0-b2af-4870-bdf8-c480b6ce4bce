package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.AppUserEditDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToAppUserEditDetailVoMapperImpl implements UserDetailToAppUserEditDetailVoMapper {

    @Override
    public AppUserEditDetailVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserEditDetailVo appUserEditDetailVo = new AppUserEditDetailVo();

        appUserEditDetailVo.setUserId( arg0.getUserId() );
        appUserEditDetailVo.setNickName( arg0.getNickName() );
        appUserEditDetailVo.setName( arg0.getName() );
        appUserEditDetailVo.setSex( arg0.getSex() );
        appUserEditDetailVo.setAvatar( arg0.getAvatar() );
        appUserEditDetailVo.setPid( arg0.getPid() );
        appUserEditDetailVo.setBirthday( arg0.getBirthday() );
        appUserEditDetailVo.setStar( arg0.getStar() );
        appUserEditDetailVo.setAnimal( arg0.getAnimal() );
        appUserEditDetailVo.setHeight( arg0.getHeight() );
        appUserEditDetailVo.setWeight( arg0.getWeight() );
        appUserEditDetailVo.setEdu( arg0.getEdu() );
        appUserEditDetailVo.setJob( arg0.getJob() );
        appUserEditDetailVo.setAffectiveStatus( arg0.getAffectiveStatus() );
        appUserEditDetailVo.setRevenue( arg0.getRevenue() );
        appUserEditDetailVo.setWechat( arg0.getWechat() );
        appUserEditDetailVo.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        appUserEditDetailVo.setAddrCityCode( arg0.getAddrCityCode() );
        appUserEditDetailVo.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        appUserEditDetailVo.setAddrStreetCode( arg0.getAddrStreetCode() );
        appUserEditDetailVo.setAddr( arg0.getAddr() );
        appUserEditDetailVo.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        appUserEditDetailVo.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        appUserEditDetailVo.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        appUserEditDetailVo.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        appUserEditDetailVo.setAddrNew( arg0.getAddrNew() );
        appUserEditDetailVo.setProgress( arg0.getProgress() );
        appUserEditDetailVo.setAuditStatus( arg0.getAuditStatus() );
        appUserEditDetailVo.setIsIdentity( arg0.getIsIdentity() );
        appUserEditDetailVo.setIsVip( arg0.getIsVip() );
        appUserEditDetailVo.setVipStartDate( arg0.getVipStartDate() );
        appUserEditDetailVo.setVipEndDate( arg0.getVipEndDate() );
        appUserEditDetailVo.setUserLevel( arg0.getUserLevel() );
        appUserEditDetailVo.setIsMatched( arg0.getIsMatched() );

        return appUserEditDetailVo;
    }

    @Override
    public AppUserEditDetailVo convert(UserDetail arg0, AppUserEditDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setName( arg0.getName() );
        arg1.setSex( arg0.getSex() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setAffectiveStatus( arg0.getAffectiveStatus() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddrProvinceCode( arg0.getAddrProvinceCode() );
        arg1.setAddrCityCode( arg0.getAddrCityCode() );
        arg1.setAddrDistrictCode( arg0.getAddrDistrictCode() );
        arg1.setAddrStreetCode( arg0.getAddrStreetCode() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNewProvinceCode( arg0.getAddrNewProvinceCode() );
        arg1.setAddrNewCityCode( arg0.getAddrNewCityCode() );
        arg1.setAddrNewDistrictCode( arg0.getAddrNewDistrictCode() );
        arg1.setAddrNewStreetCode( arg0.getAddrNewStreetCode() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setIsIdentity( arg0.getIsIdentity() );
        arg1.setIsVip( arg0.getIsVip() );
        arg1.setVipStartDate( arg0.getVipStartDate() );
        arg1.setVipEndDate( arg0.getVipEndDate() );
        arg1.setUserLevel( arg0.getUserLevel() );
        arg1.setIsMatched( arg0.getIsMatched() );

        return arg1;
    }
}
