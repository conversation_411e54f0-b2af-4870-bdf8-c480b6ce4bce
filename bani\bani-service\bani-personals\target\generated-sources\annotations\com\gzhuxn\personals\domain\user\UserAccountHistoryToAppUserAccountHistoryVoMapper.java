package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.account.AppUserAccountHistoryVo;
import com.gzhuxn.personals.domain.user.bo.UserAccountHistoryBoToUserAccountHistoryMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAccountHistoryBoToUserAccountHistoryMapper.class,UserAccountHistoryToUserAccountHistoryVoMapper.class},
    imports = {}
)
public interface UserAccountHistoryToAppUserAccountHistoryVoMapper extends BaseMapper<UserAccountHistory, AppUserAccountHistoryVo> {
}
