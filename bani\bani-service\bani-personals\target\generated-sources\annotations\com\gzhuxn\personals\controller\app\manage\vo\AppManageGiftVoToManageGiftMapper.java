package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageGift;
import com.gzhuxn.personals.domain.manage.ManageGiftToAppManageGiftVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageGiftToAppManageGiftVoMapper.class},
    imports = {}
)
public interface AppManageGiftVoToManageGiftMapper extends BaseMapper<AppManageGiftVo, ManageGift> {
}
