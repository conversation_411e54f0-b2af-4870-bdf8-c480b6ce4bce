package com.gzhuxn.personals.domain.group.bo;

import com.gzhuxn.personals.domain.group.GroupUser;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class GroupUserBoToGroupUserMapperImpl implements GroupUserBoToGroupUserMapper {

    @Override
    public GroupUser convert(GroupUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        GroupUser groupUser = new GroupUser();

        groupUser.setSearchValue( arg0.getSearchValue() );
        groupUser.setCreateBy( arg0.getCreateBy() );
        groupUser.setCreateTime( arg0.getCreateTime() );
        groupUser.setUpdateBy( arg0.getUpdateBy() );
        groupUser.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            groupUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        groupUser.setCreateDept( arg0.getCreateDept() );
        groupUser.setId( arg0.getId() );
        groupUser.setGroupId( arg0.getGroupId() );
        groupUser.setUserId( arg0.getUserId() );
        groupUser.setAdminFlag( arg0.getAdminFlag() );

        return groupUser;
    }

    @Override
    public GroupUser convert(GroupUserBo arg0, GroupUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setAdminFlag( arg0.getAdminFlag() );

        return arg1;
    }
}
