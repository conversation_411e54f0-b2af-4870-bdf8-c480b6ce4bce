package com.gzhuxn.personals.controller.app.user.bo.comment;

import com.gzhuxn.personals.domain.user.UserComment;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:55+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserCommentCreateBoToUserCommentMapperImpl implements AppUserCommentCreateBoToUserCommentMapper {

    @Override
    public UserComment convert(AppUserCommentCreateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserComment userComment = new UserComment();

        userComment.setRootId( arg0.getRootId() );
        userComment.setParentId( arg0.getParentId() );
        userComment.setType( arg0.getType() );
        userComment.setBusinessId( arg0.getBusinessId() );
        userComment.setContent( arg0.getContent() );
        userComment.setImages( arg0.getImages() );

        return userComment;
    }

    @Override
    public UserComment convert(AppUserCommentCreateBo arg0, UserComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setRootId( arg0.getRootId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setType( arg0.getType() );
        arg1.setBusinessId( arg0.getBusinessId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );

        return arg1;
    }
}
