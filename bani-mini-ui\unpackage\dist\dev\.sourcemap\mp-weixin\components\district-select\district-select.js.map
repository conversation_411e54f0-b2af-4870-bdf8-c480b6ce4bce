{"version": 3, "file": "district-select.js", "sources": ["components/district-select/district-select.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovYmFuaS9jb2RlL2JhbmktbWluaS11aS9jb21wb25lbnRzL2Rpc3RyaWN0LXNlbGVjdC9kaXN0cmljdC1zZWxlY3QudnVl"], "sourcesContent": ["<template>\n\t<uni-popup ref=\"popup\" type=\"bottom\" :safe-area=\"false\" @change=\"handlePopupChange\">\n\t\t<view class=\"district-select-container\">\n\t\t\t<!-- 头部 -->\n\t\t\t<view class=\"header\">\n\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t<text class=\"cancel-btn\" @click=\"handleCancel\">取消</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-center\">\n\t\t\t\t\t<text class=\"title\">选择地区</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t<text class=\"confirm-btn\" @click=\"handleConfirm\">完成</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 面包屑导航 -->\n\t\t\t<view class=\"breadcrumb\" v-if=\"breadcrumbList.length > 0\">\n\t\t\t\t<view class=\"breadcrumb-item\" v-for=\"(item, index) in breadcrumbList\" :key=\"index\"\n\t\t\t\t\t@click=\"handleBreadcrumbClick(index)\">\n\t\t\t\t\t<text class=\"breadcrumb-text\">{{ item.name }}</text>\n\t\t\t\t\t<text class=\"breadcrumb-arrow\" v-if=\"index < breadcrumbList.length - 1\">></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 区域列表 -->\n\t\t\t<scroll-view class=\"district-list\" scroll-y :enable-back-to-top=\"true\" :scroll-with-animation=\"true\">\n\t\t\t\t<view class=\"district-item\" v-for=\"(item, index) in currentList\"\n\t\t\t\t\t:key=\"`${forceUpdateKey}-${item.code}-${index}`\"\n\t\t\t\t\t:class=\"{ 'selected': item.code === selectedItem?.code }\" @click=\"handleItemClick(item)\">\n\t\t\t\t\t<text class=\"district-name\">{{ item.name }}</text>\n\t\t\t\t\t<view class=\"district-right\">\n\t\t\t\t\t\t<text class=\"iconfont bani-check\" v-if=\"item.code === selectedItem?.code\"></text>\n\t\t\t\t\t\t<text class=\"iconfont bani-right\" v-else-if=\"item.hasChild\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 加载状态 -->\n\t\t\t\t<view class=\"loading\" v-if=\"loading\">\n\t\t\t\t\t<uni-load-more status=\"loading\"\n\t\t\t\t\t\t:content-text=\"{ contentdown: '加载中...', contentrefresh: '加载中...', contentnomore: '加载中...' }\"></uni-load-more>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view class=\"empty\" v-if=\"!loading && currentList.length === 0\">\n\t\t\t\t\t<text class=\"empty-text\">暂无数据</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t</uni-popup>\n</template>\n\n<script setup>\nimport { ref, defineProps, defineEmits } from 'vue'\nimport { getDistrictList } from '@/api/district/district'\n\n// Props\nconst props = defineProps({\n\t// 是否显示弹窗\n\tshow: {\n\t\ttype: Boolean,\n\t\tdefault: false\n\t},\n\t// 默认选中的值\n\tdefaultValue: {\n\t\ttype: Object,\n\t\tdefault: () => ({})\n\t}\n})\n\n// Emits\nconst emit = defineEmits(['confirm', 'cancel', 'update:show'])\n\n// 响应式数据\nconst popup = ref(null)\nconst loading = ref(false)\nconst currentList = ref([]) // 当前显示的列表\nconst breadcrumbList = ref([]) // 面包屑导航\nconst selectedItem = ref(null) // 当前选中的项目\nconst selectedPath = ref([]) // 选中的路径\nconst forceUpdateKey = ref(0) // 强制更新key\n\n// 监听显示状态变化\nconst handlePopupChange = (e) => {\n\tif (e.show) {\n\t\t// 弹窗打开时初始化数据\n\t\tinitData()\n\t} else {\n\t\t// 弹窗关闭时重置数据\n\t\tresetData()\n\t}\n}\n\n// 初始化数据\nconst initData = () => {\n\tconsole.log('初始化行政区域选择器')\n\tbreadcrumbList.value = [{\n\t\tname: '全国',\n\t\tcode: '0'\n\t}]\n\tselectedPath.value = []\n\tselectedItem.value = null\n\tloadDistrictList('')\n}\n\n// 重置数据\nconst resetData = () => {\n\tcurrentList.value = []\n\tbreadcrumbList.value = []\n\tselectedPath.value = []\n\tselectedItem.value = null\n\tloading.value = false\n}\n\n// 加载行政区划列表\nconst loadDistrictList = (code = '0') => {\n\tloading.value = true\n\tgetDistrictList(code).then(response => {\n\t\tcurrentList.value = response.data\n\t}).finally(() => {\n\t\tloading.value = false\n\t})\n}\n\n// 处理项目点击\nconst handleItemClick = (item) => {\n\tselectedItem.value = item\n\tif (item.hasChild) {\n\t\t// 有下级，加载下级数据\n\t\t// 先检查是否已经在路径中，避免重复添加\n\t\tconst currentLevel = breadcrumbList.value.length\n\n\t\t// 更新选择路径和面包屑\n\t\tif (selectedPath.value.length > currentLevel) {\n\t\t\tselectedPath.value = selectedPath.value.slice(0, currentLevel)\n\t\t}\n\t\tselectedPath.value.push(item)\n\n\t\tif (breadcrumbList.value.length > currentLevel) {\n\t\t\tbreadcrumbList.value = breadcrumbList.value.slice(0, currentLevel)\n\t\t}\n\t\tbreadcrumbList.value.push(item)\n\t\tloadDistrictList(item.code)\n\t} else {\n\t\t// 没有下级，选中该项目\n\t\tconst currentLevel = breadcrumbList.value.length\n\t\tif (selectedPath.value.length > currentLevel) {\n\t\t\tselectedPath.value = selectedPath.value.slice(0, currentLevel)\n\t\t}\n\t\tselectedPath.value.push(item)\n\t\thandleConfirm()\n\t}\n}\n\n// 处理面包屑点击\nconst handleBreadcrumbClick = (index) => {\n\t// 如果点击的是最后一级，不需要处理\n\tif (index === breadcrumbList.value.length - 1) {\n\t\treturn\n\t}\n\n\t// 截取到指定层级\n\tconst targetItem = breadcrumbList.value[index]\n\tbreadcrumbList.value = breadcrumbList.value.slice(0, index + 1)\n\tselectedPath.value = selectedPath.value.slice(0, index + 1)\n\tif (0 === index) {\n\t\tselectedPath.value = []\n\t}\n\t// 清空当前选中项\n\tselectedItem.value = null\n\n\t// 重新加载该层级的下级数据\n\tloadDistrictList(targetItem.code)\n}\n\n// 处理确认\nconst handleConfirm = () => {\n\tif (selectedPath.value.length === 0) {\n\t\tuni.showToast({\n\t\t\ttitle: '请选择地区',\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn\n\t}\n\tconst flag = hasMunicipality(selectedPath.value[0])\n\t// 构建返回数据\n\tconst result = {\n\t\tprovince: selectedPath.value[0] || null,\n\t\tcity: selectedPath.value[flag ? 0 : 1] || null,\n\t\tdistrict: selectedPath.value[flag ? 1 : 2] || null,\n\t\tstreet: selectedPath.value[flag ? 2 : 3] || null,\n\t\tfullPath: selectedPath.value,\n\t\tfullName: selectedPath.value.map(item => item.name).join(''),\n\t\tcodes: {\n\t\t\tprovinceCode: selectedPath.value[0]?.code || '',\n\t\t\tcityCode: selectedPath.value[flag ? 0 : 1]?.code || '',\n\t\t\tdistrictCode: selectedPath.value[flag ? 1 : 2]?.code || '',\n\t\t\tstreetCode: selectedPath.value[flag ? 2 : 3]?.code || ''\n\t\t}\n\t}\n\tconsole.log('确认选择地区:', result)\n\temit('confirm', result)\n\thandleClose()\n}\n\n/**\n * 判断是否是直辖市\n */\nconst hasMunicipality = (item) => {\n\tswitch (item.name) {\n\t\tcase '北京市':\n\t\tcase '天津市':\n\t\tcase '上海市':\n\t\tcase '重庆市':\n\t\tcase '香港':\n\t\tcase '澳门':\n\t\t\treturn true;\n\t\tdefault:\n\t\t\treturn false;\n\t}\n}\n\n// 处理取消\nconst handleCancel = () => {\n\tconsole.log('取消选择地区')\n\temit('cancel')\n\thandleClose()\n}\n\n// 关闭弹窗\nconst handleClose = () => {\n\tpopup.value?.close()\n\temit('update:show', false)\n}\n\n// 打开弹窗\nconst open = () => {\n\tpopup.value?.open()\n}\n\n// 关闭弹窗\nconst close = () => {\n\tpopup.value?.close()\n}\n\n// 暴露方法\ndefineExpose({\n\topen,\n\tclose\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/uni.scss';\n@import '@/static/fonts/iconfont.css';\n\n.district-select-container {\n\tbackground: #fff;\n\tborder-radius: 16rpx 16rpx 0 0;\n\theight: 60vh;\n\tmax-height: 60vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 24rpx 32rpx;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\tbackground: #fff;\n\tborder-radius: 16rpx 16rpx 0 0;\n}\n\n.header-left,\n.header-right {\n\tmin-width: 100rpx;\n}\n\n.header-center {\n\tflex: 1;\n\ttext-align: center;\n}\n\n.title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.cancel-btn {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n.confirm-btn {\n\tfont-size: 28rpx;\n\tcolor: #696CF3;\n\tfont-weight: 500;\n}\n\n.breadcrumb {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 32rpx;\n\tbackground: #f8f9fa;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\tflex-wrap: wrap;\n}\n\n.breadcrumb-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-right: 16rpx;\n\tcursor: pointer;\n}\n\n.breadcrumb-text {\n\tfont-size: 26rpx;\n\tcolor: #696CF3;\n\tmargin-right: 8rpx;\n}\n\n.breadcrumb-arrow {\n\tfont-size: 24rpx;\n\tcolor: #ccc;\n}\n\n.district-list {\n\tflex: 1;\n\theight: 0; /* 关键：设置高度为0，配合flex: 1使其能够正确计算滚动区域 */\n\tpadding: 0 32rpx;\n}\n\n.district-item {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 24rpx 0;\n\tborder-bottom: 1rpx solid rgba(0, 0, 0, 0.05);\n\ttransition: background-color 0.3s ease;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t&:active {\n\t\tbackground-color: rgba(0, 0, 0, 0.02);\n\t}\n\n\t&.selected {\n\t\tbackground-color: rgba(105, 108, 243, 0.05);\n\t}\n}\n\n.district-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.district-right {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.district-right .iconfont {\n\tfont-size: 24rpx;\n\tcolor: #696CF3;\n\n\t&.bani-right {\n\t\tcolor: #ccc;\n\t}\n}\n\n.loading {\n\tpadding: 40rpx 0;\n\ttext-align: center;\n}\n\n.empty {\n\tpadding: 80rpx 0;\n\ttext-align: center;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 750rpx) {\n\t.header {\n\t\tpadding: 20rpx 24rpx;\n\t}\n\n\t.breadcrumb {\n\t\tpadding: 16rpx 24rpx;\n\t}\n\n\t.district-list {\n\t\tpadding: 0 24rpx;\n\t}\n\n\t.district-item {\n\t\tpadding: 20rpx 0;\n\t}\n}\n</style>\n", "import Component from 'E:/bani/code/bani-mini-ui/components/district-select/district-select.vue'\nwx.createComponent(Component)"], "names": ["emit", "__emit", "popup", "ref", "loading", "currentList", "breadcrumbList", "selectedItem", "<PERSON><PERSON><PERSON>", "forceUpdateKey", "handlePopupChange", "initData", "resetData", "uni", "loadDistrictList", "code", "getDistrictList", "response", "handleItemClick", "item", "currentLevel", "handleConfirm", "handleBreadcrumbClick", "index", "targetItem", "flag", "hasMunicipality", "result", "_a", "_b", "_c", "_d", "handleClose", "handleCancel", "__expose", "Component"], "mappings": "yiBAuEA,MAAMA,EAAOC,EAGPC,EAAQC,EAAG,IAAC,IAAI,EAChBC,EAAUD,EAAG,IAAC,EAAK,EACnBE,EAAcF,EAAG,IAAC,EAAE,EACpBG,EAAiBH,EAAG,IAAC,EAAE,EACvBI,EAAeJ,EAAG,IAAC,IAAI,EACvBK,EAAeL,EAAG,IAAC,EAAE,EACrBM,EAAiBN,EAAG,IAAC,CAAC,EAGtBO,EAAqB,GAAM,CAC5B,EAAE,KAELC,EAAU,EAGVC,EAAW,CAEb,EAGMD,EAAW,IAAM,CACtBE,EAAAA,MAAY,MAAA,MAAA,uDAAA,YAAY,EACxBP,EAAe,MAAQ,CAAC,CACvB,KAAM,KACN,KAAM,GACR,CAAE,EACDE,EAAa,MAAQ,CAAE,EACvBD,EAAa,MAAQ,KACrBO,EAAiB,EAAE,CACpB,EAGMF,EAAY,IAAM,CACvBP,EAAY,MAAQ,CAAE,EACtBC,EAAe,MAAQ,CAAE,EACzBE,EAAa,MAAQ,CAAE,EACvBD,EAAa,MAAQ,KACrBH,EAAQ,MAAQ,EACjB,EAGMU,EAAmB,CAACC,EAAO,MAAQ,CACxCX,EAAQ,MAAQ,GAChBY,EAAAA,gBAAgBD,CAAI,EAAE,KAAKE,GAAY,CACtCZ,EAAY,MAAQY,EAAS,IAC/B,CAAE,EAAE,QAAQ,IAAM,CAChBb,EAAQ,MAAQ,EAClB,CAAE,CACF,EAGMc,EAAmBC,GAAS,CAEjC,GADAZ,EAAa,MAAQY,EACjBA,EAAK,SAAU,CAGlB,MAAMC,EAAed,EAAe,MAAM,OAGtCE,EAAa,MAAM,OAASY,IAC/BZ,EAAa,MAAQA,EAAa,MAAM,MAAM,EAAGY,CAAY,GAE9DZ,EAAa,MAAM,KAAKW,CAAI,EAExBb,EAAe,MAAM,OAASc,IACjCd,EAAe,MAAQA,EAAe,MAAM,MAAM,EAAGc,CAAY,GAElEd,EAAe,MAAM,KAAKa,CAAI,EAC9BL,EAAiBK,EAAK,IAAI,CAC5B,KAAQ,CAEN,MAAMC,EAAed,EAAe,MAAM,OACtCE,EAAa,MAAM,OAASY,IAC/BZ,EAAa,MAAQA,EAAa,MAAM,MAAM,EAAGY,CAAY,GAE9DZ,EAAa,MAAM,KAAKW,CAAI,EAC5BE,EAAe,CACf,CACF,EAGMC,EAAyBC,GAAU,CAExC,GAAIA,IAAUjB,EAAe,MAAM,OAAS,EAC3C,OAID,MAAMkB,EAAalB,EAAe,MAAMiB,CAAK,EAC7CjB,EAAe,MAAQA,EAAe,MAAM,MAAM,EAAGiB,EAAQ,CAAC,EAC9Df,EAAa,MAAQA,EAAa,MAAM,MAAM,EAAGe,EAAQ,CAAC,EAChDA,IAAN,IACHf,EAAa,MAAQ,CAAE,GAGxBD,EAAa,MAAQ,KAGrBO,EAAiBU,EAAW,IAAI,CACjC,EAGMH,EAAgB,IAAM,aAC3B,GAAIb,EAAa,MAAM,SAAW,EAAG,CACpCK,EAAAA,MAAI,UAAU,CACb,MAAO,QACP,KAAM,MACT,CAAG,EACD,MACA,CACD,MAAMY,EAAOC,EAAgBlB,EAAa,MAAM,CAAC,CAAC,EAE5CmB,EAAS,CACd,SAAUnB,EAAa,MAAM,CAAC,GAAK,KACnC,KAAMA,EAAa,MAAMiB,EAAO,EAAI,CAAC,GAAK,KAC1C,SAAUjB,EAAa,MAAMiB,EAAO,EAAI,CAAC,GAAK,KAC9C,OAAQjB,EAAa,MAAMiB,EAAO,EAAI,CAAC,GAAK,KAC5C,SAAUjB,EAAa,MACvB,SAAUA,EAAa,MAAM,IAAIW,GAAQA,EAAK,IAAI,EAAE,KAAK,EAAE,EAC3D,MAAO,CACN,eAAcS,EAAApB,EAAa,MAAM,CAAC,IAApB,YAAAoB,EAAuB,OAAQ,GAC7C,WAAUC,EAAArB,EAAa,MAAMiB,EAAO,EAAI,CAAC,IAA/B,YAAAI,EAAkC,OAAQ,GACpD,eAAcC,EAAAtB,EAAa,MAAMiB,EAAO,EAAI,CAAC,IAA/B,YAAAK,EAAkC,OAAQ,GACxD,aAAYC,EAAAvB,EAAa,MAAMiB,EAAO,EAAI,CAAC,IAA/B,YAAAM,EAAkC,OAAQ,EACtD,CACD,EACDlB,EAAAA,0EAAY,UAAWc,CAAM,EAC7B3B,EAAK,UAAW2B,CAAM,EACtBK,EAAa,CACd,EAKMN,EAAmBP,GAAS,CACjC,OAAQA,EAAK,KAAI,CAChB,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,MACL,IAAK,KACL,IAAK,KACJ,MAAO,GACR,QACC,MAAO,EACR,CACF,EAGMc,EAAe,IAAM,CAC1BpB,EAAAA,MAAA,MAAA,MAAA,wDAAY,QAAQ,EACpBb,EAAK,QAAQ,EACbgC,EAAa,CACd,EAGMA,EAAc,IAAM,QACzBJ,EAAA1B,EAAM,QAAN,MAAA0B,EAAa,QACb5B,EAAK,cAAe,EAAK,CAC1B,EAaA,OAAAkC,EAAa,CACZ,KAXY,IAAM,QAClBN,EAAA1B,EAAM,QAAN,MAAA0B,EAAa,MACd,EAUC,MAPa,IAAM,QACnBA,EAAA1B,EAAM,QAAN,MAAA0B,EAAa,OACd,CAMA,CAAC,00BCxPD,GAAG,gBAAgBO,CAAS"}