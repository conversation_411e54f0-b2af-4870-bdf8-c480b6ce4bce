package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserSignIn;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserSignInBoToUserSignInMapperImpl implements UserSignInBoToUserSignInMapper {

    @Override
    public UserSignIn convert(UserSignInBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserSignIn userSignIn = new UserSignIn();

        userSignIn.setSearchValue( arg0.getSearchValue() );
        userSignIn.setCreateBy( arg0.getCreateBy() );
        userSignIn.setCreateTime( arg0.getCreateTime() );
        userSignIn.setUpdateBy( arg0.getUpdateBy() );
        userSignIn.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userSignIn.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userSignIn.setCreateDept( arg0.getCreateDept() );
        userSignIn.setId( arg0.getId() );
        userSignIn.setUserId( arg0.getUserId() );
        userSignIn.setDate( arg0.getDate() );
        userSignIn.setConsecutiveDays( arg0.getConsecutiveDays() );
        userSignIn.setCoin( arg0.getCoin() );

        return userSignIn;
    }

    @Override
    public UserSignIn convert(UserSignInBo arg0, UserSignIn arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setDate( arg0.getDate() );
        arg1.setConsecutiveDays( arg0.getConsecutiveDays() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
