package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserAccountHistoryBoToUserAccountHistoryMapper;
import com.gzhuxn.personals.domain.user.vo.UserAccountHistoryVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAccountHistoryBoToUserAccountHistoryMapper.class,UserAccountHistoryToAppUserAccountHistoryVoMapper.class},
    imports = {}
)
public interface UserAccountHistoryToUserAccountHistoryVoMapper extends BaseMapper<UserAccountHistory, UserAccountHistoryVo> {
}
