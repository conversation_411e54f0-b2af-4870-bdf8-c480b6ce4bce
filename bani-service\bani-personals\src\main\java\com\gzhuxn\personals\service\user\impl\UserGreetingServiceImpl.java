package com.gzhuxn.personals.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gzhuxn.common.core.utils.MapstructUtils;
import com.gzhuxn.common.mybatis.core.page.PageQuery;
import com.gzhuxn.common.mybatis.core.page.TableDataInfo;
import com.gzhuxn.common.satoken.utils.LoginHelper;
import com.gzhuxn.personals.controller.app.user.bo.AppUserGreetingCreateBo;
import com.gzhuxn.personals.controller.app.user.vo.AppUserGreetingVo;
import com.gzhuxn.personals.domain.user.UserGreeting;
import com.gzhuxn.personals.domain.user.vo.UserGreetingVo;
import com.gzhuxn.personals.mapper.user.UserGreetingMapper;
import com.gzhuxn.personals.service.base.BaniServiceImpl;
import com.gzhuxn.personals.service.user.IUserGreetingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 用户-打招呼Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class UserGreetingServiceImpl extends BaniServiceImpl<UserGreetingMapper, UserGreeting> implements IUserGreetingService {

    @Override
    public UserGreetingVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<AppUserGreetingVo> queryReceivedPageList(PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<UserGreeting> lqw = baseMapper.buildReceivedGreetingQuery(userId);
        Page<AppUserGreetingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AppUserGreetingVo.class);
        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<AppUserGreetingVo> querySentPageList(PageQuery pageQuery) {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<UserGreeting> lqw = baseMapper.buildSentGreetingQuery(userId);
        Page<AppUserGreetingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, AppUserGreetingVo.class);
        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertByBo(AppUserGreetingCreateBo bo) {
        Long userId = LoginHelper.getUserId();
        
        // 检查是否已经打过招呼
        if (hasGreeted(userId, bo.getOppositeUserId())) {
            throw new RuntimeException("您已经向该用户打过招呼了");
        }
        
        UserGreeting add = MapstructUtils.convert(bo, UserGreeting.class);
        add.setUserId(userId);
        add.setStatus(0); // 待回复状态
        validEntityBeforeSave(add);
        
        return save(add);
    }

    @Override
    public boolean deleteWithValidByIds(Collection<Long> ids, boolean isValid) {
        if (isValid) {
            // 可以添加删除前的验证逻辑
        }
        return removeByIds(ids);
    }

    @Override
    public boolean hasGreeted(Long userId, Long oppositeUserId) {
        if (userId == null) {
            userId = LoginHelper.getUserId();
        }
        UserGreeting greeting = baseMapper.getByUserIdAndOppositeUserId(userId, oppositeUserId);
        return greeting != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reply(Long id, String content) {
        UserGreeting greeting = getById(id);
        if (greeting == null) {
            throw new RuntimeException("打招呼记录不存在");
        }
        
        Long currentUserId = LoginHelper.getUserId();
        if (!greeting.getOppositeUserId().equals(currentUserId)) {
            throw new RuntimeException("无权限回复此打招呼");
        }
        
        // 更新状态为已回复
        greeting.setStatus(1);
        return updateById(greeting);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean ignore(Long id) {
        UserGreeting greeting = getById(id);
        if (greeting == null) {
            throw new RuntimeException("打招呼记录不存在");
        }
        
        Long currentUserId = LoginHelper.getUserId();
        if (!greeting.getOppositeUserId().equals(currentUserId)) {
            throw new RuntimeException("无权限忽略此打招呼");
        }
        
        // 更新状态为已忽略
        greeting.setStatus(2);
        return updateById(greeting);
    }
}
