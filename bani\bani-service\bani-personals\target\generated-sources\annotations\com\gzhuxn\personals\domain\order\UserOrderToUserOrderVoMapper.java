package com.gzhuxn.personals.domain.order;

import com.gzhuxn.personals.controller.app.order.bo.AppCreateOrderBoToUserOrderMapper;
import com.gzhuxn.personals.domain.order.bo.UserOrderBoToUserOrderMapper;
import com.gzhuxn.personals.domain.order.vo.UserOrderVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppCreateOrderBoToUserOrderMapper.class,UserOrderBoToUserOrderMapper.class,UserOrderToAppUserOrderVoMapper.class},
    imports = {}
)
public interface UserOrderToUserOrderVoMapper extends BaseMapper<UserOrder, UserOrderVo> {
}
