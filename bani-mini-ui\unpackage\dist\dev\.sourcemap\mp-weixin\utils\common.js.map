{"version": 3, "file": "common.js", "sources": ["utils/common.js"], "sourcesContent": ["import {\r\n\tref,\r\n\treactive\r\n} from 'vue'\r\nimport $store from '@/store'\r\nimport { updateUserLocation } from '@/api/my/my.js'\r\nimport { getPayStatus } from '@/api/order/order.js'\r\n/**\r\n * 显示消息提示框\r\n * @param content 提示的标题\r\n */\r\nexport function toast(content) {\r\n\tuni.showToast({\r\n\t\ticon: 'none',\r\n\t\ttitle: content\r\n\t})\r\n}\r\n\r\n/**\r\n * 显示模态弹窗\r\n * @param content 提示的标题\r\n */\r\nexport function showConfirm(content) {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tuni.showModal({\r\n\t\t\ttitle: '提示',\r\n\t\t\tcontent: content,\r\n\t\t\tcancelText: '取消',\r\n\t\t\tconfirmText: '确定',\r\n\t\t\tsuccess: function (res) {\r\n\t\t\t\tresolve(res)\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n/**\r\n * 处理支付并轮询查询支付状态\r\n * @param {Object} payData - 支付数据\r\n * @param {string} payData.appId - 应用ID\r\n * @param {string} payData.timeStamp - 时间戳\r\n * @param {string} payData.nonceStr - 随机字符串\r\n * @param {string} payData.packageValue - 订单详情扩展字符串\r\n * @param {string} payData.signType - 签名方式\r\n * @param {string} payData.paySign - 签名\r\n * @param {string} payOrderNo - 支付订单号，用于查询支付状态\r\n * @param {Object} options - 配置选项\r\n * @param {number} [options.pollInterval=2000] - 轮询间隔时间（毫秒），默认2秒\r\n * @param {number} [options.maxPollCount=30] - 最大轮询次数，默认30次（1分钟）\r\n * @returns {Promise} 返回Promise对象，resolve时返回支付结果 {success: boolean, status: number, message: string}\r\n */\r\nexport function handlePaymentWithPolling(data, options = {}) {\r\n\tconst payData = data.payData;\r\n\treturn new Promise((resolve, reject) => {\r\n\t\tconst { pollInterval = 2000, maxPollCount = 30 } = options\r\n\t\tlet pollCount = 0\r\n\t\tlet pollTimer = null\r\n\r\n\t\t// 清理轮询定时器\r\n\t\tconst clearPollTimer = () => {\r\n\t\t\tif (pollTimer) {\r\n\t\t\t\tclearInterval(pollTimer)\r\n\t\t\t\tpollTimer = null\r\n\t\t\t}\r\n\t\t\tuni.hideLoading()\r\n\t\t}\r\n\r\n\t\t// 轮询查询支付状态\r\n\t\tconst pollPayStatus = () => {\r\n\t\t\tpollCount++\r\n\t\t\tgetPayStatus(data.payOrderNo).then(res => {\r\n\t\t\t\tconst status = res.data.status\r\n\t\t\t\t// 支付成功\r\n\t\t\t\tif (status === 10) {\r\n\t\t\t\t\tclearPollTimer()\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tsuccess: true,\r\n\t\t\t\t\t\tstatus: status,\r\n\t\t\t\t\t\tmessage: '支付成功'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 支付失败或关闭\r\n\t\t\t\tif (status === 3 || status === 4) {\r\n\t\t\t\t\tclearPollTimer()\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\t\tstatus: status,\r\n\t\t\t\t\t\tmessage: status === 3 ? '支付失败' : '支付已关闭'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 其他状态继续轮询\r\n\t\t\t\tif (pollCount >= maxPollCount) {\r\n\t\t\t\t\tclearPollTimer()\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\t\tstatus: status,\r\n\t\t\t\t\t\tmessage: '支付状态查询超时'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tconsole.error('支付状态查询异常:', err)\r\n\t\t\t\tif (pollCount >= maxPollCount) {\r\n\t\t\t\t\tclearPollTimer()\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\t\tstatus: -1,\r\n\t\t\t\t\t\tmessage: '支付状态查询异常'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\t// 显示支付中提示\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '支付中...'\r\n\t\t})\r\n\r\n\t\t// 执行微信支付\r\n\t\tuni.requestPayment({\r\n\t\t\tprovider: 'wxpay',\r\n\t\t\tappId: payData.appId,\r\n\t\t\ttimeStamp: payData.timeStamp,\r\n\t\t\tnonceStr: payData.nonceStr,\r\n\t\t\tpackage: payData.packageValue,\r\n\t\t\tsignType: payData.signType,\r\n\t\t\tpaySign: payData.paySign,\r\n\t\t\tsuccess: () => {\r\n\t\t\t\t// 支付调用成功后，开始轮询查询支付状态\r\n\t\t\t\tpollTimer = setInterval(pollPayStatus, pollInterval)\r\n\t\t\t\t// 立即执行一次查询\r\n\t\t\t\tpollPayStatus()\r\n\t\t\t},\r\n\t\t\tfail: (error) => {\r\n\t\t\t\tconsole.error('微信支付调用失败:', error)\r\n\t\t\t\tclearPollTimer()\r\n\r\n\t\t\t\t// 处理不同的支付错误\r\n\t\t\t\tlet errorMessage = '支付失败'\r\n\t\t\t\tif (error.errMsg) {\r\n\t\t\t\t\tif (error.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\terrorMessage = '支付已取消'\r\n\t\t\t\t\t} else if (error.errMsg.includes('fail')) {\r\n\t\t\t\t\t\terrorMessage = '支付失败，请重试'\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\terrorMessage = '支付异常，请重试'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tresolve({\r\n\t\t\t\t\tsuccess: false,\r\n\t\t\t\t\tstatus: -1,\r\n\t\t\t\t\tmessage: errorMessage\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n/**\r\n * 参数处理\r\n * @param params 参数\r\n */\r\nexport function tansParams(params) {\r\n\tlet result = ''\r\n\tfor (const propName of Object.keys(params)) {\r\n\t\tconst value = params[propName]\r\n\t\tvar part = encodeURIComponent(propName) + \"=\"\r\n\t\tif (value !== null && value !== \"\" && typeof (value) !== \"undefined\") {\r\n\t\t\tif (typeof value === 'object') {\r\n\t\t\t\tfor (const key of Object.keys(value)) {\r\n\t\t\t\t\tif (value[key] !== null && value[key] !== \"\" && typeof (value[key]) !== 'undefined') {\r\n\t\t\t\t\t\tlet params = propName + '[' + key + ']'\r\n\t\t\t\t\t\tvar subPart = encodeURIComponent(params) + \"=\"\r\n\t\t\t\t\t\tresult += subPart + encodeURIComponent(value[key]) + \"&\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tresult += part + encodeURIComponent(value) + \"&\"\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * 创建UUID\r\n */\r\nexport function createUUID() {\r\n\tvar dt = new Date().getTime(); // 获取当前时间的毫秒数\r\n\tvar uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n\t\tvar r = (dt + Math.random() * 16) % 16 | 0;\r\n\t\tdt = Math.floor(dt / 16);\r\n\t\treturn (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);\r\n\t});\r\n\treturn uuid;\r\n}\r\n\r\n/**\r\n * 创建rule规则\r\n * @param {Object} form\r\n */\r\nexport function createFormRules(form) {\r\n\tvar rules = {};\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar rule = form[key]['rule']\r\n\t\tif (rule) {\r\n\t\t\trules[key] = rule\r\n\t\t}\r\n\t})\r\n\treturn reactive(rules)\r\n}\r\n\r\n/**\r\n * 初始化加载表单\r\n */\r\nexport function initForm(form) {\r\n\t// 加载字典数据\r\n\tvar loadTypes = [];\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar t = form[key]\r\n\t\t// 拉取字典数据\r\n\t\tif (t.dictType) {\r\n\t\t\tloadTypes.push(t.dictType)\r\n\t\t}\r\n\t})\r\n\tif (loadTypes.length > 0) {\r\n\t\t$store.dict.loads(loadTypes)\r\n\t}\r\n\r\n\t// 初始化表单\r\n\tvar retJs = {};\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar t = form[key]\r\n\t\t// 添加字段名称\r\n\t\tt['field'] = key\r\n\t\t// 值数据初始化\r\n\t\tvalNullSetDefault(t, 'val', '')\r\n\t\tvalNullSetDefault(t, 'name', '')\r\n\t\t// 显示状态\r\n\t\tvalNullSetDefault(t, 'show', false)\r\n\t\t// 拉取字典数据\r\n\t\t// if (t.dictType) {\r\n\t\t// \tt['dicts'] = $store.dict.get(t.dictType)\r\n\t\t// }\r\n\t\tconst isSelect = t.dicts ? true : false;\r\n\t\tconst placeholderMsg = (isSelect ? '请选择' : '请填写') + t.title;\r\n\t\tt['placeholder'] = placeholderMsg;\r\n\t\t// 字段验证\r\n\t\tif (t.rule) {\r\n\t\t\tif ((typeof t.rule) == 'object') {\r\n\t\t\t\tformatFormRuleObj(r.rule, placeholderMsg);\r\n\t\t\t} else if (Array.isArray(t.rule)) {\r\n\t\t\t\tt.rule.forEach(item => {\r\n\t\t\t\t\tformatFormRuleObj(item, placeholderMsg);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t\tretJs[key] = t;\r\n\t})\r\n\treturn reactive(retJs);\r\n}\r\n/**\r\n * 格式化校验规则\r\n */\r\nfunction formatFormRuleObj(ruleObj, placeholderMsg) {\r\n\tvalNullSetDefault(ruleObj, 'type', 'string');\r\n\tvalNullSetDefault(ruleObj, 'required', true);\r\n\r\n\t// 校验提示\r\n\tvalNullSetDefault(ruleObj, 'message', placeholderMsg);\r\n}\r\n\r\n/**\r\n * 值为空设置默认值\r\n */\r\nfunction valNullSetDefault(source, field, defVal) {\r\n\tif (source[field] === undefined || source[field] === null || source[field] === '') {\r\n\t\tsource[field] = defVal;\r\n\t}\r\n}\r\n\r\n/**\r\n * 初始化字典数据\r\n * @param {Object} form\r\n */\r\nexport function initFormDicts(form) {\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar t = form[key]\r\n\t\t// 拉取字典数据\r\n\t\tif (t.dictType) {\r\n\t\t\tt['dicts'] = $store.dict.get(t.dictType)\r\n\t\t}\r\n\t\tif (t.dicts) {\r\n\t\t\t// 字典名称\r\n\t\t\tt['dictNames'] = []\r\n\t\t\tt['dicts'].forEach(item => {\r\n\t\t\t\tt['dictNames'].push(item.name)\r\n\t\t\t})\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 加载表单数据\r\n * @param {Object} form\r\n * @param {Object} data\r\n */\r\nexport function initFormData(form, data) {\r\n\tif (!data) {\r\n\t\treturn;\r\n\t}\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar field = form[key]\r\n\t\tif (data[key]) {\r\n\t\t\tfield.val = data[key]\r\n\t\t\tfield.name = data[key]\r\n\t\t\tif (field.dicts) {\r\n\t\t\t\tfield.dicts.forEach((item, index) => {\r\n\t\t\t\t\tif (item.id == field.val) {\r\n\t\t\t\t\t\tfield.name = item.name\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t})\r\n}\r\n\r\n/**\r\n * 校验提交数据\r\n * @param {Object} form\r\n */\r\nexport function validFormData(form) {\r\n\tvar flag = true\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar field = form[key]\r\n\t\tif (field.requried && isEmpty(field.val)) {\r\n\t\t\ttoast(field.title + \"必填\")\r\n\t\t\tflag = false;\r\n\t\t\treturn false;\r\n\t\t}\r\n\t})\r\n\treturn flag\r\n}\r\n/**\r\n * 获取提交数据\r\n * @param {Object} form\r\n */\r\nexport function getSubmitData(form) {\r\n\tvar retJs = {}\r\n\tObject.keys(form).forEach(key => {\r\n\t\tvar field = form[key]\r\n\t\tretJs[key] = field.val\r\n\t})\r\n\treturn retJs;\r\n}\r\n/**\r\n * 日期格式化\r\n * @param {Object} date date对象\r\n * @param {Object} format 格式化\r\n */\r\nexport function formatDateTime(date, format) {\r\n\tconst o = {\r\n\t\t'M+': date.getMonth() + 1, // 月份\r\n\t\t'd+': date.getDate(), // 日\r\n\t\t'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时\r\n\t\t'H+': date.getHours(), // 小时\r\n\t\t'm+': date.getMinutes(), // 分\r\n\t\t's+': date.getSeconds(), // 秒\r\n\t\t'q+': Math.floor((date.getMonth() + 3) / 3), // 季度\r\n\t\tS: date.getMilliseconds(), // 毫秒\r\n\t\ta: date.getHours() < 12 ? '上午' : '下午', // 上午/下午\r\n\t\tA: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM\r\n\t};\r\n\tif (/(y+)/.test(format)) {\r\n\t\tformat = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));\r\n\t}\r\n\tfor (let k in o) {\r\n\t\tif (new RegExp('(' + k + ')').test(format)) {\r\n\t\t\tformat = format.replace(\r\n\t\t\t\tRegExp.$1,\r\n\t\t\t\tRegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)\r\n\t\t\t);\r\n\t\t}\r\n\t}\r\n\treturn format;\r\n}\r\n\r\n/**\r\n *  身高（cm）字典\r\n */\r\nexport function heightDicts() {\r\n\tvar heightVals = [];\r\n\tfor (var i = 140; i <= 220; i++) {\r\n\t\theightVals.push({\r\n\t\t\tid: i,\r\n\t\t\tname: i + 'cm'\r\n\t\t})\r\n\t}\r\n\treturn heightVals;\r\n}\r\n/**\r\n *  体重（kg）字典\r\n */\r\nexport function weightDicts() {\r\n\tvar weightVals = [];\r\n\tfor (var i = 40; i <= 120; i++) {\r\n\t\tweightVals.push({\r\n\t\t\tid: i,\r\n\t\t\tname: i + 'kg'\r\n\t\t})\r\n\t}\r\n\treturn weightVals;\r\n}\r\n\r\n/**\r\n * 点击复制\r\n * @param {Object} data\r\n */\r\nexport function clickCopy(data) {\r\n\tif (!data) {\r\n\t\ttoast(\"复制内容为空\")\r\n\t\treturn\r\n\t}\r\n\tuni.setClipboardData({\r\n\t\tdata: String(data),\r\n\t\tsuccess: function () {\r\n\t\t\ttoast(\"复制成功\")\r\n\t\t},\r\n\t\tfail: function (err) {\r\n\t\t\ttoast(\"复制失败\")\r\n\t\t}\r\n\t});\r\n}\r\n\r\n/**\r\n * 点击后toast 弹出功能开发中，敬请期待！\r\n */\r\nexport function clickDeveloping() {\r\n\ttoast(\"功能开发中，敬请期待！\")\r\n}\r\n\r\n// 判断是否为空\r\nexport function isEmpty(val) {\r\n\tif (val === null || val === undefined || val === '') {\r\n\t\treturn true;\r\n\t}\r\n\treturn false;\r\n}\r\n/**\r\n * 判断是否为字符串\r\n * @param {Object} val\r\n */\r\nexport function isString(val) {\r\n\treturn typeof val === 'string'\r\n}\r\n\r\n\r\n/**\r\n * 图片压缩\r\n * @param {Object} filePath 图片路径\r\n * @param {Object} quality 压缩质量\r\n */\r\nexport function compressImage(filePath, quality) {\r\n\treturn new Promise((resolve) => {\r\n\t\tuni.compressImage({\r\n\t\t\tsrc: filePath,\r\n\t\t\tquality: quality ? quality : 80, // 压缩质量，范围0-100，数值越小，质量越低，体积越小\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tresolve(res.tempFilePath)\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tconsole.error('图片压缩失败:', err)\r\n\t\t\t\t// 压缩失败时使用原图\r\n\t\t\t\tresolve(filePath)\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n\r\n/**\r\n * 获取当前页面完整路径\r\n * @description 获取当前页面的路径，包含查询参数\r\n * @returns {string} 返回完整的页面路径，如 '/pages/user/profile?id=123'，如果获取失败返回空字符串\r\n */\r\nexport function getCurrentPagePath() {\r\n\ttry {\r\n\t\t// 获取当前页面路径\r\n\t\tconst pages = getCurrentPages()\r\n\t\tif (!pages || !Array.isArray(pages) || pages.length === 0) {\r\n\t\t\tconsole.warn('无法获取当前页面信息')\r\n\t\t\treturn ''\r\n\t\t}\r\n\t\tconst currentPage = pages[pages.length - 1]\r\n\t\tif (!currentPage) {\r\n\t\t\tconsole.warn('当前页面对象不存在')\r\n\t\t\treturn ''\r\n\t\t}\r\n\r\n\t\tconst currentRoute = currentPage.route\r\n\t\tconst currentOptions = currentPage.options || {}\r\n\r\n\t\tconsole.log('当前页面路径getCurrentPagePath:', currentRoute)\r\n\r\n\t\t// 构建完整的页面路径（包含参数）\r\n\t\tlet fullPath = '/' + currentRoute\r\n\t\tif (currentOptions && Object.keys(currentOptions).length > 0) {\r\n\t\t\tconst queryString = Object.keys(currentOptions)\r\n\t\t\t\t.map(key => `${key}=${encodeURIComponent(currentOptions[key] || '')}`)\r\n\t\t\t\t.join('&')\r\n\t\t\tfullPath += '?' + queryString\r\n\t\t}\r\n\t\treturn fullPath\r\n\t} catch (error) {\r\n\t\tconsole.error('获取当前页面路径失败:', error)\r\n\t\treturn ''\r\n\t}\r\n}\r\n\r\n/**\r\n * 获取经纬度信息\r\n * @description 先查询本地存储的经纬度信息，检查是否过期（10分钟），超过10分钟则重新获取，否则从本地存储中获取并返回\r\n * @returns {Promise} 返回Promise对象，包含经纬度信息 {longitude, latitude}\r\n */\r\nexport function getLocation() {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\t// 从本地存储获取经纬度信息\r\n\t\tconst locationData = uni.getStorageSync('user_location')\r\n\t\tconst currentTime = Date.now()\r\n\r\n\t\t// 检查本地存储的数据是否存在且未过期（10分钟 = 600000毫秒）\r\n\t\tif (locationData && locationData.timestamp && (currentTime - locationData.timestamp) < 600000) {\r\n\t\t\tresolve({\r\n\t\t\t\tlongitude: locationData.longitude,\r\n\t\t\t\tlatitude: locationData.latitude\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// 本地数据不存在或已过期，重新获取位置信息\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '获取位置中...'\r\n\t\t})\r\n\t\tuni.getLocation({\r\n\t\t\ttype: 'gcj02', // 返回可以用于uni.openLocation的经纬度\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tconst locationInfo = {\r\n\t\t\t\t\tlongitude: res.longitude,\r\n\t\t\t\t\tlatitude: res.latitude,\r\n\t\t\t\t\ttimestamp: currentTime\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 保存到本地存储\r\n\t\t\t\tuni.setStorageSync('user_location', locationInfo)\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\t// 调用后台接口更新用户经纬度\r\n\t\t\t\tif (!$store.isUserShort()) {\r\n\t\t\t\t\tupdateUserLocation(res.longitude, res.latitude)\r\n\t\t\t\t}\r\n\t\t\t\tresolve({\r\n\t\t\t\t\tlongitude: res.longitude,\r\n\t\t\t\t\tlatitude: res.latitude\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tfail: (err) => {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tconsole.error('获取位置失败:', err)\r\n\t\t\t\t// 如果获取失败但本地有缓存数据，则使用缓存数据\r\n\t\t\t\tif (locationData && locationData.longitude && locationData.latitude) {\r\n\t\t\t\t\tresolve({\r\n\t\t\t\t\t\tlongitude: locationData.longitude,\r\n\t\t\t\t\t\tlatitude: locationData.latitude\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t})\r\n}\r\n"], "names": ["toast", "content", "uni", "showConfirm", "resolve", "reject", "res", "handlePaymentWithPolling", "data", "options", "payData", "pollInterval", "max<PERSON>oll<PERSON>ount", "pollCount", "pollTimer", "clearPollTimer", "pollPayStatus", "getPayStatus", "status", "err", "error", "errorMessage", "tansParams", "params", "result", "propName", "value", "part", "key", "subPart", "createUUID", "dt", "uuid", "c", "r", "heightDicts", "heightVals", "i", "weightDicts", "weightVals", "clickCopy", "clickDeveloping", "isString", "val", "compressImage", "filePath", "quality", "getCurrentPagePath", "pages", "currentPage", "currentRoute", "currentOptions", "fullPath", "queryString", "getLocation", "locationData", "currentTime", "locationInfo", "$store", "updateUserLocation"], "mappings": "mJAWO,SAASA,EAAMC,EAAS,CAC9BC,EAAAA,MAAI,UAAU,CACb,KAAM,OACN,MAAOD,CACT,CAAE,CACF,CAMO,SAASE,EAAYF,EAAS,CACpC,OAAO,IAAI,QAAQ,CAACG,EAASC,IAAW,CACvCH,EAAAA,MAAI,UAAU,CACb,MAAO,KACP,QAASD,EACT,WAAY,KACZ,YAAa,KACb,QAAS,SAAUK,EAAK,CACvBF,EAAQE,CAAG,CACX,CACJ,CAAG,CACH,CAAE,CACF,CAiBO,SAASC,EAAyBC,EAAMC,EAAU,GAAI,CAC5D,MAAMC,EAAUF,EAAK,QACrB,OAAO,IAAI,QAAQ,CAACJ,EAASC,IAAW,CACvC,KAAM,CAAE,aAAAM,EAAe,IAAM,aAAAC,EAAe,EAAI,EAAGH,EACnD,IAAII,EAAY,EACZC,EAAY,KAGhB,MAAMC,EAAiB,IAAM,CACxBD,IACH,cAAcA,CAAS,EACvBA,EAAY,MAEbZ,EAAAA,MAAI,YAAa,CACjB,EAGKc,EAAgB,IAAM,CAC3BH,IACAI,EAAAA,aAAaT,EAAK,UAAU,EAAE,KAAKF,GAAO,CACzC,MAAMY,EAASZ,EAAI,KAAK,OAExB,GAAIY,IAAW,GAAI,CAClBH,EAAgB,EAChBX,EAAQ,CACP,QAAS,GACT,OAAQc,EACR,QAAS,MACf,CAAM,EACD,MACA,CAGD,GAAIA,IAAW,GAAKA,IAAW,EAAG,CACjCH,EAAgB,EAChBX,EAAQ,CACP,QAAS,GACT,OAAQc,EACR,QAASA,IAAW,EAAI,OAAS,OACvC,CAAM,EACD,MACA,CAGGL,GAAaD,IAChBG,EAAgB,EAChBX,EAAQ,CACP,QAAS,GACT,OAAQc,EACR,QAAS,UACf,CAAM,EAEN,CAAI,EAAE,MAAMC,GAAO,CACfjB,EAAAA,MAAA,MAAA,QAAA,yBAAc,YAAaiB,CAAG,EAC1BN,GAAaD,IAChBG,EAAgB,EAChBX,EAAQ,CACP,QAAS,GACT,OAAQ,GACR,QAAS,UACf,CAAM,EAEN,CAAI,CACD,EAGDF,EAAAA,MAAI,YAAY,CACf,MAAO,QACV,CAAG,EAGDA,EAAAA,MAAI,eAAe,CAClB,SAAU,QACV,MAAOQ,EAAQ,MACf,UAAWA,EAAQ,UACnB,SAAUA,EAAQ,SAClB,QAASA,EAAQ,aACjB,SAAUA,EAAQ,SAClB,QAASA,EAAQ,QACjB,QAAS,IAAM,CAEdI,EAAY,YAAYE,EAAeL,CAAY,EAEnDK,EAAe,CACf,EACD,KAAOI,GAAU,CAChBlB,EAAAA,MAAA,MAAA,QAAA,yBAAc,YAAakB,CAAK,EAChCL,EAAgB,EAGhB,IAAIM,EAAe,OACfD,EAAM,SACLA,EAAM,OAAO,SAAS,QAAQ,EACjCC,EAAe,QACLD,EAAM,OAAO,SAAS,MAAM,EACtCC,EAAe,WAEfA,EAAe,YAIjBjB,EAAQ,CACP,QAAS,GACT,OAAQ,GACR,QAASiB,CACd,CAAK,CACD,CACJ,CAAG,CACH,CAAE,CACF,CAMO,SAASC,EAAWC,EAAQ,CAClC,IAAIC,EAAS,GACb,UAAWC,KAAY,OAAO,KAAKF,CAAM,EAAG,CAC3C,MAAMG,EAAQH,EAAOE,CAAQ,EAC7B,IAAIE,EAAO,mBAAmBF,CAAQ,EAAI,IAC1C,GAAIC,IAAU,MAAQA,IAAU,IAAM,OAAQA,EAAW,IACxD,GAAI,OAAOA,GAAU,UACpB,UAAWE,KAAO,OAAO,KAAKF,CAAK,EAClC,GAAIA,EAAME,CAAG,IAAM,MAAQF,EAAME,CAAG,IAAM,IAAM,OAAQF,EAAME,CAAG,EAAO,IAAa,CACpF,IAAIL,EAASE,EAAW,IAAMG,EAAM,IACpC,IAAIC,EAAU,mBAAmBN,CAAM,EAAI,IAC3CC,GAAUK,EAAU,mBAAmBH,EAAME,CAAG,CAAC,EAAI,GACrD,OAGFJ,GAAUG,EAAO,mBAAmBD,CAAK,EAAI,GAG/C,CACD,OAAOF,CACR,CAKO,SAASM,GAAa,CAC5B,IAAIC,EAAK,IAAI,KAAM,EAAC,QAAO,EACvBC,EAAO,uCAAuC,QAAQ,QAAS,SAAUC,EAAG,CAC/E,IAAIC,GAAKH,EAAK,KAAK,OAAM,EAAK,IAAM,GAAK,EACzC,OAAAA,EAAK,KAAK,MAAMA,EAAK,EAAE,GACfE,IAAM,IAAMC,EAAKA,EAAI,EAAM,GAAM,SAAS,EAAE,CACtD,CAAE,EACD,OAAOF,CACR,CAmMO,SAASG,GAAc,CAE7B,QADIC,EAAa,CAAA,EACRC,EAAI,IAAKA,GAAK,IAAKA,IAC3BD,EAAW,KAAK,CACf,GAAIC,EACJ,KAAMA,EAAI,IACb,CAAG,EAEF,OAAOD,CACR,CAIO,SAASE,GAAc,CAE7B,QADIC,EAAa,CAAA,EACRF,EAAI,GAAIA,GAAK,IAAKA,IAC1BE,EAAW,KAAK,CACf,GAAIF,EACJ,KAAMA,EAAI,IACb,CAAG,EAEF,OAAOE,CACR,CAMO,SAASC,EAAUhC,EAAM,CAC/B,GAAI,CAACA,EAAM,CACVR,EAAM,QAAQ,EACd,MACA,CACDE,EAAAA,MAAI,iBAAiB,CACpB,KAAM,OAAOM,CAAI,EACjB,QAAS,UAAY,CACpBR,EAAM,MAAM,CACZ,EACD,KAAM,SAAUmB,EAAK,CACpBnB,EAAM,MAAM,CACZ,CACH,CAAE,CACF,CAKO,SAASyC,GAAkB,CACjCzC,EAAM,aAAa,CACpB,CAaO,SAAS0C,EAASC,EAAK,CAC7B,OAAO,OAAOA,GAAQ,QACvB,CAQO,SAASC,EAAcC,EAAUC,EAAS,CAChD,OAAO,IAAI,QAAS1C,GAAY,CAC/BF,EAAAA,MAAI,cAAc,CACjB,IAAK2C,EACL,QAASC,GAAoB,GAC7B,QAAUxC,GAAQ,CACjBF,EAAQE,EAAI,YAAY,CACxB,EACD,KAAOa,GAAQ,CACdjB,EAAAA,6CAAc,UAAWiB,CAAG,EAE5Bf,EAAQyC,CAAQ,CAChB,CACJ,CAAG,CACH,CAAE,CACF,CAOO,SAASE,GAAqB,CACpC,GAAI,CAEH,MAAMC,EAAQ,gBAAiB,EAC/B,GAAI,CAACA,GAAS,CAAC,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,EACvD9C,OAAAA,EAAAA,MAAA,MAAA,OAAA,yBAAa,YAAY,EAClB,GAER,MAAM+C,EAAcD,EAAMA,EAAM,OAAS,CAAC,EAC1C,GAAI,CAACC,EACJ/C,OAAAA,EAAAA,MAAa,MAAA,OAAA,yBAAA,WAAW,EACjB,GAGR,MAAMgD,EAAeD,EAAY,MAC3BE,EAAiBF,EAAY,SAAW,CAAE,EAEhD/C,EAAAA,2CAAY,4BAA6BgD,CAAY,EAGrD,IAAIE,EAAW,IAAMF,EACrB,GAAIC,GAAkB,OAAO,KAAKA,CAAc,EAAE,OAAS,EAAG,CAC7D,MAAME,EAAc,OAAO,KAAKF,CAAc,EAC5C,IAAIvB,GAAO,GAAGA,CAAG,IAAI,mBAAmBuB,EAAevB,CAAG,GAAK,EAAE,CAAC,EAAE,EACpE,KAAK,GAAG,EACVwB,GAAY,IAAMC,CAClB,CACD,OAAOD,CACP,OAAQhC,EAAO,CACflB,OAAAA,EAAAA,6CAAc,cAAekB,CAAK,EAC3B,EACP,CACF,CAOO,SAASkC,GAAc,CAC7B,OAAO,IAAI,QAAQ,CAAClD,EAASC,IAAW,CAEvC,MAAMkD,EAAerD,EAAAA,MAAI,eAAe,eAAe,EACjDsD,EAAc,KAAK,IAAK,EAG9B,GAAID,GAAgBA,EAAa,WAAcC,EAAcD,EAAa,UAAa,IAAQ,CAC9FnD,EAAQ,CACP,UAAWmD,EAAa,UACxB,SAAUA,EAAa,QAC3B,CAAI,EACD,MACA,CAGDrD,EAAAA,MAAI,YAAY,CACf,MAAO,UACV,CAAG,EACDA,EAAAA,MAAI,YAAY,CACf,KAAM,QACN,QAAUI,GAAQ,CACjB,MAAMmD,EAAe,CACpB,UAAWnD,EAAI,UACf,SAAUA,EAAI,SACd,UAAWkD,CACX,EAGDtD,QAAI,eAAe,gBAAiBuD,CAAY,EAChDvD,EAAAA,MAAI,YAAa,EAEZwD,EAAAA,OAAO,eACXC,EAAAA,mBAAmBrD,EAAI,UAAWA,EAAI,QAAQ,EAE/CF,EAAQ,CACP,UAAWE,EAAI,UACf,SAAUA,EAAI,QACnB,CAAK,CACD,EACD,KAAOa,GAAQ,CACdjB,EAAAA,MAAI,YAAa,EACjBA,EAAAA,6CAAc,UAAWiB,CAAG,EAExBoC,GAAgBA,EAAa,WAAaA,EAAa,SAC1DnD,EAAQ,CACP,UAAWmD,EAAa,UACxB,SAAUA,EAAa,QAC7B,CAAM,EAEDlD,EAAOc,CAAG,CAEX,CACJ,CAAG,CACH,CAAE,CACF"}