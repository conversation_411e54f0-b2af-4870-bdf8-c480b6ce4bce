"use strict";const e=require("../../common/vendor.js"),C=require("../../api/manage/tag.js"),c=require("../../api/moment/moment.js");if(!Array){const y=e.resolveComponent("images-select"),v=e.resolveComponent("uni-icons"),_=e.resolveComponent("scroll-nav-page"),i=e.resolveComponent("uni-popup"),s=e.resolveComponent("location-select");(y+v+_+i+s)()}const Y=()=>"../../components/images-select/images-select.js",F=()=>"../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",E=()=>"../../components/scroll-nav-page/scroll-nav-page.js",R=()=>"../../uni_modules/uni-popup/components/uni-popup/uni-popup.js",j=()=>"../../components/location-select/location-select.js";Math||(Y+F+E+R+j)();const A={__name:"add",setup(y){const v=e.ref(0),_=e.ref(0),i=e.reactive({content:"",location:"",visibility:c.VISIBILITY_STATUS.ALL}),s=e.ref([]),l=e.ref([]),a=e.ref([]),p=e.ref(!1),m=e.ref(null),d=e.ref(null),u=e.ref(null),T=e.ref(null),S=e.ref([{value:c.VISIBILITY_STATUS.ALL,label:c.VISIBILITY_STATUS_NAMES[c.VISIBILITY_STATUS.ALL],description:"所有人都可以看到这条动态",icon:"eye"},{value:c.VISIBILITY_STATUS.FOLLOWERS,label:c.VISIBILITY_STATUS_NAMES[c.VISIBILITY_STATUS.FOLLOWERS],description:"只有关注我的人可以看到",icon:"heart"},{value:c.VISIBILITY_STATUS.MUTUAL,label:c.VISIBILITY_STATUS_NAMES[c.VISIBILITY_STATUS.MUTUAL],description:"只有互相关注的人可以看到",icon:"heart-filled"},{value:c.VISIBILITY_STATUS.PRIVATE,label:c.VISIBILITY_STATUS_NAMES[c.VISIBILITY_STATUS.PRIVATE],description:"只有自己可以看到",icon:"locked"}]),g=e.computed(()=>i.content.trim().length>0||s.value.length>0);e.onPageScroll(t=>{v.value=t.scrollTop});const f=()=>{p.value=!0,C.getMomentTagList().then(t=>{a.value=t.data.map(n=>({id:n.id,name:n.name,icon:n.icon}))}).finally(()=>{p.value=!1})},k=async()=>{a.value.length===0&&f(),m.value.open()},h=()=>{m.value.close()},V=t=>{const n=l.value.findIndex(o=>o.id===t.id);n>-1?l.value.splice(n,1):l.value.push(t)},I=t=>l.value.some(n=>n.id===t.id),U=()=>{d.value?d.value.open():e.index.showToast({title:"组件加载失败，请重试",icon:"none"})},B=t=>{i.location=t.name,T.value=t},x=()=>{u.value&&u.value.open()},b=()=>{u.value&&u.value.close()},z=t=>{i.visibility=t,b()},M=t=>{const n=S.value.find(o=>o.value===t);return n?n.label:"所有人"},P=async()=>{if(!g.value){e.index.showToast({title:"请输入内容或选择图片",icon:"none"});return}try{const t={content:i.content,images:s.value.map(o=>o.ossId).join(","),visibilityStatus:i.visibility,tagIds:l.value.map(o=>o.id)};if(T.value){const o=T.value;t.provinceName=o.province||"",t.cityName=o.city||"",t.districtName=o.district||"",t.location=o.city+"·"+i.location,t.lon=o.longitude||0,t.lat=o.latitude||0}e.index.__f__("log","at pagesubs/moment/add.vue:362","发布动态数据:",t);const n=await c.createMoment(t);n.code===200?(e.index.showToast({title:"发布成功",icon:"success"}),setTimeout(()=>{e.index.navigateBack()},1500)):e.index.showToast({title:n.msg||"发布失败，请重试",icon:"none"})}catch(t){e.index.__f__("error","at pagesubs/moment/add.vue:384","发布动态失败:",t),e.index.showToast({title:"发布失败，请检查网络连接",icon:"none"})}};return(t,n)=>e.e({a:i.content,b:e.o(o=>i.content=o.detail.value),c:e.t(i.content.length),d:e.o(o=>s.value=o),e:e.p({limit:6,showTip:"true",type:"user_comment.images",modelValue:s.value}),f:e.p({type:"chat",size:"24",color:"#696CF3"}),g:l.value.length>0},l.value.length>0?{h:e.f(l.value,(o,L,r)=>({a:e.t(o.name),b:o.id}))}:{},{i:e.p({type:"right",size:"16",color:"#999"}),j:e.o(k),k:e.p({type:"location",size:"24",color:"#696CF3"}),l:i.location},i.location?{m:e.t(i.location)}:{},{n:e.p({type:"right",size:"16",color:"#999"}),o:e.o(U),p:e.p({type:"eye",size:"24",color:"#696CF3"}),q:e.t(M(i.visibility)),r:e.p({type:"right",size:"16",color:"#999"}),s:e.o(x),t:e.o(P),v:!g.value,w:_.value+"px",x:e.p({title:"写动态","show-back":!0}),y:e.o(h),z:e.p({type:"close",size:"20",color:"#999"}),A:p.value},p.value?{B:e.p({type:"spinner-cycle",size:"24",color:"#696CF3"})}:a.value.length>0?{D:e.f(a.value,(o,L,r)=>e.e({a:e.t(o.name),b:I(o)},I(o)?{c:"89c61189-11-"+r+",89c61189-8",d:e.p({type:"checkmarkempty",size:"20",color:"#696CF3"})}:{},{e:I(o)?1:"",f:o.id,g:e.o(w=>V(o),o.id)}))}:{E:e.p({type:"chat",size:"48",color:"#ccc"}),F:e.o(f)},{C:a.value.length>0,G:l.value.length>0},l.value.length>0?{H:e.t(l.value.length),I:e.o(h)}:{},{J:e.sr(m,"89c61189-8",{k:"topicPopup"}),K:e.p({type:"bottom","mask-click":!0}),L:e.sr(d,"89c61189-13",{k:"LocationSelectRef"}),M:e.o(B),N:e.p({type:"close",size:"20",color:"#999"}),O:e.o(b),P:e.f(S.value,(o,L,r)=>e.e({a:"89c61189-16-"+r+",89c61189-14",b:e.p({type:o.icon,size:"24",color:i.visibility===o.value?"#696CF3":"#999"}),c:e.t(o.label),d:e.t(o.description),e:i.visibility===o.value},i.visibility===o.value?{f:"89c61189-17-"+r+",89c61189-14",g:e.p({type:"checkmarkempty",size:"20",color:"#696CF3"})}:{},{h:o.value,i:i.visibility===o.value?1:"",j:e.o(w=>z(o.value),o.value)})),Q:e.sr(u,"89c61189-14",{k:"visibilityPopupRef"}),R:e.p({type:"bottom","background-color":"#fff"})})}},N=e._export_sfc(A,[["__scopeId","data-v-89c61189"]]);A.__runtimeHooks=1;wx.createPage(N);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesubs/moment/add.js.map
