// 字典管理
import {
	getDicts
} from '@/api/dict/dict'
import {
	defineStore
} from 'pinia'
import {
	ref
} from 'vue'

// 字典列表
const initTypes = [
	// 性别
	'user_sex',
	// 个人收入状况
	'user_revenue',
	// 个人情感状况
	'user_affective_status',
	// 个人学历类型
	'user_edu',
	// 职业
	'user_job',

	// 个人标签
	'user_tag_ones',
	'user_tag_car',
	'user_tag_house',
	'user_tag_marriage',
	'user_tag_want_marry',

	// 需求标签
	'user_require_tag_edu',
	'user_require_tag_revenue',
	'user_require_tag_house',
	'user_require_tag_marriage',
	'user_require_tag_accept_child',
	'user_require_tag_trait',

	// 推荐设置
	'recommend_set_education',
	'recommend_set_location',
]
const dictStore = defineStore('dict', () => {
	const data = ref({
		// 性别
		// user_sex: [{
		// 	id: '0',
		// 	name: '男'
		// }]
	})

	// Storage key前缀
	const STORAGE_PREFIX = 'dict_'

	/**
	 * 从Storage获取字典数据
	 * @param {string} type
	 */
	function getFromStorage(type) {
		try {
			const storageData = uni.getStorageSync(STORAGE_PREFIX + type)
			return storageData ? JSON.parse(storageData) : null
		} catch (error) {
			console.error('从Storage获取字典数据失败:', type, error)
			return null
		}
	}

	/**
	 * 保存字典数据到Storage
	 * @param {string} type
	 * @param {Array} dictData
	 */
	function saveToStorage(type, dictData) {
		try {
			uni.setStorageSync(STORAGE_PREFIX + type, JSON.stringify(dictData))
		} catch (error) {
			console.error('保存字典数据到Storage失败:', type, error)
		}
	}

	/**
	 * 获取字典数据
	 * @param {string} type
	 */
	function get(type) {
		// 先从内存中取
		if (data.value[type]) {
			return data.value[type]
		}

		// 内存中没有，从Storage取
		const storageData = getFromStorage(type)
		if (storageData) {
			// 将Storage中的数据加载到内存
			data.value[type] = storageData
			return storageData
		}

		// 都没有，返回空数组
		return []
	}


	/**
	 * 获取字典数据
	 * @param {string} type
	 */
	function getByIndex(type, index) {
		if (index) {
			return data[type][index]
		}
		return get(type)
	}

	/**
	 * 获取字典数据
	 * @param {int} index
	 */
	function getIdByIndex(type, index) {
		const dicts = get(type)
		index = parseInt(index)
		if (dicts && dicts.length > index) {
			return dicts[index].id
		}
		return '';
	}

	/**
	 * 获取字典数据
	 * @param {string} id
	 */
	function getIndexById(type, id) {
		const dicts = get(type)
		if (dicts && dicts.length > 0) {
			for (let i = 0; i < dicts.length; i++) {
				if (dicts[i].id == id) {
					return i
				}
			}
		}
		return 0;
	}


	/**
	 * 获取字典数据
	 * @param {string} id
	 */
	function getNameById(type, id) {
		const dicts = get(type)
		if (dicts && dicts.length > 0) {
			for (let i = 0; i < dicts.length; i++) {
				if (dicts[i].id == id) {
					return dicts[i].name
				}
			}
		}
		return 0;
	}

	/**
	 * 获取字典名称
	 * @param {string} type
	 */
	function getNames(type) {
		const dicts = get(type)
		if (dicts && dicts.length > 0) {
			return dicts.map(item => item.name)
		}
		return []
	}

	/**
	 * 刷新字典数据
	 */
	function refresh() {
		var loadTypes = initTypes;
		Object.keys(data.value).forEach(key => {
			loadTypes.push(key)
		})
		loads(loadTypes)
	}

	/**
	 * 加载字典数据
	 * @param {Object} type
	 */
	function load(type) {
		loads([type])
	}

	/**
	 * 批量加载字典数据
	 * @param {Object} types
	 */
	function loads(types) {
		var pullTypes = []
		types.forEach((type) => {
			// 先检查内存
			if (!data.value[type]) {
				// 内存中没有，检查Storage
				const storageData = getFromStorage(type)
				if (storageData) {
					// Storage中有数据，加载到内存
					data.value[type] = storageData
				} else {
					// Storage中也没有，需要远程拉取
					pullTypes.push(type)
				}
			}
		})
		if (pullTypes.length > 0) {
			remoteLoads(pullTypes)
		}
	}

	/**
	 * 远程加载字典数据
	 * @param {ArrayL} types
	 */
	async function remoteLoads(types) {
		var res = await getDicts(types)
		set(res.data)
	}

	/**
	 * 设置字典数据
	 * @param {Object} dicts
	 */
	function set(dicts) {
		dicts.forEach(function (ele) {
			// 设置到内存
			data.value[ele.type] = ele.vals;
			// 同时保存到Storage
			saveToStorage(ele.type, ele.vals);
		});
	}

	/**
	 * 清除字典缓存
	 * @param {string} type - 可选，指定类型。不传则清除所有
	 */
	function clearCache(type) {
		if (type) {
			// 清除指定类型
			delete data.value[type]
			try {
				uni.removeStorageSync(STORAGE_PREFIX + type)
			} catch (error) {
				console.error('清除Storage缓存失败:', type, error)
			}
		} else {
			// 清除所有缓存
			data.value = {
				user_sex: [{
					id: '0',
					name: '男'
				}]
			}
			try {
				// 获取所有Storage key，清除字典相关的
				const storageInfo = uni.getStorageInfoSync()
				storageInfo.keys.forEach(key => {
					if (key.startsWith(STORAGE_PREFIX)) {
						uni.removeStorageSync(key)
					}
				})
			} catch (error) {
				console.error('清除所有Storage缓存失败:', error)
			}
		}
	}

	// 返回
	return {
		get,
		getByIndex,
		getIdByIndex,
		getIndexById,
		getNameById,
		getNames,
		refresh,
		loads,
		clearCache
	}
})
export default dictStore