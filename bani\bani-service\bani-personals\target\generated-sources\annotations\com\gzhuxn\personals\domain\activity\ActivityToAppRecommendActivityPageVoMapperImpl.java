package com.gzhuxn.personals.domain.activity;

import com.gzhuxn.personals.controller.app.recommend.vo.activity.AppRecommendActivityPageVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class ActivityToAppRecommendActivityPageVoMapperImpl implements ActivityToAppRecommendActivityPageVoMapper {

    @Override
    public AppRecommendActivityPageVo convert(Activity arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppRecommendActivityPageVo appRecommendActivityPageVo = new AppRecommendActivityPageVo();

        appRecommendActivityPageVo.setId( arg0.getId() );
        appRecommendActivityPageVo.setGroupId( arg0.getGroupId() );
        appRecommendActivityPageVo.setName( arg0.getName() );
        appRecommendActivityPageVo.setEnrollStartTime( arg0.getEnrollStartTime() );
        appRecommendActivityPageVo.setEnrollEndTime( arg0.getEnrollEndTime() );
        appRecommendActivityPageVo.setStartTime( arg0.getStartTime() );
        appRecommendActivityPageVo.setEndTime( arg0.getEndTime() );
        appRecommendActivityPageVo.setTimeLength( arg0.getTimeLength() );
        appRecommendActivityPageVo.setOfficialFlag( arg0.getOfficialFlag() );
        appRecommendActivityPageVo.setOriginalAmount( arg0.getOriginalAmount() );
        appRecommendActivityPageVo.setAmount( arg0.getAmount() );
        appRecommendActivityPageVo.setStatus( arg0.getStatus() );
        appRecommendActivityPageVo.setType( arg0.getType() );
        appRecommendActivityPageVo.setCreateByName( arg0.getCreateByName() );

        return appRecommendActivityPageVo;
    }

    @Override
    public AppRecommendActivityPageVo convert(Activity arg0, AppRecommendActivityPageVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setGroupId( arg0.getGroupId() );
        arg1.setName( arg0.getName() );
        arg1.setEnrollStartTime( arg0.getEnrollStartTime() );
        arg1.setEnrollEndTime( arg0.getEnrollEndTime() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setTimeLength( arg0.getTimeLength() );
        arg1.setOfficialFlag( arg0.getOfficialFlag() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setCreateByName( arg0.getCreateByName() );

        return arg1;
    }
}
