package com.gzhuxn.personals.controller.app.user.bo.moment;

import com.gzhuxn.personals.domain.user.UserMoment;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserMomentUpdateBoToUserMomentMapperImpl implements AppUserMomentUpdateBoToUserMomentMapper {

    @Override
    public UserMoment convert(AppUserMomentUpdateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserMoment userMoment = new UserMoment();

        userMoment.setId( arg0.getId() );
        userMoment.setContent( arg0.getContent() );
        userMoment.setImages( arg0.getImages() );
        userMoment.setLocation( arg0.getLocation() );
        userMoment.setLon( arg0.getLon() );
        userMoment.setLat( arg0.getLat() );
        userMoment.setVisibilityStatus( arg0.getVisibilityStatus() );

        return userMoment;
    }

    @Override
    public UserMoment convert(AppUserMomentUpdateBo arg0, UserMoment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setContent( arg0.getContent() );
        arg1.setImages( arg0.getImages() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );
        arg1.setVisibilityStatus( arg0.getVisibilityStatus() );

        return arg1;
    }
}
