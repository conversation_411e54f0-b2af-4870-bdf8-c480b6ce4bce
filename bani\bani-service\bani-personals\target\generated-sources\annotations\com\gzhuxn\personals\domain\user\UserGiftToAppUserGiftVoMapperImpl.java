package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.gift.AppUserGiftVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserGiftToAppUserGiftVoMapperImpl implements UserGiftToAppUserGiftVoMapper {

    @Override
    public AppUserGiftVo convert(UserGift arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserGiftVo appUserGiftVo = new AppUserGiftVo();

        appUserGiftVo.setId( arg0.getId() );
        appUserGiftVo.setUserId( arg0.getUserId() );
        appUserGiftVo.setGiftId( arg0.getGiftId() );
        appUserGiftVo.setGiftName( arg0.getGiftName() );
        appUserGiftVo.setGiftPrice( arg0.getGiftPrice() );
        appUserGiftVo.setGiftNum( arg0.getGiftNum() );
        appUserGiftVo.setCoin( arg0.getCoin() );

        return appUserGiftVo;
    }

    @Override
    public AppUserGiftVo convert(UserGift arg0, AppUserGiftVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setGiftId( arg0.getGiftId() );
        arg1.setGiftName( arg0.getGiftName() );
        arg1.setGiftPrice( arg0.getGiftPrice() );
        arg1.setGiftNum( arg0.getGiftNum() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
