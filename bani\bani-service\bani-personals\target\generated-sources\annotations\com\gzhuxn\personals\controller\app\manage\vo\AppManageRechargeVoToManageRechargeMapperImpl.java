package com.gzhuxn.personals.controller.app.manage.vo;

import com.gzhuxn.personals.domain.manage.ManageRecharge;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:58+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppManageRechargeVoToManageRechargeMapperImpl implements AppManageRechargeVoToManageRechargeMapper {

    @Override
    public ManageRecharge convert(AppManageRechargeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ManageRecharge manageRecharge = new ManageRecharge();

        manageRecharge.setId( arg0.getId() );
        manageRecharge.setOriginalAmount( arg0.getOriginalAmount() );
        manageRecharge.setAmount( arg0.getAmount() );
        if ( arg0.getDiscountRate() != null ) {
            manageRecharge.setDiscountRate( arg0.getDiscountRate().intValue() );
        }
        manageRecharge.setDiscountType( arg0.getDiscountType() );
        manageRecharge.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        manageRecharge.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        manageRecharge.setWithdrawCoin( arg0.getWithdrawCoin() );
        manageRecharge.setCoin( arg0.getCoin() );

        return manageRecharge;
    }

    @Override
    public ManageRecharge convert(AppManageRechargeVo arg0, ManageRecharge arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setOriginalAmount( arg0.getOriginalAmount() );
        arg1.setAmount( arg0.getAmount() );
        if ( arg0.getDiscountRate() != null ) {
            arg1.setDiscountRate( arg0.getDiscountRate().intValue() );
        }
        else {
            arg1.setDiscountRate( null );
        }
        arg1.setDiscountType( arg0.getDiscountType() );
        arg1.setDiscountExpireTime( arg0.getDiscountExpireTime() );
        arg1.setDiscountLimitNum( arg0.getDiscountLimitNum() );
        arg1.setWithdrawCoin( arg0.getWithdrawCoin() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
