package com.gzhuxn.personals.controller.app.user.vo;

import com.gzhuxn.personals.domain.user.vo.UserRequireTagVo;
import com.gzhuxn.personals.domain.user.vo.UserTagVo;
import com.gzhuxn.personals.domain.user.vo.UserTransitionTagVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserEditDetailVoToAppUserDetailVoMapperImpl implements AppUserEditDetailVoToAppUserDetailVoMapper {

    @Override
    public AppUserDetailVo convert(AppUserEditDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserDetailVo appUserDetailVo = new AppUserDetailVo();

        appUserDetailVo.setUserId( arg0.getUserId() );
        appUserDetailVo.setNickName( arg0.getNickName() );
        appUserDetailVo.setName( arg0.getName() );
        appUserDetailVo.setSex( arg0.getSex() );
        appUserDetailVo.setAvatar( arg0.getAvatar() );
        appUserDetailVo.setPid( arg0.getPid() );
        appUserDetailVo.setBirthday( arg0.getBirthday() );
        appUserDetailVo.setStar( arg0.getStar() );
        appUserDetailVo.setAnimal( arg0.getAnimal() );
        appUserDetailVo.setHeight( arg0.getHeight() );
        appUserDetailVo.setWeight( arg0.getWeight() );
        appUserDetailVo.setEdu( arg0.getEdu() );
        appUserDetailVo.setJob( arg0.getJob() );
        appUserDetailVo.setAffectiveStatus( arg0.getAffectiveStatus() );
        appUserDetailVo.setRevenue( arg0.getRevenue() );
        appUserDetailVo.setWechat( arg0.getWechat() );
        appUserDetailVo.setAddr( arg0.getAddr() );
        appUserDetailVo.setAddrNew( arg0.getAddrNew() );
        appUserDetailVo.setProgress( arg0.getProgress() );
        appUserDetailVo.setAuditStatus( arg0.getAuditStatus() );
        appUserDetailVo.setIsIdentity( arg0.getIsIdentity() );
        appUserDetailVo.setUserLevel( arg0.getUserLevel() );
        appUserDetailVo.setIsMatched( arg0.getIsMatched() );
        appUserDetailVo.setTags( userTagVoListToUserTransitionTagVoList( arg0.getTags() ) );
        appUserDetailVo.setRequireTags( userRequireTagVoListToUserTransitionTagVoList( arg0.getRequireTags() ) );

        return appUserDetailVo;
    }

    @Override
    public AppUserDetailVo convert(AppUserEditDetailVo arg0, AppUserDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setName( arg0.getName() );
        arg1.setSex( arg0.getSex() );
        arg1.setAvatar( arg0.getAvatar() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setAffectiveStatus( arg0.getAffectiveStatus() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setIsIdentity( arg0.getIsIdentity() );
        arg1.setUserLevel( arg0.getUserLevel() );
        arg1.setIsMatched( arg0.getIsMatched() );
        if ( arg1.getTags() != null ) {
            List<UserTransitionTagVo> list = userTagVoListToUserTransitionTagVoList( arg0.getTags() );
            if ( list != null ) {
                arg1.getTags().clear();
                arg1.getTags().addAll( list );
            }
            else {
                arg1.setTags( null );
            }
        }
        else {
            List<UserTransitionTagVo> list = userTagVoListToUserTransitionTagVoList( arg0.getTags() );
            if ( list != null ) {
                arg1.setTags( list );
            }
        }
        if ( arg1.getRequireTags() != null ) {
            List<UserTransitionTagVo> list1 = userRequireTagVoListToUserTransitionTagVoList( arg0.getRequireTags() );
            if ( list1 != null ) {
                arg1.getRequireTags().clear();
                arg1.getRequireTags().addAll( list1 );
            }
            else {
                arg1.setRequireTags( null );
            }
        }
        else {
            List<UserTransitionTagVo> list1 = userRequireTagVoListToUserTransitionTagVoList( arg0.getRequireTags() );
            if ( list1 != null ) {
                arg1.setRequireTags( list1 );
            }
        }

        return arg1;
    }

    protected UserTransitionTagVo userTagVoToUserTransitionTagVo(UserTagVo userTagVo) {
        if ( userTagVo == null ) {
            return null;
        }

        UserTransitionTagVo userTransitionTagVo = new UserTransitionTagVo();

        userTransitionTagVo.setNamespace( userTagVo.getNamespace() );
        userTransitionTagVo.setTagKey( userTagVo.getTagKey() );
        userTransitionTagVo.setTagVal( userTagVo.getTagVal() );
        userTransitionTagVo.setTagValName( userTagVo.getTagValName() );

        return userTransitionTagVo;
    }

    protected List<UserTransitionTagVo> userTagVoListToUserTransitionTagVoList(List<UserTagVo> list) {
        if ( list == null ) {
            return null;
        }

        List<UserTransitionTagVo> list1 = new ArrayList<UserTransitionTagVo>( list.size() );
        for ( UserTagVo userTagVo : list ) {
            list1.add( userTagVoToUserTransitionTagVo( userTagVo ) );
        }

        return list1;
    }

    protected UserTransitionTagVo userRequireTagVoToUserTransitionTagVo(UserRequireTagVo userRequireTagVo) {
        if ( userRequireTagVo == null ) {
            return null;
        }

        UserTransitionTagVo userTransitionTagVo = new UserTransitionTagVo();

        userTransitionTagVo.setNamespace( userRequireTagVo.getNamespace() );
        userTransitionTagVo.setTagKey( userRequireTagVo.getTagKey() );
        userTransitionTagVo.setTagVal( userRequireTagVo.getTagVal() );
        userTransitionTagVo.setTagValName( userRequireTagVo.getTagValName() );

        return userTransitionTagVo;
    }

    protected List<UserTransitionTagVo> userRequireTagVoListToUserTransitionTagVoList(List<UserRequireTagVo> list) {
        if ( list == null ) {
            return null;
        }

        List<UserTransitionTagVo> list1 = new ArrayList<UserTransitionTagVo>( list.size() );
        for ( UserRequireTagVo userRequireTagVo : list ) {
            list1.add( userRequireTagVoToUserTransitionTagVo( userRequireTagVo ) );
        }

        return list1;
    }
}
