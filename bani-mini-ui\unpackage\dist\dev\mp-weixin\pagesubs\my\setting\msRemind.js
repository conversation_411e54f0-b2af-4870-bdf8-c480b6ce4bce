"use strict";const a=require("../../../common/vendor.js"),n=require("../../../utils/common.js"),o=require("../../../api/my/config.js");if(!Array){const c=a.resolveComponent("uni-icons"),s=a.resolveComponent("scroll-nav-page");(c+s)()}const y=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js",v=()=>"../../../components/scroll-nav-page/scroll-nav-page.js";Math||(y+v)();const l={__name:"msRemind",setup(c){const s=a.ref(0),_=a.ref(0);a.ref(!1);const t=a.reactive({ms_interaction_daily_visit:!0,ms_interaction_apply_wechat:!0,ms_recommend_daily:!0,ms_activity_subscription:!0,ms_system:!0});a.onPageScroll(e=>{s.value=e.scrollTop});const r=e=>{t.ms_interaction_daily_visit=e.detail.value,n.toast(e.detail.value?"已开启每日来访提醒":"已关闭每日来访提醒"),o.updateUserConfig({configKey:"ms_interaction_daily_visit",val:e.detail.value?1:0})},m=e=>{t.ms_recommend_daily=e.detail.value,n.toast(e.detail.value?"已开启每日推荐消息":"已关闭每日推荐消息"),o.updateUserConfig({configKey:"ms_recommend_daily",val:e.detail.value?1:0})},u=e=>{t.ms_interaction_apply_wechat=e.detail.value,n.toast(e.detail.value?"已开启微信获取通知":"已关闭微信获取通知"),o.updateUserConfig({configKey:"ms_interaction_apply_wechat",val:e.detail.value?1:0})},p=e=>{t.ms_activity_subscription=e.detail.value,n.toast(e.detail.value?"已开启活动订阅通知":"已关闭活动订阅通知"),o.updateUserConfig({configKey:"ms_activity_subscription",val:e.detail.value?1:0})},d=e=>{t.ms_system=e.detail.value,n.toast(e.detail.value?"已开启系统消息":"已关闭系统消息"),o.updateUserConfig({configKey:"ms_system",val:e.detail.value?1:0})};return a.onLoad(()=>{o.getUserConfigMap(1).then(e=>{if(e.data)for(let i in e.data)t[i]instanceof Boolean||typeof t[i]=="boolean"?t[i]=e.data[i]==="1":t[i]=e.data[i]})}),(e,i)=>({a:a.p({type:"eye",size:"20",color:"#696CF3"}),b:t.ms_interaction_daily_visit,c:a.o(r),d:a.p({type:"chatbubble",size:"20",color:"#696CF3"}),e:t.ms_interaction_apply_wechat,f:a.o(u),g:a.p({type:"heart",size:"20",color:"#696CF3"}),h:t.ms_recommend_daily,i:a.o(m),j:a.p({type:"gift",size:"20",color:"#696CF3"}),k:t.ms_activity_subscription,l:a.o(p),m:a.p({type:"notification",size:"20",color:"#696CF3"}),n:t.ms_system,o:a.o(d),p:_.value+"px",q:a.p({title:"消息通知","show-back":!0})})}},g=a._export_sfc(l,[["__scopeId","data-v-9410bf39"]]);l.__runtimeHooks=1;wx.createPage(g);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/my/setting/msRemind.js.map
