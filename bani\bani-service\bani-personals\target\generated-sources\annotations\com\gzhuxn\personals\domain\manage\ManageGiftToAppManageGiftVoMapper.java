package com.gzhuxn.personals.domain.manage;

import com.gzhuxn.personals.controller.app.manage.vo.AppManageGiftVo;
import com.gzhuxn.personals.controller.app.manage.vo.AppManageGiftVoToManageGiftMapper;
import com.gzhuxn.personals.domain.manage.bo.ManageGiftBoToManageGiftMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {ManageGiftBoToManageGiftMapper.class,AppManageGiftVoToManageGiftMapper.class,ManageGiftToAdminManageGiftVoMapper.class,ManageGiftToManageGiftVoMapper.class},
    imports = {}
)
public interface ManageGiftToAppManageGiftVoMapper extends BaseMapper<ManageGift, AppManageGiftVo> {
}
