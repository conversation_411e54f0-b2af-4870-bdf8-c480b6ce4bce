package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.vo.UserDetailVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserDetailToUserDetailVoMapperImpl implements UserDetailToUserDetailVoMapper {

    @Override
    public UserDetailVo convert(UserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetailVo userDetailVo = new UserDetailVo();

        userDetailVo.setUserId( arg0.getUserId() );
        userDetailVo.setPid( arg0.getPid() );
        userDetailVo.setBirthday( arg0.getBirthday() );
        userDetailVo.setStar( arg0.getStar() );
        userDetailVo.setAnimal( arg0.getAnimal() );
        userDetailVo.setHeight( arg0.getHeight() );
        userDetailVo.setWeight( arg0.getWeight() );
        userDetailVo.setEdu( arg0.getEdu() );
        userDetailVo.setJob( arg0.getJob() );
        userDetailVo.setRevenue( arg0.getRevenue() );
        userDetailVo.setWechat( arg0.getWechat() );
        userDetailVo.setAddr( arg0.getAddr() );
        userDetailVo.setAddrNew( arg0.getAddrNew() );
        userDetailVo.setProgress( arg0.getProgress() );
        userDetailVo.setAuditStatus( arg0.getAuditStatus() );
        userDetailVo.setLon( arg0.getLon() );
        userDetailVo.setLat( arg0.getLat() );

        return userDetailVo;
    }

    @Override
    public UserDetailVo convert(UserDetail arg0, UserDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setPid( arg0.getPid() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setStar( arg0.getStar() );
        arg1.setAnimal( arg0.getAnimal() );
        arg1.setHeight( arg0.getHeight() );
        arg1.setWeight( arg0.getWeight() );
        arg1.setEdu( arg0.getEdu() );
        arg1.setJob( arg0.getJob() );
        arg1.setRevenue( arg0.getRevenue() );
        arg1.setWechat( arg0.getWechat() );
        arg1.setAddr( arg0.getAddr() );
        arg1.setAddrNew( arg0.getAddrNew() );
        arg1.setProgress( arg0.getProgress() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setLon( arg0.getLon() );
        arg1.setLat( arg0.getLat() );

        return arg1;
    }
}
