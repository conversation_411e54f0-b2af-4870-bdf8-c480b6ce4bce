package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.album.AppUserAlbumListVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAlbumToAppUserAlbumListVoMapperImpl implements UserAlbumToAppUserAlbumListVoMapper {

    @Override
    public AppUserAlbumListVo convert(UserAlbum arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserAlbumListVo appUserAlbumListVo = new AppUserAlbumListVo();

        appUserAlbumListVo.setId( arg0.getId() );
        appUserAlbumListVo.setImage( arg0.getImage() );
        appUserAlbumListVo.setCreateTime( arg0.getCreateTime() );

        return appUserAlbumListVo;
    }

    @Override
    public AppUserAlbumListVo convert(UserAlbum arg0, AppUserAlbumListVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setImage( arg0.getImage() );
        arg1.setCreateTime( arg0.getCreateTime() );

        return arg1;
    }
}
