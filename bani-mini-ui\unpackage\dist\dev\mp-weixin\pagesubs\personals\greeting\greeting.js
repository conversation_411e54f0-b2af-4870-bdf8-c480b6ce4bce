"use strict";const e=require("../../../common/vendor.js"),o=require("../../../api/user/greeting.js");Array||e.resolveComponent("uni-icons")();const g=()=>"../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";Math||(g+_)();const _=()=>"../../../components/scroll-nav-page/scroll-nav-page.js",v={__name:"greeting",setup(i){let a=e.ref(null),r=e.ref({});const t=e.ref("嗨，想和你认识一下～"),s=e.computed(()=>t.value.trim().length>0&&t.value.trim().length<=60);e.onLoad(n=>{e.index.__f__("log","at pagesubs/personals/greeting/greeting.vue:77","打招呼页面参数:",n),n&&n.userId&&(a.value=n.userId,r.value={userId:n.userId,nickName:n.nickName||"用户",avatar:n.avatar||""},c())});const c=async()=>{try{(await o.checkUserGreeted(a.value)).data&&e.index.showModal({title:"提示",content:"您已经向该用户打过招呼了，请等待对方回复",showCancel:!1,success:()=>{e.index.navigateBack()}})}catch(n){e.index.__f__("error","at pagesubs/personals/greeting/greeting.vue:104","检查打招呼状态失败:",n)}},u=async()=>{if(!s.value){e.index.showToast({title:"请输入打招呼内容",icon:"none"});return}if(!a.value){e.index.showToast({title:"用户信息错误",icon:"none"});return}try{e.index.showLoading({title:"发送中..."}),await o.sendUserGreeting({oppositeUserId:a.value,content:t.value.trim()}),e.index.hideLoading(),e.index.showToast({title:"打招呼发送成功",icon:"success"}),setTimeout(()=>{e.index.navigateBack()},1500)}catch(n){e.index.hideLoading(),e.index.__f__("error","at pagesubs/personals/greeting/greeting.vue:149","发送打招呼失败:",n),e.index.showToast({title:n.message||"发送失败，请重试",icon:"none"})}},l=()=>{e.index.navigateBack()};return(n,h)=>({a:e.p({type:"hand",size:"20",color:"#696CF3"}),b:t.value,c:e.o(d=>t.value=d.detail.value),d:e.t(t.value.length),e:e.o(l),f:e.o(u),g:s.value?"":1,h:e.p({type:"vip-filled",size:"16",color:"#FFD700"}),i:e.p({title:"林风婚恋","show-back":!0})})}},p=e._export_sfc(v,[["__scopeId","data-v-90759be4"]]);wx.createPage(p);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesubs/personals/greeting/greeting.js.map
