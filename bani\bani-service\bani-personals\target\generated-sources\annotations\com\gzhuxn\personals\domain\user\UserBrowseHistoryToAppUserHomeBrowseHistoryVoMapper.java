package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.browsehistory.AppUserHomeBrowseHistoryVo;
import com.gzhuxn.personals.domain.user.bo.UserBrowseHistoryBoToUserBrowseHistoryMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserBrowseHistoryBoToUserBrowseHistoryMapper.class,UserBrowseHistoryToUserBrowseHistoryVoMapper.class},
    imports = {}
)
public interface UserBrowseHistoryToAppUserHomeBrowseHistoryVoMapper extends BaseMapper<UserBrowseHistory, AppUserHomeBrowseHistoryVo> {
}
