package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.bo.questionapply.AppUserQuestionApplyAnswerBoToUserQuestionApplyMapper;
import com.gzhuxn.personals.controller.app.user.bo.questionapply.AppUserQuestionApplyBoToUserQuestionApplyMapper;
import com.gzhuxn.personals.domain.user.bo.UserQuestionApplyBoToUserQuestionApplyMapper;
import com.gzhuxn.personals.domain.user.vo.UserQuestionApplyVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {AppUserQuestionApplyBoToUserQuestionApplyMapper.class,AppUserQuestionApplyAnswerBoToUserQuestionApplyMapper.class,UserQuestionApplyBoToUserQuestionApplyMapper.class},
    imports = {}
)
public interface UserQuestionApplyToUserQuestionApplyVoMapper extends BaseMapper<UserQuestionApply, UserQuestionApplyVo> {
}
