package com.gzhuxn.personals.controller.app.message.vo;

import com.gzhuxn.personals.domain.message.MsgGroup;
import com.gzhuxn.personals.domain.message.MsgGroupToAppMsgGroupDetailVoMapper;
import com.gzhuxn.personals.domain.message.MsgGroupUserToAppMsgGroupDetailUserVoMapper;
import com.gzhuxn.personals.domain.message.MsgGroupUserToAppMsgGroupUserVoMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {MsgGroupUserToAppMsgGroupUserVoMapper.class,MsgGroupUserToAppMsgGroupDetailUserVoMapper.class,MsgGroupToAppMsgGroupDetailVoMapper.class},
    imports = {}
)
public interface AppMsgGroupDetailVoToMsgGroupMapper extends BaseMapper<AppMsgGroupDetailVo, MsgGroup> {
}
