package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.signin.AppUserSignInResultVo;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserSignInToAppUserSignInResultVoMapperImpl implements UserSignInToAppUserSignInResultVoMapper {

    @Override
    public AppUserSignInResultVo convert(UserSignIn arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserSignInResultVo appUserSignInResultVo = new AppUserSignInResultVo();

        appUserSignInResultVo.setId( arg0.getId() );
        appUserSignInResultVo.setDate( arg0.getDate() );
        appUserSignInResultVo.setConsecutiveDays( arg0.getConsecutiveDays() );
        appUserSignInResultVo.setCoin( arg0.getCoin() );

        return appUserSignInResultVo;
    }

    @Override
    public AppUserSignInResultVo convert(UserSignIn arg0, AppUserSignInResultVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setDate( arg0.getDate() );
        arg1.setConsecutiveDays( arg0.getConsecutiveDays() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
