package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.domain.user.bo.UserVipRecordBoToUserVipRecordMapper;
import com.gzhuxn.personals.domain.user.vo.UserVipRecordVo;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserVipRecordBoToUserVipRecordMapper.class},
    imports = {}
)
public interface UserVipRecordToUserVipRecordVoMapper extends BaseMapper<UserVipRecord, UserVipRecordVo> {
}
