package com.gzhuxn.personals.domain.user;

import com.gzhuxn.personals.controller.app.user.vo.account.AppUserAccountDetailVo;
import com.gzhuxn.personals.domain.user.bo.UserAccountBoToUserAccountMapper;
import io.github.linpeilie.AutoMapperConfig__1077;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__1077.class,
    uses = {UserAccountBoToUserAccountMapper.class,UserAccountToUserAccountVoMapper.class},
    imports = {}
)
public interface UserAccountToAppUserAccountDetailVoMapper extends BaseMapper<UserAccount, AppUserAccountDetailVo> {
}
