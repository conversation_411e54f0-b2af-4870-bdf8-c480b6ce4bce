package com.gzhuxn.personals.controller.app.user.vo.invitation;

import com.gzhuxn.personals.domain.user.UserInvitation;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:57+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class AppUserInvitationVoToUserInvitationMapperImpl implements AppUserInvitationVoToUserInvitationMapper {

    @Override
    public UserInvitation convert(AppUserInvitationVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserInvitation userInvitation = new UserInvitation();

        userInvitation.setId( arg0.getId() );
        userInvitation.setUserId( arg0.getUserId() );
        userInvitation.setOppositeUserId( arg0.getOppositeUserId() );

        return userInvitation;
    }

    @Override
    public UserInvitation convert(AppUserInvitationVo arg0, UserInvitation arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setOppositeUserId( arg0.getOppositeUserId() );

        return arg1;
    }
}
