package com.gzhuxn.personals.domain.user.bo;

import com.gzhuxn.personals.domain.user.UserAccount;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-13T00:00:56+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.3 (Oracle Corporation)"
)
@Component
public class UserAccountBoToUserAccountMapperImpl implements UserAccountBoToUserAccountMapper {

    @Override
    public UserAccount convert(UserAccountBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserAccount userAccount = new UserAccount();

        userAccount.setSearchValue( arg0.getSearchValue() );
        userAccount.setCreateBy( arg0.getCreateBy() );
        userAccount.setCreateTime( arg0.getCreateTime() );
        userAccount.setUpdateBy( arg0.getUpdateBy() );
        userAccount.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userAccount.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userAccount.setCreateDept( arg0.getCreateDept() );
        userAccount.setUserId( arg0.getUserId() );
        userAccount.setCoin( arg0.getCoin() );

        return userAccount;
    }

    @Override
    public UserAccount convert(UserAccountBo arg0, UserAccount arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setCoin( arg0.getCoin() );

        return arg1;
    }
}
