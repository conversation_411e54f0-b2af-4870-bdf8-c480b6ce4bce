{"version": 3, "file": "gift.js", "sources": ["pagesubs/my/gift/gift.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXN1YnNcbXlcZ2lmdFxnaWZ0LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<scroll-nav-page title=\"礼物记录\" :show-back=\"true\" @heightChange=\"handleNavHeightChange\">\r\n\t\t<template #content>\r\n\t\t\t<z-paging ref=\"paging\" v-model=\"datas\" :auto=\"true\" :refresher-enabled=\"true\" :loading-more-enabled=\"true\"\r\n\t\t\t\t@query=\"queryList\">\r\n\t\t\t\t<template #top>\r\n\t\t\t\t\t<!-- 顶部菜单 -->\r\n\t\t\t\t\t<view class=\"top-menu margin-split\" :style=\"{ paddingTop: navBarHeight + 'px' }\">\r\n\t\t\t\t\t\t<uni-segmented-control :current=\"segmentedIndex\" :values=\"['收到的礼物', '发出的礼物']\"\r\n\t\t\t\t\t\t\t@clickItem=\"switchTab\" styleType=\"text\" activeColor=\"#696CF3\"></uni-segmented-control>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t<!-- 自定义刷新组件 -->\r\n\t\t\t\t<template #refresher=\"{ refresherStatus }\">\r\n\t\t\t\t\t<custom-refresher :refresher-status=\"refresherStatus\" />\r\n\t\t\t\t</template>\r\n\t\t\t\t<!-- 礼物记录列表 -->\r\n\t\t\t\t<view class=\"gift-list margin-split\">\r\n\t\t\t\t\t<view class=\"gift-card\" v-for=\"(item, index) in datas\" :key=\"item.id\">\r\n\t\t\t\t\t\t<!-- 卡片内容 -->\r\n\t\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t\t<view class=\"user-section\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"item.oppAvatar\"></image>\r\n\t\t\t\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t\t\t\t<!-- 昵称与时间同一行 -->\r\n\t\t\t\t\t\t\t\t\t<view class=\"name-time-row\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"name-icons\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"nick-name\">{{ item.oppNickName }}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<!-- 性别图标 -->\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"iconfont gender-icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:class=\"item.oppSex === '0' ? 'bani-xingbie-nan' : 'bani-xingbie-nv'\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:style=\"{ color: item.oppSex === '0' ? '#4A90E2' : '#E91E63' }\"></text>\r\n\t\t\t\t\t\t\t\t\t\t\t<text v-if=\"item.oppIsIdentity\" class=\"verified-tag\">已实名</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"time\">{{ item.time }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<text class=\"user-detail\">{{ item.oppAge }} · {{ item.oppHeight }} · {{ item.oppCity }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- 礼物信息 -->\r\n\t\t\t\t\t\t\t<view class=\"gift-section\">\r\n\t\t\t\t\t\t\t\t<view class=\"gift-detail\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"gift-name\">{{ item.giftName }}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"gift-count\"> × {{ item.giftNum }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<text class=\"gift-price\">{{ item.coin }}花瓣</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 空状态 -->\r\n\t\t\t\t<template #empty>\r\n\t\t\t\t\t<view class=\"empty-state\">\r\n\t\t\t\t\t\t<image class=\"empty-icon\" src=\"/static/image/empty.png\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t<text class=\"empty-text\">暂无礼物记录</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t</z-paging>\r\n\t\t</template>\r\n\t</scroll-nav-page>\r\n</template>\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { onLoad, onPageScroll } from \"@dcloudio/uni-app\"\r\nimport { getUserGiftPage } from '@/api/my/gift'\r\n\r\n// 导航栏相关\r\nconst pageScrollTop = ref(0)\r\nconst navBarHeight = ref(0) // 初始为0，等待组件传递真实高度\r\n\r\n// 页面数据\r\nconst segmentedIndex = ref(0)\r\nconst queryType = ref(2) // 1-我送给他人的礼物、2-送给我的礼物\r\n\r\n// z-paging组件\r\nconst paging = ref(null)\r\nconst datas = ref([])\r\n\r\n// 页面滚动监听\r\nonPageScroll((e) => {\r\n\tpageScrollTop.value = e.scrollTop\r\n})\r\n\r\n// 导航栏高度变化处理\r\nconst handleNavHeightChange = (height) => {\r\n\tnavBarHeight.value = height\r\n}\r\n\r\nonLoad((param) => {\r\n\t// 默认显示收到的礼物\r\n\tif (!param.type) {\r\n\t\tparam.type = 2\r\n\t}\r\n\tconst type = parseInt(param.type)\r\n\tsegmentedIndex.value = type === 2 ? 0 : 1 // 2-收到的礼物(index=0), 1-发出的礼物(index=1)\r\n\tqueryType.value = type\r\n})\r\n\r\nfunction queryList(pageNum, pageSize) {\r\n\tgetUserGiftPage({\r\n\t\tpageNum: pageNum,\r\n\t\tpageSize: pageSize,\r\n\t\tqueryType: queryType.value,\r\n\t}).then(res => {\r\n\t\tpaging.value.complete(res.rows);\r\n\t})\r\n}\r\n\r\nconst switchTab = (e) => {\r\n\tif (segmentedIndex.value !== e.currentIndex) {\r\n\t\tsegmentedIndex.value = e.currentIndex\r\n\t\t// 0-收到的礼物(queryType=2), 1-发出的礼物(queryType=1)\r\n\t\tqueryType.value = segmentedIndex.value === 0 ? 2 : 1\r\n\t\tpaging.value.reload()\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/uni.scss';\r\n\r\n// z-paging组件样式\r\n:deep(.z-paging-content) {\r\n\tmin-height: calc(100vh - 200px);\r\n}\r\n\r\n.gift-list {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 16rpx;\r\n}\r\n\r\n.gift-card {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba($primary-color, 0.08);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\toverflow: hidden;\r\n\ttransition: all 0.3s ease;\r\n\r\n\t&:hover {\r\n\t\ttransform: translateY(-2rpx);\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba($primary-color, 0.12);\r\n\t}\r\n\r\n\t.card-content {\r\n\t\tpadding: 24rpx;\r\n\r\n\t\t.user-section {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-start;\r\n\t\t\tgap: 16rpx;\r\n\t\t\tmargin-bottom: 16rpx;\r\n\r\n\t\t\t.avatar {\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba($primary-color, 0.15);\r\n\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.user-info {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmin-width: 0;\r\n\r\n\t\t\t\t.name-time-row {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\r\n\t\t\t\t\t.name-icons {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tgap: 6rpx;\r\n\r\n\t\t\t\t\t\t.nick-name {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tletter-spacing: 0.5rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.gender-icon {\r\n\t\t\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.verified-tag {\r\n\t\t\t\t\t\t\tbackground: #696CF3;\r\n\t\t\t\t\t\t\tcolor: white;\r\n\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\tpadding: 2rpx 6rpx;\r\n\t\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.time {\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-detail {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.gift-section {\r\n\t\t\tbackground: rgba($primary-color, 0.05);\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 16rpx 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.gift-detail {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.gift-name {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: $primary-color;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.gift-count {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.gift-price {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #FF6B9D;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tbackground: rgba(255, 107, 157, 0.1);\r\n\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 空状态样式\r\n.empty-state {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 120rpx 40rpx;\r\n\r\n\t.empty-icon {\r\n\t\twidth: 200rpx;\r\n\t\theight: 200rpx;\r\n\t\tmargin-bottom: 32rpx;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.empty-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: 500;\r\n\t}\r\n}\r\n</style>", "import MiniProgramPage from 'E:/bani/code/bani-mini-ui/pagesubs/my/gift/gift.vue'\nwx.createPage(MiniProgramPage)"], "names": ["pageScrollTop", "ref", "navBarHeight", "segmentedIndex", "queryType", "paging", "datas", "onPageScroll", "e", "handleNavHeightChange", "height", "onLoad", "param", "type", "queryList", "pageNum", "pageSize", "getUserGiftPage", "res", "switchTab", "MiniProgramPage"], "mappings": "8qBAqEA,MAAAA,EAAAC,EAAA,IAAA,CAAA,EACAC,EAAAD,EAAA,IAAA,CAAA,EAGAE,EAAAF,EAAA,IAAA,CAAA,EACAG,EAAAH,EAAA,IAAA,CAAA,EAGAI,EAAAJ,EAAA,IAAA,IAAA,EACAK,EAAAL,EAAA,IAAA,EAAA,EAGAM,EAAA,aAAAC,GAAA,CACAR,EAAA,MAAAQ,EAAA,SACA,CAAA,EAGA,MAAAC,EAAAC,GAAA,CACAR,EAAA,MAAAQ,CACA,EAEAC,EAAA,OAAAC,GAAA,CAEAA,EAAA,OACAA,EAAA,KAAA,GAEA,MAAAC,EAAA,SAAAD,EAAA,IAAA,EACAT,EAAA,MAAAU,IAAA,EAAA,EAAA,EACAT,EAAA,MAAAS,CACA,CAAA,EAEA,SAAAC,EAAAC,EAAAC,EAAA,CACAC,kBAAA,CACA,QAAAF,EACA,SAAAC,EACA,UAAAZ,EAAA,KACA,CAAA,EAAA,KAAAc,GAAA,CACAb,EAAA,MAAA,SAAAa,EAAA,IAAA,CACA,CAAA,CACA,CAEA,MAAAC,EAAAX,GAAA,CACAL,EAAA,QAAAK,EAAA,eACAL,EAAA,MAAAK,EAAA,aAEAJ,EAAA,MAAAD,EAAA,QAAA,EAAA,EAAA,EACAE,EAAA,MAAA,OAAA,EAEA,05BCpHA,GAAG,WAAWe,CAAe"}